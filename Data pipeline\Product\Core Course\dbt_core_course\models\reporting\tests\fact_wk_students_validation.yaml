version: 2

models:
  - name: fact_wk_students
    columns:
      - name: first_week_date
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        tests:
          - not_null:
              severity: error
      - name: group_id
        tests:
          - not_null:
              severity: error
      - name: lab_teacher_id
        tests:
          - not_null:
              severity: error
      - name: consultant_id
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        tests:
          - not_null:
              severity: error
      - name: product_type
        tests:
          - accepted_values:
              values: ['d2c', 'core course']
              severity: error
      - name: status
        tests:
          - accepted_values:
              values: ['valid']
              severity: error
      - name: location
        tests:
          - accepted_values:
              values: ['InCenter', 'OutCenter']
              severity: error
      - name: class_access_type
        tests:
          - accepted_values:
              values: ['No Access', 'In-Center', 'Online', 'Full Access']
              severity: error
      - name: service_type
        tests:
          - accepted_values:
              values: ['Standard', 'VIP', 'Teen Standard', 'Teen VIP']
              severity: error
      - name: is_membership
        tests:
          - accepted_values:
              values: ['Membership', 'Level']
              severity: error
      - name: is_teen
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: is_promotional
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: study_plan_type
        tests:
          - accepted_values:
              values: ['twelveweeks', 'eightweeks_default', 'fourweeks', 'eightweeks', 'not_applicable']
              severity: error
      - name: self_booking_access_flag
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: dbt_unique_id
        tests:
          - not_null:
              severity: error
          - unique:
              severity: warn
