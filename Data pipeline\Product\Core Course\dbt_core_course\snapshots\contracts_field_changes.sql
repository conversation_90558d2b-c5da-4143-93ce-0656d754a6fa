{% snapshot contracts_field_changes %}
{{
 config(
 unique_key='contract_id',
 target_schema ='snapshot',
 strategy='check',
 check_cols=['contract_id',
'consultant_id',
'lab_teacher_id',
'group_id',
'service_type',
'location',
'class_access_type',
'is_membership',
'student_id',
'center_id',
'status',
'start_date',
'end_date'
],
 )
}}

WITH contract_acccess_type as (
    select 
        contract_id ,
        CASE
            WHEN COUNT(DISTINCT class_type_description) = 1 THEN MAX(class_type_description)
            ELSE 'full access'
        END AS class_type 
    from {{ref('dt_cs_contract_class_access_type')}}
    where is_active = true 
    group by contract_id
),

contracts_field_changes as(
    select 
        contracts.contract_reference_id as contract_id
        ,users.user_reference_id as student_id
        ,cs_centers.center_reference_id as center_id
        ,contracts.consultant_id
        ,contracts.lab_teacher_id
        ,contracts.group_id
        ,contracts.status
        ,contracts.service_type
        ,contracts.location
        ,contract_acccess_type.class_type as class_access_type
        ,contracts.is_membership
        ,contracts.start_date
        ,contracts.end_date
    from {{ref('dt_cs_contracts')}} as contracts
    left join contract_acccess_type 
    on contract_acccess_type.contract_id = contracts.id
    left join {{ref('dt_cs_centers')}} as cs_centers
        on cs_centers.id = contracts.center_id
    left join {{ref('dt_cs_users')}} as users
        on contracts.student_id = users.id
)

select 
    contract_id
    ,student_id
    ,center_id
    ,consultant_id
    ,lab_teacher_id
    ,group_id
    ,status
    ,service_type
    ,location
    ,class_access_type
    ,is_membership
    ,start_date
    ,end_date
FROM contracts_field_changes
{% endsnapshot %}
