{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = ['id', 'template_id'],
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}



SELECT
    {{etl_load_date()}},
    id,
    name AS notification_name,
    CAST(from_iso8601_timestamp(createdat) AS timestamp(6)) AS created_at,
    CAST(from_iso8601_timestamp(updatedat) AS timestamp(6)) AS updated_at,
    step.name AS step_name,
    variant.name AS variant_name,
    variant.templateid AS template_id
FROM
{{ source('stage_novu_service','notification_templates') }}
CROSS JOIN 
    UNNEST(CAST(json_parse(steps) AS ARRAY<ROW(name VARCHAR, variants ARRAY<ROW(name VARCHAR, templateid VARCHAR)>)>)) AS t(step)
CROSS JOIN 
    UNNEST(step.variants) AS t2(variant)