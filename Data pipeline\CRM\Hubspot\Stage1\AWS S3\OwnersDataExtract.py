import logging

import authentication
import requests
import DataFormatingTeams
import PostApiCall
import AssociationFormatting
import SalesPipeline
import json

OauthConnect = authentication.Oauth
DataFormatConnect = DataFormatingTeams.OwnersDataFormatting
AssociationConnect = AssociationFormatting.AssociationFormatting
SalesPipelineConnect = SalesPipeline.PipelineFormatting
ApiConnect = PostApiCall

OwnersDataExtract = []
OwnersData = []


class OwnersDataFetch:
    @staticmethod
    def GetExtract(ConfigInfo, Bucket, Properties, DefaultProperties, CycleId, Object):
        global AdditionalProperty, TerritoryCode, DataExtract, Queryvalue
        OwnersPropertiesStructure = {}
        DataProperties = Properties + DefaultProperties
        for SingleProperty in DataProperties:
            OwnersPropertiesStructure[SingleProperty] = ''
        AccessToken = OauthConnect.authenticate(ConfigInfo['Territory'])
        Operation = ConfigInfo['Operation']
        Headers = {'accept': 'application/json', 'Authorization': 'Bearer ' + AccessToken}
        if Object == 'owners' and ConfigInfo['Operation'] == 'data_extract':
            Queryvalue = {"limit": "400", "archived": "false", "properties":DataProperties}
        if Object in ['customobject']:
            Queryvalue = {"limit": "100", "archived": "false", "associations": "contacts", "properties":DataProperties}
        if Object in ['associationdeals', 'associationcontacts','associationcompanies']:
            Queryvalue = {"limit": "100", "archived": "false", "associations": "contacts,deals,companies", "properties":DataProperties}
        if Object in ['contacts', 'deals', 'owners', 'companies'] and ConfigInfo['Operation'] == 'data_extract_archive':
            Queryvalue = {"limit": "100", "archived": "true", "properties":DataProperties}
        if Object in ['salespipeline']:
            Queryvalue = {"limit": "100", "archived": "false"}

        QueryString = Queryvalue
        logging.warning(QueryString)
        DataRequestUrl = ConfigInfo['Url']
        OwnersDataResponse = PostApiCall.ApiCall(DataRequestUrl=DataRequestUrl, DefaultInputJson=None, Headers=Headers,
                                                 Object=Object, QueryString=QueryString, Operation=Operation)
        # OwnersDataResponse = requests.request("GET", DataRequestUrl, headers=Headers,
        #                                         params=OwnersQueryString).json()
        if 'status' in OwnersDataResponse:
            error = OwnersDataResponse['error']
            logging.warning(error)
        else:
            # print(OwnersDataResponse['results'])
            TotalRecord = len(OwnersDataResponse['results'])
            TerritoryCode = ConfigInfo['Territory']
            CycleId=CycleId
            if TotalRecord == 0:
                logging.warning("No records")
            if TotalRecord != 0:
                AdditionalProperty = {"territory_code": TerritoryCode, "cycleid": CycleId}
                for Count in range(0, TotalRecord, 1):
                    OwnersDataResponse['results'][Count].update(AdditionalProperty)
                OwnersDataExtract.extend(OwnersDataResponse['results'])
                logging.warning("It is here")
        while "paging" in OwnersDataResponse:  # 10
            DataRequestUrl = OwnersDataResponse["paging"]["next"]["link"]
            OwnersDataResponse = PostApiCall.ApiCall(DataRequestUrl=DataRequestUrl, DefaultInputJson=None,
                                                     Headers=Headers, Object=Object, QueryString=None,
                                                     Operation=Operation)
            if "results" in OwnersDataResponse:
                TotalRecord = len(OwnersDataResponse["results"])
                for Count in range(0, TotalRecord, 1):  # 55
                    OwnersDataResponse["results"][Count].update(AdditionalProperty)
                OwnersDataExtract.extend(OwnersDataResponse["results"])
            else:
                if OwnersDataResponse['status'] == 'error' and OwnersDataResponse[
                    'category'] == 'EXPIRED_AUTHENTICATION':
                    AccessToken = OauthConnect.authenticate(ConfigInfo['Territory'])
                    Headers = {'accept': 'application/json', 'Authorization': 'Bearer ' + AccessToken}
                    OwnersDataResponse = PostApiCall.ApiCall(DataRequestUrl=DataRequestUrl, DefaultInputJson=None,
                                                             Headers=Headers, Object=Object, QueryString=None,
                                                             Operation=Operation)
        logging.warning("It is outer formatting")

        if TerritoryCode == 'CH' and (Object in ['contacts', 'deals'] and ConfigInfo['Operation'] == 'data_extract_archive' or Object in ['associationdeals', 'associationcontacts']):
            center_list = [
                "WSE Bienne", "WSE Fribourg", "WSE Genève",
                "WSE Juniors Fribourg", "WSE Juniors Genève",
                "WSE Juniors Lausanne", "WSE Juniors Montreux",
                "WSE La Chaux-de-Fonds", "WSE Lausanne",
                "WSE Lugano", "WSE Montreux", "WSE Neuchâtel",
                "WSE Online", "WSE Praha"
            ]
            brand_name_list = ['WSE']
            # Filter dictionaries based on condition2_value
            filtered_list_of_dicts = []
            for dict in OwnersDataExtract:
                center_name = dict['properties']['center_name']
                if Object in ['contacts', 'associationcontacts']:
                    brand_name = dict['properties']['brand_name']
                    if center_name in center_list and brand_name in brand_name_list:
                        filtered_list_of_dicts.append(dict)
                    else:
                        logging.warning("center does not belong")
                if Object in ['deals', 'associationdeals']:
                    if center_name in center_list:
                        filtered_list_of_dicts.append(dict)
                    else:
                        logging.warning("center does not belong")
            logging.warning(len(filtered_list_of_dicts))
            if Object in ['associationdeals', 'associationcontacts']:
                logging.warning("entering formating")
                DataExtract = AssociationConnect.associationformatting(filtered_list_of_dicts, TerritoryCode, CycleId,
                                                                       Object, OwnersPropertiesStructure,
                                                                       DataProperties)

            if Object in ['contacts', 'deals'] and Operation == 'data_extract_archive':
                DataExtract = AssociationConnect.GetExtractDataFormatting(DataExtract=filtered_list_of_dicts,
                                                                          TerritoryCode=TerritoryCode, CycleId=CycleId,
                                                                          PropertiesStructure=OwnersPropertiesStructure,
                                                                          DataProperties=DataProperties)

        else:
            if Object == 'owners' and Operation == 'data_extract':
                DataExtract = DataFormatConnect.TeamsOwnerFormatting(OwnersDataExtract, CycleId, TerritoryCode)
            if Object in ['associationcompanies', 'customobject', 'associationdeals', 'associationcontacts']:
                logging.warning("entering formating")
                DataExtract = AssociationConnect.associationformatting(OwnersDataExtract, TerritoryCode, CycleId, Object, OwnersPropertiesStructure, DataProperties)

            if Object in ['owners', 'companies','contacts', 'deals'] and Operation == 'data_extract_archive':
                DataExtract = AssociationConnect.GetExtractDataFormatting(DataExtract=OwnersDataExtract,
                                                                          TerritoryCode=TerritoryCode, CycleId=CycleId,
                                                                          PropertiesStructure=OwnersPropertiesStructure,
                                                                          DataProperties=DataProperties )
            if Object in ['salespipeline']:
                DataExtract = SalesPipelineConnect.pipeline_flatten(OwnersDataExtract, DataProperties, AdditionalProperty)
        return DataExtract
