{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_idam_user_roles') }}
)
SELECT {{etl_load_date()}},
    userroles.id AS id,
    user_basic_info_id,
    role.description as role,
    Case
        When role.description not in (
            'student',
            'prospect'
        ) Then 'staff'
        When role.description in (
            'student',
            'prospect'
        ) Then 'student'
        Else role.description
    End as role_type,
    created,
    last_updated,
    is_active
from
    ods_data as userroles
    Left Join (
        select
            id,
            description
        from
            {{ ref('ods_idam_role') }}
    ) as role
    ON userroles.role_id = role.id
