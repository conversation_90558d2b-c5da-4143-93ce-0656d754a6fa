{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}


Select 
    mess.message_id,
    mess.student_reference_id,
    mess.channel,
    mess.title,
    mess.notification_id,
    mess.notification_type,
    mess.variant_id,
    mess.variant,
    mess.created_at,
    mess.updated_at
FROM 
    {{ ref('dt_novu_messages') }} as mess
UNION
Select 
    jobs.message_id,
    jobs.student_reference_id,
    jobs.channel,
    jobs.title,
    jobs.notification_id,
    jobs.notification_type,
    jobs.variant_id,
    jobs.variant,
    jobs.created_at,
    jobs.updated_at
FROM
    {{ ref('dt_novu_jobs') }} as jobs