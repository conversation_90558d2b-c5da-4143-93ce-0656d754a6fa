import os
from airflow import DAG
from airflow.operators.python_operator import Python<PERSON><PERSON><PERSON>
from airflow.operators.trigger_dagrun import TriggerDagRunOperator
from datetime import timed<PERSON><PERSON>, datetime
from airflow.operators.bash import BashOperator
from dependencies.step_function_trigger import execute_step_function  # Import your external module
from dependencies.slack_alerts import task_failure_callback, task_warning_callback, task_success_callback
from dependencies.cloud_operations import S3
from dependencies import db_operations, glue_trigger
from dependencies.pipeline_prerequisite import toggle_dag_state
import logging
import re

# Define your DAG
default_args = {
    'owner': 'wse_data_team',
    'start_date': datetime(2023, 10, 30)
}

redshift_execute = db_operations.Database

dag = DAG('core_course_workflow',
        default_args=default_args,
        schedule_interval='0 2,7,21 * * *',  # You can set your desired schedule_interval
        catchup=False
        )


# Define the tasks
# function to call prerequisite
def execute_prerequisite():
    toggle_dag_state(dag)


def execute_external_module():
    result = execute_step_function(
        state_machine_arn='arn:aws:states:eu-west-1:262158335980:stateMachine:core_course_data_ingestion_statemachine',
        input_data={
            "input": "airflow execution"
        })  # Call the function with your input parameters
    return result


def ods_checkpoint_log():
    print("ODS Execution Completed")
    
def dt_checkpoint_log():
    print("DT Execution Completed")

def stage_checkpoint_log():
    print("Stage Execution Completed")

def redshift_copy_command(**kwargs):
    table_name,layer = kwargs['table_name'],kwargs['layer']
    file_path = 's3://prod-corecourse/'+S3.dynamic_file_path('prod-corecourse','data/'+layer+'/'+table_name+'/')+'data/'
    redshift_table_name = f"{layer}.{table_name}"
    
    pre_reqisite_query = """TRUNCATE TABLE {} """.format(redshift_table_name)
    truncate_statement = "truncating the table " + redshift_table_name + " before copying the latest data"
    truncate_execute = redshift_execute.execution('WriteTable', pre_reqisite_query, truncate_statement)
    print(truncate_execute)
    
    statement_name = "This query is "+ layer +" copy of table " + redshift_table_name + " to redshift"
    copy_command_query = """COPY {}
    FROM '{}'
    IAM_ROLE 'arn:aws:iam::262158335980:role/RedshitS3access'
    PARQUET;""".format(redshift_table_name, file_path)
    print(copy_command_query)
    copy_execute = redshift_execute.execution('WriteTable', copy_command_query, statement_name)
    print(copy_execute)
    print("Data ",layer," copy from S3 to redshift completed for table", redshift_table_name)


def redshift_sp(**kwargs):
    query = kwargs['query']
    redshift_execute.execution('WriteTable',query,'Executing stored procedure : {}'.format(query))
    print('Stored Procedure Execution Completed')

# function to check dbt test logs

def parse_dbt_test_results(output):
    pattern = r"Done\.\s+PASS=(\d+)\s+WARN=(\d+)\s+ERROR=(\d+)\s+SKIP=(\d+)\s+TOTAL=(\d+)"
    match = re.search(pattern, output)
    
    if match:
        return {
            'PASS': int(match.group(1)),
            'WARN': int(match.group(2)),
            'ERROR': int(match.group(3)),
            'SKIP': int(match.group(4)),
            'TOTAL': int(match.group(5))
        }
    return None

# this will enhanced in the upcomming iteration 
dbt_test_list =['dbt_test_stage_analytics','dbt_test_analytics','dbt_test_reporting'] 

def check_dbt_output(**context):
    ti = context['ti']
    for dbt_test_task_id in dbt_test_list:
        dbt_output = ti.xcom_pull(task_ids=dbt_test_task_id)
        
        if not dbt_output:
            error_message = f"No output received from {dbt_test_task_id} task."
            logging.error(error_message)
            context['task_instance'].xcom_push(key='error_message', value=error_message)
            raise Exception(error_message)
        
        results = parse_dbt_test_results(dbt_output)
        if results:
            summary = f"DBT Test Results: PASS={results['PASS']} WARN={results['WARN']} ERROR={results['ERROR']} SKIP={results['SKIP']} TOTAL={results['TOTAL']}"
            logging.info(summary)
            
            if results['ERROR'] > 0:
                message = f"{dbt_test_task_id} DBT tests completed with errors: {summary}"
                logging.error(message)
                context['task_instance'].xcom_push(key='error_message', value=message)
                raise Exception(message)
            elif results['WARN'] > 0:
                message = f"{dbt_test_task_id} DBT tests completed with warnings: {summary}"
                logging.warning(message)
                context['task_instance'].xcom_push(key='warning_message', value=message)
                task_warning_callback(context)
            else:
                message = f"{dbt_test_task_id} DBT tests completed successfully: {summary}"
                logging.info(message)
        else:
            error_message = f"{dbt_test_task_id} Could not parse DBT test results from the output."
            logging.error(error_message)
            context['task_instance'].xcom_push(key='error_message', value=error_message)
            raise Exception(error_message)

# basic prerequisite check
prerequisite_check = PythonOperator(
    task_id='prerequisite_check',
    python_callable=execute_prerequisite,
    provide_context=True,
    dag=dag
)

# execute step function
raw_layer = PythonOperator(
    task_id='raw_layer',
    python_callable=execute_external_module,
    on_failure_callback=task_failure_callback,
    retries=1,  # Number of retries
    retry_delay=timedelta(minutes=5),
    dag=dag
)

process_tasks = []
collection_list = ['jobs','messages','subscribers','notificationtemplates']
for i  in collection_list:
    op_kwargs = {
        "glue_job_name": "Novu DB data ingestion",
        "glue_args": {
            "--collection_name": i
        }
    }
    task = PythonOperator(
        task_id=f"raw_layer_novu_{i}",
        python_callable=glue_trigger.run_glue_job,
        # on_failure_callback=task_failure_callback,
        op_kwargs=op_kwargs,
        retries=1,  # Number of retries
        retry_delay=timedelta(minutes=5),
        dag=dag
    )
    process_tasks.append(task)
process_tasks.append(raw_layer)
# execute dbt
HOME = os.environ["HOME"]  # retrieve the location of your home folder
dbt_path = os.path.join(HOME, "dbt/dbt_core_course")  # path to your dbt project
print("dbt path: ", dbt_path)

ods_learning_service = BashOperator(
    task_id="ods_learning_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models ods.learning_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

ods_center_configuration_service = BashOperator(
    task_id="ods_center_configuration_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models ods.center_configuration_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

ods_idam_service = BashOperator(
    task_id="ods_idam_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models ods.idam_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

ods_schedule_and_booking_service = BashOperator(
    task_id="ods_schedule_and_booking_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models ods.schedule_and_booking_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

ods_digital_student_workbook_service = BashOperator(
    task_id="ods_digital_student_workbook_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models ods.digital_student_workbook_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

ods_prospect_service = BashOperator(
    task_id="ods_prospect_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models ods.prospect_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

ods_novu_service = BashOperator(
    task_id="ods_contract_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models ods.novu_service.*",  # run the model!
    # on_failure_callback=task_failure_callback,
    dag=dag
)

ods_conversation_ai_service = BashOperator(
    task_id="ods_conversation_ai_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models ods.conversation_ai_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

onboarding_service = BashOperator(
    task_id="ods_onboarding_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models ods.onboarding_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

dt_learning_service = BashOperator(
    task_id="dt_learning_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models data_transformation.learning_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

dt_center_configuration_service = BashOperator(
    task_id="dt_center_configuration_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models data_transformation.center_configuration_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

dt_idam_service = BashOperator(
    task_id="dt_idam_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models data_transformation.idam_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

dt_schedule_and_booking_service = BashOperator(
    task_id="dt_schedule_and_booking_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models data_transformation.schedule_and_booking_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

dt_digital_student_workbook_service = BashOperator(
    task_id="dt_digital_student_workbook_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models data_transformation.digital_student_workbook_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

dt_novu_service = BashOperator(
    task_id="dt_contract_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models data_transformation.novu_service.*",  # run the model!
    # on_failure_callback=task_failure_callback,
    dag=dag
)

dt_prospect_service = BashOperator(
    task_id="dt_prospect_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models data_transformation.prospect_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

dt_conversation_ai_service = BashOperator(
    task_id="dt_conversation_ai_service",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models data_transformation.conversation_ai_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

raw_checkpoint = PythonOperator(
    task_id='raw_checkpoint',
    python_callable=ods_checkpoint_log,
    on_failure_callback=task_failure_callback,
    dag=dag
)

ods_checkpoint = PythonOperator(
    task_id='ods_checkpoint',
    python_callable=ods_checkpoint_log,
    on_failure_callback=task_failure_callback,
    dag=dag
)

dt_checkpoint  = PythonOperator(
    task_id='dt_checkpoint',
    python_callable=dt_checkpoint_log,
    on_failure_callback=task_failure_callback,
    dag=dag
)

# stage_checkpoint = PythonOperator(
#     task_id='stage_checkpoint',
#     python_callable=stage_checkpoint_log,
#     on_failure_callback=task_failure_callback,
#     dag=dag
# )

stage_analytics = BashOperator(
    task_id="stage_analytics",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models stage_analytics.*",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

analytics = BashOperator(
    task_id="analytics",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models analytics.daily.* --exclude tag:speaking_ai_beta",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

reporting = BashOperator(
    task_id="reporting",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models reporting.* --exclude tag:speaking_ai_beta",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

dbt_test_stage_analytics = BashOperator(
    task_id="dbt_test_stage_analytics",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt test --models stage_analytics.tests.*",  # run the test!
    on_failure_callback=task_failure_callback,
    dag=dag
)

dbt_test_analytics = BashOperator(
    task_id="dbt_test_analytics",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt test --models analytics.tests.*",  # run the test!
    on_failure_callback=task_failure_callback,
    dag=dag
)

dbt_test_reporting = BashOperator(
    task_id="dbt_test_reporting",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt test --models reporting.tests.*",  # run the test!
    on_failure_callback=task_failure_callback,
    dag=dag
)

check_dbt_output_task = PythonOperator(
    task_id='check_dbt_output',
    python_callable=check_dbt_output,
    provide_context=True,
    on_failure_callback=task_failure_callback,  # Add this line
    on_success_callback =task_success_callback,
    dag=dag
)

# stage_analytics_layer = ['ls_booked_student', 'snb_scheduled_class_property', 'union_classes']

# for stage_table in stage_analytics_layer:
#     op_kwargs = {'table_name': stage_table,'layer' : 'stage_analytics'}
#     stage_analytics_to_redshift = PythonOperator(
#         task_id=f"stage_analytics_to_redshift_{stage_table}",
#         python_callable=redshift_copy_command,  
#         on_failure_callback=task_failure_callback,
#         op_kwargs=op_kwargs,
#         dag=dag
#     )
    
#     # Set up task Dependancy for identical tasks
#     reporting >> stage_analytics_to_redshift >> stage_checkpoint

analytics_layer = ['users','class_bookings']

# ['b2b_contracts', 'bookings', 'class_bookings',
#                 'classes','contracts','territory_centers',
#                 'users']

hubspot_redshift_procedure = PythonOperator(
        task_id=f"hubspot_redshift_procedure",
        python_callable=redshift_sp,  
        on_failure_callback=task_failure_callback,
        op_kwargs={'query':'call hubspot_crm.sp_hubspot_core_course_mapping()'},
        dag=dag
    )

trigger_powerbi_dag = TriggerDagRunOperator(
    task_id='trigger_powerbi_dag',
    trigger_dag_id='core_course_powerbi_dag',
    wait_for_completion=False,
    on_failure_callback=task_failure_callback,
    dag=dag
)

trigger_backup_critical_tables_dag = TriggerDagRunOperator(
    task_id='trigger_backup_critical_tables_dag',
    trigger_dag_id='backup_critical_tables',
    wait_for_completion=False,
    on_failure_callback=task_failure_callback,
    dag=dag
)

for analytics_table in analytics_layer:
    op_kwargs = {'table_name': analytics_table,'layer' : 'analytics'}
    analytics_to_redshift = PythonOperator(
        task_id=f"analytics_to_redshift_{analytics_table}",
        python_callable=redshift_copy_command,  
        on_failure_callback=task_failure_callback,
        op_kwargs=op_kwargs,
        dag=dag
    )
    
    # Set up task Dependancy for identical tasks
    reporting >> analytics_to_redshift >>hubspot_redshift_procedure


# Set up the task dependency
prerequisite_check >> process_tasks >> raw_checkpoint
raw_checkpoint >> [ods_learning_service, ods_center_configuration_service, ods_idam_service,
            ods_schedule_and_booking_service, ods_digital_student_workbook_service, ods_prospect_service, ods_conversation_ai_service, onboarding_service, ods_novu_service] >> ods_checkpoint
ods_checkpoint >> [dt_learning_service, dt_center_configuration_service, dt_idam_service,
                dt_schedule_and_booking_service, dt_digital_student_workbook_service, dt_prospect_service, dt_conversation_ai_service, dt_novu_service] >> dt_checkpoint
dt_checkpoint >> stage_analytics
stage_analytics >> analytics >> reporting
hubspot_redshift_procedure >> dbt_test_stage_analytics >> dbt_test_analytics >> dbt_test_reporting >> check_dbt_output_task >> trigger_powerbi_dag >> trigger_backup_critical_tables_dag


if __name__ == "__main__":
    dag.cli()
