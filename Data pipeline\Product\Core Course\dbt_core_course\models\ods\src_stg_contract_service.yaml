version: 2

sources:
  - name: stage_contract_service
    description: >
      Source data from the Contract Service which manages student contracts, master contracts,
      products, pricing, and related configurations for education centers.
    database: awsdatacatalog
    schema: stg_contract_service
    tables:
      - name: b2bcontracttypes
        description: Types of business-to-business contracts available in the system.
        columns:
          - name: id
            description: Primary key for the B2B contract type
          - name: name
            description: Name of the B2B contract type

      - name: centerclassaccess
        description: Configuration for class access permissions at the center level.
        columns:
          - name: id
            description: Primary key for the center class access record
          - name: centerid
            description: Foreign key reference to the center
          - name: classaccesstypeid
            description: Foreign key reference to the class access type

      - name: centerproducts
        description: Products available at specific centers.
        columns:
          - name: id
            description: Primary key for the center product record
          - name: centerid
            description: Foreign key reference to the center
          - name: productid
            description: Foreign key reference to the product

      - name: centers
        description: Education centers registered in the contract system.
        columns:
          - name: id
            description: Primary key for the center record
          - name: centerreferenceid
            description: External reference ID for the center
          - name: name
            description: Name of the center
          - name: territoryid
            description: Foreign key reference to the territory

      - name: classaccesstypes
        description: Types of class access permissions available in the system.
        columns:
          - name: id
            description: Primary key for the class access type
          - name: name
            description: Name of the class access type

      - name: companyadditionalinfo
        description: Additional information about companies with B2B contracts.
        columns:
          - name: id
            description: Primary key for the company additional info record
          - name: companyid
            description: Foreign key reference to the company
          - name: contactperson
            description: Name of the contact person at the company
          - name: email
            description: Email address for the company contact

      - name: contractauditfields
        description: Fields that are tracked for auditing contract changes.
        columns:
          - name: id
            description: Primary key for the contract audit field
          - name: fieldname
            description: Name of the field being audited

      - name: contractchangetypes
        description: Types of changes that can be made to contracts.
        columns:
          - name: id
            description: Primary key for the contract change type
          - name: name
            description: Name of the contract change type

      - name: contractchanges
        description: Records of changes made to contracts.
        columns:
          - name: id
            description: Primary key for the contract change record
          - name: contractid
            description: Foreign key reference to the contract
          - name: changetypeid
            description: Foreign key reference to the change type
          - name: oldvalue
            description: Previous value before the change
          - name: newvalue
            description: New value after the change
          - name: createddate
            description: Timestamp when the change was created

      - name: contractclassaccesstype
        description: Class access types associated with specific contracts.
        columns:
          - name: id
            description: Primary key for the contract class access type record
          - name: contractid
            description: Foreign key reference to the contract
          - name: classaccesstypeid
            description: Foreign key reference to the class access type

      - name: contractdocumenttypes
        description: Types of documents that can be associated with contracts.
        columns:
          - name: id
            description: Primary key for the contract document type
          - name: name
            description: Name of the document type

      - name: contractprice
        description: Pricing information for contracts.
        columns:
          - name: id
            description: Primary key for the contract price record
          - name: contractid
            description: Foreign key reference to the contract
          - name: price
            description: Price amount for the contract
          - name: currency
            description: Currency code for the price

      - name: contractproducts
        description: Products associated with specific contracts.
        columns:
          - name: id
            description: Primary key for the contract product record
          - name: contractid
            description: Foreign key reference to the contract
          - name: productid
            description: Foreign key reference to the product

      - name: contracts
        description: Individual student contracts with details about services, pricing, and status.
        columns:
          - name: id
            description: Primary key for the contract record
          - name: code
            description: Code identifier for the contract
          - name: number
            description: Contract number
          - name: crmcontractnumber
            description: Contract number in the CRM system
          - name: producttypeid
            description: Foreign key reference to the product type
          - name: servicetype
            description: Foreign key reference to the service type
          - name: studentid
            description: Foreign key reference to the student
          - name: centerid
            description: Foreign key reference to the center
          - name: startdate
            description: Start date for the contract
          - name: enddate
            description: End date for the contract
          - name: startlevelid
            description: Starting level for the student
          - name: endlevelid
            description: Target ending level for the student
          - name: price
            description: Price of the contract
          - name: state
            description: Current state of the contract
          - name: status
            description: Current status of the contract
          - name: consultantid
            description: ID of the consultant who sold the contract
          - name: labteacherid
            description: ID of the lab teacher assigned to the student
          - name: contracttype
            description: Type of contract
          - name: contractreferenceid
            description: External reference ID for the contract
          - name: createddate
            description: Timestamp when the contract was created
          - name: lastupdateddate
            description: Timestamp when the contract was last updated

      - name: contractsauditinfo
        description: Audit information for contract changes.
        columns:
          - name: id
            description: Primary key for the contract audit record
          - name: contractid
            description: Foreign key reference to the contract
          - name: modifiedfieldid
            description: ID of the field that was modified
          - name: oldvalue
            description: Previous value before the change
          - name: newvalue
            description: New value after the change
          - name: modifiedbyid
            description: ID of the user who made the change
          - name: modifieddate
            description: Timestamp when the change was made

      - name: contractsourcetypes
        description: Source types for contracts (e.g., online, in-center).
        columns:
          - name: id
            description: Primary key for the contract source type
          - name: name
            description: Name of the source type

      - name: contractstates
        description: Possible states for contracts in their lifecycle.
        columns:
          - name: id
            description: Primary key for the contract state
          - name: name
            description: Name of the contract state

      - name: contractstatuses
        description: Possible statuses for contracts.
        columns:
          - name: id
            description: Primary key for the contract status
          - name: name
            description: Name of the contract status

      - name: contracttransfers
        description: Records of contract transfers between centers or students.
        columns:
          - name: id
            description: Primary key for the contract transfer record
          - name: contractid
            description: Foreign key reference to the contract
          - name: fromcenterid
            description: ID of the center transferring from
          - name: tocenterid
            description: ID of the center transferring to
          - name: transferdate
            description: Date when the transfer occurred
          - name: status
            description: Status of the transfer

      - name: contracttypes
        description: Types of contracts available in the system.
        columns:
          - name: id
            description: Primary key for the contract type
          - name: name
            description: Name of the contract type

      - name: contractvalidations
        description: Validation records for contracts.
        columns:
          - name: id
            description: Primary key for the contract validation record
          - name: contractid
            description: Foreign key reference to the contract
          - name: validationstate
            description: Current validation state
          - name: validationdate
            description: Date when the validation occurred

      - name: contractvalidationstate
        description: Possible validation states for contracts.
        columns:
          - name: id
            description: Primary key for the contract validation state
          - name: name
            description: Name of the validation state

      - name: currentvalidationstate
        description: Current validation states for contracts.
        columns:
          - name: id
            description: Primary key for the current validation state
          - name: name
            description: Name of the current validation state

      - name: groupclassaccesstype
        description: Class access types for groups.
        columns:
          - name: id
            description: Primary key for the group class access type record
          - name: groupid
            description: Foreign key reference to the group
          - name: classaccesstypeid
            description: Foreign key reference to the class access type

      - name: groupproducts
        description: Products associated with specific groups.
        columns:
          - name: id
            description: Primary key for the group product record
          - name: groupid
            description: Foreign key reference to the group
          - name: productid
            description: Foreign key reference to the product

      - name: groups
        description: Groups of students or contracts for management purposes.
        columns:
          - name: id
            description: Primary key for the group record
          - name: name
            description: Name of the group
          - name: centerid
            description: Foreign key reference to the center
          - name: companyid
            description: Foreign key reference to the company

      - name: locations
        description: Physical locations where services are provided.
        columns:
          - name: id
            description: Primary key for the location record
          - name: name
            description: Name of the location

      - name: mastercontractauditinfo
        description: Audit information for master contract changes.
        columns:
          - name: id
            description: Primary key for the master contract audit record
          - name: mastercontractid
            description: Foreign key reference to the master contract
          - name: modifiedfieldid
            description: ID of the field that was modified
          - name: oldvalue
            description: Previous value before the change
          - name: newvalue
            description: New value after the change
          - name: modifiedbyid
            description: ID of the user who made the change
          - name: modifieddate
            description: Timestamp when the change was made

      - name: mastercontractcourseauditinfo
        description: Audit information for master contract course changes.
        columns:
          - name: id
            description: Primary key for the master contract course audit record
          - name: mastercontractid
            description: Foreign key reference to the master contract
          - name: mastercontractcourseid
            description: Foreign key reference to the master contract course
          - name: modifiedfieldid
            description: ID of the field that was modified
          - name: oldvalue
            description: Previous value before the change
          - name: newvalue
            description: New value after the change
          - name: modifiedbyid
            description: ID of the user who made the change
          - name: modifieddate
            description: Timestamp when the change was made
          - name: changetype
            description: Type of change made

      - name: mastercontractcourses
        description: Courses associated with master contracts.
        columns:
          - name: id
            description: Primary key for the master contract course record
          - name: mastercontractid
            description: Foreign key reference to the master contract
          - name: producttypeid
            description: Foreign key reference to the product type
          - name: studytypeid
            description: Foreign key reference to the study type
          - name: b2bcontracttypeid
            description: Foreign key reference to the B2B contract type
          - name: price
            description: Price for the course
          - name: refund
            description: Refund amount if applicable
          - name: startdate
            description: Start date for the course
          - name: enddate
            description: End date for the course
          - name: state
            description: Current state of the course
          - name: status
            description: Current status of the course

      - name: mastercontractlimitmappings
        description: Limit mappings for master contract courses.
        columns:
          - name: id
            description: Primary key for the master contract limit mapping record
          - name: mastercontractcourseid
            description: Foreign key reference to the master contract course
          - name: mastercontracttypeoflimitid
            description: Foreign key reference to the master contract type of limit
          - name: limitcount
            description: Count of the limit
          - name: createddate
            description: Timestamp when the record was created
          - name: lastupdateddate
            description: Timestamp when the record was last updated

      - name: mastercontractpriceinformation
        description: Price information for master contracts.
        columns:
          - name: id
            description: Primary key for the master contract price information record
          - name: mastercontractid
            description: Foreign key reference to the master contract
          - name: price
            description: Price amount
          - name: currency
            description: Currency code

      - name: mastercontractproducts
        description: Products associated with master contracts.
        columns:
          - name: id
            description: Primary key for the master contract product record
          - name: mastercontractid
            description: Foreign key reference to the master contract
          - name: productid
            description: Foreign key reference to the product

      - name: mastercontracts
        description: Master contracts for B2B relationships with companies.
        columns:
          - name: id
            description: Primary key for the master contract record
          - name: mastercontractnumber
            description: Master contract number
          - name: centerid
            description: Foreign key reference to the center
          - name: territoryid
            description: Foreign key reference to the territory
          - name: companyid
            description: Foreign key reference to the company
          - name: startdate
            description: Start date for the master contract
          - name: enddate
            description: End date for the master contract
          - name: saledate
            description: Date when the master contract was sold
          - name: consultantid
            description: ID of the consultant who sold the master contract
          - name: state
            description: Current state of the master contract
          - name: status
            description: Current status of the master contract
          - name: canceldate
            description: Date when the master contract was cancelled, if applicable
          - name: reason
            description: Reason for cancellation or other status changes
          - name: modifiedbyid
            description: ID of the user who last modified the master contract
          - name: createddate
            description: Timestamp when the master contract was created
          - name: lastupdateddate
            description: Timestamp when the master contract was last updated
          - name: currentvalidationstate
            description: Current validation state of the master contract

      - name: mastercontracttypeoflimit
        description: Types of limits that can be applied to master contracts.
        columns:
          - name: id
            description: Primary key for the master contract type of limit
          - name: limittype
            description: Type of limit

      - name: newversioncompanies
        description: Companies in the new version of the contract system.
        columns:
          - name: id
            description: Primary key for the company record
          - name: name
            description: Name of the company
          - name: centerid
            description: Foreign key reference to the center

      - name: productlevels
        description: Levels associated with products.
        columns:
          - name: id
            description: Primary key for the product level record
          - name: name
            description: Name of the level
          - name: order
            description: Ordering sequence for the level

      - name: products
        description: Products available in the system.
        columns:
          - name: id
            description: Primary key for the product record
          - name: name
            description: Name of the product
          - name: producttypeid
            description: Foreign key reference to the product type

      - name: producttypeproducts
        description: Mapping between product types and products.
        columns:
          - name: id
            description: Primary key for the product type product mapping
          - name: producttypeid
            description: Foreign key reference to the product type
          - name: productid
            description: Foreign key reference to the product

      - name: producttypes
        description: Types of products available in the system.
        columns:
          - name: id
            description: Primary key for the product type
          - name: name
            description: Name of the product type

      - name: roles
        description: User roles in the contract system.
        columns:
          - name: id
            description: Primary key for the role record
          - name: name
            description: Name of the role

      - name: servicetypes
        description: Types of services offered.
        columns:
          - name: id
            description: Primary key for the service type
          - name: name
            description: Name of the service type

      - name: studytype
        description: Types of study methods available.
        columns:
          - name: id
            description: Primary key for the study type
          - name: name
            description: Name of the study type

      - name: territory
        description: Territories or regions where centers operate.
        columns:
          - name: id
            description: Primary key for the territory record
          - name: name
            description: Name of the territory
          - name: code
            description: Code identifier for the territory

      - name: territoryclassaccess
        description: Class access configurations at the territory level.
        columns:
          - name: id
            description: Primary key for the territory class access record
          - name: territoryid
            description: Foreign key reference to the territory
          - name: classaccesstypeid
            description: Foreign key reference to the class access type

      - name: timezone
        description: Timezone information for centers and territories.
        columns:
          - name: id
            description: Primary key for the timezone record
          - name: name
            description: Name of the timezone

      - name: transferstatuses
        description: Possible statuses for contract transfers.
        columns:
          - name: id
            description: Primary key for the transfer status
          - name: name
            description: Name of the transfer status

      - name: users
        description: Users in the contract system including students and staff.
        columns:
          - name: id
            description: Primary key for the user record
          - name: userreferenceid
            description: External reference ID for the user
          - name: username
            description: Username for system login
          - name: firstname
            description: First name of the user
          - name: lastname
            description: Last name of the user
          - name: studentcode
            description: Code identifier for students
          - name: centerid
            description: Foreign key reference to the center
          - name: createddate
            description: Timestamp when the user record was created
          - name: lastupdateddate
            description: Timestamp when the user record was last updated
          - name: usertype
            description: Type of user (student, staff, etc.)
          - name: employeecode
            description: Code identifier for employees
          - name: userexternalid
            description: External identifier for the user
          - name: territoryid
            description: Foreign key reference to the territory

      - name: usertype
        description: Types of users in the system.
        columns:
          - name: id
            description: Primary key for the user type
          - name: name
            description: Name of the user type
