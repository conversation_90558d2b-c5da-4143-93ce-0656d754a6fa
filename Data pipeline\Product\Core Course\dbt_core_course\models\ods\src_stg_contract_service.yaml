version: 2

sources:
  - name: stage_contract_service
    database: awsdatacatalog  
    schema: stg_contract_service  
    tables:
      - name:	b2bcontracttypes
      - name:	centerclassaccess
      - name:	centerproducts
      - name:	centers
      - name:	classaccesstypes
      - name:	companyadditionalinfo
      - name:	contractauditfields
      - name:	contractchangetypes
      - name: contractchanges
      - name:	contractclassaccesstype
      - name:	contractdocumenttypes
      - name:	contractprice
      - name:	contractproducts
      - name:	contracts
      - name:	contractsauditinfo
      - name:	contractsourcetypes
      - name:	contractstates
      - name:	contractstatuses
      - name:	contracttransfers
      - name:	contracttypes
      - name:	contractvalidations
      - name:	contractvalidationstate
      - name:	currentvalidationstate
      - name:	groupclassaccesstype
      - name:	groupproducts
      - name:	groups
      - name:	locations
      - name:	mastercontractauditinfo
      - name:	mastercontractcourseauditinfo
      - name:	mastercontractcourses
      - name:	mastercontractlimitmappings
      - name:	mastercontractpriceinformation
      - name:	mastercontractproducts
      - name:	mastercontracts
      - name:	mastercontracttypeoflimit
      - name:	newversioncompanies
      - name:	productlevels
      - name:	products
      - name:	producttypeproducts
      - name:	producttypes
      - name:	roles
      - name:	servicetypes
      - name:	studytype
      - name:	territory
      - name:	territoryclassaccess
      - name:	timezone
      - name:	transferstatuses
      - name:	users
      - name:	usertype
