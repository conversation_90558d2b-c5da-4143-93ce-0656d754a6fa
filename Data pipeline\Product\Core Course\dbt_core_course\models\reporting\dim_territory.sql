{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}
WITH capitalized AS (
    SELECT
        territory_reference_id as territory_id
        ,SPLIT(territory_name, ' ') AS words_territory
        ,region
    from {{ ref('territory_centers') }}

)

select DISTINCT 
        territory_id
        ,ARRAY_JOIN(TRANSFORM(words_territory, words_territory -> CONCAT(UPPER(SUBSTR(words_territory, 1, 1)), LOWER(SUBSTR(words_territory, 2)))),
         ' ') AS "territory_name"
        ,upper(region) AS region

  FROM capitalized
