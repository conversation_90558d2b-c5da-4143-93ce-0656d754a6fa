{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        {{cast_to_int('id')}},
        name,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('lastupdatedate')}} as lastupdatedate,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdatedate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'contractsourcetypes')}}
)

SELECT
    {{etl_load_date()}},
    id,
    name,
    createddate as created_date,
    lastupdatedate as last_update_date
FROM 
    RankedRecords
WHERE 
    rn = 1;