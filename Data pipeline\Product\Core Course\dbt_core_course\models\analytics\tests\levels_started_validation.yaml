version: 2

models:
  - name: levels_started
    columns:
      - name: student_reference_id
        tests:
          - not_null:
              severity: error
      - name: digital_books_log_id
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        tests:
          - not_null:
              severity: error
      - name: registration_id
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        tests:
          - not_null:
              severity: error
      - name: date_granted
        tests:
          - not_null:
              severity: error
      - name: local_date_granted
        tests:
          - not_null:
              severity: error
      - name: category
        tests:
          - accepted_values:
              values: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20']
              severity: warn
      - name: category_type
        tests:
          - accepted_values:
              values: ['level']
              severity: warn
      - name: unlock_type
        tests:
          - accepted_values:
              values: ['standard', 'promo']
              severity: warn
      - name: operation_type
        tests:
          - accepted_values:
              values: ['refund', 'bill']
              severity: warn
      - name: workbook_type
        tests:
          - accepted_values:
              values: ['digital', 'printed']
              severity: warn
      - name: is_teen
        tests:
          - not_null:
              severity: warn
      - name: sequence
        tests:
          - not_null:
              severity: warn
      - name: is_restart
        tests:
          - not_null:
              severity: warn
