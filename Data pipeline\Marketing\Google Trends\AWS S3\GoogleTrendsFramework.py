import Packages

S3 = Packages.CloudOperations.S3
Redshift = Packages.DbOperations.Database
IamRole = Packages.DbOperations.IamRole
Region = Packages.DbOperations.Region


class Trends:
    @staticmethod
    def TrendsSuggestions(Pytrend, CompanyKeywordsList, Competitors):
        """Function Will Get The Suggestion Mid For Input Company Keyword And Type"""
        KeywordsCodeList = []
        SuggestionsResponseList = []

        # Loop through the company keyword list
        for Company in CompanyKeywordsList:
            SuggestionsAttempts, SuggestionsFetched = 0, False

            # Loop until suggestions are fetched successfully
            while not SuggestionsFetched:
                try:
                    Packages.time.sleep(60)
                    Packages.logging.warning("Attempt:'%s'", format(SuggestionsAttempts))

                    # Get keyword suggestions using Pytrends
                    KeywordsCodeResponse = Pytrend.suggestions(keyword=Company)
                    KeywordsCodeList.extend(KeywordsCodeResponse)
                except Exception as ErrorMessage:

                    # Handle exception and retry if suggestions are not fetched successfully
                    Packages.logging.warning("SuggestionsErrorMessage:'%s'", format(ErrorMessage))
                    Packages.logging.warning(f'Trying Again In {60 + 5 * SuggestionsAttempts} Seconds.')
                    Packages.time.sleep(60 + 5 * SuggestionsAttempts)
                    SuggestionsAttempts += 1
                    if SuggestionsAttempts > 5:
                        Packages.logging.warning('Failed After 5 SuggestionsAttempts, Abort Fetching.')
                        raise Exception
                else:
                    SuggestionsFetched = True
        Packages.logging.warning("KeywordsCodeList:'%s'", format(KeywordsCodeList))

        # Loop through the keyword suggestion list and match with input company keywords and type
        for KeywordsCodeDataResponse in KeywordsCodeList:
            for Company in CompanyKeywordsList:
                if Competitors[Company]['Keyword'] == KeywordsCodeDataResponse['title'] and \
                        Competitors[Company]['CompanyType'] == KeywordsCodeDataResponse['type']:
                    SuggestionsResponseList.append(KeywordsCodeDataResponse)
        Packages.logging.warning("SuggestionsResponseList:'%s'", format(SuggestionsResponseList))
        return SuggestionsResponseList

    @staticmethod
    def BuildPayload(Pytrend, KeywordsCodeDf, CutoffDate, LoadType, Territory):
        """Function Is Used To Build Pytrends Payload"""
        Packages.logging.warning(Territory)
        DataExtractionCompanyKeywordsList = KeywordsCodeDf['mid'].to_list()
        Packages.logging.warning("DataExtractionCompanyKeywordsList:'%s'", format(DataExtractionCompanyKeywordsList))
        Category, SearchType, DateInterval = 0, '', 'all'

        # Check if the load type is incremental, and if so, set the date range accordingly
        if LoadType == "Incremental":
            """date conversion from date: previous date +1 and ToDate: current date -4"""
            DateFormat = "%Y-%m-%d"
            FromDateConversion = \
                Packages.datetime.datetime.strptime(CutoffDate, DateFormat) + Packages.datetime.timedelta(days=1)
            FromDate = FromDateConversion.strftime(DateFormat)
            Packages.logging.warning(FromDate)
            GetCurrentDayDate = Packages.datetime.datetime.today().strftime(DateFormat)
            ToDateConversion = \
                Packages.datetime.datetime.strptime(GetCurrentDayDate, DateFormat) - Packages.datetime.timedelta(days=4)
            ToDate = ToDateConversion.strftime(DateFormat)
            Packages.logging.warning(ToDate)
            DateInterval = str(FromDate) + ' ' + str(ToDate)
            Packages.logging.warning("Incremental Execution Date Range Set In BuildPayload")
            Packages.logging.warning("DateInterval:'%s'", format(DateInterval))

        # Build the Pytrends payload
        Pytrend.build_payload(kw_list=DataExtractionCompanyKeywordsList,
                              timeframe=DateInterval,
                              geo=Territory,
                              cat=Category,
                              gprop=SearchType)
        return "Payload Build Completed"

    @staticmethod
    def DataRequest(Pytrend):
        DataResponseDictionary = {} # create empty dictionary to store response data
        DataRequestAttempts, DataRequestFetched = 0, False # initialize request attempts and fetched status variables

        # loop until data is fetched successfully
        while not DataRequestFetched:
            try:
                Packages.time.sleep(60) # wait for 60 seconds before making request
                Packages.logging.warning("Attempt:'%s'", format(DataRequestAttempts))
                DataRequestIteratorCount = 0

                # try making request up to 5 times
                while DataRequestIteratorCount < 5:
                    Packages.logging.warning("Execution:'%s'", format(DataRequestIteratorCount))
                    DataResponseDf = Pytrend.interest_over_time()
                    Packages.logging.warning("DataResponseDf:'%s'", format(DataResponseDf))

                    # if response is not empty, store data in dictionary and break out of inner loop
                    if len(DataResponseDf) != 0:
                        Packages.logging.warning("Received Response")
                        DataResponseDf = DataResponseDf.drop('isPartial', axis=1)  # drop 'isPartial' column
                        DataResponseDictionary['result'] = DataResponseDf
                        Packages.logging.warning("DataResponseDictionary:'%s'", format(DataResponseDictionary))
                        break

                    # if response is empty, wait for longer and try again
                    Packages.logging.warning("Not Received Response")
                    Packages.logging.warning(f'Trying Again In {60 + 5 * DataRequestIteratorCount} Seconds.')
                    Packages.time.sleep(60 + 5 * DataRequestIteratorCount)
                    DataRequestIteratorCount += 1

                    # if attempts exceeded 5, abort and raise exception
                    Packages.logging.warning("Retrying Request")
            except Exception as ErrorMessage:
                Packages.logging.warning("ErrorMessage:'%s'", format(ErrorMessage))
                Packages.logging.warning(f'Trying Again In {60 + 5 * DataRequestAttempts} Seconds.')
                Packages.time.sleep(60 + 5 * DataRequestAttempts)
                DataRequestAttempts += 1
                if DataRequestAttempts > 5:
                    Packages.logging.warning('Failed After 5 DataRequestAttempts, Abort Fetching.')
                    raise Exception
            else:
                DataRequestFetched = True # set fetched status to True if data is fetched successfully
        TrendsResponseDf = Packages.pd.concat(DataResponseDictionary, axis=1) # concatenate data frames in dictionary
        Packages.logging.warning("TrendsResponseDf:'%s'", format(TrendsResponseDf))
        return TrendsResponseDf # return concatenated data frame

    @staticmethod
    def CleanResponse(TrendsResponseDf, CompanyNameList, Territory):
        """Function Is Used To Clean The Response"""

        # drop outside header from the columns
        TrendsResponseDf.columns = TrendsResponseDf.columns.droplevel(0)

        # add Territory column with value
        TrendsResponseDf['Territory'] = Territory

        # insert date_range column at index 0
        TrendsResponseDf.insert(0, 'date_range', TrendsResponseDf.index)
        Packages.logging.warning("TrendsResponseDf:'%s'", format(TrendsResponseDf))

        # reset the index of the dataframe to start from 0
        TrendsResponseDf = TrendsResponseDf.reset_index(drop=True)

        # create a copy of TrendsResponseDf called ModifiedTrendsDf
        ModifiedTrendsDf = TrendsResponseDf[0:]
        Packages.logging.warning("---------ModifiedTrendsDf-----------")
        Packages.logging.warning("ModifiedTrendsDf:'%s'", format(ModifiedTrendsDf))
        Packages.logging.warning("ModifiedTrendsDf.columns:'%s'", format(ModifiedTrendsDf.columns))
        Packages.logging.warning("CompanyNameList:'%s'", format(CompanyNameList))

        # replace the column names of ModifiedTrendsDf with the names in CompanyNameList
        ModifiedTrendsDf.columns = CompanyNameList
        Packages.logging.warning("After Column Change")
        Packages.logging.warning("ModifiedTrendsDf:'%s'", format(ModifiedTrendsDf))
        """ For Date Format Conversion"""

        # convert the 'Date' column to a datetime format
        ModifiedTrendsDf['Date'] = Packages.pd.to_datetime(ModifiedTrendsDf['Date'], unit='ms').dt.strftime('%Y-%m-%d')

        # convert the ModifiedTrendsDf to JSON format
        TrendsResponseJson = Packages.json.loads(ModifiedTrendsDf.to_json(orient='records'))
        Packages.logging.warning("TrendsResponseJson:'%s'", format(TrendsResponseJson))
        return TrendsResponseJson

    @staticmethod
    def TransformResponse(TrendsResponseJson, CompanyKeywordsList, Competitors, ServiceType):
        """Function Is Used To Transform The Response"""

        # Create an empty list to store the transformed response
        TransformedResponseJson = []

        # Iterate through each item in the TrendsResponseJson list
        for DataResponse in TrendsResponseJson:
            for Company in CompanyKeywordsList:

                # Check if the keyword is in the DataResponse
                if Company in DataResponse:

                    # If the keyword is present, create a dictionary with the required fields
                    TransformationStructure = {
                        "Date": DataResponse['Date'],
                        "Territory": DataResponse['Territory'],
                        "ServiceType": ServiceType,
                        "CompanyType": Competitors[Company]['CompanyType'],
                        "CompanyName": Company,
                        "Points": DataResponse[Company]
                    }

                    # Append the created dictionary to the TransformedResponseJson list
                    TransformedResponseJson.append(TransformationStructure)

        # Return the transformed response as a list of dictionaries
        return TransformedResponseJson

    @staticmethod
    def ResponseValidation(LoadType, DataResponse):
        """Function Is Used to Zeros in Date Wise Response"""

        # Create a copy of DataResponse dataframe to avoid modifying the original data
        DataResponseCheck = DataResponse.copy()

        # Check if LoadType is 'Initial'
        if LoadType == 'Initial':
            # Convert 'Date' column to datetime format and extract year_month information
            DataResponseCheck['year_month'] = Packages.pd.to_datetime(DataResponseCheck['Date'])

            # Group the data by year_month and ServiceType, sum the 'Points' column and sort the result by sum
            InitialLoadTotalSum = \
                DataResponseCheck.groupby([DataResponseCheck['year_month'].dt.strftime('%Y-%m'), 'ServiceType'])[
                    'Points'].sum().sort_values().reset_index(name='sum')
            Packages.logging.warning("Initial Load  Sum Records Processed %s", format(len(InitialLoadTotalSum)))
            Packages.logging.warning(InitialLoadTotalSum)
            Packages.logging.warning("DataResponse Sum: %s", format(InitialLoadTotalSum))
            # Return the InitialLoadTotalSum
            return InitialLoadTotalSum

        # Check if LoadType is 'Incremental'
        if LoadType == 'Incremental':
            # Convert 'Date' column to datetime format and extract year_month_date information
            DataResponseCheck['year_month_date'] = Packages.pd.to_datetime(DataResponseCheck['Date'])

            # Group the data by year_month_date and ServiceType, sum the 'Points' column and sort the result by sum
            IncrementalLoadTotalSum = \
                DataResponseCheck.groupby(
                    [DataResponseCheck['year_month_date'].dt.strftime('%Y-%m-%d'), 'ServiceType'])[
                    'Points'].sum().sort_values().reset_index(name='sum')
            Packages.logging.warning("Incremental Load Sum Records processed %s", format(len(IncrementalLoadTotalSum)))
            DataResponseTotalSum = IncrementalLoadTotalSum
            Packages.logging.warning("DataResponse Sum: %s", format(DataResponseTotalSum))
            # Return the IncrementalLoadTotalSum
            return IncrementalLoadTotalSum

    @staticmethod
    def CaptureZeroResponse(DataResponse, Territory, ServiceType, Bucket, CycleId):
        """Function Is Used To Store ZeroResponse In S3 As CSV File"""

        # Count the number of zeros in the sum of the data response
        ZeroResponseCount = (DataResponse['sum'] == 0).sum()
        Packages.logging.warning("Zero's in DataResponse %s", format(ZeroResponseCount))

        # Define the S3 key where the zero response CSV file will be stored
        ZeroFileKey = f"GoogleTrendsZeroResponse/{Territory}/{ServiceType}/{CycleId}/" + \
                      f"{Territory}{ServiceType}Response.csv"

        # Write the zero response data to an S3 CSV file
        S3WriteZeroResponse = S3.WriteCsvFile(FilePath=ZeroFileKey,
                                              Bucket=Bucket,
                                              DataResponse=DataResponse)
        Packages.logging.warning("S3WriteZeroResponse:'%s'", format(S3WriteZeroResponse))
        # Return the S3 key where the zero response CSV file was stored
        return ZeroFileKey

    @staticmethod
    def CaptureResponse(DataResponse, Territory, ServiceType, Bucket, CycleId):
        """Function Is Used To Store Response In S3 As CSV File"""

        # FromDate and ToDate are initialized as the minimum and maximum date values in DataResponse, respectively.
        FromDate = min(DataResponse['Date'])
        ToDate = max(DataResponse['Date'])

        # The file key is created using the values of Territory, ServiceType, CycleId, FromDate, and ToDate.
        FileKey = f"GoogleTrendsResponse/{Territory}/{ServiceType}/{CycleId}/" + \
                  f"{FromDate}-{ToDate}/{Territory}{ServiceType}Response.csv"

        # The S3.WriteCsvFile() method is called to write the data response to the CSV file located at the specified file path.
        S3WriteDataResponse = S3.WriteCsvFile(FilePath=FileKey,
                                              Bucket=Bucket,
                                              DataResponse=DataResponse)
        Packages.logging.warning("S3WriteDataResponse:'%s'", format(S3WriteDataResponse))
        # The file key is returned by the function.
        return FileKey

    @staticmethod
    def DataExtractionProcess(Territory, Competitors, ServiceType, LoadType, CutoffDate, Bucket, CycleId):
        """Function Is Used To Extract Data From Trends"""

        # Create a Pytrends object with specific configurations
        Pytrend = Packages.TrendReq(timeout=30, hl=f"en-{Territory}", retries=2, backoff_factor=0.1)
        Packages.logging.warning("ServiceType:'%s'", format(ServiceType))

        # Convert the keys of the Competitors dictionary to a list
        CompanyKeywordsList = list(Competitors.keys())
        Packages.logging.warning("CompanyKeywordsList:'%s'", format(CompanyKeywordsList))

        # Use the TrendsSuggestions function to get keyword suggestions for each competitor
        SuggestionsResponse = Trends.TrendsSuggestions(Pytrend=Pytrend,
                                                        CompanyKeywordsList=CompanyKeywordsList,
                                                        Competitors=Competitors)

        # Check if the length of the CompanyKeywordsList is equal to the length of the suggestions_response
        # If not, log a warning message and raise an exception
        if len(CompanyKeywordsList) != len(SuggestionsResponse):
            Packages.logging.warning("SuggestionsResponse:'%s'", format(SuggestionsResponse))
            Packages.logging.warning("no of input keys:'%s'", format(len(CompanyKeywordsList)))
            Packages.logging.warning("no of output keys:'%s'", format(len(SuggestionsResponse)))
            raise Exception

        # Convert the SuggestionsResponse to a Pandas DataFrame
        KeywordsCodeDf = Packages.pd.DataFrame(SuggestionsResponse)
        Packages.logging.warning("KeywordsCodeDf:'%s'", format(KeywordsCodeDf))

        # Create a list of company names by extracting the 'title' column from the KeywordsCodeDf DataFrame
        CompanyNameList = list(KeywordsCodeDf['title'].values)

        # Add 'Date' and 'Territory' columns to the CompanyNameList
        CompanyNameList.insert(0, 'Date')
        CompanyNameList.insert(len(CompanyNameList), 'Territory')

        # Build the payload for the Trends API
        Trends.BuildPayload(Pytrend=Pytrend,
                            KeywordsCodeDf=KeywordsCodeDf,
                            CutoffDate=CutoffDate,
                            LoadType=LoadType,
                            Territory=Territory)

        # Make a request to the Trends API and get the response as a Pandas DataFrame
        TrendsResponseDf = Trends.DataRequest(Pytrend=Pytrend)

        # Clean the response and convert it to a JSON object
        TrendsResponseJson = Trends.CleanResponse(TrendsResponseDf=TrendsResponseDf,
                                                  CompanyNameList=CompanyNameList,
                                                  Territory=Territory)

        # Transform the response to a JSON object that can be written to S3 and Redshift
        TransformedResponseJson = Trends.TransformResponse(TrendsResponseJson=TrendsResponseJson,
                                                           CompanyKeywordsList=CompanyKeywordsList,
                                                           Competitors=Competitors,
                                                           ServiceType=ServiceType)

        # Convert the transformed response to a Pandas DataFrame
        TransformedResponseDf = Packages.pd.DataFrame(TransformedResponseJson)

        # Validate the response data based on the LoadType parameter
        ResponseValidation = Trends.ResponseValidation(LoadType=LoadType,
                                                       DataResponse=TransformedResponseDf)

        # Write the validated response data to S3 which contains the sum of zero data response
        S3WriteZeroResponse = Trends.CaptureZeroResponse(DataResponse=ResponseValidation,
                                                         Territory=Territory,
                                                         ServiceType=ServiceType,
                                                         Bucket=Bucket,
                                                         CycleId=CycleId)

        # Write the validated response data to S3 which contains the non-zero data response
        S3WriteResponse = Trends.CaptureResponse(DataResponse=TransformedResponseDf,
                                                 Territory=Territory,
                                                 ServiceType=ServiceType,
                                                 Bucket=Bucket,
                                                 CycleId=CycleId)
        """execute copy command to move data from s3 file to redshift table"""

        # movement of data from s3 to redshift table
        CopyCommandQuery = """COPY google.trends FROM 's3://{}/{}' iam_role '{}' region '{}' IGNOREHEADER 1 CSV
                                                            timeformat 'auto';""" \
            .format(Bucket, S3WriteResponse, IamRole, Region)
        Packages.logging.warning("CopyCommandQuery:'%s'", format(CopyCommandQuery))

        ExecuteCopyCommand = Redshift.Execution(ExecutionType="WriteTable",
                                                Query=CopyCommandQuery,
                                                StatementName=f"{Territory}_{ServiceType}")
        Packages.logging.warning("ExecuteCopyCommand:'%s'", format(ExecuteCopyCommand))

        #createing execution summary dictionary
        Summary = {
            "S3": {"ZeroDataResponse": S3WriteZeroResponse,
                   "DataResponse": S3WriteResponse},
            "Redshift": ExecuteCopyCommand,
            "CutoffDate": max(TransformedResponseDf['Date']),
            "RecordsProcessed": len(TrendsResponseDf)
        }
        return Summary
