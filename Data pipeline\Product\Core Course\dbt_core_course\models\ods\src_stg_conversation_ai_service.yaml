version: 2

sources:
  - name: stage_conversation_ai_service
    description: >
      Source data from the Conversation AI Service which provides AI-powered conversation
      capabilities for language practice and learning.
    database: awsdatacatalog
    schema: stg_conversation_ai_service
    tables:
      - name: raw_data_conversation
        description: Raw conversation data between users and the AI conversation system.
        columns:
          - name: chat_id
            description: Unique identifier for the chat session
          - name: content_id
            description: Identifier for the content being discussed
          - name: contract_id
            description: ID of the contract associated with the user
          - name: end_date
            description: ISO timestamp when the conversation ended
          - name: gpt4o_mini_cost
            description: Cost of using the GPT-4o mini model for this conversation
          - name: hasEnded
            description: Boolean flag indicating if the conversation has ended
          - name: messages
            description: JSON array containing all messages in the conversation
          - name: start_date
            description: ISO timestamp when the conversation started
          - name: total_input_tokens
            description: Total number of input tokens used in the conversation
          - name: total_output_tokens
            description: Total number of output tokens generated in the conversation
          - name: user_id
            description: ID of the user participating in the conversation