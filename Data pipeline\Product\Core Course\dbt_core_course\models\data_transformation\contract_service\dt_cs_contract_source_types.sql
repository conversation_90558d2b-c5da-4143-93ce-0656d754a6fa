{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_contract_source_types'
        ) }}

    {% if is_incremental() %}
        where last_update_date > ((select max(last_update_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    id,
    name,
    created_date,
    last_update_date
from ods_data