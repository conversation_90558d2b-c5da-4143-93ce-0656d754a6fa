version: 2

models:
  - name: class_bookings
    columns:
      - name: class_booking_id
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: class_id
        tests:
          - not_null:
              severity: error
      - name: class_center_reference_id
        tests:
          - not_null:
              severity: error
      - name: class_start_datetime
        tests:
          - not_null:
              severity: error
      - name: class_local_start_datetime
        tests:
          - not_null:
              severity: error
      - name: student_local_start_datetime
        tests:
          - not_null:
              severity: error
      - name: class_number_of_seats
        tests:
          - not_null:
              severity: warn
      - name: class_created_datetime
        tests:
          - not_null:
              severity: error
      - name: class_local_created_datetime
        tests:
          - not_null:
              severity: error
      - name: class_last_updated_datetime
        tests:
          - not_null:
              severity: warn
      - name: class_local_last_updated_datetime
        tests:
          - not_null:
              severity: warn
      - name: class_service_type
        tests:
          - accepted_values:
              values: ['vip', 'standard', 'combined']
              severity: warn
      - name: class_type_billable
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: warn
      - name: cancellations_12hrs_flag
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: warn
      - name: cancellations_24hrs_flag
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: warn
      - name: attended_flag
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: warn
      - name: technology_platform_staff_flag
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: warn
      - name: technology_student_flag
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: warn
      - name: no_show_flag
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: warn
      - name: late_cancellation_flag
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: warn
      - name: billable_flag
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: warn
