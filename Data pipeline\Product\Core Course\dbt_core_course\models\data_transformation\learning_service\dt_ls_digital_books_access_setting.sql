{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'registration_course_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_digital_books_access_setting') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT 
    {{etl_load_date()}},
    registration_course_id,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(category.path, '.', 1), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(category.path, '.', 1), '[^0-9]', '')
    END AS category,
    category_type.name as category_type,
    student_id,
    can_be_changed,
    created ,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated,
    CASE
        When workbook_type = 0 then 'digital'
        When workbook_type = 1 then 'printed'
        When workbook_type = 2 then 'notapplicable'
        Else CAST (
            workbook_type AS varchar
        )
    end as workbook_type,
    release_work_book
from
    ods_data as digbookaccsetting
    Left Join (
        select
            id,
            path,
            category_type_id
        from
            {{ ref('ods_ls_category') }}
    ) as category
    ON digbookaccsetting.category_id = category.id
    Left Join (
        select
            id,
            name
        from
            {{ ref('ods_ls_category_type') }}
    ) as category_type
    on category.category_type_id = category_type.id
    Left Join (
        select
            id,
            center_id
        from
            {{ ref('ods_ls_registration_course') }}
    ) as registrationcourse
    ON digbookaccsetting.registration_course_id = registrationcourse.id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = registrationcourse.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
