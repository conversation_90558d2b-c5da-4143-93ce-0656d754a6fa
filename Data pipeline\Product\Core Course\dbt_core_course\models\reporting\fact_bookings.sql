{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}
with calendar as
    (
    select
    "date"
    ,last_month_date
    from reporting.dim_calendar
    )

, bookings as(
    SELECT class_booking_id
    , booking_id
    , booking_territory_id                 AS student_territory_id
    , booking_center_reference_id          AS student_center_id
    , class_center_reference_id
    , class_id
    , student_reference_id
    , class_local_start_datetime
    , booking_created_datetime
    , booking_last_updated_datetime
    , greatest(COALESCE(booking_last_updated_datetime,{{filter_date()}}),
                COALESCE(class_last_updated_datetime,{{filter_date()}}),
                COALESCE(class_close_date,{{filter_date()}}))         AS last_updated_datetime
    , date_format(class_local_start_datetime, '%Y-%m')              AS class_month             
    , date(date_format(class_local_start_datetime, '%Y-%m-%d'))     AS class_date
    , booking_cancelled_datetime
    , class_price
    , booked_by_role
    , booked_by
    , CASE WHEN book_mode = 'standby' 
            then TRUE
            WHEN book_mode = 'book' 
            then FALSE
            ELSE NULL end as standby_flag
    , booking_cancelled_by_role
    , booking_cancelled_by
    , CASE
        WHEN class_result IS NULL AND booking_cancelled_flag = true THEN 'booking_cancelled'
        ELSE class_result
    END AS class_result
    , class_type
    , class_type_billable
    , class_encounter_level
    , class_encounter_unit
    , attended_flag                        
    , booking_cancelled_flag               AS cancelled_flag
    , late_cancellation_flag               AS late_cancelled_flag
    ,CASE 
        WHEN class_center_reference_id LIKE 'v%goc'
            THEN true
            ELSE false
        END                                 AS goc_flag                           
    , no_show_flag                                 
    , technology_student_flag
    , technology_platform_staff_flag                    
    , billable_flag 
    , technology_staff_flag
    ,CASE 
        WHEN 
            rescheduled_flag = true 
            AND (lead_booked_datetime BETWEEN date_add('hour', -1, booking_local_cancelled_datetime) AND date_add('hour', 1, booking_local_cancelled_datetime)) 
                THEN true
                ELSE false
        END                                 AS rescheduled_flag             
    , ready_flag
    , lead_booked_datetime
    , mm_ready_flag
    , wb_ready_flag                               

FROM {{ref('class_bookings')}} as cb
LEFT JOIN  {{ref('territory_centers')}} as tc
                    ON cb.class_center_reference_id = tc.center_reference_id
WHERE class_local_start_datetime >= {{filter_date()}}
)
SELECT 
    attended_flag
    , billable_flag 
    , standby_flag
    , booked_by_role
    , booking_cancelled_by_role
    , booking_cancelled_by
    , booking_created_datetime
    , last_updated_datetime
    , booking_cancelled_datetime
    , booking_id
    , cancelled_flag
    , class_booking_id
    , class_date
    , class_id
    , class_type
    , class_type_billable
    , class_month
    , class_price
    , class_result
    , concat(student_reference_id ,'_',cast(CAL.last_month_date as varchar)) as compound_key
    , late_cancelled_flag
    , no_show_flag 
    , student_reference_id
    , student_center_id
    , technology_platform_staff_flag 
    , technology_staff_flag
    , technology_student_flag
    , rescheduled_flag
    , ready_flag  
    , lead_booked_datetime 
    , mm_ready_flag
    , wb_ready_flag  
    , CAST(current_timestamp AS TIMESTAMP(6)) as load_date
FROM bookings B
LEFT JOIN calendar CAL on CAL."date" = B.class_date
order by last_updated_datetime desc