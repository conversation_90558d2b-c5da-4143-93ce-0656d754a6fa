{{ config(
    tags=["speaking_ai_beta"],
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'chat_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}


SELECT
  {{etl_load_date()}},
  chat_id,
  user_id,
  user_type,
  feedback,
  CAST(from_iso8601_timestamp(completed_date) AS timestamp(6)) AS completed_date
  FROM
  {{ source('stage_speaking_ai_beta_service', 'raw_data_feedback') }}
  