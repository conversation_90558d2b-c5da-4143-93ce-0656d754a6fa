{{
    config(
        tags=["incremental","init","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}

{{ga_select("it")}}
{{source('ga_it', 'ga') }}
{{ga_where()}}

union all

{{ga_select("tn")}}
{{source('ga_tn', 'ga') }}
{{ga_where()}}

union all

{{ga_select("de")}}
{{source('ga_de', 'ga') }}
{{ga_where()}}

union all

{{ga_select("dz")}}
{{source('ga_dz', 'ga') }}
{{ga_where()}}


union all

{{ga_select("ec")}}
{{source('ga_ec', 'ga') }}
{{ga_where()}}


union all

{{ga_select("es")}}
{{source('ga_es', 'ga') }}
{{ga_where()}}

union all

{{ga_select("com")}}
{{source('ga_com', 'ga') }}
{{ga_where()}}

union all

{{ga_select("vn")}}
{{source('ga_vn', 'ga') }}
{{ga_where()}}

union all

{{ga_select("sa")}}
{{source('ga_sa', 'ga') }}
{{ga_where()}}

union all

{{ga_select("ve")}}
{{source('ga_ve', 'ga') }}
{{ga_where()}}

union all

{{ga_select("mx")}}
{{source('ga_mx', 'ga') }}
{{ga_where()}}

union all

{{ga_select("mn")}}
{{source('ga_mn', 'ga') }}
{{ga_where()}}

union all

{{ga_select("pa")}}
{{source('ga_pa', 'ga') }}
{{ga_where()}}

union all

{{ga_select("kz")}}
{{source('ga_kz', 'ga') }}
{{ga_where()}}

union all

{{ga_select("pe")}}
{{source('ga_pe', 'ga') }}
{{ga_where()}}

union all

{{ga_select("il")}}
{{source('ga_il', 'ga') }}
{{ga_where()}}

union all

{{ga_select("la")}}
{{source('ga_la', 'ga') }}
{{ga_where()}}

union all

{{ga_select("ma")}}
{{source('ga_ma', 'ga') }}
{{ga_where()}}