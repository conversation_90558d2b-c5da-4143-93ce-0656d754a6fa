{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        id,
        isemailverified,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        isactive,
        sendmailpreference,
        preferredcontactmethod,
        userbasicinfoid,
        socialnetworkid1,
        socialnetworkaddress1,
        socialnetworkid2,
        socialnetworkaddress2,
        socialnetworkid3,
        socialnetworkaddress3,
        socialnetworkid4,
        socialnetworkaddress4,
        photoname,
        mobiletelephone,
        hometelephone,
        worktelephone,
        fax,
        address1,
        address2,
        city,
        state,
        postalcode,
        callbetweenfrom,
        callbetweento,
        personalprofessionid,
        personalmotivationid,
        personalnationalityid,
        personalnativelanguageid,
        personalsecondarylanguageid,
        interesthobbies,
        aboutme,
        studyreason,
        whystudyenglish,
        workmotivation,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_idam_service',
            'useradditionalinfo'
        ) }}
)
SELECT
    {{etl_load_date()}},
    id,
    isemailverified as is_email_verified,
    created as created,
    lastupdated as last_updated,
    isactive as is_active,
    sendmailpreference as send_mail_preference,
    preferredcontactmethod as preferred_contact_method,
    userbasicinfoid as user_basic_info_id,
    socialnetworkid1 as social_network_id1,
    socialnetworkaddress1 as social_network_address1,
    socialnetworkid2 as social_network_id2,
    socialnetworkaddress2 as social_network_address2,
    socialnetworkid3 as social_network_id3,
    socialnetworkaddress3 as social_network_address3,
    socialnetworkid4 as social_network_id4,
    socialnetworkaddress4 as social_network_address4,
    photoname as photo_name,
    mobiletelephone as mobile_telephone,
    hometelephone as home_telephone,
    worktelephone as work_telephone,
    fax,
    address1 as address1,
    address2 as address2,
    city,
    state,
    postalcode as postal_code,
    callbetweenfrom as call_between_from,
    callbetweento as call_between_to,
    personalprofessionid as personal_profession_id,
    personalmotivationid as personal_motivation_id,
    personalnationalityid as personal_nationality_id,
    personalnativelanguageid as personal_native_language_id,
    personalsecondarylanguageid as personal_secondary_language_id,
    interesthobbies as interest_hobbies,
    aboutme as about_me,
    studyreason as study_reason,
    whystudyenglish as why_study_english,
    workmotivation as work_motivation
FROM
    rankedrecords
WHERE
    rn = 1;
