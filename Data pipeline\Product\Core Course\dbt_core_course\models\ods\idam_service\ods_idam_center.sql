{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        id,
        centerreferenceid,
        name,
        territoryid,
        centercode,
        studentcode,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                id
        ) AS rn
    FROM
        {{ source(
            'stage_idam_service',
            'center'
        ) }}
)
SELECT
    {{etl_load_date()}},
    id,
    centerreferenceid as center_reference_id,
    name,
    territoryid as territory_id,
    centercode as center_code,
    studentcode as student_code
FROM
    rankedrecords
WHERE
    rn = 1;
