{{
    config(
        tags=["incremental","mart","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}
SELECT 
IFNULL(gsc_main.lang,ga_conversions.lang) lang,
page,
regexp_extract(page, r"https?://([^/]+)") hostname,
regexp_extract(page, r"https?://[^/]+(/.*)") as page_path,
case 
when page like "https://world.wallstreetenglish.com/%" THEN regexp_extract(replace(page,"https://world.wallstreetenglish.com/","https://world.wallstreetenglish.com/subdomain-world/"), r"https?://[^/]+(/.*)")
when page like "https://blog.wallstreet.it/%" THEN regexp_extract(replace(page,"https://blog.wallstreet.it/","https://blog.wallstreet.it/subdomain-blog/"), r"https?://[^/]+(/.*)")
when page like "https://blog.wallstreetenglish.com.ec/%" THEN regexp_extract(replace (page,"https://blog.wallstreetenglish.com.ec/","https://blog.wallstreetenglish.com.ec/subdomain-blog/"), r"https?://[^/]+(/.*)")
else regexp_extract(page, r"https?://[^/]+(/.*)") end as page_path_with_subdomain,
event_date,
ga_conversions.* EXCEPT(page,event_date,lang) , 
ifnull(clicks,0) AS clicks,
ifnull(impressions,0) AS impressions,
ifnull(branded_impressions,0) AS impressions_branded,
ifnull(branded_clicks,0) AS clicks_branded,
ifnull(local_impressions,0) AS impressions_local,
ifnull(local_clicks,0) AS clicks_local,


gsc_main.queries, 
crawl.page_title,
crawl.page_title_length_pixel,
crawl.status_code,
crawl.meta_description,

{% for conv in var('conversion_events') %}

IFNULL(conv_onlp_ggorganic_{{conv}},0) {% if not loop.last %} + {% endif %} 

{% endfor %} AS conversions_onlp_ggorganic,

{% for conv in var('conversion_events') %}

IFNULL(conv_session_organic_{{conv}},0) {% if not loop.last %} + {% endif %} 

{% endfor %}  as conversions_session_organic,

{% for conv in var('conversion_events') %}

IFNULL(conv_session_allsrc_{{conv}},0)  {% if not loop.last %} + {% endif %} 
{% endfor %} as conversions_session_allsrc,

{% for conv in var('conversion_events') %}

IFNULL(conv_onlp_allsrc_{{conv}},0)  {% if not loop.last %} + {% endif %} 

{% endfor %} AS conversions_onlp_allsrc,


CASE 
WHEN page LIKE "%/english-courses%" OR page LIKE "%/formation-anglais%" or page like "%/schulungskurse%" or page like "%/curso-ingles%" or page like "%/corsi-di-inglese%" THEN "English courses"
WHEN page LIKE "%/blog%" THEN "Blog"
WHEN page LIKE "%/exercises%" OR page LIKE "%/exercices-anglais%" or page like "%/ubungen%" or page like "%/ejercicios-ingles%" or page like "%esercizi%" THEN "Exercises"
WHEN page LIKE "%/english-tests%" OR page LIKE "%/tests-anglais%" or page like "%/englisch-tests%" or page like "%/test-ingles%" or page like "%/test-inglese%" THEN "English tests"
WHEN page LIKE "%/certifications%" OR page LIKE "%/examen-anglais%" or page like "%/englisch-exams%" or page like "%/examen-oficial-ingles%" or page like "%/certificazioni%" THEN "Certifications"
WHEN page LIKE "%/prices%" OR page LIKE "%/tarifs%" or page like "%/precios%" THEN "Prices"
WHEN page LIKE "%/institutes%" OR page LIKE "%/centres%" or page LIKE "%/zentren%" or page like "%/academia-ingles%" or page like "%/scuola-inglese%" THEN "Schools"
WHEN page LIKE "%/english-institutes%" or page LIKE "%/apprendre-anglais%" or page like "%/englisch-zentren%" or page like "%/aprender-ingles%" THEN "City schools"
WHEN REGEXP_CONTAINS(page,r"^(https?://[^/]+/?$)") THEN "Home"
ELSE "Other"
END as page_type



FROM   {{ ref('gsc_bypage_test') }} gsc_main
LEFT JOIN {{ ref('access_test') }} access USING (lang) -- should be deleted
FULL JOIN {{ ref('ga_conversions_test') }} ga_conversions USING (page,event_date)
FULL JOIN {{ ref('crawl_init_test') }} crawl USING (page)



where {{increment()}}
{% if is_incremental() %}
and ga_conversions.event_date {{daily_run()}}
{% endif %}  