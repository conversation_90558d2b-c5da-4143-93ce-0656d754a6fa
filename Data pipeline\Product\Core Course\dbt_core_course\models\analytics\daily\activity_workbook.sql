{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}


With user as (
    select 
        ssds_id,
        user_id
    from {{ref('dt_ls_user')}}
),
registration as (
    select 
        user_id,
        id as registration_id,
        start_date,
        end_date
        from {{ref("dt_ls_registration")}}
),
activity_workbook as (
SELECT
    activity_progress.id as activity_id
    ,registration.registration_id
    ,activity_progress.activity_reference_id
    ,activity_progress.student_id
    ,activity_progress.created
    ,activity_progress.local_created
    ,activity_progress.last_updated
    ,activity_progress.local_last_updated
    ,activity_progress.course_contents_level as level
    ,activity_progress.course_contents_unit as unit
    ,activity_progress.course_contents_lesson as lesson
    ,activity_progress.activity
    ,activity_progress.activity_url
    ,activity_progress.is_active
    ,activity_progress.is_completed
    ,activity_progress.study_mode
    ,activity_progress.category_type
    ,activity_progress.no_of_attempts
    ,cast(activity_progress.duration as decimal(25,5)) as duration
    ,activity_progress.score
    ,ROW_NUMBER() OVER (PARTITION BY id ORDER BY created DESC) AS rn
FROM 
    {{ref("dt_dsw_activity_progress")}} as activity_progress
Left Join user ON user.ssds_id = activity_progress.student_id
Left Join registration ON registration.user_id = user.user_id 
        and registration.start_date <  activity_progress.created
        and registration.end_date >  activity_progress.created)

SELECT 
    activity_workbook.activity_id
    ,activity_workbook.registration_id
    ,activity_workbook.activity_reference_id
    ,activity_workbook.student_id
    ,activity_workbook.created as created_date
    ,activity_workbook.local_created as local_created_date
    ,activity_workbook.last_updated as last_updated_date
    ,activity_workbook.local_last_updated as local_last_updated_date
    ,activity_workbook.level
    ,activity_workbook.unit
    ,activity_workbook.lesson
    ,activity_workbook.activity
    ,activity_workbook.activity_url
    ,activity_workbook.is_active
    ,activity_workbook.is_completed
    ,activity_workbook.study_mode
    ,activity_workbook.category_type
    ,activity_workbook.no_of_attempts
    ,activity_workbook.score 
    ,activity_workbook.duration as duration_secs
    ,activity_workbook.duration/60 as duration_mins
    ,cap.percentile_25_duration_mins
    ,cap.median_duration_mins
    ,cap.percentile_75_duration_mins
FROM 
    activity_workbook
    left join {{ ref('activity_cap') }} cap on activity_workbook.activity_reference_id = cap.content_item_id and date(activity_workbook.last_updated) = cap.date_completed
WHERE 
    rn = 1