{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH 
base_2025 as
(
SELECT
    tc.territory_name
    , date_add('year',1,fdsp."date") as "date"
    , sum(fdsp."total students serviced") as "total students serviced"
    , sum(fdsp."total students completing 21 days") as "total students completing 21 days"
    , sum(fdsp."active students serviced") as "active students serviced"
    , sum(fdsp."students done 1+") as "students done 1+"
    , sum(fdsp."students completed 21days") as "students completed 21days"
    , sum(fdsp."total encounters") as "total encounters"
    ,case when sum(cast(fdsp."total students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."active students serviced" as decimal(10, 5))) / sum(cast(fdsp."total students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "activity rate"
    , case when sum(cast(fdsp."active students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."total encounters" as decimal(10, 5))) / sum(cast(fdsp."active students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "mpr"
    , case when sum(cast(fdsp."total students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."students done 1+" as decimal(10, 5))) / sum(cast(fdsp."total students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "total done 1+"
FROM
    {{ ref('fact_daily_student_progress') }} fdsp
LEFT JOIN {{ ref('territory_centers') }} tc
    ON fdsp.center_reference_id = tc.center_reference_id
WHERE fdsp."date" between date '2024-01-01' and date'2024-12-31'
and fdsp.location = 'InCenter'
and fdsp.group_id = 'Individual'
and fdsp.contract_inclusions = true
and fdsp.is_promotional = false
GROUP BY
    tc.territory_name,
    date_add('year',1,fdsp."date"))
,
base_2024 as
(
SELECT
    tc.territory_name
    , date_add('year',1,fdsp."date") as "date"
    , sum(fdsp."total students serviced") as "total students serviced"
    , sum(fdsp."total students completing 21 days") as "total students completing 21 days"
    , sum(fdsp."active students serviced") as "active students serviced"
    , sum(fdsp."students done 1+") as "students done 1+"
    , sum(fdsp."students completed 21days") as "students completed 21days"
    , sum(fdsp."total encounters") as "total encounters"
    ,case when sum(cast(fdsp."total students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."active students serviced" as decimal(10, 5))) / sum(cast(fdsp."total students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "activity rate"
    , case when sum(cast(fdsp."active students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."total encounters" as decimal(10, 5))) / sum(cast(fdsp."active students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "mpr"
    , case when sum(cast(fdsp."total students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."students done 1+" as decimal(10, 5))) / sum(cast(fdsp."total students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "total done 1+"
FROM
    {{ ref('fact_daily_student_progress') }} fdsp
LEFT JOIN {{ ref('territory_centers') }} tc
    ON fdsp.center_reference_id = tc.center_reference_id
WHERE fdsp."date" between date '2023-01-01' and date'2023-12-31'
and fdsp.location = 'InCenter'
and fdsp.group_id = 'Individual'
and fdsp.contract_inclusions = true
and fdsp.is_promotional = false
GROUP BY
    tc.territory_name,
    date_add('year',1,fdsp."date"))
,
base_2023 as
(
SELECT
    tc.territory_name
    , date_add('year',4,fdsp."date") as "date"
    , sum(fdsp."total students serviced") as "total students serviced"
    , sum(fdsp."total students completing 21 days") as "total students completing 21 days"
    , sum(fdsp."active students serviced") as "active students serviced"
    , sum(fdsp."students done 1+") as "students done 1+"
    , sum(fdsp."students completed 21days") as "students completed 21days"
    , sum(fdsp."total encounters") as "total encounters"
    ,case when sum(cast(fdsp."total students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."active students serviced" as decimal(10, 5))) / sum(cast(fdsp."total students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "activity rate"
    , case when sum(cast(fdsp."active students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."total encounters" as decimal(10, 5))) / sum(cast(fdsp."active students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "mpr"
    , case when sum(cast(fdsp."total students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."students done 1+" as decimal(10, 5))) / sum(cast(fdsp."total students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "total done 1+"
FROM
    {{ ref('fact_daily_student_progress') }} fdsp
LEFT JOIN {{ ref('territory_centers') }} tc
    ON fdsp.center_reference_id = tc.center_reference_id
WHERE fdsp."date" between date '2019-01-01' and date'2019-12-31'
and fdsp.location = 'InCenter'
and fdsp.group_id = 'Individual'
and fdsp.contract_inclusions = true
and fdsp.is_promotional = false
GROUP BY
    tc.territory_name,
    date_add('year',4,fdsp."date")
)
,
base_2022 as
(
SELECT
    tc.territory_name
    , date_add('year',3,fdsp."date") as "date"
    , sum(fdsp."total students serviced") as "total students serviced"
    , sum(fdsp."total students completing 21 days") as "total students completing 21 days"
    , sum(fdsp."active students serviced") as "active students serviced"
    , sum(fdsp."students done 1+") as "students done 1+"
    , sum(fdsp."students completed 21days") as "students completed 21days"
    , sum(fdsp."total encounters") as "total encounters"
    , case when sum(cast(fdsp."total students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."active students serviced" as decimal(10, 5))) / sum(cast(fdsp."total students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "activity rate"
    , case when sum(cast(fdsp."active students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."total encounters" as decimal(10, 5))) / sum(cast(fdsp."active students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "mpr"
    , case when sum(cast(fdsp."total students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."students done 1+" as decimal(10, 5))) / sum(cast(fdsp."total students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "total done 1+"
FROM
    {{ ref('fact_daily_student_progress') }} fdsp
LEFT JOIN {{ ref('territory_centers') }} tc
    ON fdsp.center_reference_id = tc.center_reference_id
WHERE fdsp."date" between date '2019-01-01' and date'2019-12-31'
and fdsp.location = 'InCenter'
and fdsp.group_id = 'Individual'
and fdsp.contract_inclusions = true
and fdsp.is_promotional = false
GROUP BY
    tc.territory_name,
    date_add('year',3,fdsp."date")
)
,
base_2021 as
(
SELECT
    tc.territory_name
    , date_add('year',2,fdsp."date") as "date"
    , sum(fdsp."total students serviced") as "total students serviced"
    , sum(fdsp."total students completing 21 days") as "total students completing 21 days"
    , sum(fdsp."active students serviced") as "active students serviced"
    , sum(fdsp."students done 1+") as "students done 1+"
    , sum(fdsp."students completed 21days") as "students completed 21days"
    , sum(fdsp."total encounters") as "total encounters"
    ,case when sum(cast(fdsp."total students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."active students serviced" as decimal(10, 5))) / sum(cast(fdsp."total students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "activity rate"
    , case when sum(cast(fdsp."active students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."total encounters" as decimal(10, 5))) / sum(cast(fdsp."active students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "mpr"
    , case when sum(cast(fdsp."total students serviced" as decimal(10, 5))) = 0 then 0
        else cast(sum(cast(fdsp."students done 1+" as decimal(10, 5))) / sum(cast(fdsp."total students serviced" as decimal(10, 5))) as decimal(10, 5)) end as "total done 1+"
FROM
    {{ ref('fact_daily_student_progress') }} fdsp
LEFT JOIN {{ ref('territory_centers') }} tc
    ON fdsp.center_reference_id = tc.center_reference_id
WHERE fdsp."date" between date '2019-01-01' and date'2019-12-31'
and fdsp.location = 'InCenter'
and fdsp.group_id = 'Individual'
and fdsp.contract_inclusions = true
and fdsp.is_promotional = false
GROUP BY
    tc.territory_name,
    date_add('year',2,fdsp."date")
)
,
base_all as
(
    select *
    from 
    base_2021
    union all
    select *
    from 
    base_2022
    union all
    select *
    from 
    base_2023
    union all
    select *
    from 
    base_2024
    union all
    select *
    from 
    base_2025
)

SELECT
    fdsp.territory_name as territory
    , fdsp."date"
    , fdsp."total students serviced"
    , fdsp."total students completing 21 days"
    , fdsp."active students serviced"
    , fdsp."students done 1+"
    , fdsp."students completed 21days"
    , fdsp."total encounters"
    , fdsp."activity rate"
    , fdsp."mpr"
    , fdsp."total done 1+"
    , (fdsp."activity rate" * (1+fdt."activity")) * fdsp."total students serviced" as "expected - active students serviced"
    , (fdsp."mpr" * (1+fdt."mpr")) * fdsp."active students serviced" as "expected - total encounters"
    , (fdsp."total done 1+" * (1+fdt."totaldone1+")) * fdsp."total students serviced" as "expected - students done 1+"
FROM base_all as fdsp
LEFT JOIN {{ref('fact_dynamic_targets')}} as fdt
    ON fdsp.territory_name || cast(year(fdsp."date") as varchar) = lower(fdt.territoryyear)