import logging
import os
import boto3
import json

def lambda_handler(event, context):
    # Define the name of the S3 bucket from Environment Variables of Lambda
    Bucket = os.environ.get('bucket')
    # Create a Boto3 resource for S3
    Boto3Resource = boto3.resource("s3")

    # Access the specific bucket using the Boto3 resource
    S3Bucket = Boto3Resource.Bucket(Bucket)
    # Define the path to the execution check file in S3
    ExecutionCheckFilePath = Boto3Resource.Object(Bucket, 'ExecutionCheck.json')

    # Retrieve the content of the execution check file
    ExecutionCheckFileContent = ExecutionCheckFilePath.get()['Body'].read().decode('utf-8')

    # Parse the JSON content of the execution check file
    ExecutionCheck = json.loads(ExecutionCheckFileContent)
    # Print the parsed execution check data
    logging.warning(ExecutionCheck)

    # Define the folder path for the logs based on the CycleId value from the execution check
    Folder = f"Logs/{ExecutionCheck['CycleId']}/SaveTableLogs"

    # Get the list of files in the S3 bucket under the specified folder path
    FilesInS3 = [f.key.split(Folder + "/")[-1] for f in S3Bucket.objects.filter(Prefix=Folder).all()]

    # Print the count of files found in the specified folder
    logging.warning(len(FilesInS3))

    # Check if the number of files matches the expected count and count number is hardcoded due to no config details for savetable
    if len(FilesInS3) == 17:
        logging.warning("Stage1 savetable has been completed")
    else:
        logging.warning("SaveasTable logs count not matching")
        raise Exception