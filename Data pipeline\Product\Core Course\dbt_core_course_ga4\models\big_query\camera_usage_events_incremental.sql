{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

SELECT
    event_date,
    event_timestamp,
    event_name,
    user_id,
    platform,
    user_level,
    user_origin_level,
    user_center,
    user_role,
    class_id,
    class_access,
    camera_usage_time
FROM
    {{ ref('camera_usage_events') }}
{% if is_incremental() %}
WHERE
    event_date > (SELECT max(event_date) from {{this}})
{% endif %}