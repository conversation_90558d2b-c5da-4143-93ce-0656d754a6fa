import HistoryPackages

S3 = HistoryPackages.CloudOperations.S3
Redshift = HistoryPackages.DbOperations.Database
IamRole = HistoryPackages.DbOperations.IamRole
Region = HistoryPackages.DbOperations.Region

"""reading the Facebook Ads credential from secret key management services and assigning to variables"""
SecretInstance = HistoryPackages.CloudOperations.SecretManager
FacebookAccountCredential = SecretInstance.GetSecret("FacebookAds", "eu-north-1")
AppId = FacebookAccountCredential["app_id"]
AppSecret = FacebookAccountCredential["app_secret"]
AccessToken = FacebookAccountCredential["admin_system_user_token"]
HistoryPackages.FacebookAdsApi.init(app_id=AppId, app_secret=AppSecret, access_token=AccessToken, api_version="v15.0",
                             timeout=1200)


class Ads:
    @staticmethod
    def GetCampaignInformation(AccountId, FromDate, ToDate):
        """Function is used to extract data from Facebook Ads"""
        HistoryPackages.logging.warning("FromDate:'%s'", format(FromDate))
        HistoryPackages.logging.warning("ToDate:'%s'", format(ToDate))
        Fields = ["account_name", "account_id", "campaign_name", "campaign_id", "impressions", "objective","reach",
                  "clicks", "spend", "actions", "date_start", "date_stop"]
        Params = {
            "time_range": {"since": FromDate, "until": ToDate}, "time_increment": 1,
            "level": "campaign", "limit": 1000}
        Insights = []
        Attempts, Fetched = 0, False
        while not Fetched:
            try:
                HistoryPackages.time.sleep(10)
                HistoryPackages.logging.warning("Attempts:'%s'", format(Attempts))
                data_response = list(HistoryPackages.AdAccount(AccountId).get_insights(params=Params, fields=Fields))
                Insights.extend(data_response)
            except Exception as ErrorMessage:
                HistoryPackages.logging.warning("ErrorMessage:'%s'", format(ErrorMessage))
                HistoryPackages.logging.warning(f'Trying again in {60 + 3 * Attempts} seconds.')
                HistoryPackages.time.sleep(60 + 3 * Attempts)
                Attempts += 1
                if Attempts > 3:
                    HistoryPackages.logging.warning('Failed after 3 Attempts, abort fetching.')
                    raise Exception
            else:
                Fetched = True
        return Insights

    @staticmethod
    def TransformResponse(Insights, Territory):
        """Function is used to Transform JSON Response"""
        TransformedResponseJson = []
        if len(Insights) == 0:
            return TransformedResponseJson
        for Columns in Insights:
            # If we do not receive an API response for these keys, we will set their values to zero
            if "clicks" not in Columns:
                Columns["clicks"] = "0"
            if "spend" not in Columns:
                Columns["spend"] = "0"
            if "impressions" not in Columns:
                Columns["impressions"] = "0"
            if "reach" not in Columns:
                Columns["reach"] = "0"
            # Check if "actions" key is present in Columns dictionary
            if "actions" in Columns:
                for iterate in range(len(Columns["actions"])):
                    TransformStructure = {
                        "AccountId": Columns["account_id"],
                        "AccountName": Columns["account_name"],
                        "ActionType": Columns["actions"][iterate]["action_type"],
                        "ActionValue": Columns["actions"][iterate]["value"],
                        "CampaignId": Columns["campaign_id"],
                        "CampaignName": Columns["campaign_name"],
                        "Clicks": Columns["clicks"],
                        "DateStart": Columns["date_start"],
                        "DateStop": Columns["date_stop"],
                        "Impressions": Columns["impressions"],
                        "Spend": Columns["spend"],
                        "Territory": Territory,
                        "Objective": Columns["objective"],
                        "Reach":Columns["reach"]
                    }
                    TransformedResponseJson.append(TransformStructure)
            else:
                TransformStructure = {
                    "AccountId": Columns["account_id"],
                    "AccountName": Columns["account_name"],
                    "ActionType": None,
                    "ActionValue": None,
                    "CampaignId": Columns["campaign_id"],
                    "CampaignName": Columns["campaign_name"],
                    "Clicks": Columns["clicks"],
                    "DateStart": Columns["date_start"],
                    "DateStop": Columns["date_stop"],
                    "Impressions": Columns["impressions"],
                    "Spend": Columns["spend"],
                    "Territory": Territory,
                    "Objective": Columns["objective"],
                    "Reach": Columns["reach"]
                }
                TransformedResponseJson.append(TransformStructure)
        return TransformedResponseJson

    @staticmethod
    def CreateDataFrame(TransformedResponseJson):
        """Function is used to create DataFrame"""
        if len(TransformedResponseJson) != 0:
            AdsDf = HistoryPackages.pd.DataFrame(TransformedResponseJson)
            TransformedColumnName = ["AccountId", "AccountName", "ActionType", "ActionValue", "CampaignId",
                                     "CampaignName", "Clicks", "DateStart", "DateStop", "Impressions", "Spend",
                                     "Territory", "Objective","Reach"]
            ReindexAdsResponseDF = AdsDf.reindex(columns=TransformedColumnName)
            return ReindexAdsResponseDF
        else:
            EmptyDf = HistoryPackages.pd.DataFrame(TransformedResponseJson)
            return EmptyDf

    @staticmethod
    def CaptureResponse(DataResponse, Territory, Bucket, CycleId):
        """Function Is Used To Store Response In S3 As CSV File"""
        FromDate = min(DataResponse["DateStart"])
        ToDate = max(DataResponse["DateStop"])
        FileKey = f"FacebookAdsResponse/{Territory}/{CycleId}/" + \
                  f"{FromDate}-{ToDate}/{Territory}Response.csv"
        S3WriteDataResponse = S3.WriteCsvFile(FilePath=FileKey,
                                              Bucket=Bucket,
                                              DataResponse=DataResponse)
        HistoryPackages.logging.warning("S3WriteDataResponse:'%s'", format(S3WriteDataResponse))
        return FileKey

    @staticmethod
    def DataExtractionProcess(AccountId, Territory, LoadType, FromDate, ToDate, Bucket, CycleId):
        """Function Is Used To Extract Data From Ads"""
        HistoryPackages.logging.warning("AccountId:'%s'", format(AccountId))
        HistoryPackages.logging.warning("Territory:'%s'", format(Territory))
        HistoryPackages.logging.warning("LoadType:'%s'", format(LoadType))
        HistoryPackages.logging.warning("FromDate:'%s'", format(FromDate))
        HistoryPackages.logging.warning("ToDate:'%s'", format(ToDate))
        """declaring empty list to capture DataResponse"""
        ResponseJson = []
        if LoadType == "Initial":
            DateRange = HistoryPackages.Utils.Tools.GetMonthRanges(FromDate, ToDate)
            for Date in DateRange:
                DataResponseJson = Ads.GetCampaignInformation(AccountId=AccountId,
                                                              FromDate=Date["FromDate"],
                                                              ToDate=Date["ToDate"])
                if len(DataResponseJson) != 0:
                    ResponseJson.extend(DataResponseJson)

        TransformedResponseJson = Ads.TransformResponse(Insights=ResponseJson, Territory=Territory)
        ResponseDf = Ads.CreateDataFrame(TransformedResponseJson=TransformedResponseJson)
        if len(ResponseDf) == 0:
            HistoryPackages.logging.warning("no response to store")
            Summary = {
                "CutoffDate": ToDate,
                "RecordsProcessed": 0
            }
            return Summary
        S3WriteResponse = Ads.CaptureResponse(DataResponse=ResponseDf,
                                              Territory=Territory,
                                              Bucket=Bucket,
                                              CycleId=CycleId)
        """execute copy command to move data from s3 file to redshift table"""
        CopyCommandQuery = """COPY facebook.ads FROM 's3://{}/{}' iam_role '{}' region '{}' IGNOREHEADER 1 CSV
                                                            timeformat 'auto';""" \
            .format(Bucket, S3WriteResponse, IamRole, Region)
        HistoryPackages.logging.warning("CopyCommandQuery:'%s'", format(CopyCommandQuery))

        ExecuteCopyCommand = Redshift.Execution(ExecutionType="WriteTable",
                                                Query=CopyCommandQuery,
                                                StatementName=f"{Territory}")
        HistoryPackages.logging.warning("ExecuteCopyCommand:'%s'", format(ExecuteCopyCommand))
        Summary = {
            "S3": {"DataResponse": S3WriteResponse},
            "Redshift": ExecuteCopyCommand,
            "CutoffDate": max(ResponseDf['DateStop']),
            "RecordsProcessed": len(ResponseDf)
        }
        return Summary
