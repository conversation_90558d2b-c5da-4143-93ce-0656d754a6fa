{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        classtype,
        {{ cast_to_timestamp('startdate') }} as startdate,
        numberofseats,
        numberofstudents,
        numberofwaitingstudents,
        iscomplete,
        isclosed,
        isonline,
        DURATION,
        ispro,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        isvisibleingroup,
        isb2b,
        technology,
        isrestrictedtoonlineonly,
        isteen,
        noofseatsinstandby,
        id,
        refclassid,
        classcode,
        centerid,
        classdescription,
        teacherid,
        categoriesabbreviations,
        companyid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'class'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    classtype as class_type,
    startdate as start_date,
    numberofseats as number_of_seats,
    numberofstudents as number_of_students,
    numberofwaitingstudents as number_of_waiting_students,
    iscomplete as is_complete,
    isclosed as is_closed,
    isonline as is_online,
    DURATION,
    ispro as is_pro,
    created,
    lastupdated as last_updated,
    isvisibleingroup as is_visible_in_group,
    isb2b as is_b2_b,
    technology,
    isrestrictedtoonlineonly as is_restricted_to_online_only,
    isteen as is_teen,
    noofseatsinstandby as no_of_seats_in_standby,
    id,
    refclassid as ref_class_id,
    classcode as class_code,
    centerid as center_id,
    classdescription as class_description,
    teacherid as teacher_id,
    categoriesabbreviations as categories_abbreviations,
    companyid as company_id
FROM
    rankedrecords
WHERE
    rn = 1;
