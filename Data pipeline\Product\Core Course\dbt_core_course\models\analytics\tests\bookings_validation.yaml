version: 2

models:
  - name: bookings
    columns:
      - name: class_id
        tests:
          - not_null:
              severity: error
      - name: ref_class_id
        tests:
          - not_null:
              severity: warn
      - name: booking_id
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: student_id
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        tests:
          - not_null:
              severity: error
      - name: book_mode
        tests:
          - accepted_values:
              values: ['book', 'standby']
              severity: error
      - name: booking_created_datetime
        tests:
          - not_null:
              severity: error
      - name: booking_local_created_datetime
        tests:
          - not_null:
              severity: error
      - name: booking_last_updated_datetime
        tests:
          - not_null:
              severity: error
      - name: booking_local_last_updated_datetime
        tests:
          - not_null:
              severity: error
      - name: booking_cancelled_flag
        tests:
          - accepted_values:
              values: ['false', 'true']
              quote: false
              severity: error
      - name: auto_cancel_type
        tests:
          - accepted_values:
              values: ['not auto cancel', '12', '24']
              severity: error
      - name: booking_accessed_flag
        tests:
          - accepted_values:
              values: ['false', 'true']
              quote: false
              severity: error
      - name: standby_to_booked_flag
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: standby_to_booked_24hrs
        tests:
          - accepted_values:
              values: [0, 1]
              quote: false
              severity: error
      - name: cancellations_12hrs
        tests:
          - accepted_values:
              values: [0, 1]
              quote: false
              severity: error
      - name: cancellations_24hrs
        tests:
          - accepted_values:
              values: [0, 1]
              quote: false
              severity: error
      - name: cancellations_12hrs_not_sbtb_24hrs
        tests:
          - accepted_values:
              values: [0, 1]
              quote: false
              severity: error
