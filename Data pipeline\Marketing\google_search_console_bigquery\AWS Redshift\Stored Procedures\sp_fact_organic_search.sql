-- DROP PROCEDURE hubspot_crm.sp_fact_organic_search();

CREATE OR REPLACE PROCEDURE hubspot_crm.sp_fact_organic_search()
	LANGUAGE plpgsql
AS $$
	

BEGIN

truncate table hubspot_crm.fact_organic_search ;

insert into hubspot_crm.fact_organic_search (

SELECT *
FROM (
    SELECT COALESCE(daily_date, data_date) AS "date",
         COALESCE(crm.territory_name, gsc.territory_name) AS territory_name,
         COALESCE(crm.page_type, gsc.page_type) AS page_type,
         COALESCE(page, page_url) AS page,
         COALESCE(host_name, split_part(page, '/', 1)) AS host_name,
         COALESCE(page_path, SUBSTRING(page, POSITION('/' IN page))) AS page_path,
         impressions,
         clicks,
         lead_mql,
         contracts_funnel,
         sales_funnel,
         refunds,
         refunds_amount,
         lead_clean
    FROM (
        SELECT daily_date,
               territory_name,
               page,
               page_type,
               SUM(lead_mql) AS lead_mql,
               SUM(contracts_funnel) AS contracts_funnel,
               SUM(sales_funnel) AS sales_funnel,
               SUM(refunds) AS refunds,
               SUM(refunds_amount) AS refunds_amount,
               SUM(lead_clean) AS lead_clean
        FROM hubspot_crm.temp_sales_crm
        WHERE source IN ('web', 'web inbound')
        AND r_channel = 'organic search'
        GROUP BY daily_date, territory_name, page,page_type
    ) AS crm

    FULL OUTER JOIN (
        SELECT data_date,
            territory_name,
            page_type,
            SPLIT_PART(page_url,'?',1) AS page_url,
            host_name,
            SPLIT_PART(page_path,'?',1) AS page_path,
            SUM(impressions) AS impressions,
            SUM(clicks)      AS clicks
        FROM devdwh.google.organic_search
        GROUP BY data_date, territory_name, page_type, page_url, host_name, page_path
    ) AS gsc
    ON crm.daily_date = gsc.data_date
    AND crm.territory_name = gsc.territory_name
    AND crm.page = gsc.page_url
    )
WHERE territory_name IS NOT NULL
AND territory_name !=''

);

    COMMIT;


END;





$$
;