{{ config(
    materialized="incremental",
    unique_key=["event_name", "event_timestamp"],
    incremental_strategy="insert_overwrite",
    partition_by={"field": "event_date", "data_type": "date","granularity": "day"},
    cluster_by=["event_name", "event_timestamp"]
) }}

{% if is_incremental() %}
    {% set max_existing_date_query %}
        SELECT MAX(event_date) AS max_event_date
        FROM {{ this }}
    {% endset %}

    {% set max_existing_date_result = run_query(max_existing_date_query) %}
    {% if max_existing_date_result is not none and max_existing_date_result.rows|length > 0 %}
        {% set max_existing_date = max_existing_date_result.columns[0].values()[0] %}
    {% else %}
        {% set max_existing_date = none %}
    {% endif %}
{% else %}
    {% set max_existing_date = none %}
{% endif %}

{% if max_existing_date is none %}
    {% set start_date = "DATE_SUB(CURRENT_DATE(), INTERVAL 148 DAY)" %} 
    -- the INTERVAL will change if we do full refresh to get data from september 2024
{% else %}
    {% set start_date = "DATE_ADD(DATE('" ~ max_existing_date ~ "'), INTERVAL 1 DAY)" %}
{% endif %}

{% set end_date = "DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)" %}

WITH source_data AS (
    SELECT *
    FROM `core-course-bigquery.analytics_358724658.events_intraday_*`
    WHERE 
    event_name IN ('page_view', 'screen_view','session_start')
    and PARSE_DATE('%Y%m%d', _TABLE_SUFFIX) BETWEEN {{ start_date }} AND {{ end_date }}
)

SELECT
    PARSE_DATE('%Y%m%d', event_date) AS event_date,
    event_timestamp,
    event_name,
    MAX(user_id) AS user_id, 
    MAX(CASE WHEN prop.key = 'user_identifier' THEN prop.value.string_value END) as user_identifier,
    COALESCE(
      MAX(CASE WHEN param.key = 'firebase_screen' THEN param.value.string_value END),
      MAX(CASE WHEN param.key = 'page_location' THEN param.value.string_value END)
    ) AS screen,
    MAX(CASE WHEN prop.key = 'user_role' THEN prop.value.string_value END) AS role,
    MAX(CASE WHEN param.key = 'growthbook_variant' THEN param.value.string_value END) AS gb_variant,
    MAX(CASE WHEN prop.key = 'variation_id' THEN prop.value.string_value END) AS variant,
    MAX(CASE WHEN prop.key = 'experiment_id' THEN prop.value.string_value END) AS experiment,
    CURRENT_TIMESTAMP() AS etl_load_date
FROM source_data,
UNNEST(event_params) AS param
LEFT JOIN UNNEST(user_properties) AS prop
ON TRUE
GROUP BY
    event_date,
    event_name,
    event_timestamp
