import packages

s3 = packages.cloud_operations.S3
redshift = packages.db_operations


class Planner:
    @staticmethod
    def execute(sql, source):
        packages.logging.warning("planner started")
        planner_execute = redshift.DbQueryExecution.execute_query(sql, 'planner')
        planner_describe = redshift.DbQueryExecution.describe_query(planner_execute)
        planner_response = redshift.DbQueryExecution.get_statement_result(planner_describe['Id'])
        count = 0
        for record in planner_response:
            if record['status'] == "0":
                count += 1
        if count == 0:
            status = "previous execution completed"
            update_query = "UPDATE sharepoint.execution_planner SET status = '0' where source = '" + source + "';"
            update_planner_execute = redshift.DbQueryExecution.execute_query(update_query, 'update planner')
            redshift.DbQueryExecution.describe_query(update_planner_execute)
        else:
            status = "previous execution not completed"
        return status
