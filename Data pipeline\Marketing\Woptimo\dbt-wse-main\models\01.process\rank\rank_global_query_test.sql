{{
    config(
        tags=["incremental","rank","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}
select
query,event_date,
min(rank_organic) rank_organic,
max(is_featured_snippet) is_featured_snippet,
string_agg(page order by rank_organic asc LIMIT 1) page,
from {{ ref('rank_fill_norank_test') }} 
where {{increment()}}
group by query,event_date
