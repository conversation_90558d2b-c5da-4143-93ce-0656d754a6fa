{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
    id,
    fieldname,
    {{cast_to_timestamp('createddate')}} as createddate,
    {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
    ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'contractauditfields')}}
)

SELECT
    {{etl_load_date()}},
    id,
    fieldname as field_name,
    {{cast_to_timestamp('createddate')}} as created_date,
    lastupdateddate as last_updated_date
FROM 
    RankedRecords
WHERE 
    rn = 1;