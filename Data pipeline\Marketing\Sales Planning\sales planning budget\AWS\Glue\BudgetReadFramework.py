import Packages

S3 = Packages.CloudOperations.S3
secret_instance = Packages.CloudOperations.SecretManager
Redshift = Packages.DbOperations.Database
IamRole = Packages.DbOperations.IamRole
Region = Packages.DbOperations.Region


class BudgetOperations:
    @staticmethod
    def ReadWorkbook(FilePath, data_only=True):
        Workbook = Packages.load_workbook(FilePath, data_only=data_only)
        return Workbook

    @staticmethod
    def ReadTable(Workbook, SheetName, TableName):
        Sheet = Workbook[SheetName]
        TableRange = Sheet.tables[TableName].ref
        TableData = []
        for row in Sheet[TableRange]:
            TableData.append([cell.value for cell in row])
        TableDf = Packages.pd.DataFrame(TableData)
        new_header = TableDf.iloc[0]
        TableDf = TableDf[1:]
        TableDf.columns = new_header
        return TableDf

    @staticmethod
    def TransformResponse(ReadTable):
        DfToJson = ReadTable.to_dict(orient='records')  # convert DataFrame to JSON dict
        MonthList = list(ReadTable.columns)  # get columns as a list
        MonthList.remove('Center name')
        TransformedResponse = []
        for Data in DfToJson:
            for Month in MonthList:
                TransformDict = {"CenterName": Data['Center name'],
                                 "Month": Packages.datetime.datetime.strptime(
                                     Month + "-" + str(Packages.datetime.datetime.now().year),
                                     "%b-%d-%Y").strftime("%Y-%m-01"),
                                 "Budget": Data[Month]}
                TransformedResponse.append(TransformDict)
        BudgetResponse = Packages.pd.DataFrame(TransformedResponse)
        return BudgetResponse

    @staticmethod
    def AddNewColumns(DataResponse, Territory, CycleId):
        DataResponse.insert(0, 'CycleId', CycleId)
        DataResponse['Territory'] = Territory
        return DataResponse

    @staticmethod
    def CaptureResponse(DataResponse, Territory, SheetName, Bucket, CycleId):
        """Function Is Used To Store Response In S3 As CSV File"""
        FileKey = \
            f"BudgetResponse/{Territory}/{SheetName}/{CycleId}/{Territory}_{SheetName}Response.csv"
        S3WriteDataResponse = S3.WriteCsvFile(FilePath=FileKey,
                                              Bucket=Bucket,
                                              DataResponse=DataResponse)
        Packages.logging.warning("S3WriteDataResponse:'%s'", format(S3WriteDataResponse))
        return FileKey

    @staticmethod
    def DataExtractionProcess(CycleId, Territory, SubSite, Folder,
                              File, SheetName, TableName, Bucket):
        """Function Is Used To Extract Data From Sales Planning Tool Sharepoint Folder"""
        SharepointCredential = secret_instance.GetSecret('SharePointSalesPlanningTool', 'eu-west-1')
        """Variable Declaration for Sharepoint authentication"""
        Url, Username, Password, SiteUrl = [SharepointCredential[key] for key in
                                            ('Url', 'Username', 'Password', 'SiteUrl')]
        """Authentication to SharePoint"""
        AuthCookie = Packages.Office365(Url, username=Username, password=Password).GetCookies()
        Site = Packages.Site(SiteUrl + SubSite, version=Packages.Version.v2016, authcookie=AuthCookie)
        """Get the File from Sharepoint"""
        SiteFolder = Site.Folder(Folder)
        response = SiteFolder.get_file(File)
        """Getting the Sheet name"""
        ReadWorkbook = BudgetOperations.ReadWorkbook(FilePath=Packages.io.BytesIO(response))
        ReadTable = BudgetOperations.ReadTable(Workbook=ReadWorkbook, SheetName=SheetName, TableName=TableName)
        TransformResponse = BudgetOperations.TransformResponse(ReadTable=ReadTable)
        AddedColumns = BudgetOperations.AddNewColumns(DataResponse=TransformResponse, Territory=Territory, CycleId=CycleId)
        # return AddedColumns
        S3WriteResponse = BudgetOperations.CaptureResponse(DataResponse=AddedColumns, Territory=Territory,
                                                            SheetName=SheetName, Bucket=Bucket,
                                                           CycleId=CycleId)
        # return S3WriteResponse
        """execute delete query if data is present in redshift table for current cycle to avoid duplication"""
        DeleteQuery = """delete from sales_planning_tool.{} where CycleId ='{}' and Territory ='{}';""". \
            format(TableName, CycleId, Territory)
        Packages.logging.warning("DeleteQuery:'%s'", format(DeleteQuery))
        ExecuteDeleteQuery = Redshift.Execution(ExecutionType="WriteTable",
                                                Query=DeleteQuery,
                                                StatementName=f"{Territory}_{SheetName}")
        Packages.logging.warning("ExecuteDeleteQuery:'%s'", format(ExecuteDeleteQuery))
        """execute copy command to move data from s3 file to redshift table"""
        CopyCommandQuery = """COPY sales_planning_tool.{} FROM 's3://{}/{}' iam_role '{}' region '{}' IGNOREHEADER 1 CSV
                                                                    timeformat 'auto';""" \
            .format(TableName, Bucket, S3WriteResponse, IamRole, Region)
        Packages.logging.warning("CopyCommandQuery:'%s'", format(CopyCommandQuery))
        ExecuteCopyCommand = Redshift.Execution(ExecutionType="WriteTable",
                                                Query=CopyCommandQuery,
                                                StatementName=f"{Territory}_{SheetName}")
        Packages.logging.warning("ExecuteCopyCommand:'%s'", format(ExecuteCopyCommand))
        Summary = {
            "S3": {"DataResponse": S3WriteResponse},
            "Redshift": ExecuteCopyCommand,
            "RecordsProcessed": len(AddedColumns)
        }
        """main table data delete"""
        DWTableNamesSwitch = {"budget": "Budget"}
        DWTableName = DWTableNamesSwitch.get(TableName)
        Packages.logging.warning("DWTableName:'%s'", format(DWTableName))
        """execute delete query to delete the excisting data in the main table"""
        DWTableDeleteQuery = """delete from salesplanningtool.{}
            where  Territory = '{}';""". \
            format(DWTableName, Territory)
        Packages.logging.warning("DWTableDeleteQuery:'%s'", format(DWTableDeleteQuery))
        ExecuteDWTableDeleteQuery = Redshift.Execution(ExecutionType="WriteTable",
                                                       Query=DWTableDeleteQuery,
                                                       StatementName=f"{Territory}_{SheetName}")
        Packages.logging.warning("ExecuteDWTableDeleteQuery:'%s'", format(ExecuteDWTableDeleteQuery))
        """main table data load"""
        DWTableInsertQuery = """insert into salesplanningtool.{}(select *,
                        TO_DATE(convert(timestamp, substring(CycleId, 1, 8)), 'YYYY-MM-DD') AS LoadDate
                        from sales_planning_tool.{}
                        where CycleId in (select max(CycleId) CYCLEID
                            from sales_planning_tool.{}
                            where Territory = '{}')
                        and Territory = '{}');""". \
            format(DWTableName, TableName, TableName, Territory, Territory)
        Packages.logging.warning("DWTableInsertQuery:'%s'", format(DWTableInsertQuery))
        ExecuteDWTableInsertQuery = Redshift.Execution(ExecutionType="WriteTable",
                                                       Query=DWTableInsertQuery,
                                                       StatementName=f"{Territory}_{SheetName}")
        Packages.logging.warning("ExecuteDWTableInsertQuery:'%s'", format(ExecuteDWTableInsertQuery))
        return Summary
