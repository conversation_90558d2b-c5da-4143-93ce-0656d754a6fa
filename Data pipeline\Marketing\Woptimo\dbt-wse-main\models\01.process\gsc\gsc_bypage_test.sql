{{
    config(
        tags=["incremental","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}
SELECT event_date, 
sum(clicks) clicks,
sum(local_clicks) local_clicks,
sum(CASE WHEN is_branded is TRUE THEN clicks ELSE 0 END ) AS branded_clicks,

sum(impressions) impressions,
sum(local_impressions) local_impressions,
sum(CASE WHEN is_branded is TRUE THEN impressions ELSE 0 END ) AS branded_impressions,
SPLIT(page,"?")[safe_offset(0)] AS page,
  ARRAY_AGG(STRUCT(
  IFNULL(query,"(not provided)") as query,
  clicks,
  impressions,
  sum_position,
  rank_organic,
  is_featured_snippet,
  sum_top_position,
  site_impressions
)) AS queries,
lang

FROM {{ ref('gsc_merge_query_test') }}
where {{increment()}}
group by event_date,page,lang