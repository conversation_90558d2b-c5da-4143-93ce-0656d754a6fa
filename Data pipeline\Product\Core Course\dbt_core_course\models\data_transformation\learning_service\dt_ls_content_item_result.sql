{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_content_item_result') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT {{etl_load_date()}},
    contitemresult.id as id,
    registration_id,
    contitem.description as content_item,
    contitemresult.content_item_id,
    contitemtype.name as content_item_type,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(category.path, '.', 1), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(category.path, '.', 1), '[^0-9]', '')
    END AS level,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(category.path, '.', 2), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(category.path, '.', 2), '[^0-9]', '')
    END AS unit,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(category.path, '.', 3), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(category.path, '.', 3), '[^0-9]', '')
    END AS lesson,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(category.path, '.', 4), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(category.path, '.', 4), '[^0-9]', '')
    END AS mini_cycle,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(category.path, '.', 5), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(category.path, '.', 5), '[^0-9]', '')
    END AS mini_cycle_stage,
    category.path as category_path,
    contitemrestype.name as content_itemresult_type,
    score,
    date_completed,
    {{ convert_to_local_timestamp(
        'date_completed',
        'tz.time_zone_id'
    ) }} as local_date_completed,
    total_questions,
    total_question_answered,
    total_correct_answers,
    teacher_id,
    Duration,
    date_started,
    {{ convert_to_local_timestamp(
        'date_started',
        'tz.time_zone_id'
    ) }} as local_date_started,
    display_on_list,
    Comment,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated,
    ref_class_id,
    class_id,
    to_process_in_background,
    student_id,
    CASE
        WHEN activity_captured_type_id = 0 then 'noasr'
        WHEN activity_captured_type_id = 1 then 'asron'
        WHEN activity_captured_type_id = 2 then 'asroff'
        ELSE CAST(
            activity_captured_type_id AS Varchar
        )
    end as activity_captured_type,
    CASE
        WHEN study_mode = 0 then 'online'
        WHEN study_mode = 1 then 'incenter'
        WHEN study_mode = 2 then 'offline'
        WHEN study_mode = 3 then 'mobile'
        ELSE CAST(
            study_mode AS Varchar
        )
    end as study_mode
from
    ods_data as contitemresult
    Left Join (
        select
            id,
            description,
            content_item_type_id,
            ancestor_category_id
        from
            {{ ref('ods_ls_content_item') }}
    ) as contitem
    ON contitemresult.content_item_id = contitem.id
    Left Join (
        select
            id,
            name
        from
            {{ ref('ods_ls_content_item_type') }}
    ) as contitemtype
    ON contitem.content_item_type_id = contitemtype.id
    Left Join (
        select
            id,
            path
        from
            {{ ref('ods_ls_category') }}
    ) as category
    ON contitem.ancestor_category_id = category.id
    Left Join (
        select
            id,
            name
        from
            {{ ref('ods_ls_content_item_result_type') }}
    ) as contitemrestype
    ON contitemresult.content_item_result_type_id = contitemrestype.id
    Left Join (
        select
            id,
            center_id
        from
            {{ ref('ods_ls_registration') }}
    ) as registration
    ON contitemresult.registration_id = registration.id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = registration.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
