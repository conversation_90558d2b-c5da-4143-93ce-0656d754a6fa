{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        name
    FROM 
        {{source('stage_contract_service', 'contractvalidationstate')}}
)

SELECT 
    {{etl_load_date()}},
    id,
    name
FROM
    RankedRecords