from datetime import datetime

def TimeFormat(CutOffDate)  :
    """
    Convert CutOffDate to epoch time in milliseconds.

    Args:
        CutOffDate (str): The date string in the format "%Y-%m-%d %H:%M:%S.%f" or "%Y-%m-%d %H:%M:%S".

    Returns:
        int: The epoch time in milliseconds.

    Raises:
        ValueError: If the CutOffDate format is not supported.
    """
    try:
        # Convert CutOffDate to datetime object
        UtcTime = datetime.strptime(CutOffDate, "%Y-%m-%d %H:%M:%S.%f")
        Epoch = datetime(1970, 1, 1)
        # Calculate the time difference from Epoch in seconds and convert to milliseconds
        EpochTime = (UtcTime - Epoch).total_seconds() * 1000
        return int(EpochTime)
    except ValueError:
        # If the format "%Y-%m-%d %H:%M:%S.%f" is not supported, try "%Y-%m-%d %H:%M:%S"
        UtcTime = datetime.strptime(CutOffDate, "%Y-%m-%d %H:%M:%S")
        Epoch = datetime(1970, 1, 1)
        # Calculate the time difference from Epoch in seconds and convert to milliseconds
        EpochTime = (UtcTime - Epoch).total_seconds() * 1000
        return int(EpochTime)
