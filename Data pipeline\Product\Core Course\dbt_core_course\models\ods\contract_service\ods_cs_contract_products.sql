{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        (contractid || cast(productid as varchar)) as dbt_unique_id,
        contractid,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
        productid,
        ROW_NUMBER() OVER (PARTITION BY (contractid || cast(productid as varchar)) ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'contractproducts')}}
)

SELECT 
    {{etl_load_date()}},
    dbt_unique_id,
    contractid as contract_id,
    createddate as created_date,
    lastupdateddate as last_updated_date,
    productid as product_id
FROM 
    RankedRecords
WHERE 
    rn = 1;