{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'registration_course_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        canbechanged,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        workbooktype,
        releaseworkbook,
        registrationcourseid,
        categoryid,
        studentid,
        ROW_NUMBER() over (
            PARTITION BY registrationcourseid
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'digitalbooksaccesssetting'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    canbechanged as can_be_changed,
    created,
    lastupdated as last_updated,
    workbooktype as workbook_type,
    releaseworkbook as release_work_book,
    registrationcourseid as registration_course_id,
    categoryid as category_id,
    studentid as student_id
FROM
    rankedrecords
WHERE
    rn = 1;
