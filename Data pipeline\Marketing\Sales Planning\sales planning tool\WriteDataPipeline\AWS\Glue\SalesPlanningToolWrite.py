import sys
from awsglue.utils import getResolvedOptions
import CloudOperations
import DbOperations
import time
import json
import logging
from shareplum import Site, Office365
from shareplum.site import Version
import boto3
import io
import pandas as pd
from openpyxl import Workbook, load_workbook
import datetime
from datetime import date
import SalesPlanningToolWriteFramework

Args = getResolvedOptions(sys.argv,
                                   ['Status', 'Stage', 'Operation', 'Territory', 'Center', 'ReferenceCenterId',
                                    'TerritoryReferenceId', 'SubSite', 'Folder', 'File', 'Category', 'CycleId'])
S3 = CloudOperations.S3
Bucket = "sales-planning-tool-write-develop"
instance = SalesPlanningToolWriteFramework.SalesPlanningTool
secret_instance = CloudOperations.SecretManager
Status = Args['Status']
Stage = Args['Stage']
Operation = Args['Operation']
Territory = Args['Territory']
Center = Args['Center']
ReferenceCenterId = Args['ReferenceCenterId']
TerritoryReferenceId = Args['TerritoryReferenceId']
SubSite = Args['SubSite']
Folder = Args['Folder']
File = Args['File']
Category = Args['Category']
CycleId = Args['CycleId']
Function = 'write_process'

PlanningToolClass = getattr(instance, Category)()
"""Is Used To Extract Data From Sales Planning Tool Sharepoint Folder"""
SharepointCredential = secret_instance.GetSecret('SharePointSalesPlanningTool', 'eu-west-1')
"""Variable Declaration for Sharepoint authentication"""
Url, Username, Password, SiteUrl = [SharepointCredential[key] for key in
                                    ('Url', 'Username', 'Password', 'SiteUrl')]
"""Authentication to SharePoint"""
AuthCookie = Office365(Url, username=Username, password=Password).GetCookies()
Site = Site(SiteUrl + SubSite, version=Version.v2016, authcookie=AuthCookie)
"""Get the File from Sharepoint"""
SiteFolder = Site.Folder(Folder)
ReadResponse = SiteFolder.get_file(File)
PlanningToolExecuteWrite = getattr(PlanningToolClass, Function)(ReadResponse, File, SiteFolder, Territory, TerritoryReferenceId, ReferenceCenterId)
print(PlanningToolExecuteWrite)

Logs = {
    "Status": 200,
    "Stage": int(Stage),
    "Operation": Operation,
    "Territory": Territory,
    "Center":Center,
    "CycleId": CycleId
}
logging.warning("Logs:'%s'", format(Logs))
S3.WriteJsonFile(Bucket, f"Logs/{CycleId}/Stage{Stage}/{Territory}{Center}.json", Logs)