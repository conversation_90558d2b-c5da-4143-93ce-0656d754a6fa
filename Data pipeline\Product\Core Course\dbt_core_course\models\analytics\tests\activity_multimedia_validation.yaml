version: 2

models:
  - name: activity_multimedia
    columns:
      - name: activity_id
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: registration_id
        tests:
          - not_null:
              severity: error
      - name: student_id
        tests:
          - not_null:
              severity: error
      - name: content_item_id
        tests:
          - not_null:
              severity: error
      - name: created_date
        tests:
          - not_null:
              severity: error
      - name: local_created_date
        tests:
          - not_null:
              severity: error
      - name: last_updated_date
        tests:
          - not_null:
              severity: error
      - name: local_last_updated_date
        tests:
          - not_null:
              severity: error
      - name: started_date
        tests:
          - not_null:
              severity: error
      - name: local_started_date
        tests:
          - not_null:
              severity: error
      - name: level
        tests:
          - not_null:
              severity: error
      - name: content_item_type
        tests:
          - not_null:
              severity: error
      - name: activity_captured_type
        tests:
          - not_null:
              severity: warn
