{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'user_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        gender,
        {{ cast_to_timestamp('birthdate') }} as birthdate,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        isemailverified,
        userid,
        username,
        password,
        firstname,
        lastname,
        roleid,
        ssdsid,
        email,
        socialnetworkid1,
        socialnetworkaddress1,
        socialnetworkid2,
        socialnetworkaddress2,
        socialnetworkid3,
        socialnetworkaddress3,
        socialnetworkid4,
        socialnetworkaddress4,
        photoname,
        mobiletelephone,
        hometelephone,
        worktelephone,
        nationalityid,
        centerid,
        territoryid,
        preferredlanguageid,
        ROW_NUMBER() over (
            PARTITION BY userid
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'user'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    gender,
    birthdate as birth_date,
    created,
    lastupdated as last_updated,
    isemailverified as is_email_verified,
    userid as user_id,
    username as user_name,
    password,
    firstname as first_name,
    lastname as last_name,
    roleid as role_id,
    ssdsid as ssds_id,
    email,
    socialnetworkid1 as social_network_id1,
    socialnetworkaddress1 as social_network_address1,
    socialnetworkid2 as social_network_id2,
    socialnetworkaddress2 as social_network_address2,
    socialnetworkid3 as social_network_id3,
    socialnetworkaddress3 as social_network_address3,
    socialnetworkid4 as social_network_id4,
    socialnetworkaddress4 as social_network_address4,
    photoname as photo_name,
    mobiletelephone as mobile_telephone,
    hometelephone as home_telephone,
    worktelephone as work_telephone,
    nationalityid as nationality_id,
    centerid as center_id,
    territoryid as territory_id,
    preferredlanguageid as preferred_language_id
FROM
    rankedrecords
WHERE
    rn = 1;
