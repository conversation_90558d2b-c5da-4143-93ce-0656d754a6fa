{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_idam_territory') }}
)
SELECT {{etl_load_date()}},
    is_active,
    id,
    territory_reference_id,
    name,
    code,
    iso_code
from
    ods_data as territory
