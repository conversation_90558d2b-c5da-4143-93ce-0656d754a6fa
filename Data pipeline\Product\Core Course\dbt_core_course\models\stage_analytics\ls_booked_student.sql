{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with
    raw_data as (
        select *
        from
            {{ref('dt_ls_booked_student')}}
    )

    , ref_class as (
        select 
            id as class_id,
            ref_class_id
        from
            {{ref('ods_ls_class')}}
    )
    , sort_data_fields as (
        select 
            raw_data.id,
            raw_data.class_id,
            raw_data.student_id,
            raw_data.book_date,
            raw_data.local_book_date,
            raw_data.result,
            raw_data.attended,
            raw_data.created,
            raw_data.local_created,
            raw_data.last_updated,
            raw_data.local_last_updated,
            raw_data.booked_by,
            raw_data.booked_role_title,
            raw_data.booked_person_type,
            raw_data.cancelled_by,
            raw_data.cancelled_role_title,
            raw_data.cancelled_person_type,
            raw_data.is_cancelled,
            raw_data.book_mode_modified_date,
            raw_data.local_book_mode_modified_date,
            raw_data.standby_notification_type,
            raw_data.is_accessed,
            raw_data.registration_id,
            ref_class.ref_class_id,
            case
                when book_mode = 0
                    then 'book'
                when book_mode = 1
                    then 'standby'
                else cast(
                        book_mode as varchar(10)
                    )
            end as class_book_mode
            , row_number() over (partition by raw_data.class_id, raw_data.student_id, raw_data.book_mode order by created desc) as booking_order_desc
            , case 
                when book_mode = 0 and book_mode_modified_date is not null
                        then 1
                else 0
            end as standby_to_booked_flag
        from
            raw_data
            Left join ref_class
            ON ref_class.class_id = raw_data.class_id
    )

    , renaming as (
        select 
            id,
            class_id,
            ref_class_id,
            student_id,
            class_book_mode as book_mode,
            book_date,
            local_book_date,
            result,
            attended,
            created,
            local_created,
            last_updated,
            local_last_updated,
            booked_by,
            booked_role_title,
            booked_person_type,
            cancelled_by,
            cancelled_role_title,
            cancelled_person_type,
            is_cancelled,
            book_mode_modified_date,
            local_book_mode_modified_date,
            standby_notification_type,
            is_accessed,
            registration_id,
            booking_order_desc,
            standby_to_booked_flag
        from 
            sort_data_fields

    )
select *
from renaming