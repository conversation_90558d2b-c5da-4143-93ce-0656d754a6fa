{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        sequence,
        isskippable,
        duration,
        isactive,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        placementtestlevelid,
        title,
        url,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'placementtestactivity'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    sequence,
    isskippable as is_skippable,
    duration,
    isactive as is_active,
    created,
    lastupdated as last_updated,
    id,
    placementtestlevelid as placement_test_level_id,
    title,
    url
FROM
    rankedrecords
WHERE
    rn = 1;
