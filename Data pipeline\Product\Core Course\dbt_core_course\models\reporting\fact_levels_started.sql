{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH MinSequence as (
    SELECT
        student_reference_id,
        min("sequence") as min_sequence
    FROM 
        {{ref("levels_started")}}
    WHERE
        workbook_type = 'digital' AND operation_type = 'bill' AND unlock_type = 'standard'
    GROUP BY
        student_reference_id
)

SELECT
    center.territory_name                   as territory_name,
    center.center_name                     as center_name,
    student_progress.local_date_granted    as "date",
    contracts.contract_type                as contract_type,
    contracts.product_type                 as product_type,
    contracts.location                     as "location",
    contracts.service_type                 as service_type,
    CASE 
        WHEN student_progress.is_teen = true then 'DDMT'
        WHEN student_progress.is_teen = false then 'DDM'
    END                                     as billing_code,
    student_progress.unlock_type           as unlock_type,
    student_progress.workbook_type         as workbook_type,
    student_progress.operation_type        as operation_type,
    student_progress.is_restart            as is_restart,
    CASE
        WHEN student_progress.sequence = MinSequence.min_sequence then 'First'
        WHEN student_progress.sequence = MinSequence.min_sequence+1 and student_progress.operation_type = 'refund' then 'First'
        WHEN student_progress.sequence != MinSequence.min_sequence then 'Later'
    END                                     as first_later,
    SUM(
        CASE
            WHEN student_progress.operation_type = 'bill' then 1
            WHEN student_progress.operation_type = 'refund' then -1
            ELSE 0
        END)                            as levels_started,
    SUM(
        CASE
            WHEN student_progress.operation_type = 'bill' and student_progress.is_teen = true then 1
            WHEN student_progress.operation_type = 'refund' and student_progress.is_teen = true then -1
            ELSE 0
        END)                            as levels_started_teens,
    SUM(
        CASE
            WHEN student_progress.operation_type = 'bill' and student_progress.is_teen = false then 1
            WHEN student_progress.operation_type = 'refund' and student_progress.is_teen = false then -1
            ELSE 0
        END)                            as levels_started_not_teens,
    CAST(current_timestamp AS TIMESTAMP(6)) AS load_date 
FROM
    {{ref("levels_started")}} as student_progress
LEFT JOIN
    {{ref("territory_centers")}} as center
    ON student_progress.center_reference_id = center.center_reference_id
LEFT JOIN
    {{ref("contracts")}} as contracts
    ON student_progress.contract_reference_id = contracts.contract_reference_id
LEFT JOIN 
    MinSequence
    ON student_progress.student_reference_id = MinSequence.student_reference_id
WHERE
    student_progress.local_date_granted between cast('2022-09-13' as timestamp(6)) and current_date
GROUP BY
    center.territory_name,
    center.center_id,
    center.center_name,
    student_progress.local_date_granted,
    contracts.contract_type,
    contracts.product_type,
    contracts.location,
    contracts.service_type,
    CASE 
        WHEN student_progress.is_teen = true then 'DDMT'
        WHEN student_progress.is_teen = false then 'DDM'
    END,
    student_progress.unlock_type,
    student_progress.workbook_type,
    student_progress.operation_type,
    student_progress.is_restart,
    CASE
        WHEN student_progress.sequence = MinSequence.min_sequence then 'First'
        WHEN student_progress.sequence = MinSequence.min_sequence+1 and student_progress.operation_type = 'refund' then 'First'
        WHEN student_progress.sequence != MinSequence.min_sequence then 'Later'
    END
