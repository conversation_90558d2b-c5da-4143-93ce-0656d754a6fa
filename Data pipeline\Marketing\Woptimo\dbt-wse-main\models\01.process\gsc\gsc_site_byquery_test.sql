{{
    config(
        tags=["incremental","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}
select
lang,event_date,
IFNULL(query,"(not provided)") as query,
sum(impressions) site_impressions,
sum(sum_top_position) sum_top_position
 from {{ ref('gsc_site_init_test') }}
 where {{increment()}}
group by event_date,query,lang