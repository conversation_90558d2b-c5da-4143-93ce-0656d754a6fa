import logging
import os
from dependencies import cloud_operations
from dependencies.pipeline_prerequisite import toggle_dag_state
from dependencies.slack_alerts import task_failure_callback, task_success_callback
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.operators.bash import Ba<PERSON><PERSON>perator
from datetime import timed<PERSON><PERSON>, datetime
from dependencies import glue_trigger

default_args = {
    'owner': 'wse_data_team',
    'start_date': datetime(2025, 2, 17)
}

dag = DAG('speaking_ai_beta',
          default_args=default_args,
          schedule_interval="0 9 * * *", # Runs daily at 9:00 AM UTC (2:30 PM IST)
          catchup=False,
          tags=['core-course'],
          max_active_tasks=15)
# Limit the number of parallel tasks to 15

s3 = cloud_operations.S3

file_info = s3.read_json_file(bucket="prod-corecourse", file_path="config/speaking_ai_beta_config.json")
logging.warning(file_info)


# function to call prerequisite
def execute_prerequisite():
    toggle_dag_state(dag)

# case conversation for airflow task name
def kebab_to_snake(kebab_str):
    return kebab_str.replace("-", "_")

# basic prerequisite check
prerequisite_check = PythonOperator(
    task_id='prerequisite_check',
    python_callable=execute_prerequisite,
    provide_context=True,
    dag=dag
)

# get the core course dbt path
HOME = os.environ["HOME"]  # retrieve the location of your home folder
dbt_path = os.path.join(HOME, "dbt/dbt_core_course")  # path to your dbt project
logging.warning("dbt path: ", dbt_path)

dbt_models_execution = BashOperator(
    task_id="dbt_models_execution",
    bash_command="cd /home/<USER>/dbt"
                 + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                 + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                 + f" && dbt run --select tag:speaking_ai_beta",  # run the model!
    on_failure_callback=task_failure_callback,
    on_success_callback=task_success_callback,
    dag=dag
)

process_tasks = []
for i, data in enumerate(file_info):
    if data['execution_type'] == 'yes':
        op_kwargs = {
            "glue_job_name": "speaking_ai_beat_data_ingestion",
            "glue_args": {
                "--Object": file_info[i]["Object"],
                "--FilterColumn": file_info[i]["FilterColumn"],
                "--OdsObject": file_info[i]["OdsObject"]
            }
        }
        task = PythonOperator(
            task_id = kebab_to_snake(data['Object']),
            python_callable=glue_trigger.run_glue_job,
            on_failure_callback=task_failure_callback,
            op_kwargs=op_kwargs,
            retries=1,  # Number of retries
            retry_delay=timedelta(minutes=5),
            dag=dag
        )
        process_tasks.append(task)

# Set up the task dependency
prerequisite_check >> process_tasks >> dbt_models_execution
