{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'dbt_unique_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_class_category') }}
)
SELECT 
    {{etl_load_date()}},
    dbt_unique_id,
    class_id,
    category_id
from
    ods_data as classcategory
