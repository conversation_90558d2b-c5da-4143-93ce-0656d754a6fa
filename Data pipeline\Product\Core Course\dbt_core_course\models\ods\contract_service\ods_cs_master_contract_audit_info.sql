{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        mastercontractid,
        modifiedfieldid,
        previousvalue,
        presentvalue,
        changetype,
        reason,
        modifiedbyid,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('effectivedate')}} as effectivedate,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY createddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'mastercontractauditinfo')}}
)

SELECT
    {{etl_load_date()}},
    id,
    mastercontractid as master_contract_id,
    modifiedfieldid as modified_field_id,
    previousvalue as previous_value,
    presentvalue as present_value,
    changetype as change_type,
    reason,
    modifiedbyid as modified_by_id,
    createddate as created_date,
    effectivedate as effective_date
FROM 
    RankedRecords
WHERE 
    rn = 1