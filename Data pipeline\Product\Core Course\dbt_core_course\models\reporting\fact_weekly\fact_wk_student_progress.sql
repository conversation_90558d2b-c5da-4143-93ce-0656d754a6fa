{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet',
    unique_key = 'dbt_unique_id',
    pre_hook=[
        """
        {% if is_incremental() %}
            {% if var('start_date', none) and var('end_date', none) %}
                DELETE FROM {{ this }}
                WHERE first_week_date BETWEEN DATE '{{ var('start_date') }}' AND DATE '{{ var('end_date') }}'
            {% else %}
                -- Default: Delete records for the last 6 weeks
                DELETE FROM {{ this }}
                WHERE first_week_date > (
                    SELECT DATE_TRUNC('WEEK', DATE_ADD('week', -6, CURRENT_DATE))
                )
            {% endif %}
        {% endif %}
        """
    ]
) }}


-- getting last 6 weeks based on current date
{% set end_date = var('end_date', modules.datetime.date.today()) %}
{% set start_date = var('start_date', (end_date - modules.datetime.timedelta(weeks=6))) %}



with calendar as (
    select
        "date"
        ,first_week_date
        ,last_week_date
        ,first_month_date
        ,last_month_date
        ,year_month_key
    from
        reporting.dim_calendar
    where
        first_week_date = "date"
        AND "date" <= CURRENT_DATE
        AND "date" <= DATE '{{ end_date }}'
        AND "date" >= DATE '{{ start_date }}'
)
SELECT
        cal.first_week_date,
        fws.group_id,
        fws.center_reference_id,
        fws.contract_type,
        fws.study_plan_type,
        fws.bookmark_mm_level,
        fws.location,
        fws.class_access_type,
        fws.service_type,
        fws.is_membership,
        fws.is_teen,
        fws.is_promotional,
        fws.contract_inclusions,
        cast(CASE
            WHEN fws.valid_current_date is null THEN 'Not Valid' 
            when fws.first_contract = true and date_add('week',4,fws.start_date) >= fws.first_week_date 
            then 'Onboarding'
            when fws.first_contract = false and date_add('week',4,fws.start_date) >= fws.first_week_date
            then 'Reactivated'
            WHEN fwp.student_id IS NULL or fwp.sessions_4wk is null THEN 'Inactive'
            WHEN fwp.segment = -1 then 'Inactive' 
            WHEN fwp.segment = 1 then  'Infrequent Short'
            WHEN fwp.segment = 2 then  'Infrequent Long'
            WHEN fwp.segment = 3 then  'Frequent Very Short'
            WHEN fwp.segment = 4 then  'Frequent Short'
            WHEN fwp.segment = 5 then  'Frequent Long' 
        end as varchar) as adjusted_segment ,
        cast(CASE
            WHEN fws_last.valid_current_date is null THEN 'Not Valid'  
            when fws_last.first_contract = true and date_add('week',4,fws_last.start_date) >= fws_last.first_week_date
            then 'Onboarding'
            when fws_last.first_contract = false and date_add('week',4,fws_last.start_date) >= fws_last.first_week_date
            then 'Reactivated'
            WHEN fwp_last.student_id IS NULL or fwp_last.sessions_4wk is null THEN 'Inactive'
            WHEN fwp_last.segment = -1 then 'Inactive' 
            WHEN fwp_last.segment = 1 then  'Infrequent Short'
            WHEN fwp_last.segment = 2 then  'Infrequent Long'
            WHEN fwp_last.segment = 3 then  'Frequent Very Short'
            WHEN fwp_last.segment = 4 then  'Frequent Short'
            WHEN fwp_last.segment = 5 then  'Frequent Long' 
        end as varchar) as last_adjusted_segment ,
        COUNT(fws.valid_rolling_7days) AS "Total Students Serviced 1wk",
        COUNT(fws.valid_rolling_14days) AS "Total Students Serviced 2wk",
        COUNT(fws.valid_rolling_28days) AS "Total Students Serviced 4wk",
        COUNT(fws.valid_current_date) FILTER (WHERE fws.start_date <= cal.first_week_date) AS "Total Students Period 1wk",
        COUNT(fws.valid_current_date) FILTER (WHERE fws.start_date <= date_add('week', -1, cal.first_week_date)) AS "Total Students Period 2wk",
        COUNT(fws.valid_current_date) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date)) AS "Total Students Period 4wk",
        COUNT(fws.valid_current_date) FILTER (WHERE fws.start_date <= date_add('week', -7, cal.first_week_date)) AS "Total Students Period 8wk",
        COUNT(CASE WHEN fws.start_date <= cal.first_week_date AND (fwp.multimedia_activities_completed_1wk > 0 OR fwp.workbook_activities_completed_1wk > 0 OR fwp.enc_attended_1wk > 0) THEN fws.valid_current_date END) AS "Active Students Serviced 1wk",
        COUNT(CASE WHEN fws.start_date <= date_add('week', -1, cal.first_week_date) AND (fwp.multimedia_activities_completed_2wk > 0 OR fwp.workbook_activities_completed_2wk > 0 OR fwp.enc_attended_2wk > 0) THEN fws.valid_current_date END) AS "Active Students Serviced 2wk",
        COUNT(CASE WHEN fws.start_date <= date_add('week', -3, cal.first_week_date) AND (fwp.multimedia_activities_completed_4wk > 0 OR fwp.workbook_activities_completed_4wk > 0 OR fwp.enc_attended_4wk > 0) THEN fws.valid_current_date END) AS "Active Students Serviced 4wk",
        COUNT(fws.valid_current_date) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date) 
                    and fwp.duration_4wk+fwp.enc_attended_4wk*60+fwp.cc_attended_4wk*60+fwp.sc_attended_4wk*60 <= 15) AS "Studied 15 mins 4wk",
        COUNT(fws.valid_current_date) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date) 
                    and fwp.duration_4wk+fwp.enc_attended_4wk*60+fwp.cc_attended_4wk*60+fwp.sc_attended_4wk*60 between 15.0000001 and 60) AS "Studied 15 to 60 mins 4wk",
        COUNT(fws.valid_current_date) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date) 
                    and fwp.duration_4wk+fwp.enc_attended_4wk*60+fwp.cc_attended_4wk*60+fwp.sc_attended_4wk*60 between 60.0000001 and 180) AS "Studied 1 to 3 hours 4wk",
        COUNT(fws.valid_current_date) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date) 
                    and fwp.duration_4wk+fwp.enc_attended_4wk*60+fwp.cc_attended_4wk*60+fwp.sc_attended_4wk*60 between 180.0000001 and 360) AS "Studied 3 to 6 hours 4wk",
        COUNT(fws.valid_current_date) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date) 
                    and fwp.duration_4wk+fwp.enc_attended_4wk*60+fwp.cc_attended_4wk*60+fwp.sc_attended_4wk*60 between 360.0000001 and 600) AS "Studied 6 to 10 hours 4wk",
        COUNT(fws.valid_current_date) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date) 
                    and fwp.duration_4wk+fwp.enc_attended_4wk*60+fwp.cc_attended_4wk*60+fwp.sc_attended_4wk*60 > 600) AS "Studied 10+ hours 4wk",
        COUNT(fws.valid_current_date) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date) 
                    and fwp.duration_4wk+fwp.enc_attended_4wk*60+fwp.cc_attended_4wk*60+fwp.sc_attended_4wk*60 > 60*6) AS "Engaged Students 4wk",
        COUNT(fws.valid_current_date) FILTER (WHERE fws.start_date <= date_add('week', -7, cal.first_week_date) 
                    and fwp.duration_8wk+fwp.enc_attended_8wk*60+fwp.cc_attended_8wk*60+fwp.sc_attended_8wk*60 > 60*10) AS "Long-term Engaged Students 8wk",
        SUM(fwp.enc_attended_1wk) FILTER (WHERE fws.start_date <= cal.first_week_date) as "Total Encounters 1wk",
        SUM(fwp.enc_attended_2wk) FILTER (WHERE fws.start_date <= date_add('week', -1, cal.first_week_date)) as "Total Encounters 2wk",
        SUM(fwp.enc_attended_4wk) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date)) as "Total Encounters 4wk",
        SUM(fwp.cc_attended_1wk) FILTER (WHERE fws.start_date <= cal.first_week_date)  as "Total complementary classes attended 1wk",
        SUM(fwp.cc_attended_2wk) FILTER (WHERE fws.start_date <= date_add('week', -1, cal.first_week_date)) as "Total complementary classes attended 2wk",
        SUM(fwp.cc_attended_4wk) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date)) as "Total complementary classes attended 4wk",
        SUM(fwp.sc_attended_1wk) FILTER (WHERE fws.start_date <= cal.first_week_date)  as "Total social clubs attended 1wk",
        SUM(fwp.sc_attended_2wk) FILTER (WHERE fws.start_date <= date_add('week', -1, cal.first_week_date)) as "Total social clubs attended 2wk",
        SUM(fwp.sc_attended_4wk) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date)) as "Total social clubs attended 4wk",
        SUM(fwp.duration_mm_1wk) FILTER (WHERE fws.start_date <= cal.first_week_date)  as "Total time studied MM 1wk",
        SUM(fwp.duration_mm_2wk) FILTER (WHERE fws.start_date <= date_add('week', -1, cal.first_week_date)) as "Total time studied MM 2wk",
        SUM(fwp.duration_mm_4wk) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date)) as "Total time studied MM 4wk",
        SUM(fwp.lvls_started_1wk) as "Levels started 1wk",
        SUM(fwp.lvls_started_first_1wk) as "Levels started first 1wk",
        SUM(fwp.lvls_started_later_1wk) as "Levels started later 1wk",
        SUM(fwp.lvls_started_2wk) as "Levels started 2wk",
        SUM(fwp.lvls_started_first_2wk) as "Levels started first 2wk",
        SUM(fwp.lvls_started_later_2wk) as "Levels started later 2wk",
        SUM(fwp.lvls_started_4wk) as "Levels started 4wk",
        SUM(fwp.lvls_started_first_4wk) as "Levels started first 4wk",
        SUM(fwp.lvls_started_later_4wk) as "Levels started later 4wk",
        SUM(fwp.duration_mm_1wk) FILTER (WHERE fws.start_date <= cal.first_week_date) AS total_duration_mm_1wk,
        SUM(fwp.duration_wb_1wk) FILTER (WHERE fws.start_date <= cal.first_week_date) AS total_duration_wb_1wk,
        SUM(fwp.duration_1wk) FILTER (WHERE fws.start_date <= cal.first_week_date) AS total_duration_1wk,
        SUM(fwp.lessons_complete_1wk) FILTER (WHERE fws.start_date <= cal.first_week_date) AS total_lessons_complete_1wk,
        SUM(fwp.duration_mm_2wk) FILTER (WHERE fws.start_date <= date_add('week', -1, cal.first_week_date)) AS total_duration_mm_2wk,
        SUM(fwp.duration_wb_2wk) FILTER (WHERE fws.start_date <= date_add('week', -1, cal.first_week_date)) AS total_duration_wb_2wk,
        SUM(fwp.duration_2wk) FILTER (WHERE fws.start_date <= date_add('week', -1, cal.first_week_date)) AS total_duration_2wk,
        SUM(fwp.lessons_complete_1wk) FILTER (WHERE fws.start_date <= date_add('week', -1, cal.first_week_date)) AS total_lessons_complete_2wk,
        SUM(fwp.duration_mm_4wk) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date)) AS total_duration_mm_4wk,
        SUM(fwp.duration_wb_4wk) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date)) AS total_duration_wb_4wk,
        SUM(fwp.duration_4wk) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date)) AS total_duration_4wk,
        SUM(fwp.lessons_complete_1wk) FILTER (WHERE fws.start_date <= date_add('week', -3, cal.first_week_date)) AS total_lessons_complete_4wk,
        SUM(fwp.duration_mm_8wk) FILTER (WHERE fws.start_date <= date_add('week', -7, cal.first_week_date)) AS total_duration_mm_8wk,
        SUM(fwp.duration_wb_8wk) FILTER (WHERE fws.start_date <= date_add('week', -7, cal.first_week_date)) AS total_duration_wb_8wk,
        SUM(fwp.duration_8wk) FILTER (WHERE fws.start_date <= date_add('week', -7, cal.first_week_date)) AS total_duration_8wk,
        SUM(fwp.lessons_complete_1wk) FILTER (WHERE fws.start_date <= date_add('week', -7, cal.first_week_date)) AS total_lessons_complete_8wk,
        TO_HEX(SHA256(cast(
            cast(cal.first_week_date as varchar)
            || cast(fws.group_id as varchar)
            || cast(fws.center_reference_id as varchar)
            || cast(fws.contract_type as varchar)
            || cast(fws.location as varchar)
            || cast(fws.class_access_type as varchar)
            || cast(fws.service_type as varchar)
            || cast(fws.is_membership as varchar)
            || cast(fws.is_teen as varchar)
            || cast(fws.is_promotional as varchar)
            || cast(fws.study_plan_type as varchar)
            || cast(coalesce(fws.bookmark_mm_level ,'No Level') as varchar)
            || cast(fws.contract_inclusions  as varchar)
            || cast(CASE
            WHEN fws.valid_current_date is null THEN 'Not Valid' 
            when fws.first_contract = true and date_add('week',4,fws.start_date) >= fws.first_week_date 
            then 'Onboarding'
            when fws.first_contract = false and date_add('week',4,fws.start_date) >= fws.first_week_date
            then 'Reactivated'
            WHEN fwp.student_id IS NULL or fwp.sessions_4wk is null THEN 'Inactive'
            WHEN fwp.segment = -1 then 'Inactive' 
            WHEN fwp.segment = 1 then  'Infrequent Short'
            WHEN fwp.segment = 2 then  'Infrequent Long'
            WHEN fwp.segment = 3 then  'Frequent Very Short'
            WHEN fwp.segment = 4 then  'Frequent Short'
            WHEN fwp.segment = 5 then  'Frequent Long' 
        end as varchar)
            || cast(CASE
            WHEN fws_last.valid_current_date is null THEN 'Not Valid'  
            when fws_last.first_contract = true and date_add('week',4,fws_last.start_date) >= fws_last.first_week_date
            then 'Onboarding'
            when fws_last.first_contract = false and date_add('week',4,fws_last.start_date) >= fws_last.first_week_date
            then 'Reactivated'
            WHEN fwp_last.student_id IS NULL or fwp_last.sessions_4wk is null THEN 'Inactive'
            WHEN fwp_last.segment = -1 then 'Inactive' 
            WHEN fwp_last.segment = 1 then  'Infrequent Short'
            WHEN fwp_last.segment = 2 then  'Infrequent Long'
            WHEN fwp_last.segment = 3 then  'Frequent Very Short'
            WHEN fwp_last.segment = 4 then  'Frequent Short'
            WHEN fwp_last.segment = 5 then  'Frequent Long' 
        end as varchar)
            as varbinary))) as dbt_unique_id
    ,CAST(current_timestamp AS TIMESTAMP(6)) AS load_date
    FROM calendar cal
    left join {{ ref('fact_wk_students') }} fws on cal.first_week_date = fws.first_week_date 
    left join {{ ref('fact_wk_progress') }} fwp on fws.first_week_date = fwp.first_week_date and fws.student_reference_id = fwp.student_id
    left join {{ ref('fact_wk_progress') }} fwp_last on fwp_last.first_week_date = date_add('week', -4, fws.first_week_date) and fws.student_reference_id = fwp_last.student_id
    left join {{ ref('fact_wk_students') }} fws_last on fws_last.first_week_date = date_add('week', -4, fws.first_week_date) and fws.student_reference_id = fws_last.student_reference_id and fws_last.product_type = fws.product_type
    WHERE 
    fws.product_type = 'core course'
    group by
    cal.first_week_date,
    fws.group_id,
    fws.center_reference_id,
    fws.contract_type,
    fws.study_plan_type,
    fws.bookmark_mm_level,
    fws.location,
    fws.class_access_type,
    fws.service_type,
    fws.is_membership,
    fws.is_teen,
    fws.is_promotional,
    fws.contract_inclusions,
    cast(CASE WHEN fws.valid_current_date is null THEN 'Not Valid' 
            when fws.first_contract = true and date_add('week',4,fws.start_date) >= fws.first_week_date 
            then 'Onboarding'
            when fws.first_contract = false and date_add('week',4,fws.start_date) >= fws.first_week_date
            then 'Reactivated'
            WHEN fwp.student_id IS NULL or fwp.sessions_4wk is null THEN 'Inactive'
            WHEN fwp.segment = -1 then 'Inactive' 
            WHEN fwp.segment = 1 then  'Infrequent Short'
            WHEN fwp.segment = 2 then  'Infrequent Long'
            WHEN fwp.segment = 3 then  'Frequent Very Short'
            WHEN fwp.segment = 4 then  'Frequent Short'
            WHEN fwp.segment = 5 then  'Frequent Long' 
        end as varchar),
    cast(CASE
            WHEN fws_last.valid_current_date is null THEN 'Not Valid'  
            when fws_last.first_contract = true and date_add('week',4,fws_last.start_date) >= fws_last.first_week_date
            then 'Onboarding'
            when fws_last.first_contract = false and date_add('week',4,fws_last.start_date) >= fws_last.first_week_date
            then 'Reactivated'
            WHEN fwp_last.student_id IS NULL or fwp_last.sessions_4wk is null THEN 'Inactive'
            WHEN fwp_last.segment = -1 then 'Inactive' 
            WHEN fwp_last.segment = 1 then  'Infrequent Short'
            WHEN fwp_last.segment = 2 then  'Infrequent Long'
            WHEN fwp_last.segment = 3 then  'Frequent Very Short'
            WHEN fwp_last.segment = 4 then  'Frequent Short'
            WHEN fwp_last.segment = 5 then  'Frequent Long' 
        end as varchar) 