{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet',
) }}

WITH base_data AS (
  SELECT
      *,
      ROW_NUMBER() OVER (PARTITION BY user_id, class_id ORDER BY event_date, event_timestamp) AS rn
  FROM {{ source('bigquery', 'camera_usage_events') }}
),
 
class_group AS (
  SELECT
      event_date,
      event_timestamp,
      event_name,
      user_id,
      user_level,
      user_origin_level,
      user_center,
      user_role,
      class_id,
      class_access,
      camera_usage_time,
      SUM(CASE WHEN class_id IS NOT NULL THEN 1 ELSE 0 END)
          OVER (PARTITION BY user_id, event_date ORDER BY event_date, event_timestamp) AS class_group
  FROM base_data
  WHERE rn = 1
),
 
filled_class_ids AS (
  SELECT
      *,
      CASE
        WHEN class_id IS NOT NULL THEN class_id
        WHEN event_timestamp - MIN(event_timestamp)
             OVER (PARTITION BY user_id, event_date, class_group ORDER BY event_timestamp)
             <= 3800000000
        THEN MAX(class_id)
             OVER (PARTITION BY user_id, event_date, class_group ORDER BY event_timestamp)
        ELSE NULL
      END AS final_class_id
  FROM class_group
),
 
user_event_information AS (
  SELECT
      event_date,
      user_id,
      user_role,
      final_class_id AS class_id,
      SUM(camera_usage_time) AS total_camera_usage_time,
      CASE WHEN SUM(camera_usage_time) > 0 THEN 1 ELSE 0 END AS user_turned_camera,
      CASE WHEN SUM(COALESCE(camera_usage_time, 0)) = 0 THEN 1 ELSE 0 END AS user_not_turned_camera,
      COUNT(final_class_id) AS events,
      MIN(event_timestamp) AS start_class_timestamp,
      MAX(event_timestamp) AS last_event_of_the_class
  FROM filled_class_ids
  GROUP BY
      event_date,
      user_id,
      user_role,
      final_class_id
),
-- Extract teacher record (composite key: teacher's user_id, event_date, class_id)
teacher_data AS (
  SELECT 
    event_date,
    user_id,
    user_role,
    class_id,
    total_camera_usage_time/1000/60 AS teacher_total_camera_usage_time,
    events AS teacher_events,
    start_class_timestamp AS teacher_start_class_timestamp,
    last_event_of_the_class AS teacher_last_event_of_the_class
  FROM user_event_information
  WHERE user_role = 'teacher'
),
 
-- Aggregate student rows for the same event_date and class_id
student_data AS (
  SELECT 
    event_date,
    class_id,
    SUM(total_camera_usage_time)/1000/60 AS students_total_camera_usage_time,
    MAX(total_camera_usage_time)/1000/60 AS student_max_camera_usage_time,
    SUM(CASE WHEN user_turned_camera = 1 THEN user_turned_camera ELSE 0 END) AS user_turned_camera,
    SUM(CASE WHEN user_not_turned_camera = 1 THEN user_not_turned_camera ELSE 0 END) AS user_not_turned_camera,
    MAX(events) AS events,  -- Assuming all student rows share the same event value
    MIN(start_class_timestamp) AS start_class_timestamp,
    MAX(last_event_of_the_class) AS last_event_of_the_class
  FROM user_event_information
  WHERE user_role = 'student'
  GROUP BY event_date, class_id
),
final_data as(
SELECT
    cb.class_id,
    cb.class_teacher_user_reference_id,
    cb.class_type,
    max(date(cb.class_local_start_datetime ))                 AS class_date,
    COUNT(DISTINCT cb.student_reference_id) FILTER (WHERE cb.attended_flag = true) AS attendees,
    max(cb.class_number_of_seats)                                AS capacity,
    CASE 
      WHEN GREATEST(t.teacher_total_camera_usage_time,s.student_max_camera_usage_time) 
      > date_diff('minute', c.class_local_start_datetime, c.class_local_end_datetime) 
        THEN  date_diff('minute', c.class_local_start_datetime, c.class_local_end_datetime) 
      WHEN GREATEST(t.teacher_total_camera_usage_time,s.student_max_camera_usage_time) 
      < date_diff('minute', c.class_local_start_datetime, c.class_local_end_datetime) 
        THEN  GREATEST(t.teacher_total_camera_usage_time,s.student_max_camera_usage_time) 
        END AS class_duration,
    teacher_total_camera_usage_time,
    s.students_total_camera_usage_time,
    s.student_max_camera_usage_time,
    s.user_turned_camera,
    s.user_not_turned_camera,
    COALESCE(s.events, t.teacher_events) AS events,
    s.start_class_timestamp,
    s.last_event_of_the_class
FROM teacher_data t
LEFT JOIN student_data s
    ON t.event_date = s.event_date
    AND t.class_id = s.class_id
LEFT JOIN {{ ref('class_bookings') }} cb
    ON t.class_id = cb.class_id
LEFT JOIN {{ ref('classes') }} c 
    ON t.class_id = c.class_id
where cb.class_local_start_datetime >= TIMESTAMP '2025-02-19 00:00:00'  -- limit to bring class information alone after the tracking started
    AND cb.class_cancelled_flag      = FALSE 
    AND cb.booking_cancelled_flag    = FALSE 
    AND cb.class_type_billable       = TRUE
    AND cb.class_online_flag         = TRUE
  GROUP BY
      cb.class_type,
      cb.class_id,
      CASE 
      WHEN GREATEST(t.teacher_total_camera_usage_time,s.student_max_camera_usage_time) 
      > date_diff('minute', c.class_local_start_datetime, c.class_local_end_datetime) 
        THEN  date_diff('minute', c.class_local_start_datetime, c.class_local_end_datetime) 
      WHEN GREATEST(t.teacher_total_camera_usage_time,s.student_max_camera_usage_time) 
      < date_diff('minute', c.class_local_start_datetime, c.class_local_end_datetime) 
        THEN  GREATEST(t.teacher_total_camera_usage_time,s.student_max_camera_usage_time) 
        END,
      cb.class_teacher_user_reference_id,
      teacher_total_camera_usage_time,
     s.students_total_camera_usage_time,
     s.student_max_camera_usage_time,
     s.user_turned_camera,
     s.user_not_turned_camera,
    COALESCE(s.events, t.teacher_events),
    s.start_class_timestamp,
    s.last_event_of_the_class
)

SELECT *
FROM final_data