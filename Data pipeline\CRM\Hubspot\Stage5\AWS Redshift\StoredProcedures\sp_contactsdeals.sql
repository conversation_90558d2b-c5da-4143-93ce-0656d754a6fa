CREATE OR REPLACE PROCEDURE hubspot_crm.sp_contactsdeals()
	LANGUAGE plpgsql
AS $$


BEGIN

TRUNCATE table hubspot_crm.contactsdeals;


INSERT INTO hubspot_crm.contactsdeals
select
    contactid                        ,
    contract_date                    ,
    lead_date                        ,
    mql_date                         ,
    booked_date                      ,
    showed_date                      ,
    center_name                      ,
    hubspot_owner_id                 ,
    individual_corporate             ,
    source                           ,
    sub_source_group                 ,
     case when channel is NULL then '' else channel end as channel,
    channel_drill_down_1             ,
    channel_drill_down_2             ,
    landing_page                     ,
    page,
    campaign                        ,
    useful_contacted_Date              ,
    tmk_owner                        ,
    course_age_group                 ,
    dealid                           ,
    territory_name                   ,
    sales                            ,
    refund_date,
    refunded_amount,
    hubspot_team_iddeal                ,
    dealrows                         ,
        amount_gifts                 ,
    AmountInterest                  ,
    core_course_amount               ,
    core_course_levels               ,
    core_course_7_class_access_type  ,
    market_leader_1_amount           ,
    market_leader_2_hours            ,
    test_prep_group_1_amount         ,
    test_prep_group_2_quantity       ,
    business_partner_1_amount        ,
    certifications_1_amount          ,
    certifications_2_quantity        ,
    core_course_fit_2_hours          ,
    core_course_fit_amount           ,
    core_course_online_1_amount      ,
    ilc_1_amount                     ,
    test_prep_executive_1_amount     ,
    test_prep_executive_2_quantity   ,
    business_partner_2_hours         ,
    ilc_2_hours                      ,
    business_partner_5_start_level   ,
    business_partner_6_end_level     ,
    market_leader_5_start_level      ,
    market_leader_6_end_level        ,
    pe_end_level                     ,
    pe_start_level                   ,
    core_course_fit_5_start_level    ,
    core_course_fit_6_end_level      ,
    core_course_online_2_levels      ,
    core_course_online_5_start_level ,
    core_course_online_6_end_level,
    test_prep_units   ,
    FA_Levels         ,
    FA_amount         ,
    FA2_Levels         ,
    FA2_amount         ,
    FA3_Levels         ,
    FA3_amount         ,
        FA4_Levels         ,
    FA4_amount         ,
        FAM_Levels         ,
    FAM_amount         ,
IC_Levels        ,
    IC_amount         ,
        IC2_Levels      ,
    IC2_amount         ,
    IC3_Levels       ,
    IC3_amount        ,
        IC4_Levels         ,
    IC4_amount         ,
            ICM_Levels   ,
    ICM_amount


    from
 (
SELECT  CET.hs_object_id                                                                                      AS contactid

            , CASE
                  WHEN DET.territory_code <> 'IT' AND "Deal Stage" = 'closedwon' THEN DET."Deal Close Date"
                  WHEN DET.territory_code = 'IT' AND "Deal Stage" = 'contractsent' THEN DET."Deal Close Date" END
                                                                                                                    AS contract_date

            , CASE
                                       WHEN CET.territory_code IN ('IT') THEN CASE
                                                             WHEN CET.hs_analytics_source IN
                                                                  ('PAID_SEARCH', 'PAID_SOCIAL', 'EMAIL_MARKETING',
                                                                   'OTHER_WEB', 'ORGANIC_SEARCH')
                                                                 THEN CET.recent_conversion_date
                                                             WHEN CET.hs_lifecyclestage_lead_date IS NOT NULL
                                                                 THEN CET.hs_lifecyclestage_lead_date
                                                             ELSE CET.createdate END
                  WHEN CET.territory_code IN ('CH') AND hs_lifecyclestage_salesqualifiedlead_date IS NOT NULL
                      THEN CAST(CET.Lead_Date AS DATE)
                  WHEN CET.territory_code IN ('CH') AND hs_lifecyclestage_salesqualifiedlead_date IS NULL
                      THEN NULL
                  WHEN CET.territory_code IN ('FR', 'PE', 'MN', 'CO', 'KR') THEN CAST(CET.Lead_Date AS DATE)
                  ELSE CAST(CET.hs_lifecyclestage_lead_date AS DATE) END
                                                                                                                    AS lead_date
            , CASE
                WHEN CET.territory_code IN ('IT') THEN CASE
                                                             WHEN CET.hs_analytics_source IN
                                                                  ('PAID_SEARCH', 'PAID_SOCIAL', 'EMAIL_MARKETING',
                                                                   'OTHER_WEB', 'ORGANIC_SEARCH')
                                                                 THEN CET.recent_conversion_date
                                                             WHEN CET.hs_lifecyclestage_lead_date IS NOT NULL
                                                                 THEN CET.hs_lifecyclestage_lead_date
                                                             ELSE CET.createdate END
                  WHEN CET.territory_code IN ('CH') AND hs_lifecyclestage_salesqualifiedlead_date IS NOT NULL
                      THEN CAST(CET.MQL_Date AS DATE)
                  WHEN CET.territory_code IN ('CH') AND hs_lifecyclestage_salesqualifiedlead_date IS NULL
                      THEN NULL
                  WHEN CET.territory_code IN ('FR', 'PE', 'MN', 'CO', 'KR') THEN CAST(CET.MQL_Date AS DATE)
                  ELSE CAST(CET.hs_lifecyclestage_marketingqualifiedlead_date AS DATE) END
                                                                                                                    AS mql_date

            , CASE
                  WHEN CET.territory_code IN ('CH', 'FR', 'PE', 'MN', 'CO', 'KR')
                      THEN CAST(CET.Booked_Date AS DATE)
                  ELSE CAST(CET.hs_lifecyclestage_salesqualifiedlead_date AS DATE) END
                                                                                                                    AS booked_date


            , CAST(DET."Deal Create Date" AS DATE)                                                                  AS showed_date

            , CASE

                  WHEN (DET."Center Name" = '') AND (CET.center_name = '') THEN 'No Center'
                  WHEN (DET."Center Name" IS NULL AND CET.center_name IS NULL) THEN 'No Center'
                  WHEN (DET."Center Name" IS NULL AND CET.center_name = '') THEN 'No Center'
                  WHEN (DET."Center Name" = '' AND CET.center_name IS NULL) THEN 'No Center'
                  WHEN (DET."Center Name" = '') THEN CET.center_name
                  WHEN DET."Center Name" IS NULL THEN CET.center_name
                  WHEN CET.center_name IS NULL THEN DET."Center Name"
                  ELSE DET."Center Name"
    END                                                                                                             AS center_name
            , CASE
                  WHEN (CET.hubspot_owner_id is NULL and DET.hubspot_owner_id is NULL) then 0
                  WHEN (DET.hubspot_owner_id IS NULL) THEN CET.hubspot_owner_id
                  WHEN CET.hubspot_owner_id IS NULL THEN DET.hubspot_owner_id
                ELSE DET.hubspot_owner_id

                   END                                                                     AS hubspot_owner_id

            , CASE
                  WHEN (DET.individual_corporate IS NULL AND CET.individual_corporate IS NULL) THEN ''
                  WHEN (DET.individual_corporate IS NULL AND CET.individual_corporate = '') THEN ''
                  WHEN (DET.individual_corporate = '' AND CET.individual_corporate IS NULL) THEN ''
                  WHEN (DET.individual_corporate = '' AND CET.individual_corporate = '') THEN ''
                  WHEN DET.individual_corporate IS NULL THEN CET.individual_corporate
                  WHEN DET.individual_corporate = '' THEN CET.individual_corporate
                  WHEN CET.individual_corporate IS NULL THEN DET.individual_corporate
                  ELSE DET.individual_corporate END                                                                 AS individual_corporate
            , CASE
                  WHEN (DET.Source = '') AND (CET.Source = '') THEN 'No Source'
                  WHEN (CET.source IS NULL AND DET.source IS NULL) THEN 'No Source'
                  WHEN (DET.source IS NULL AND CET.Source = '') THEN 'No Source'
                  WHEN (CET.source IS NULL AND DET.Source = '') THEN 'No Source'
                  WHEN DET.Source = '' THEN CET.Source
                  WHEN (DET.Source IS NULL) THEN CET.source
                  WHEN CET.source IS NULL THEN DET.source
                  ELSE DET.source END                                                                               AS Source
     , CASE
           WHEN CET.territory_code IN ('IT') THEN CASE
                                                      WHEN CET.sub_source IN ('Test', 'Test Esercizi', 'Test Ielts', 'Test Toefl')
                                                          THEN 'Test'
                                                      ELSE 'Quality' END
           ELSE '' END AS sub_source_group
            , CASE
                  WHEN (CASE
                            WHEN (DET.Source = '') AND (CET.Source = '') THEN 'No Source'
                            WHEN (CET.source IS NULL AND DET.source IS NULL) THEN 'No Source'
                            WHEN (DET.source IS NULL AND CET.Source = '') THEN 'No Source'
                            WHEN (CET.source IS NULL AND DET.Source = '') THEN 'No Source'
                            WHEN DET.Source = '' THEN CET.Source
                            WHEN (DET.Source IS NULL) THEN CET.source
                            WHEN CET.source IS NULL THEN DET.source
                            ELSE DET.source END) IN ('Renewals', 'Renewal') THEN ''
                  WHEN (CASE
                            WHEN (DET.Source = '') AND (CET.Source = '') THEN 'No Source'
                            WHEN (CET.source IS NULL AND DET.source IS NULL) THEN 'No Source'
                            WHEN (DET.source IS NULL AND CET.Source = '') THEN 'No Source'
                            WHEN (CET.source IS NULL AND DET.Source = '') THEN 'No Source'
                            WHEN DET.Source = '' THEN CET.Source
                            WHEN (DET.Source IS NULL) THEN CET.source
                            WHEN CET.source IS NULL THEN DET.source
                            ELSE DET.source END) IN ('Call In', 'Walk In') THEN CET.how_did_you_hear_about_us
                  WHEN (CASE
                            WHEN (DET.Source = '') AND (CET.Source = '') THEN 'No Source'
                            WHEN (CET.source IS NULL AND DET.source IS NULL) THEN 'No Source'
                            WHEN (DET.source IS NULL AND CET.Source = '') THEN 'No Source'
                            WHEN (CET.source IS NULL AND DET.Source = '') THEN 'No Source'
                            WHEN DET.Source = '' THEN CET.Source
                            WHEN (DET.Source IS NULL) THEN CET.source
                            WHEN CET.source IS NULL THEN DET.source
                            ELSE DET.source END) IN ('Outbound Call') THEN call_campaign
                  WHEN (CASE
                            WHEN (DET.Source = '') AND (CET.Source = '') THEN 'No Source'
                            WHEN (CET.source IS NULL AND DET.source IS NULL) THEN 'No Source'
                            WHEN (DET.source IS NULL AND CET.Source = '') THEN 'No Source'
                            WHEN (CET.source IS NULL AND DET.Source = '') THEN 'No Source'
                            WHEN DET.Source = '' THEN CET.Source
                            WHEN (DET.Source IS NULL) THEN CET.source
                            WHEN CET.source IS NULL THEN DET.source
                            ELSE DET.source END) IN ('Web') THEN CET.hs_analytics_source
                  WHEN (CASE
                            WHEN (DET.Source = '') AND (CET.Source = '') THEN 'No Source'
                            WHEN (CET.source IS NULL AND DET.source IS NULL) THEN 'No Source'
                            WHEN (DET.source IS NULL AND CET.Source = '') THEN 'No Source'
                            WHEN (CET.source IS NULL AND DET.Source = '') THEN 'No Source'
                            WHEN DET.Source = '' THEN CET.Source
                            WHEN (DET.Source IS NULL) THEN CET.source
                            WHEN CET.source IS NULL THEN DET.source
                            ELSE DET.source END) IN ('Promoters', 'Events') THEN CET.sub_source
                  WHEN (CASE
                            WHEN (DET.Source = '') AND (CET.Source = '') THEN 'No Source'
                            WHEN (CET.source IS NULL AND DET.source IS NULL) THEN 'No Source'
                            WHEN (DET.source IS NULL AND CET.Source = '') THEN 'No Source'
                            WHEN (CET.source IS NULL AND DET.Source = '') THEN 'No Source'
                            WHEN DET.Source = '' THEN CET.Source
                            WHEN (DET.Source IS NULL) THEN CET.source
                            WHEN CET.source IS NULL THEN DET.source
                            ELSE DET.source END) NOT IN
                       ('Promoters', 'Events', 'Web', 'Outbound Call', 'Renewals', 'Renewal', 'Call In', 'Walk In')
                      THEN ''
    END                                                                                                             AS channel

            , CASE
                  WHEN (
                              CET.hs_analytics_source_data_1 = '' OR CET.hs_analytics_source_data_1 IS NULL) THEN ''
                  ELSE CET.hs_analytics_source_data_1 END                                                           AS channel_drill_down_1

            , CASE
                  WHEN (
                              CET.hs_analytics_source_data_2 = '' OR CET.hs_analytics_source_data_2 IS NULL) THEN ''
                  ELSE hs_analytics_source_data_2 END                                                               AS channel_drill_down_2
            , CASE
                  WHEN (CET.first_conversion_event_name IS NOT NULL OR CET.first_conversion_event_name <> '') THEN
                      CASE
                          WHEN POSITION(': ' IN first_conversion_event_name) > 0 THEN
                              SUBSTRING(first_conversion_event_name,1, POSITION(': ' IN first_conversion_event_name) - 1)
                          ELSE '' END
                  ELSE '' END
                                                                                                                    AS landing_page
     ,CASE
        WHEN POSITION('?' IN hs_analytics_first_url) > 0
        THEN SUBSTRING(hs_analytics_first_url FROM 1 FOR POSITION('?' IN hs_analytics_first_url) - 1)
        ELSE '' END as page
            , CASE
                  WHEN ((CET.hs_analytics_source <> '' OR CET.hs_analytics_source IS NOT NULL) AND
                        CET.hs_analytics_source in ('PAID_SOCIAL','SOCIAL_MEDIA')) THEN hs_analytics_source_data_2
                  WHEN ((CET.hs_analytics_source <> '' OR CET.hs_analytics_source IS NOT NULL) AND
                        CET.hs_analytics_source = 'PAID_SEARCH') THEN hs_analytics_source_data_1
                  ELSE ''
    END                                                                                                             AS campaign

            , CASE
                  WHEN CET.territory_code IN ('IT') THEN
                      CASE
                          WHEN (CASE
                  WHEN (DET.Source = '') AND (CET.Source = '') THEN 'No Source'
                  WHEN (CET.source IS NULL AND DET.source IS NULL) THEN 'No Source'
                  WHEN (DET.source IS NULL AND CET.Source = '') THEN 'No Source'
                  WHEN (CET.source IS NULL AND DET.Source = '') THEN 'No Source'
                  WHEN DET.Source = '' THEN CET.Source
                  WHEN (DET.Source IS NULL) THEN CET.source
                  WHEN CET.source IS NULL THEN DET.source
                  ELSE DET.source END) = 'Web Inbound' THEN CET.useful_contact_date
                          WHEN (CASE
                  WHEN (DET.Source = '') AND (CET.Source = '') THEN 'No Source'
                  WHEN (CET.source IS NULL AND DET.source IS NULL) THEN 'No Source'
                  WHEN (DET.source IS NULL AND CET.Source = '') THEN 'No Source'
                  WHEN (CET.source IS NULL AND DET.Source = '') THEN 'No Source'
                  WHEN DET.Source = '' THEN CET.Source
                  WHEN (DET.Source IS NULL) THEN CET.source
                  WHEN CET.source IS NULL THEN DET.source
                  ELSE DET.source END) = 'Renewal' THEN DET."Deal Create Date"
                          WHEN (CASE
                  WHEN (DET.Source = '') AND (CET.Source = '') THEN 'No Source'
                  WHEN (CET.source IS NULL AND DET.source IS NULL) THEN 'No Source'
                  WHEN (DET.source IS NULL AND CET.Source = '') THEN 'No Source'
                  WHEN (CET.source IS NULL AND DET.Source = '') THEN 'No Source'
                  WHEN DET.Source = '' THEN CET.Source
                  WHEN (DET.Source IS NULL) THEN CET.source
                  WHEN CET.source IS NULL THEN DET.source
                  ELSE DET.source END) NOT IN ('Credit Note', 'Transfer In', 'Transfer Out', 'Renewal', 'Web Inbound',
                                              'Existing Customer') THEN CASE
                                                                            WHEN CET.territory_code IN ('IT') THEN CASE
                                                                                                                       WHEN CET.hs_analytics_source IN
                                                                                                                            ('PAID_SEARCH',
                                                                                                                             'PAID_SOCIAL',
                                                                                                                             'EMAIL_MARKETING',
                                                                                                                             'OTHER_WEB',
                                                                                                                             'ORGANIC_SEARCH')
                                                                                                                           THEN CET.recent_conversion_date
                                                                                                                       WHEN CET.hs_lifecyclestage_lead_date IS NOT NULL
                                                                                                                           THEN CET.hs_lifecyclestage_lead_date
                                                                                                                       ELSE CET.createdate END
                              END
                          else NULL END
                ELSE CAST(CET.Useful_Contact_Date AS DATE) end AS useful_contacted_date
            , CET.TMK_Owner                                                                                         AS tmk_owner


            , CASE
                  WHEN (CET.course_age_group IS NULL OR CET.course_age_group = '') THEN ''
                  ELSE CET.course_age_group END                                                                     AS course_age_group

            , DET."Deal Id"                                                                                         AS dealid

--      , DET.DealName

            , CASE
                  WHEN (CET.territory_name = '' OR CET.territory_name IS NULL) THEN DET.territory_name
                  ELSE CET.territory_name END                                                                       AS territory_name
--                   , DET.Amount
            , CASE
                  WHEN "Deal Stage" = 'closedwon' AND DET.territory_name <> 'Italy' THEN amount
                  WHEN "Deal Stage" = 'contractsent' AND DET.territory_name = 'Italy'
                      THEN amount END                                                                               AS Sales
            , null as refund_date
            ,null as refunded_amount

            , DET.HubSpot_Team_Id                                                                                   AS hubspot_team_iddeal
            , CASE
                  WHEN DET."Deal Id" IS NOT NULL
                      THEN ROW_NUMBER() OVER (PARTITION BY DET."Deal Id" ORDER BY CET.CreateDate) END               AS DealRows
            , CASE
                  WHEN "Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' THEN amount_gifts
                  WHEN "Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy'
                      THEN amount_gifts END                                                                         AS amount_gifts
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy') THEN DET.Amount_Interest
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy')
                      THEN DET.amount_interest END                                                                  AS AmountInterest
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy') THEN cast(DET.Core_Course_Amount as DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy')
                      THEN cast(DET.Core_Course_Amount as DOUBLE PRECISION) END                                                               AS core_course_amount

            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('FA2','FA3','FA4','FAM','ICM','IC2','IC3','IC4')) THEN cast(DET.Core_Course_Levels as bigint)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('FA2','FA3','FA4','FAM','ICM','IC2','IC3','IC4'))
                      THEN cast(DET.Core_Course_Levels as bigint) else NULL END                                                               AS core_course_levels

            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy')
                      THEN DET.Core_course_7_class_access_type
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy')
                      THEN DET.core_course_7_class_access_type
    END                                                                                                             AS core_course_7_class_access_type

            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND
                        DET.Market_leader_1_amount <> '')
                      THEN CAST(DET.Market_leader_1_amount AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND
                        DET.Market_leader_1_amount <> '')
                      THEN CAST(DET.Market_leader_1_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS Market_leader_1_amount

            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND
                        DET.market_leader_2_hours <> '')
                      THEN CAST(DET.Market_Leader_2_Hours AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND
                        DET.market_leader_2_hours <> '')
                      THEN CAST(DET.Market_Leader_2_Hours AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS Market_Leader_2_Hours

            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND
                        DET.Test_Prep_Group_1_Amount <> '')
                      THEN CAST(DET.Test_Prep_Group_1_Amount AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND
                        DET.Test_Prep_Group_1_Amount <> '')
                      THEN CAST(DET.Test_Prep_Group_1_Amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS Test_Prep_Group_1_Amount

            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND
                        DET.Test_Prep_Group_2_Quantity <> '')
                      THEN CAST(DET.Test_Prep_Group_2_Quantity AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND
                        DET.Test_Prep_Group_2_Quantity <> '')
                      THEN CAST(DET.Test_Prep_Group_2_Quantity AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS Test_Prep_Group_2_Quantity
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND
                        DET.business_partner_1_amount IS NOT NULL)
                      THEN CAST(DET.business_partner_1_amount AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND
                        DET.business_partner_1_amount IS NOT NULL)
                      THEN CAST(DET.business_partner_1_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS business_partner_1_amount
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND
                        DET.certifications_1_amount IS NOT NULL)
                      THEN CAST(DET.certifications_1_amount AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND
                        DET.certifications_1_amount IS NOT NULL)
                      THEN CAST(DET.certifications_1_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS certifications_1_amount
    ,CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND
                        DET.certifications_2_quantity IS NOT NULL)
                      THEN CAST(DET.certifications_2_quantity AS bigint)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND
                        DET.certifications_2_quantity IS NOT NULL)
                      THEN CAST(DET.certifications_2_quantity AS bigint)
                  ELSE NULL END                                                                                     AS certifications_2_quantity
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND
                        DET.core_course_fit_2_hours <> '')
                      THEN CAST(DET.core_course_fit_2_hours AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND
                        DET.core_course_fit_2_hours <> '')
                      THEN CAST(DET.core_course_fit_2_hours AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS core_course_fit_2_hours
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND
                        DET.core_course_fit_amount IS NOT NULL)
                      THEN CAST(DET.core_course_fit_amount AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND
                        DET.core_course_fit_amount IS NOT NULL)
                      THEN CAST(DET.core_course_fit_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS core_course_fit_amount
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND
                        DET.core_course_online_1_amount IS NOT NULL)
                      THEN CAST(DET.core_course_online_1_amount AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND
                        DET.core_course_online_1_amount IS NOT NULL)
                      THEN CAST(DET.core_course_online_1_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS core_course_online_1_amount
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND DET.ilc_1_amount IS NOT NULL)
                      THEN CAST(DET.ilc_1_amount AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND DET.ilc_1_amount IS NOT NULL)
                      THEN CAST(DET.ilc_1_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS ilc_1_amount
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND
                        DET.test_prep_executive_1_amount IS NOT NULL)
                      THEN CAST(DET.test_prep_executive_1_amount AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND
                        DET.test_prep_executive_1_amount IS NOT NULL)
                      THEN CAST(DET.test_prep_executive_1_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS test_prep_executive_1_amount
     ,CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND
                        DET.test_prep_executive_2_quantity<> '')
                      THEN CAST(DET.test_prep_executive_2_quantity AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND
                        DET.test_prep_executive_2_quantity<>'')
                      THEN CAST(DET.test_prep_executive_2_quantity AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS test_prep_executive_2_quantity
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND
                        DET.business_partner_2_hours IS NOT NULL)
                      THEN CAST(DET.business_partner_2_hours AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND
                        DET.business_partner_2_hours IS NOT NULL)
                      THEN CAST(DET.business_partner_2_hours AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS business_partner_2_hours
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' AND DET.ilc_2_hours <> '')
                      THEN CAST(DET.ilc_2_hours AS DOUBLE PRECISION)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' AND DET.ilc_2_hours <> '')
                      THEN CAST(DET.ilc_2_hours AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS ilc_2_hours
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy')
                      THEN DET.business_partner_5_start_level
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy')
                      THEN DET.business_partner_5_start_level END                                                   AS business_partner_5_start_level
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy')
                      THEN DET.business_partner_6_end_level
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy')
                      THEN DET.business_partner_6_end_level END                                                     AS business_partner_6_end_level
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy')
                      THEN DET.market_leader_5_start_level
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy')
                      THEN DET.market_leader_5_start_level END                                                      AS market_leader_5_start_level
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy') THEN DET.market_leader_6_end_level
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy')
                      THEN DET.market_leader_6_end_level END                                                        AS market_leader_6_end_level
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy') THEN DET.pe_end_level
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy')
                      THEN DET.pe_end_level END                                                                     AS pe_end_level
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy') THEN DET.pe_start_level
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy')
                      THEN DET.pe_start_level END                                                                   AS pe_start_level
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy')
                      THEN DET.core_course_fit_5_start_level
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy')
                      THEN DET.core_course_fit_5_start_level END                                                    AS core_course_fit_5_start_level
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy')
                      THEN DET.core_course_fit_6_end_level
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy')
                      THEN DET.core_course_fit_6_end_level END                                                      AS core_course_fit_6_end_level
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and DET.core_course_online_2_levels <>'') then CAST(DET.core_course_online_2_levels as double precision)
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy'and DET.core_course_online_2_levels <>'') then CAST(DET.core_course_online_2_levels as double precision)
                  else    NULL     END                                           AS core_course_online_2_levels
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy')
                      THEN DET.core_course_online_5_start_level
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy')
                      THEN DET.core_course_online_5_start_level END                                                 AS core_course_online_5_start_level
            , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy')
                      THEN DET.core_course_online_6_end_level
                  WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy')
                      THEN DET.core_course_online_6_end_level END                                                   AS core_course_online_6_end_level
,CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy') and test_prep_group_1_amount > 0 then 1
WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy') and test_prep_group_1_amount > 0 then 1
else 0 end as test_prep_units,
    case when (core_course_7_type in ('FA2','FA3','FA4','FAM') and "Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy') then core_course_levels
        when (core_course_7_type in ('FA2','FA3','FA4','FAM') and "Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy') then core_course_levels
        else null end as FA_Levels,
    CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('FA2','FA3','FA4','FAM')) then core_course_amount
WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name='Italy' and core_course_7_type in ('FA2','FA3','FA4','FAM')) then core_course_amount
else null end as FA_amount,
    case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('FA2')) then core_course_levels
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('FA2')) then core_course_levels
else null end as FA2_levels,
    case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('FA2')) then core_course_amount
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('FA2')) then core_course_amount
else null end as FA2_amount,
        case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('FA3')) then core_course_levels
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('FA3')) then core_course_levels
else null end as FA3_levels,
    case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('FA3')) then core_course_amount
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('FA3')) then core_course_amount
else null end as FA3_amount,
            case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('FA4')) then core_course_levels
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('FA4')) then core_course_levels
else null end as FA4_levels,
    case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('FA4')) then core_course_amount
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('FA4')) then core_course_amount
else null end as FA4_amount,
                case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('FAM')) then core_course_levels
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('FAM')) then core_course_levels
else null end as FAM_levels,
    case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('FAM')) then core_course_amount
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('FAM')) then core_course_amount
else null end as FAM_amount,
    case when (core_course_7_type in ('IC2','IC3','TC4','ICM') and "Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy') then core_course_levels
        when (core_course_7_type in ('IC2','IC3','TC4','ICM') and "Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy') then core_course_levels
        else null end as IC_Levels,
    CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('IC2','IC3','TC4','ICM')) then core_course_amount
WHEN ("Deal Stage" = 'contractsent' AND CET.territory_name='Italy' and core_course_7_type in ('IC2','IC3','TC4','ICM')) then core_course_amount
else null end as IC_amount,
    case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('IC2')) then core_course_levels
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('IC2')) then core_course_levels
else null end as IC2_levels,
    case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('IC2')) then core_course_amount
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('IC2')) then core_course_amount
else null end as IC2_amount,
        case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('IC3')) then core_course_levels
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('IC3')) then core_course_levels
else null end as IC3_levels,
    case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('IC3')) then core_course_amount
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('IC3')) then core_course_amount
else null end as IC3_amount,
            case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('IC4')) then core_course_levels
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('IC4')) then core_course_levels
else null end as IC4_levels,
    case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('IC4')) then core_course_amount
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('IC4')) then core_course_amount
else null end as IC4_amount,
                case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('ICM')) then core_course_levels
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('ICM')) then core_course_levels
else null end as ICM_levels,
    case when ("Deal Stage" = 'closedwon' AND CET.territory_name <> 'Italy' and core_course_7_type in ('ICM')) then core_course_amount
when ("Deal Stage" = 'contractsent' AND CET.territory_name = 'Italy' and core_course_7_type in ('ICM')) then core_course_amount
else null end as ICM_amount



--      , null                                                                                                          as source_key_fk
--      , null                                                                                                          as center_key_fk


FROM (SELECT hs_object_id,
             territory_code,
             contract_date,
             hs_lifecyclestage_customer_date,
             hs_lifecyclestage_salesqualifiedlead_date,
             Lead_Date,
             hs_lifecyclestage_lead_date,
             MQL_Date,
             hs_lifecyclestage_marketingqualifiedlead_date,
             Booked_Date,
             Show_Date,
             Call_Campaign,
             Center_Name,
             hubspot_owner_id,
             createdate,
             hs_analytics_first_url,
             hs_analytics_source_data_1,
             hs_analytics_source_data_2,
             hs_analytics_source,
             first_conversion_event_name,
             tmk_owner,
             territory_name,
             course_age_group,
             useful_contact_date,
             source,
             sub_source,
             individual_corporate,
             how_did_you_hear_about_us,
             hs_lifecyclestage_opportunity_date,
             associateid,
             recent_conversion_date

      FROM (SELECT CE.*, associateid
            FROM hubspot_crm.contactsenriched CE
                     LEFT JOIN hubspot_crm.associationcontactstodeals AssContDeal
                               ON CE.hs_object_id = AssContDeal.contactid
                                   AND CE.territory_code = AssContDeal.territory_code
            WHERE deleteflag IN ('N', 'R')
              --France specific exclusions
              AND (excludefromstats__c = '' OR excludefromstats__c = 'false')
              AND (Actual_Status NOT IN ('Delete', 'Deletion Asked', 'Duplicate') OR
                   Actual_Status IS NULL) --is null?
              AND ((CE.territory_name = 'France' AND center_name IS NOT NULL AND
                    lead_source IS NOT NULL) OR CE.territory_name <> 'France')
              AND CE.territory_code <> 'LA'
              AND CE.territory_code <> 'FR'
            AND CE.territory_code <> 'MA') A) CET

         FULL JOIN
     (SELECT hs_object_id                     AS "Deal Id"
           , Dealname
           , territory_name
           , CAST(timezonecreatedate AS DATE) AS "Deal Create Date"
           , CAST(timezoneclosedate AS DATE)  AS "Deal Close Date"
           , Individual_Corporate
           , Center_name                      AS "Center Name"
           , course_age_group
           , Source
           , Sub_source
           , Amount
           , Amount_Gifts
           , Amount_Interest
           , Dealstage                        AS "Deal Stage"
           , Hubspot_Owner_id
           , Hubspot_team_id
           , Core_Course_Amount
           , Core_Course_Levels
           , Core_course_7_class_access_type
           , core_course_7_type
           , hs_analytics_source
           , Market_leader_1_amount
           , Market_Leader_2_Hours
           , Test_Prep_Group_1_Amount
           , Test_Prep_Group_2_Quantity
           , territory_code
           , business_partner_1_amount
           , certifications_1_amount
           , core_course_fit_amount
           , core_course_online_1_amount
           , ilc_1_amount
           , test_prep_executive_1_amount
           , business_partner_2_hours
           , core_course_fit_2_hours
           , ilc_2_hours
           , business_partner_5_start_level
           , business_partner_6_end_level
           , market_leader_5_start_level
           , market_leader_6_end_level
           , pe_end_level
           , pe_start_level
           , core_course_fit_5_start_level
           , core_course_fit_6_end_level
           , core_course_online_2_levels
           , core_course_online_5_start_level
           , core_course_online_6_end_level
      ,certifications_2_quantity
      ,test_prep_executive_2_quantity



      FROM hubspot_crm.dealsenriched D
      WHERE territory_code <> 'FR'
        AND territory_code <> 'LA'
        AND territory_code <> 'MA'
        AND deleteflag IN ('N', 'R')) DET
     ON CET.associateid = DET."Deal Id"
         AND CET.territory_code = DET.territory_code

--
-- ;
UNION
------------v3 logic------
SELECT  DE.hs_object_id                                                                                  AS contactid
            , CASE
                  WHEN DE.territory_code <> 'IT' AND dealstage = 'closedwon' THEN DE.timezoneclosedate
                  WHEN DE.territory_code = 'IT' AND dealstage = 'contractsent'
                      THEN DE.timezoneclosedate END                                                            AS contract_date
            , CAST(lead_date AS DATE)                                                                          AS lead_date
            , CAST(mql_date AS DATE)                                                                           AS mql_date
            , CAST(booked_date AS DATE)                                                                        AS booked_date
            , CAST(showed_date AS DATE)                                                                        AS showed_date
            , DE.center_name
            , CASE
                  WHEN (DE.hubspot_owner_id IS NULL) THEN 0

                  ELSE DE.hubspot_owner_id END                                                                 AS hubspot_owner_id
            , individual_corporate
            , CASE WHEN DE.deal_source = '' THEN 'No Source' ELSE DE.deal_source END                           AS Source
            ,'' as sub_source_group
            , CASE WHEN De.channel = '' THEN '' ELSE DE.channel END                                        AS channel
            , channel_drill_down_1
            , channel_drill_down_2
            , CASE
                  WHEN POSITION('- ' IN conversion) > 0 THEN
                      SUBSTRING(conversion,1, POSITION('- ' IN conversion) - 1)
                  ELSE '' END                                                                                  AS landing_page
        , '' as page
            , CASE
                  WHEN channel = 'PAID_SEARCH' THEN channel_drill_down_1
                  WHEN channel in ('PAID_SOCIAL','SOCIAL_MEDIA') THEN channel_drill_down_2
                  ELSE '' END                                                                                  AS campaign
            , CAST(contacted_date AS DATE)                                                                     AS useful_contact_date
            , ''                                                                                               AS tmk_owner
            , course_age_group
            , DE.id                                                                                            AS dealid
            , territory_name
            , CASE WHEN DE.dealstage = 'closedwon' THEN DE.amount ELSE '' END                                  AS sales
                        , null as refund_date
            ,null as refunded_amount
            , DE.hubspot_team_id                                                                               AS hubspot_team_iddeal
            , CASE
                  WHEN DE.id IS NOT NULL
                      THEN ROW_NUMBER() OVER (PARTITION BY DE.id ORDER BY DE.CreateDate) END                   AS DealRows
             , CASE
                  WHEN dealstage = 'closedwon' AND DE.territory_name <> 'Italy' THEN amount_gifts
                  WHEN dealstage = 'contractsent' AND DE.territory_name = 'Italy'
                      THEN amount_gifts END                                                                         AS amount_gifts
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy') THEN DE.Amount_Interest
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy')
                      THEN DE.amount_interest END                                                                  AS AmountInterest
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy') THEN cast(DE.Core_Course_Amount as DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy')
                      THEN cast(DE.Core_Course_Amount as DOUBLE PRECISION) END                                                               AS core_course_amount
    ,CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('FA2','FA3','FA4','FAM','ICM','IC2','IC3','IC4')) THEN cast(DE.Core_Course_Levels as bigint)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('FA2','FA3','FA4','FAM','ICM','IC2','IC3','IC4'))
                      THEN cast(DE.Core_Course_Levels as bigint) else NULL END                                                               AS core_course_levels

            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy')
                      THEN DE.Core_course_7_class_access_type
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy')
                      THEN DE.core_course_7_class_access_type
    END                                                                                                             AS core_course_7_class_access_type

            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND
                        DE.Market_leader_1_amount <> '')
                      THEN CAST(DE.Market_leader_1_amount AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND
                        DE.Market_leader_1_amount <> '')
                      THEN CAST(DE.Market_leader_1_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS Market_leader_1_amount

            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND
                        DE.market_leader_2_hours <> '')
                      THEN CAST(DE.Market_Leader_2_Hours AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND
                        DE.market_leader_2_hours <> '')
                      THEN CAST(DE.Market_Leader_2_Hours AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS Market_Leader_2_Hours

            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND
                        DE.Test_Prep_Group_1_Amount <> '')
                      THEN CAST(DE.Test_Prep_Group_1_Amount AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND
                        DE.Test_Prep_Group_1_Amount <> '')
                      THEN CAST(DE.Test_Prep_Group_1_Amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS Test_Prep_Group_1_Amount

            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND
                        DE.Test_Prep_Group_2_Quantity <> '')
                      THEN CAST(DE.Test_Prep_Group_2_Quantity AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND
                        DE.Test_Prep_Group_2_Quantity <> '')
                      THEN CAST(DE.Test_Prep_Group_2_Quantity AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS Test_Prep_Group_2_Quantity
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND
                        DE.business_partner_1_amount IS NOT NULL)
                      THEN CAST(DE.business_partner_1_amount AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND
                        DE.business_partner_1_amount IS NOT NULL)
                      THEN CAST(DE.business_partner_1_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS business_partner_1_amount
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND
                        DE.certifications_1_amount IS NOT NULL)
                      THEN CAST(DE.certifications_1_amount AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND
                        DE.certifications_1_amount IS NOT NULL)
                      THEN CAST(DE.certifications_1_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS certifications_1_amount
     ,    CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND
                        DE.certifications_2_quantity IS NOT NULL)
                      THEN CAST(DE.certifications_2_quantity AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND
                        DE.certifications_2_quantity IS NOT NULL)
                      THEN CAST(DE.certifications_2_quantity AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS certifications_2_quantity
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND
                        DE.core_course_fit_2_hours <> '')
                      THEN CAST(DE.core_course_fit_2_hours AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND
                        DE.core_course_fit_2_hours <> '')
                      THEN CAST(DE.core_course_fit_2_hours AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS core_course_fit_2_hours
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND
                        DE.core_course_fit_amount IS NOT NULL)
                      THEN CAST(DE.core_course_fit_amount AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND
                        DE.core_course_fit_amount IS NOT NULL)
                      THEN CAST(DE.core_course_fit_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS core_course_fit_amount
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND
                        DE.core_course_online_1_amount IS NOT NULL)
                      THEN CAST(DE.core_course_online_1_amount AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND
                        DE.core_course_online_1_amount IS NOT NULL)
                      THEN CAST(DE.core_course_online_1_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS core_course_online_1_amount
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND DE.ilc_1_amount IS NOT NULL)
                      THEN CAST(DE.ilc_1_amount AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND DE.ilc_1_amount IS NOT NULL)
                      THEN CAST(DE.ilc_1_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS ilc_1_amount
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND
                        DE.test_prep_executive_1_amount IS NOT NULL)
                      THEN CAST(DE.test_prep_executive_1_amount AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND
                        DE.test_prep_executive_1_amount IS NOT NULL)
                      THEN CAST(DE.test_prep_executive_1_amount AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS test_prep_executive_1_amount
     ,CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND
                        DE.test_prep_executive_2_quantity <>'')
                      THEN CAST(DE.test_prep_executive_2_quantity AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND
                        DE.test_prep_executive_2_quantity <>'')
                      THEN CAST(DE.test_prep_executive_2_quantity AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS test_prep_executive_2_quantity
     , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND
                        DE.business_partner_2_hours IS NOT NULL)
                      THEN CAST(DE.business_partner_2_hours AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND
                        DE.business_partner_2_hours IS NOT NULL)
                      THEN CAST(DE.business_partner_2_hours AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS business_partner_2_hours
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' AND DE.ilc_2_hours <> '')
                      THEN CAST(DE.ilc_2_hours AS DOUBLE PRECISION)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy' AND DE.ilc_2_hours <> '')
                      THEN CAST(DE.ilc_2_hours AS DOUBLE PRECISION)
                  ELSE NULL END                                                                                     AS ilc_2_hours
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy')
                      THEN DE.business_partner_5_start_level
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy')
                      THEN DE.business_partner_5_start_level END                                                   AS business_partner_5_start_level
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy')
                      THEN DE.business_partner_6_end_level
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy')
                      THEN DE.business_partner_6_end_level END                                                     AS business_partner_6_end_level
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy')
                      THEN DE.market_leader_5_start_level
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy')
                      THEN DE.market_leader_5_start_level END                                                      AS market_leader_5_start_level
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy') THEN DE.market_leader_6_end_level
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy')
                      THEN DE.market_leader_6_end_level END                                                        AS market_leader_6_end_level
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy') THEN DE.pe_end_level
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy')
                      THEN DE.pe_end_level END                                                                     AS pe_end_level
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy') THEN DE.pe_start_level
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy')
                      THEN DE.pe_start_level END                                                                   AS pe_start_level
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy')
                      THEN DE.core_course_fit_5_start_level
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy')
                      THEN DE.core_course_fit_5_start_level END                                                    AS core_course_fit_5_start_level
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy')
                      THEN DE.core_course_fit_6_end_level
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy')
                      THEN DE.core_course_fit_6_end_level END                                                      AS core_course_fit_6_end_level
            , CASE
                  WHEN (dealstage= 'closedwon' AND DE.territory_name <> 'Italy' and DE.core_course_online_2_levels <>'') then CAST(DE.core_course_online_2_levels as double precision)
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy'and DE.core_course_online_2_levels <>'') then CAST(DE.core_course_online_2_levels as double precision)
                  else    NULL     END                                           AS core_course_online_2_levels
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy')
                      THEN DE.core_course_online_5_start_level
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy')
                      THEN DE.core_course_online_5_start_level END                                                 AS core_course_online_5_start_level
            , CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy')
                      THEN DE.core_course_online_6_end_level
                  WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy')
                      THEN DE.core_course_online_6_end_level END                                                   AS core_course_online_6_end_level,
    CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy') and test_prep_group_1_amount > 0 then 1
WHEN (dealstage = 'contractsent' AND DE.territory_name = 'Italy') and test_prep_group_1_amount > 0 then 1
else 0 end as test_prep_units,
    case when (core_course_7_type in ('FA2','FA3','FA4','FAM') and dealstage = 'closedwon' AND DE.territory_name <> 'Italy') then core_course_levels
        when (core_course_7_type in ('FA2','FA3','FA4','FAM') and dealstage = 'contractsent' AND DE.territory_name = 'Italy') then core_course_levels
        else null end as FA_Levels,
    CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('FA2','FA3','FA4','FAM')) then core_course_amount
WHEN (dealstage = 'contractsent' AND DE.territory_name='Italy' and core_course_7_type in ('FA2','FA3','FA4','FAM')) then core_course_amount
else null end as FA_amount,
    case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('FA2')) then core_course_levels
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('FA2')) then core_course_levels
else null end as FA2_levels,
    case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('FA2')) then core_course_amount
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('FA2')) then core_course_amount
else null end as FA2_amount,
        case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('FA3')) then core_course_levels
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('FA3')) then core_course_levels
else null end as FA3_levels,
    case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('FA3')) then core_course_amount
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('FA3')) then core_course_amount
else null end as FA3_amount,
            case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('FA4')) then core_course_levels
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('FA4')) then core_course_levels
else null end as FA4_levels,
    case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('FA4')) then core_course_amount
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('FA4')) then core_course_amount
else null end as FA4_amount,
                case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('FAM')) then core_course_levels
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('FAM')) then core_course_levels
else null end as FAM_levels,
    case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('FAM')) then core_course_amount
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('FAM')) then core_course_amount
else null end as FAM_amount,
    case when (core_course_7_type in ('IC2','IC3','TC4','ICM') and dealstage = 'closedwon' AND DE.territory_name <> 'Italy') then core_course_levels
        when (core_course_7_type in ('IC2','IC3','TC4','ICM') and dealstage = 'contractsent' AND DE.territory_name = 'Italy') then core_course_levels
        else null end as IC_Levels,
    CASE
                  WHEN (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('IC2','IC3','TC4','ICM')) then core_course_amount
WHEN (dealstage = 'contractsent' AND DE.territory_name='Italy' and core_course_7_type in ('IC2','IC3','TC4','ICM')) then core_course_amount
else null end as IC_amount,
    case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('IC2')) then core_course_levels
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('IC2')) then core_course_levels
else null end as IC2_levels,
    case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('IC2')) then core_course_amount
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('IC2')) then core_course_amount
else null end as IC2_amount,
        case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('IC3')) then core_course_levels
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('IC3')) then core_course_levels
else null end as IC3_levels,
    case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('IC3')) then core_course_amount
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('IC3')) then core_course_amount
else null end as IC3_amount,
            case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('IC4')) then core_course_levels
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('IC4')) then core_course_levels
else null end as IC4_levels,
    case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('IC4')) then core_course_amount
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('IC4')) then core_course_amount
else null end as IC4_amount,
                case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('ICM')) then core_course_levels
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('ICM')) then core_course_levels
else null end as ICM_levels,
    case when (dealstage = 'closedwon' AND DE.territory_name <> 'Italy' and core_course_7_type in ('ICM')) then core_course_amount
when (dealstage = 'contractsent' AND DE.territory_name = 'Italy' and core_course_7_type in ('ICM')) then core_course_amount
else null end as ICM_amount

FROM hubspot_crm.dealsenriched DE
WHERE territory_code in ('LA','MA')
  AND deleteflag IN ('N', 'R')
UNION
------------------myanmar vietnam------------------
SELECT CET.contactid                                                                   AS contactid,
       CASE WHEN dealstage = 'closedwon' THEN CAST(AB.timezoneclosedate AS DATE) ELSE NULL END         AS contract_date,        ----Need to verify trevor
       CAST(CET.hs_lifecyclestage_lead_date AS DATE)                                   AS lead_date,
       CAST(CET.hs_lifecyclestage_marketingqualified_lead_date AS DATE)                AS mql_date,
       CAST(CET.booked_date AS DATE)                                                   AS booked_date,
       cast(AB.timezonecreatedate as date) AS showed_date,----Need to verify trevor
       CASE

           WHEN (AB.center_name = '') AND (CET.center_name = '') THEN 'No Center'
           WHEN (AB.center_name IS NULL AND CET.center_name IS NULL) THEN 'No Center'
           WHEN (AB.center_name IS NULL AND CET.center_name = '') THEN 'No Center'
           WHEN (AB.center_name = '' AND CET.center_name IS NULL) THEN 'No Center'
           WHEN (AB.center_name = '') THEN CET.center_name
           WHEN AB.center_name IS NULL THEN CET.center_name
           WHEN CET.center_name IS NULL THEN AB.center_name
           ELSE AB.center_name END                                                     AS center_name,
       CASE
           WHEN (CET.hubspot_owner_id is NULL and AB.hubspot_owner_id is NULL) then 0
           WHEN (AB.hubspot_owner_id IS NULL) THEN CET.hubspot_owner_id
           WHEN CET.hubspot_owner_id IS NULL THEN AB.hubspot_owner_id
else AB.hubspot_owner_id
            END                                                AS hubspot_owner_id,
       ''                                                                              AS individual_corporate,
       CASE WHEN AB.channel in ('Renewals', 'Upgrade') then AB.channel

WHEN (AB.channel = '') AND (CET.channel = '') THEN 'No Source'

WHEN (CET.channel IS NULL AND AB.channel IS NULL) THEN 'No Source'

WHEN (AB.channel IS NULL AND CET.channel = '') THEN 'No Source'

WHEN (CET.channel IS NULL AND AB.channel = '') THEN 'No Source'

WHEN CET.channel = '' THEN AB.channel

WHEN (CET.channel IS NULL) THEN AB.channel

WHEN AB.channel IS NULL THEN CET.channel

ELSE CET.channel END

AS Source
       ,'' as sub_source_group,
       CET.source                                                                 AS channel,              ---- Trevor need to verify
       ''                                                                              AS channel_drill_down_1, ---- we have sub_source_level_1,2,3
       ''                                                                              AS channel_Drill_down_2,----we have sub_source_level_1,2,3 how to derive on this
       ''                                                                              AS landing_page,----Need to verify trevor
       '' as page,
       case when campaign is NULL then '' else campaign end as camapign,
       CAST(CET.hs_lifecyclestage_marketingqualified_lead_date AS DATE)                                                                            AS useful_contact_date,----Need to verify trevor
       ''                                                                              AS tmk_owner,----Need to verify trevor
       ''                                                                              AS course_age_group,----Need to verify trevor
       CASE WHEN (AB.id IS NULL) THEN NULL ELSE AB.id END                              AS dealid,
       CASE
           WHEN (CET.territory_name = '' OR CET.territory_name IS NULL) THEN AB.territory_name
           ELSE CET.territory_name END                                                 AS territory_name,
       CASE WHEN dealstage = 'closedwon' THEN amount ELSE '' END                       AS sales     ----Need to verify trevor
       ,case when (refunded_amount <> 0 and termination_status = 'Approved') then cast(termination_date as DATE) else null end as refund_date
	   ,case when (termination_status = 'Approved' and termination_type = 'Cancel and Refund') then cast(refunded_amount as float) else null end as refunded_amount

       ,NULL                                                                            AS hubspot_team_iddeal,----Need to verify trevor
       CASE
           WHEN AB.id IS NOT NULL
               THEN ROW_NUMBER() OVER (PARTITION BY AB.id ORDER BY CET.createdate) END AS DealRows,
       NULL                                                                            AS amount_gifts,
       NULL                                                                            AS amount_interest,
       NULL                                                                            AS core_course_amount,
       NULL                                                                            AS core_course_levels,
       NULL                                                                            AS core_course_7_class_access_type
        ,
       NULL                                                                            AS Market_leader_1_amount
        ,
       NULL                                                                            AS Market_Leader_2_Hours
        ,
       NULL                                                                            AS Test_Prep_Group_1_Amount
        ,
       NULL                                                                            AS Test_Prep_Group_2_Quantity
        ,
       NULL                                                                            AS business_partner_1_amount
        ,
       NULL                                                                            AS certifications_1_amount
       , NULL                                                                           AS certifications_2_quantity
        ,
       NULL                                                                            AS core_course_fit_2_hours
        ,
       NULL                                                                            AS core_course_fit_amount
        ,
       NULL                                                                            AS core_course_online_1_amount
        ,
       NULL                                                                            AS ilc_1_amount
        ,
       NULL                                                                            AS test_prep_executive_1_amount
       , NULL                                                                                    AS test_prep_executive_2_quantity
        ,
       NULL                                                                            AS business_partner_2_hours
        ,
       NULL                                                                            AS ilc_2_hours
        ,
       NULL                                                                            AS business_partner_5_start_level
        ,
       NULL                                                                            AS business_partner_6_end_level
        ,
       NULL                                                                            AS market_leader_5_start_level
        ,
       NULL                                                                            AS market_leader_6_end_level
        ,
       NULL                                                                            AS pe_end_level
        ,
       NULL                                                                            AS pe_start_level
        ,
       NULL                                                                            AS core_course_fit_5_start_level
        ,
       NULL                                                                            AS core_course_fit_6_end_level
        ,
       NULL                                                                            AS core_course_online_2_levels
        ,
       NULL                                                                            AS core_course_online_5_start_level
        ,
       NULL                                                                            AS core_course_online_6_end_level
,    CASE
                  WHEN (dealstage = 'closedwon' AND AB.territory_name <> 'Italy') and test_prep_group_1_amount > 0 then 1
WHEN (dealstage = 'contractsent' AND AB.territory_name = 'Italy') and test_prep_group_1_amount > 0 then 1
else 0 end as test_prep_units,
    NULL as FA_Levels,
    NULL as FA_amount,
    NULL as FA2_levels,
    NULL as FA2_amount,
    NULL as FA3_levels,
    NULL as FA3_amount,
    NULL as FA4_levels,
    NULL as FA4_amount,
    NULL as FAM_levels,
NULL as FAM_amount,
 NULL as IC_Levels,
    NULL as IC_amount,
   NULL as IC2_levels,
    NULL as IC2_amount,
       NULL as IC3_levels,
    NULL as IC3_amount,
            NULL as IC4_levels,
    NULL as IC4_amount,
            NULL as ICM_levels,
    NULL as ICM_amount

FROM (SELECT id AS contactid,
             ammvn.associatedid,
             cmmvn.source,
             cmmvn.channel,
             sub_source_level_3,
             campaign,
             hs_lifecyclestage_lead_date,
             hs_lifecyclestage_marketingqualified_lead_date,
             booked_date,
             center_name,
             hubspot_owner_id,
             territory_name,
             cmmvn.createdate
      FROM hubspot_crm.contactsenriched_mmvn cmmvn
               LEFT JOIN hubspot_crm.associationcontacttodeals_mmvn ammvn
                         ON cmmvn.id = ammvn.contactid) CET
         FULL JOIN
     (SELECT *, CASE WHEN closedate <> '1970-01-01 00:00:00.000' THEN 'closedwon' ELSE '' END AS dealstage
      FROM hubspot_crm.dealsenriched_mmvn) AB
     ON CET.associatedid = AB.id
UNION

--------- france-----------------
SELECT CETF.hs_object_id                                                                                              AS contactid
     , CASE WHEN "Deal Stage" = 'closedwon' THEN "Deal Close Date" ELSE NULL END         AS contract_date
     , CAST(CETF.Lead_Date AS DATE)                                                                                   AS lead_date
     , CAST(CETF.MQL_Date AS DATE)                                                                                    AS mql_date
     , CAST(CETF.Booked_Date AS DATE)
                                                                                                                      AS booked_date
     , CAST(CETF.Show_Date AS DATE)                                                                                   AS showed_date
     , CASE

           WHEN (DETF."Center Name" = '') AND (CETF.center_name = '') THEN 'No Center'
           WHEN (DETF."Center Name" IS NULL AND CETF.center_name IS NULL) THEN 'No Center'
           WHEN (DETF."Center Name" IS NULL AND CETF.center_name = '') THEN 'No Center'
           WHEN (DETF."Center Name" = '' AND CETF.center_name IS NULL) THEN 'No Center'
           WHEN (DETF."Center Name" = '') THEN CETF.center_name
           WHEN DETF."Center Name" IS NULL THEN CETF.center_name
           WHEN CETF.center_name IS NULL THEN DETF."Center Name"
           ELSE DETF."Center Name"
    END                                                                                                               AS center_name
     , CASE
         WHEN (CETF.hubspot_owner_id IS NULL and DETF.hubspot_owner_id IS NULL) then 0
           WHEN (DETF.hubspot_owner_id IS NULL) THEN CETF.hubspot_owner_id
           WHEN CETF.hubspot_owner_id IS NULL THEN DETF.hubspot_owner_id
         ELSE  DETF.Hubspot_Owner_id
           END                                                                             AS hubspot_owner_id
     , CASE
           WHEN (DETF.individual_corporate IS NULL AND CETF.individual_corporate IS NULL) THEN ''
           WHEN (DETF.individual_corporate IS NULL AND CETF.individual_corporate = '') THEN ''
           WHEN (DETF.individual_corporate = '' AND CETF.individual_corporate IS NULL) THEN ''
           WHEN (DETF.individual_corporate = '' AND CETF.individual_corporate = '') THEN ''
           WHEN DETF.individual_corporate IS NULL THEN CETF.individual_corporate
           WHEN DETF.individual_corporate = '' THEN CETF.individual_corporate
           WHEN CETF.individual_corporate IS NULL THEN DETF.individual_corporate
           ELSE DETF.individual_corporate END                                                                         AS individual_corporate
     , CASE
           WHEN (DETF.Source = '') AND (CETF.Source = '') THEN 'No Source'
           WHEN (CETF.source IS NULL AND DETF.source IS NULL) THEN 'No Source'
           WHEN (DETF.source IS NULL AND CETF.Source = '') THEN 'No Source'
           WHEN (CETF.source IS NULL AND DETF.Source = '') THEN 'No Source'
           WHEN DETF.Source = '' THEN CETF.Source
           WHEN (DETF.Source IS NULL) THEN CETF.source
           WHEN CETF.source IS NULL THEN DETF.source
           ELSE DETF.source END                                                                                       AS Source
     ,'' as sub_source_group
     , CASE
           WHEN (CASE
                     WHEN (DETF.Source = '') AND (CETF.Source = '') THEN 'No Source'
                     WHEN (CETF.source IS NULL AND DETF.source IS NULL) THEN 'No Source'
                     WHEN (DETF.source IS NULL AND CETF.Source = '') THEN 'No Source'
                     WHEN (CETF.source IS NULL AND DETF.Source = '') THEN 'No Source'
                     WHEN DETF.Source = '' THEN CETF.Source
                     WHEN (DETF.Source IS NULL) THEN CETF.source
                     WHEN CETF.source IS NULL THEN DETF.source
                     ELSE DETF.source END) IN ('Renewals', 'Renewal') THEN ''
           WHEN (CASE
                     WHEN (DETF.Source = '') AND (CETF.Source = '') THEN 'No Source'
                     WHEN (CETF.source IS NULL AND DETF.source IS NULL) THEN 'No Source'
                     WHEN (DETF.source IS NULL AND CETF.Source = '') THEN 'No Source'
                     WHEN (CETF.source IS NULL AND DETF.Source = '') THEN 'No Source'
                     WHEN DETF.Source = '' THEN CETF.Source
                     WHEN (DETF.Source IS NULL) THEN CETF.source
                     WHEN CETF.source IS NULL THEN DETF.source
                     ELSE DETF.source END) IN ('Call In', 'Walk In') THEN CETF.how_did_you_hear_about_us
           WHEN (CASE
                     WHEN (DETF.Source = '') AND (CETF.Source = '') THEN 'No Source'
                     WHEN (CETF.source IS NULL AND DETF.source IS NULL) THEN 'No Source'
                     WHEN (DETF.source IS NULL AND CETF.Source = '') THEN 'No Source'
                     WHEN (CETF.source IS NULL AND DETF.Source = '') THEN 'No Source'
                     WHEN DETF.Source = '' THEN CETF.Source
                     WHEN (DETF.Source IS NULL) THEN CETF.source
                     WHEN CETF.source IS NULL THEN DETF.source
                     ELSE DETF.source END) IN ('Outbound Call') THEN call_campaign
           WHEN (CASE
                     WHEN (DETF.Source = '') AND (CETF.Source = '') THEN 'No Source'
                     WHEN (CETF.source IS NULL AND DETF.source IS NULL) THEN 'No Source'
                     WHEN (DETF.source IS NULL AND CETF.Source = '') THEN 'No Source'
                     WHEN (CETF.source IS NULL AND DETF.Source = '') THEN 'No Source'
                     WHEN DETF.Source = '' THEN CETF.Source
                     WHEN (DETF.Source IS NULL) THEN CETF.source
                     WHEN CETF.source IS NULL THEN DETF.source
                     ELSE DETF.source END) IN ('Web') THEN CETF.hs_analytics_source
           WHEN (CASE
                     WHEN (DETF.Source = '') AND (CETF.Source = '') THEN 'No Source'
                     WHEN (CETF.source IS NULL AND DETF.source IS NULL) THEN 'No Source'
                     WHEN (DETF.source IS NULL AND CETF.Source = '') THEN 'No Source'
                     WHEN (CETF.source IS NULL AND DETF.Source = '') THEN 'No Source'
                     WHEN DETF.Source = '' THEN CETF.Source
                     WHEN (DETF.Source IS NULL) THEN CETF.source
                     WHEN CETF.source IS NULL THEN DETF.source
                     ELSE DETF.source END) IN ('Promoters', 'Events') THEN CETF.sub_source
           WHEN (CASE
                     WHEN (DETF.Source = '') AND (CETF.Source = '') THEN 'No Source'
                     WHEN (CETF.source IS NULL AND DETF.source IS NULL) THEN 'No Source'
                     WHEN (DETF.source IS NULL AND CETF.Source = '') THEN 'No Source'
                     WHEN (CETF.source IS NULL AND DETF.Source = '') THEN 'No Source'
                     WHEN DETF.Source = '' THEN CETF.Source
                     WHEN (DETF.Source IS NULL) THEN CETF.source
                     WHEN CETF.source IS NULL THEN DETF.source
                     ELSE DETF.source END) NOT IN
                ('Promoters', 'Events', 'Web', 'Outbound Call', 'Renewals', 'Renewal', 'Call In', 'Walk In') THEN ''
    END                                                                                                               AS channel

     , CASE
           WHEN (
                       CETF.hs_analytics_source_data_1 = '' OR CETF.hs_analytics_source_data_1 IS NULL) THEN ''
           ELSE CETF.hs_analytics_source_data_1 END                                                                   AS channel_drill_down_1

     , CASE
           WHEN (
                       CETF.hs_analytics_source_data_2 = '' OR CETF.hs_analytics_source_data_2 IS NULL) THEN ''
           ELSE hs_analytics_source_data_2 END                                                                        AS channel_drill_down_2
     , ''                                                                                                           AS landing_page
     , CASE
        WHEN POSITION('?' IN hs_analytics_first_url) > 0
        THEN SUBSTRING(hs_analytics_first_url FROM 1 FOR POSITION('?' IN hs_analytics_first_url) - 1)
        ELSE '' END as page
     , CASE
           WHEN ((CETF.hs_analytics_source <> '' OR CETF.hs_analytics_source IS NOT NULL) AND
                 CETF.hs_analytics_source in ('PAID_SOCIAL','SOCIAL_MEDIA')) THEN hs_analytics_source_data_2
           WHEN ((CETF.hs_analytics_source <> '' OR CETF.hs_analytics_source IS NOT NULL) AND
                 CETF.hs_analytics_source = 'PAID_SEARCH') THEN hs_analytics_source_data_1
           ELSE ''
    END                                                                                                               AS campaign


     , CAST(CETF.Useful_Contact_Date AS DATE)                                                                         AS useful_contact_date

     , CETF.TMK_Owner                                                                                                 AS tmk_owner


     , CASE
           WHEN (CETF.course_age_group IS NULL OR CETF.course_age_group = '') THEN ''
           ELSE CETF.course_age_group END                                                                             AS course_age_group
     , DETF."Deal Id"
                                                                                                                      AS dealid
     , CASE
           WHEN (CETF.territory_name = '' OR CETF.territory_name IS NULL) THEN DETF.territory_name
           ELSE CETF.territory_name END                                                                               AS territory_name
     , CASE
           WHEN "Deal Stage" = 'closedwon'  THEN DETF.Amount
    END                                                                                                               AS Sales
                            , null as refund_date
            ,null as refunded_amount
     , DETF.HubSpot_Team_Id                                                                                           AS HubSpotTeamIdDeal
     , CASE
           WHEN DETF."Deal Id" IS NOT NULL
               THEN ROW_NUMBER()
                    OVER (PARTITION BY DETF."Deal Id" ORDER BY CETF.CreateDate) END                                   AS DealRows
     , DETF.Amount_Gifts

     , DETF.Amount_Interest
     , DETF.Core_Course_Amount

     , CASE
                  WHEN ("Deal Stage" = 'closedwon' AND CETF.territory_name <> 'Italy' and DETF.core_course_7_type in ('FA2','FA3','FA4','FAM','ICM','IC2','IC3','IC4')) THEN cast(DETF.Core_Course_Levels as bigint)
                  WHEN ("Deal Stage" = 'contractsent' AND CETF.territory_name = 'Italy' and DETF.core_course_7_type in ('FA2','FA3','FA4','FAM','ICM','IC2','IC3','IC4'))
                      THEN cast(DETF.Core_Course_Levels as bigint) else NULL END                                                               AS core_course_levels

     , DETF.Core_course_7_class_access_type

     , NULL                                                                                                           AS Market_leader_1_amount

     , NULL                                                                                                           AS Market_Leader_2_Hours

     , NULL                                                                                                           AS Test_Prep_Group_1_Amount

     , NULL                                                                                                           AS Test_Prep_Group_2_Quantity
     , NULL                                                                                                           AS business_partner_1_amount
     , NULL                                                                                                           AS certifications_1_amount
     , NULL                                                                                    AS certifications_2_quantity
     , NULL                                                                                                           AS core_course_fit_2_hours
     , NULL                                                                                                           AS core_course_fit_amount
     , NULL                                                                                                           AS core_course_online_1_amount
     , NULL                                                                                                           AS ilc_1_amount
     , NULL                                                                                                           AS test_prep_executive_1_amount
     ,NULL                                                                                   AS test_prep_executive_2_quantity
     , NULL                                                                                                           AS business_partner_2_hours
     , NULL                                                                                                           AS ilc_2_hours
     , NULL                                                                                                           AS business_partner_5_start_level
     , NULL                                                                                                           AS business_partner_6_end_level
     , NULL                                                                                                           AS market_leader_5_start_level
     , NULL                                                                                                           AS market_leader_6_end_level
     , NULL                                                                                                           AS pe_end_level
     , NULL                                                                                                           AS pe_start_level
     , NULL                                                                                                           AS core_course_fit_5_start_level
     , NULL                                                                                                           AS core_course_fit_6_end_level
     , NULL                                                                                                           AS core_course_online_2_levels
     , NULL                                                                                                           AS core_course_online_5_start_level
     , NULL                                                                                                           AS core_course_online_6_end_level
,    CASE
                  WHEN ("Deal Stage" = 'closedwon' AND DETF.territory_name <> 'Italy') and test_prep_group_1_amount > 0 then 1
WHEN ("Deal Stage" = 'contractsent' AND DETF.territory_name = 'Italy') and test_prep_group_1_amount > 0 then 1
else 0 end as test_prep_units,
    NULL as FA_Levels,
    NULL as FA_amount,
    NULL as FA2_levels,
    NULL as FA2_amount,
    NULL as FA3_levels,
    NULL as FA3_amount,
    NULL as FA4_levels,
    NULL as FA4_amount,
    NULL as FAM_levels,
NULL as FAM_amount,
 NULL as IC_Levels,
    NULL as IC_amount,
   NULL as IC2_levels,
    NULL as IC2_amount,
       NULL as IC3_levels,
    NULL as IC3_amount,
            NULL as IC4_levels,
    NULL as IC4_amount,
            NULL as ICM_levels,
    NULL as ICM_amount
-- ,null as source_key_fk,
-- null as center_key_fk

FROM (SELECT hs_object_id,
             territory_code,
             contract_date,
             hs_lifecyclestage_customer_date,
             hs_lifecyclestage_salesqualifiedlead_date,
             Lead_Date,
             hs_lifecyclestage_lead_date,
             MQL_Date,
             hs_lifecyclestage_marketingqualifiedlead_date,
             Booked_Date,
             hs_lifecyclestage_evangelist_date,
             Show_Date,
             hs_lifecyclestage_other_Date,
             Call_Campaign,
             Center_Name,
             hubspot_owner_id,
             createdate,
             hs_analytics_first_url,
             hs_analytics_source_data_1,
             hs_analytics_source_data_2,
             hs_analytics_source,
             tmk_owner,
             territory_name,
             course_age_group,
             useful_contact_date,
             source,
             sub_source,
             individual_corporate,
             how_did_you_hear_about_us,
             hs_lifecyclestage_opportunity_date
      FROM hubspot_crm.contactsenriched
      WHERE deleteflag IN ('N', 'R')
        AND territory_name = 'France' -- Specific for the UNION
        --France specific exclusions
        AND (excludefromstats__c = '' OR excludefromstats__c = 'false')
        AND (Actual_Status NOT IN ('Delete', 'Deletion Asked', 'Duplicate') OR
             Actual_Status IS NULL)   --is null?
        AND ((territory_name = 'France' AND center_name IS NOT NULL AND
              lead_source IS NOT NULL) OR territory_name <> 'France')) CETF

         LEFT JOIN (SELECT hs_object_id                                                           AS "Deal Id"
                         , NULL                                                                   AS Dealname
                         , territory_name
                         , NULL                                                                   AS "Deal Create Date"
                         , CASE
        --when status_reason = 'Signed' then cast(replace(sign_date, 'Nan', null) as date)
                               WHEN status_reason = 'Signed' THEN CAST(sign_date AS DATE)
                               ELSE NULL END                                                      AS "Deal Close Date"
                         , NULL                                                                   AS Individual_Corporate
                         , NULL                                                                   AS "Center Name"
                         , CASE WHEN contract_category = 'Renewals' THEN 'Renewals' ELSE NULL END AS Source
                         , NULL                                                                   AS Sub_Source
                         , amount_for_sales___contract                                            AS Amount
                         , CAST(NULL AS DOUBLE PRECISION)                                         AS Amount_Gifts
                         , CAST(NULL AS DOUBLE PRECISION)                                         AS Amount_Interest
                         , 'closedwon'                                                            AS "Deal Stage"
                         , CAST(NULL AS BIGINT)                                                   AS Hubspot_Owner_id
                         , CAST(NULL AS BIGINT)                                                   AS Hubspot_team_id
                         , CAST(NULL AS DOUBLE PRECISION)                                         AS Core_Course_Amount
                         , CAST(NULL AS BIGINT)                                                   AS Core_Course_Levels
                         , NULL                                                                   AS Core_course_7_class_access_type
                         , NULL                                                                   AS Market_leader_1_amount
                         , NULL                                                                   AS Market_Leader_2_Hours
                         , NULL                                                                   AS Test_Prep_Group_1_Amount
                         , NULL                                                                   AS Test_Prep_Group_2_Quantity
                         ,NULL as core_course_7_type
                         , territory_code
                         , contactid
                    FROM hubspot_crm.customobjectenriched) DETF
                   ON DETF.territory_name = CETF.territory_name
                       AND DETF.contactid = CETF.hs_object_id ) AB;

    COMMIT;


END;


$$
;