version: 2

snapshots:
  - name: contracts_field_changes
    description: >
      Tracks changes to key fields in contracts over time using DBT's snapshot functionality.
      This allows for historical analysis of how contract details have changed.
    config:
      unique_key: contract_id
      strategy: check
      target_schema: snapshot
    columns:
      - name: contract_id
        description: Unique identifier for the contract
      - name: student_id
        description: Reference ID for the student associated with the contract
      - name: center_id
        description: Reference ID for the center associated with the contract
      - name: consultant_id
        description: ID of the consultant assigned to the contract
      - name: lab_teacher_id
        description: ID of the lab teacher assigned to the contract
      - name: group_id
        description: ID of the group associated with the contract
      - name: status
        description: Current status of the contract
      - name: service_type
        description: Type of service provided under the contract
      - name: location
        description: Location where the service is provided
      - name: class_access_type
        description: Type of class access granted by the contract
      - name: is_membership
        description: Flag indicating if the contract is a membership
      - name: start_date
        description: Start date of the contract
      - name: end_date
        description: End date of the contract
      - name: dbt_scd_id
        description: Unique identifier for the snapshot record
      - name: dbt_updated_at
        description: Timestamp when the snapshot record was updated
      - name: dbt_valid_from
        description: Timestamp from which the record is valid
      - name: dbt_valid_to
        description: Timestamp until which the record is valid

  - name: contracts_product_type_changes
    description: >
      Tracks changes to product types associated with contracts over time.
      Allows for historical analysis of how contract products have changed.
    config:
      unique_key: contract_id
      strategy: check
      target_schema: snapshot
    columns:
      - name: contract_id
        description: Unique identifier for the contract
      - name: product
        description: Comma-separated list of product IDs associated with the contract
      - name: dbt_scd_id
        description: Unique identifier for the snapshot record
      - name: dbt_updated_at
        description: Timestamp when the snapshot record was updated
      - name: dbt_valid_from
        description: Timestamp from which the record is valid
      - name: dbt_valid_to
        description: Timestamp until which the record is valid
