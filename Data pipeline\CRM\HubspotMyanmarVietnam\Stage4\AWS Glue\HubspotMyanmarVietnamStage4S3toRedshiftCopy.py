import sys
import logging
from awsglue.utils import getResolvedOptions
sys.path.insert ( 0 , '/glue/lib/installation' )
keys = [ k for k in sys.modules.keys () if 'boto' in k ]
for k in keys:
    if 'boto' in k:
        del sys.modules [ k ]
import boto3
import CloudOperations
import DbOperations
import LogFileGeneration

logs = LogFileGeneration.LogFile
s3_connect = CloudOperations.S3




redshift_execute = DbOperations.Database
config_info = getResolvedOptions(sys.argv, ['Object', 'Status', 'Stage', 'Operation', 'Table','file_path', 'CycleId', 'Bucket'])
bucket = config_info['Bucket']
table_name = config_info['Table']
file_path = config_info['file_path']
pre_requisite_query = """TRUNCATE TABLE {} """.format(table_name)
logging.warning(pre_requisite_query)
truncate_statement = "truncating the table " + table_name + " before copying the latest data"
truncate_execute = redshift_execute.Execution('WriteTable', pre_requisite_query, truncate_statement)
statement_name = "This query is enrichment copy of table " + table_name + " to redshift"
copy_command_query = """COPY {}
FROM '{}'
IAM_ROLE 'arn:aws:iam::262158335980:role/RedshitS3access'
PARQUET;""".format(table_name, file_path)
logging.warning(copy_command_query)
copy_execute = redshift_execute.Execution('WriteTable',copy_command_query, statement_name)
logging.warning(copy_execute)
logging.warning("Data enrichemnet copy from S3 to redshift completed for table", table_name)
log_file = logs.RedshiftEnrichedCopy(file_path= file_path, Object=config_info['Object'], Operation= config_info['Operation'], Stage=config_info['Stage'], Status=200, Table=table_name, Cycleid=config_info['CycleId'], Bucket=bucket)
logging.warning("Log file has been generated")