{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('date') }} as date,
        isdeleted,
        isprivate,
        hasreachedstudent,
        id,
        studentid,
        result,
        reporterid,
        followupactivitytypeid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                date DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'followupactivity'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    "date",
    isdeleted as is_deleted,
    isprivate as is_private,
    hasreachedstudent as has_reached_student,
    id,
    studentid as student_id,
    result,
    reporterid as reporter_id,
    followupactivitytypeid as follow_up_activity_type_id
FROM
    rankedrecords
WHERE
    rn = 1;
