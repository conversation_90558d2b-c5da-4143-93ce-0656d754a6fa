import os
import json
from dependencies import glue_trigger
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.operators.bash import Bash<PERSON>perator
from dependencies.slack_alerts import task_failure_callback
from datetime import datetime
from dependencies.cloud_operations import S3
from dependencies import db_operations
from airflow.providers.amazon.aws.operators.s3 import S3DeleteObjectsOperator
from airflow.providers.amazon.aws.operators.glue import GlueJobOperator
from airflow.hooks.S3_hook import S3Hook
from dependencies import cloud_operations

# Define the DAG
default_args = {
    'owner': 'wse_data_team',
    'start_date': datetime(2024, 2, 8),
    # Add other default_args as needed
}
redshift_execute = db_operations.Database
s3 = cloud_operations.S3

file_info = s3.read_json_file(bucket="etl-dev-gsc-extract", file_path="config/gsc_input_config.json")

dag = DAG('google_search_console', default_args=default_args, schedule_interval=None, catchup=False)

def checkpoint():
    print("Execution Staring")

checkpoint = PythonOperator(
    task_id='checkpoint',
    python_callable=checkpoint,
    dag=dag
)

checkpoint

for input_data in file_info:
    op_kwargs = {
        "glue_job_name": input_data['glue_job_name'],
        "glue_args": input_data['glue_args']
        }
    glue_extract = PythonOperator(
        task_id=str(input_data['task_id']),
        python_callable=glue_trigger.run_glue_job,
        op_kwargs=op_kwargs,
        dag=dag
        )
    checkpoint >> glue_extract

glue_s3file_savetable = GlueJobOperator(
        task_id="save_s3file_table",
        job_name='s3_file_athena_gsc',
        iam_role_name='AWSGlueServiceRole-AZURE',
        dag=dag
    )

glue_extract >> glue_s3file_savetable

HOME = os.environ["HOME"]  # retrieve the location of your home folder
dbt_path = os.path.join(HOME, "dbt/dbt_marketing_bigquery_gsc")  # path to your dbt project

ods_gsc_api_history = BashOperator(
    task_id="ods_gsc_api_history",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models ods.ods_gsc_api_history",  # run the model!
    # on_failure_callback=task_failure_callback,
    dag=dag
)

glue_s3file_savetable >> ods_gsc_api_history


if __name__ == "__main__":
    dag.cli()