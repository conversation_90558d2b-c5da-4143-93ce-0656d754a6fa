{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_master_contract_course_audit_info'
        ) }}
    {% if is_incremental() %}
        where created_date > ((select max(created_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    mccourseauditinfo.id as id,
    mccourseauditinfo.master_contract_id as master_contract_id,
    mccourseauditinfo.master_contract_course_id as master_contract_course_id,
    cauditfields.field_name as modifiedfield,
    mccourseauditinfo.previous_value as previous_value,
    mccourseauditinfo.present_value as present_value,
    cchange_types.name as change_type,
    mccourseauditinfo.reason as reason,
    mccourseauditinfo.modified_by_id as modified_by_id,
    mccourseauditinfo.created_date as created_date,
    {{convert_to_local_timestamp('mccourseauditinfo.created_date','time_zone_id')}} as local_created_date,
    mccourseauditinfo.effective_date as effective_date,
    {{convert_to_local_timestamp('mccourseauditinfo.effective_date','time_zone_id')}} as local_effective_date
from ods_data as mccourseauditinfo
    left join (
        select id,
            field_name
        from {{ ref( 'ods_cs_contract_audit_fields' ) }}
    ) as cauditfields on mccourseauditinfo.modified_field_id = cauditfields.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_contract_change_types' ) }}
    ) as cchange_types on mccourseauditinfo.change_type = cchange_types.id
    left join (
        select id,
            center_id
        from {{ ref( 'ods_cs_master_contracts' ) }}
    ) as mcontracts on mccourseauditinfo.master_contract_id = mcontracts.id
    left join (
        select id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on mcontracts.center_id = tz.id