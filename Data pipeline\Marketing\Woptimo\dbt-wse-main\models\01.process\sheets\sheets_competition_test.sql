{{ config(
    materialized='table',
    tags = ["test"],
    pre_hook='
CREATE OR REPLACE EXTERNAL TABLE  `wse-marketing.raw_data_sheets.sheets_competition` (
competitor_name STRING,
competitor_brand_matching STRING,
competitor_domain STRING,
semrush_limit STRING,
semrush_database STRING
)

OPTIONS (
uris=["https://docs.google.com/spreadsheets/d/1g7dd0ywEDDTt9KNuPNVVbYjFakKtzbLu-cX2qTACMYU/"],
format="GOOGLE_SHEETS",
sheet_range="competition",
skip_leading_rows=1
)
    ',
) }}

select *,

lower(replace(replace (competitor_name," ","_"),"-","_")) as competitor_field,
lower(replace(competitor_domain,".","_")) as cloud_storage_prefix

FROM `wse-marketing.raw_data_sheets.sheets_competition`