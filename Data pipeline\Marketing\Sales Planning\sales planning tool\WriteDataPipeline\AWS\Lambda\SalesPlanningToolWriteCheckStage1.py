import boto3
import json

Bucket = "sales-planning-tool-write-develop"
Boto3Resource = boto3.resource("s3")
S3Bucket = Boto3Resource.Bucket(Bucket)


def lambda_handler(event, context):
    ExecutionCheckFilePath = Boto3Resource.Object(Bucket, 'ExecutionCheck.json')
    ExecutionCheckFileContent = ExecutionCheckFilePath.get()['Body'].read().decode('utf-8')
    ExecutionCheck = json.loads(ExecutionCheckFileContent)
    print(ExecutionCheck)
    Folder = f"Logs/{ExecutionCheck['CycleId']}/Stage1"
    FilesInS3 = [f.key.split(Folder + "/")[1] for f in S3Bucket.objects.filter(Prefix=Folder).all()]
    print(FilesInS3)
    print(len(FilesInS3))

    if len(FilesInS3) == 2:
        print("Stage 1 completed")
    else:
        print("Stage 1 not completed")
        raise Exception
    return "Stage 1 Completed"
