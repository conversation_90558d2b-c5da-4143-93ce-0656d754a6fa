{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with misentrydate as (
    select contract_id,
           min(created_date) as misentry_date
    from {{ref('dt_cs_contracts_audit_info')}}
    where modified_field = 'state'
      and present_value = 'misentry'
    group by contract_id
),
calendar as (
    select first_month_date,
           last_month_date,
           year_month_key,
           date_add('day', 4, last_month_date) as next_month_4th
    from reporting.dim_calendar
    where "date" = last_month_date
      and "date" <= current_date
      and "date" >= {{filter_date()}}
),
contracts_dims as (
    select CAL.last_month_date,
           C.student_reference_id,
           concat(C.student_reference_id, '_', C.contract_reference_id, '_', cast(CAL.last_month_date as varchar)) as compound_key,
           C.contract_reference_id,
           CC.group_id,
           CC.lab_teacher_id,
           CC.consultant_id,
           TC.center_reference_id,
           C.product_type,
           CC.status,
           CC.location,
           CC.class_access_type,
           CC.service_type,
           CC.is_membership,
           C.is_teen,
           C.is_promotional,
           C.contract_type,
           CASE
               WHEN c.contract_product like('%self-booking%') THEN true
               ELSE false
           END AS self_booking_access_flag,
           row_number() over (partition by C.student_id, C.product_type, CAL.year_month_key order by valid_from desc) as RN
    from calendar CAL
             left join {{ref('contracts_changes')}} CC on CC.valid_from <= CAL.last_month_date
                                                     and CC.valid_to >= CAL.first_month_date
             left join {{ref('contracts')}} C on C.contract_reference_id = CC.contract_id
             left join {{ref('territory_centers')}} TC on C.center_reference_id = TC.center_reference_id
             left join misentrydate ME on C.contract_id = me.contract_id
    where CC.status = 'valid'
      and (ME.misentry_date is null or ME.misentry_date >= next_month_4th)
      and C.state not in ('removed', 'rpending')
      and C.product_type in ('core course', 'd2c')
)

    
    
select *, 
CAST(current_timestamp AS TIMESTAMP(6)) as load_date 
from contracts_dims 
where RN = 1
