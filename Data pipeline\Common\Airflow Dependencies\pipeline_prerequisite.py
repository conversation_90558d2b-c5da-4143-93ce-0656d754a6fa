import logging
from airflow.models import Dag<PERSON>un, DagModel
from airflow.settings import Session


def update_dag_paused_state(dag_id, is_paused):
    session = Session()
    dag_model = session.query(DagModel).filter(DagModel.dag_id == dag_id).first()

    if dag_model:
        dag_model.is_paused = is_paused
        session.commit()
        logging.warning(f"DAG '{dag_id}' paused state updated to {is_paused}")
    else:
        logging.warning(f"DAG '{dag_id}' not found in the database.")
    session.close()


# Define a Python function to disable/enable the DAG based on the most recent failed run
def toggle_dag_state(dag):
    dag_id = dag.dag_id

    recent_failed_run = (
        Session()
        .query(DagRun)
        .filter(DagRun.dag_id == dag_id)
        .order_by(DagRun.start_date.desc())
        .offset(1)  # Skip the first record
        .first()  # Get the second record
    )
    logging.warning(f"the Previous execution status is: {recent_failed_run.state}")
    if recent_failed_run.state == "running":
        logging.warning("previous execution in running")
        raise Exception

    if recent_failed_run.state == "failed":
        logging.warning(f"Found a recent failed run: {recent_failed_run.execution_date}")
        # If there's a failed DAG run, disable the DAG
        update_dag_paused_state(dag_id, True)
        raise Exception
    else:
        logging.warning("No recent failed runs found or the most recent run was not in a failed state.")
        # If there are no failed DAG runs or the most recent run is not failed, unpause the DAG
        update_dag_paused_state(dag_id, False)
    Session().commit()
