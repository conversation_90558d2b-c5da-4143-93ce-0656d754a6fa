{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_master_contracts'
        ) }}    
    {% if is_incremental() %}
        where last_updated_date > ((select max(last_updated_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    mcontracts.id as id,
    master_contract_number,
    center_id,
    territory_id,
    company_id,
    start_date,
    end_date,
    sale_date,
    consultant_id,
    cstates.name as state,
    cstatus.name as status,
    cancel_date,
    reason,
    modified_by_id,
    created_date,
    {{convert_to_local_timestamp('created_date','time_zone_id')}} as local_created_date,
    last_updated_date,
    {{convert_to_local_timestamp('last_updated_date','time_zone_id')}} as local_last_updated_date,
    cvalidationstate.name as current_validation_state
from ods_data as mcontracts
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_contract_states' ) }}
    ) as cstates on mcontracts.state = cstates.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_contract_statuses' ) }}
    ) as cstatus on mcontracts.status = cstatus.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_current_validation_state' ) }}
    ) as cvalidationstate on mcontracts.current_validation_state = cvalidationstate.id
    left join (
        select id,
            center_reference_id 
        from {{ ref( 'ods_cs_centers' ) }}
    ) as center on center.id = mcontracts.center_id
    Left Join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as TZ ON Center.center_reference_id = tz.center_reference_id