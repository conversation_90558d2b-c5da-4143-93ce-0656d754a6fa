import Packages

# Parsing command line arguments
Args = Packages.getResolvedOptions(Packages.sys.argv,
                                   ['Status', 'Stage', 'Operation', 'Territory', 'AccountId',
                                    'CycleId', 'CutoffDate', 'LoadType'])
Bucket = "facebook-ads-production"
S3 = Packages.CloudOperations.S3

# Assigning values from command line arguments to variables
Status = Args['Status']
Stage = Args['Stage']
Operation = Args['Operation']
Territory = Args['Territory']
CycleId = Args['CycleId']
CutoffDate = Args['CutoffDate']
LoadType = Args['LoadType']
AccountId = Args['AccountId']

Packages.logging.warning("LoadType:'%s'", format(LoadType))
Packages.logging.warning("CutoffDate:'%s'", format(CutoffDate))

# Creating a data extraction process request object
DataRequest = Packages.FacebookAdsFramework.Ads.DataExtractionProcess(Territory=Territory,
                                                                      AccountId=AccountId,
                                                                      CutoffDate=CutoffDate,
                                                                      LoadType=LoadType,
                                                                      Bucket=Bucket,
                                                                      CycleId=CycleId)
Packages.logging.warning("Summary:'%s'", format(DataRequest))

# Creating a dictionary containing log information
Logs = {
    "Status": 200,
    "Stage": int(Stage),
    "AccountId": AccountId,
    "CutoffDate": DataRequest['CutoffDate'],
    "LoadType": LoadType,
    "Operation": Operation,
    "Territory": Territory,
    "Summary": str(DataRequest),
    "CycleId": CycleId
}
Packages.logging.warning("Logs:'%s'", format(Logs))

# Writing the 'Logs' dictionary as a JSON file to S3 bucket
S3.WriteJsonFile(Bucket, f"Logs/{CycleId}/Stage{Stage}/{Territory}.json", Logs)
