version: 2

models:
  - name: dt_ga4_events
    description: "Data transformation layer for GA4 events"
    tests:
                
      - dbt_utils.expression_is_true:
          expression: "event_timestamp <= current_timestamp"
          severity: warn
      
      - dbt_utils.expression_is_true:
          expression: "local_event_timestamp <= current_timestamp"
          severity: warn

    columns:
      - name: dbt_unique_id
        description: "Unique identifier for each event record"
        tests:
          - unique:
              severity: warn
          - not_null:
              severity: warn

      - name: event_date
        description: "Date of the event"
        tests:
          - not_null:
              severity: warn

      - name: event_timestamp
        description: "UTC timestamp of the event"
        tests:
          - not_null:
              severity: error

      - name: local_event_timestamp
        description: "Local timestamp based on center timezone"
        tests:
          - not_null:
              severity: error

      - name: event_name
        description: "Name of the GA4 event"
        tests:
          - not_null:
              severity: warn

      - name: user_id
        description: "ID of the user triggering the event"
        tests:
          - not_null:
              severity: warn

      - name: event_params
        description: "Event parameters array"
        tests:
          - not_null:
              severity: warn

      - name: user_properties
        description: "User properties array"
        tests:
          - not_null:
             severity: warn
