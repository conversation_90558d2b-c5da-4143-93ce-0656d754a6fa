version: 2

models:
  - name: dim_contracts
    columns:
      - name: contract_id
        tests:
          - unique:
              severity: warn
          - not_null:
              severity: error
      - name: center_id
        tests:
          - not_null:
              severity: error
      - name: contract_created_date
        tests:
          - not_null:
              severity: error
      - name: contract_start_date
        tests:
          - not_null:
              severity: error
      - name: contract_end_date
        tests:
          - not_null:
              severity: error
      - name: location
        tests:
          - accepted_values:
              values: ['incenter', 'outcenter']
              severity: error
      - name: product_type
        tests:
          - accepted_values:
              values: ['core course', 'market leader', 'test prep', 'other', 'business partner', 'd2c']
              severity: error
      - name: start_level
        tests:
          - not_null:
              severity: error
      - name: end_level
        tests:
          - not_null:
              severity: error
      - name: vip_flag
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: b2b_flag
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: promotional_flag
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: teen_flag
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: membership_flag
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: self_booking_access_flag
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error