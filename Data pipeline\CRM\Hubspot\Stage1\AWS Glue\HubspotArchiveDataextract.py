import sys
import ast
import timeit
import datetime
import logging
import pandas as pd
from pyspark import SparkConf, SparkContext
from pyspark.sql import SparkSession
from awsglue.context import GlueContext
from awsglue.transforms import *
from awsglue.job import Job
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, TimestampType

# Import required modules and classes
import OwnersDataExtract
import LogFileGeneration
import PostApiCall
import NullDataFix
import LoggingStatements
import DataframeModule


# Get configuration information from command-line arguments
from awsglue.utils import getResolvedOptions

ConfigInfo = getResolvedOptions(sys.argv, [
    'Stage', 'Territory', 'Object', 'Status', 'Properties', 'Operation',
    'Filter', 'Url', 'CutoffDate', 'DefaultProperties', 'Bucket'
])
Properties = ast.literal_eval(ConfigInfo['Properties'])
DefaultProperties = ast.literal_eval(ConfigInfo['DefaultProperties'])

# Print configuration information
logging.warning("Configinfo:'%s'", format(ConfigInfo))
logging.warning("properties:'%s'", format(Properties))
logging.warning("DefaultProperties:'%s'", format(DefaultProperties))


# Create Spark configuration and context
conf = SparkConf().setMaster("local").setAppName("My app")
sc = SparkContext.getOrCreate(conf=conf)
spark = SparkSession(sc)
glueContext = GlueContext(sc)
job = Job(glueContext)

starttime = timeit.default_timer()
# Initialize connections and instances
OwnersConnect = OwnersDataExtract.OwnersDataFetch
APICall = PostApiCall
Logs = LogFileGeneration.LogFile
NullDatafixConnect = NullDataFix.NullDataFill
LogsStatementConnect = LoggingStatements.LogStatements
DataframeConnect = DataframeModule.DataFrameCreate
Bucket = ConfigInfo['Bucket']

# Print dynamic job information
TerritoryCode = ConfigInfo['Territory']
Object = ConfigInfo['Object']
CycleId = ConfigInfo['CycleId']
logging.warning("Object:'%s'", format(Object))
logging.warning("Cycleid : '%s'", format(CycleId))
logging.warning(TerritoryCode)

# Check the object type and operation
if Object in ['contacts', 'deals', 'owners', 'companies'] and ConfigInfo['Operation'] == 'data_extract_archive':
    try:
        # Fetch data based on the object type
        DataFetchObject = OwnersConnect.GetExtract(ConfigInfo, Bucket, Properties, DefaultProperties, CycleId, Object)
        logging.warning(DataFetchObject)

        # Count API calls and the number of processed records
        ApiCalls = APICall.ApiCount()
        logstatements = LogsStatementConnect.logs_statements(ApiCalls, DataFetchObject)
        if len(DataFetchObject) == 0:
            # Create a log file if no records are processed
            LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'], CutOffDate=ConfigInfo['CutoffDate'],
                                                   Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                                   RecordsProcessed=len(DataFetchObject), NoOfApiCall=ApiCalls,
                                                   CycleId=CycleId, Object=Object, Bucket=Bucket)
        else:

            # Create a DataFrame from the fetched data
            spark_df = DataframeConnect.dataframe_module(DataFetchObject, NullDatafixConnect, spark, Bucket, CycleId,
                                                         ConfigInfo, Object, TerritoryCode)

            # Create a log file with processed records
            LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'], CutOffDate=None,
                                                   Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                                   RecordsProcessed=len(DataFetchObject), NoOfApiCall=ApiCalls,
                                                   CycleId=CycleId, Object=Object, Bucket=Bucket)
    except Exception as error:
        raise error
