{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('startdate') }} as startdate,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        studyplantype,
        showcurrentunitbannerflag,
        showcurrentlevelpopupflag,
        isfirstlessonpopupshown,
        id,
        userid,
        categoryid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'studyplanner'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    startdate as start_date,
    created,
    lastupdated as last_updated,
    studyplantype as study_plan_type,
    showcurrentunitbannerflag as show_current_unit_banner_flag,
    showcurrentlevelpopupflag as show_current_level_popup_flag,
    isfirstlessonpopupshown as is_first_lesson_popup_shown,
    id,
    userid as user_id,
    categoryid as category_id
FROM
    rankedrecords
WHERE
    rn = 1;
