{"Stage1": [{"Territory": "US", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "lastmodifieddate", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "CH", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "lastmodifieddate", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "DO", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "lastmodifieddate", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "SA", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "lastmodifieddate", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "PE", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "lastmodifieddate", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "ES", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "lastmodifieddate", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "CL", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "AR", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "EC", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "IT", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "DE", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "DZ", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "TN", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "UY", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "MN", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "MX", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "CO", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "KZ", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "KR", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "LA", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "MA", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "LY", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "TR", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/OwnerProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "US", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "CH", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "EC", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "CL", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "AR", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "CO", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "IT", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "MN", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "MX", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "DE", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "DZ", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "KZ", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "DO", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "ES", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "PE", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "SA", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "TN", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "UY", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "KR", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "LA", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "MA", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "LY", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "TR", "Object": "associationdeals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/DealProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsDealsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "US", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "CH", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "EC", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "CL", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "AR", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "CO", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "IT", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "MN", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "MX", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "DE", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "DZ", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "KZ", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "DO", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "ES", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "PE", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "SA", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "TN", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "UY", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "KR", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "LA", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "MA", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "LY", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "TR", "Object": "associationcontacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ContactsProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "DE", "Object": "associationcompanies", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/CompaniesProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/AssociationsCompaniesUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid"]}, {"Territory": "ES", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "ES", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "ES", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "CH", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveSwissContactsProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "CH", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveSwissDealsProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "CH", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "US", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": "None", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "US", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": "None", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "US", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "EC", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "EC", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "EC", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "CL", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "CL", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "CL", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "AR", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "AR", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "AR", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "CO", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "CO", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archivedAt", "archived", "cycleid"]}, {"Territory": "CO", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "MN", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "MN", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "MN", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "PE", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "PE", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "PE", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "SA", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "SA", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "SA", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "DO", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "DO", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "DO", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "TN", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "TN", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "TN", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "IT", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "IT", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "IT", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "MX", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "MX", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "MX", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "KZ", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "KZ", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "KZ", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "UY", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "UY", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "UY", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "DE", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "DE", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "DE", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "DZ", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "DZ", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "DZ", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "KR", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "KR", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "KR", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "LA", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "LA", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "LA", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "MA", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "MA", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "MA", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "LY", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "LY", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "LY", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "TR", "Object": "contacts", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveContactsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "TR", "Object": "deals", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/ArchiveDealsUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "TR", "Object": "owners", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveProperties"}, "Operation": "data_extract_archive", "Filter": "None", "Url": {"$ref": "#/OwnersUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "DE", "Object": "companies", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/ArchiveCompaniesProperties"}, "Operation": "data_extract_archive", "Filter": "hs_lastmodifieddate", "Url": {"$ref": "#/ArchiveCompaniesUrl"}, "CutoffDate": null, "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "archivedAt", "cycleid"]}, {"Territory": "AR", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "CL", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "CO", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "DE", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "DO", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "CH", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "DZ", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "EC", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "ES", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "IT", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "KR", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "KZ", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "LA", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "MA", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "MN", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "MX", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "PE", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "SA", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "US", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "UY", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "TN", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "LY", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}, {"Territory": "TR", "Object": "salespipeline", "Status": 404, "Stage": 1, "Properties": {"$ref": "#/PipelineStageProperties"}, "Operation": "data_extract", "Filter": "None", "Url": {"$ref": "#/SalesPipelineUrl"}, "CutoffDate": "null", "DefaultProperties": ["id", "createdAt", "updatedAt", "territory_code", "archived", "cycleid", "label"]}], "ContactsProperties": ["actualstatus__c", "address"], "DealProperties": ["dealid", "isdeleted"], "OwnerProperties": ["id", "createdAt", "updatedAt", "email", "firstName", "lastName", "userId", "archived", "id_0_teams", "name_0_teams", "id_1_teams", "name_1_teams", "primary_1_teams", "id_2_teams", "name_2_teams", "primary_2_teams", "id_3_teams", "name_3_teams", "primary_3_teams", "id_4_teams", "name_4_teams", "primary_4_teams", "territory_code"], "CompaniesProperties": ["annualrevenue", "booked_date", "domain", "hs_object_id", "name", "hubspot_owner_id", "company_source", "contacted_date", "contract_date", "createdate", "create_deal", "hs_date_entered_customer", "hs_date_entered_evangelist", "hs_date_entered_lead", "hs_date_entered_marketingqualifiedlead", "hs_date_entered_opportunity", "hs_date_entered_other", "hs_date_entered_salesqualifiedlead", "hs_date_entered_subscriber", "decision_maker_date", "hubspot_team_id", "industry", "lead_date", "lifecyclestage", "lost_date", "mql_date", "hs_createdate", "hs_analytics_source_data_1", "hs_analytics_source_data_2", "hs_analytics_source", "potential", "qualifying_stage_company", "segment", "show_date", "state", "value_per_employee", "hs_lastmodifieddate"], "AssociationCompaniesProperties": ["hs_object_id", "name", "hubspot_owner_id", "contract_date", "createdate", "hubspot_team_id", "hs_lastmodifieddate"], "AssociationContactsProperties": ["hs_object_id", "createdate", "lastmodifieddate"], "CustomObjectProperties": ["contract_category", "sign_date", "amount_for_sales___contract", "hs_object_id", "hs_createdate", "hs_lastmodifieddate", "name", "status_reason"], "ArchiveProperties": ["createdate", "email", "firstname", "hs_object_id", "lastmodifieddate", "lastname"], "ArchiveSwissContactsProperties": ["createdate", "brand_name", "center_name", "email", "firstname", "hs_object_id", "lastmodifieddate", "lastname"], "ArchiveSwissDealsProperties": ["createdate", "center_name", "email", "firstname", "hs_object_id", "lastmodifieddate", "lastname"], "ArchiveCompaniesProperties": ["createdate", "domain", "hs_lastmodifieddate", "hs_object_id", "name"], "AssociationProperties": ["amount", "closedate", "createdate", "dealname", "dealstage", "hs_lastmodifieddate", "hs_object_id", "pipeline"], "PipelineStageProperties": ["label", "metadata", "id", "createdAt", "updatedAt", "archived", "probability"], "ContactsUrl": "https://api.hubapi.com/crm/v3/objects/contacts/search", "DealsUrl": "https://api.hubapi.com/crm/v3/objects/deals/search", "OwnersUrl": "https://api.hubapi.com/crm/v3/owners", "AssociationsDealsUrl": "https://api.hubapi.com/crm/v3/objects/deals", "AssociationsCompaniesUrl": "https://api.hubapi.com/crm/v3/objects/companies", "AssociationsContactsUrl": "https://api.hubapi.com/crm/v3/objects/contacts", "ArchiveContactsUrl": "https://api.hubapi.com/crm/v3/objects/contacts", "ArchiveDealsUrl": "https://api.hubapi.com/crm/v3/objects/deals", "CompaniesUrl": "https://api.hubapi.com/crm/v3/objects/companies/search", "CustomObjectFranceUrl": "https://api.hubapi.com/crm/v3/objects/2-105740594", "ArchiveCompaniesUrl": "https://api.hubapi.com/crm/v3/objects/companies", "SalesPipelineUrl": "https://api.hubapi.com/crm/v3/pipelines/deals"}