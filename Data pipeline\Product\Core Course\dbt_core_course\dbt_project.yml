name: "dbt_core_course"
version: "1.0.0"
config-version: 2

profile: "dbt_core_course"

model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

clean-targets:
  - "target"
  - "dbt_packages"

models:
  dbt_core_course:
    data_transformation:
      learning_service:
        +materialized: table
        +schema: dt_learning_service
      contract_service:
        +materialized: table
        +schema: dt_contract_service
      center_configuration_service:
        +materialized: table
        +schema: dt_center_configuration_service
      idam_service:
        +materialized: table
        +schema: dt_idam_service
      schedule_and_booking_service:
        +schema: dt_schedule_and_booking_service
        +materialized: table
      digital_student_workbook_service:
        +schema: dt_digital_student_workbook_service
        +materialized: table
      prospect_service:
        +schema: dt_prospect_service
        +materialized: table
      conversation_ai_service:
        +materialized: table
        +schema: dt_conversation_ai_service
      novu_service:
        +materialized: table
        +schema: ods_novu_service
      speaking_ai_beta_service:
        +materialized: table
        +schema: dt_speaking_ai_beta_service
      google_analytics_service:
        +materialized: table
        +schema: dt_google_analytics_service
    ods:
      learning_service:
        +materialized: table
        +schema: ods_learning_service
      contract_service:
        +materialized: table
        +schema: ods_contract_service
      center_configuration_service:
        +materialized: table
        +schema: ods_center_configuration_service
      idam_service:
        +materialized: table
        +schema: ods_idam_service
      schedule_and_booking_service:
        +schema: ods_schedule_and_booking_service
        +materialized: table
      digital_student_workbook_service:
        +schema: ods_digital_student_workbook_service
        +materialized: table
      prospect_service:
        +schema: ods_prospect_service
        +materialized: table
      conversation_ai_service:
        +materialized: table
        +schema: ods_conversation_ai_service
      onboarding_service:
        +materialized: table
        +schema: ods_onboarding_service
      novu_service:
        +materialized: table
        +schema: ods_novu_service
      speaking_ai_beta_service:
        +materialized: table
        +schema: ods_speaking_ai_beta_service
      google_analytics_service:
        +materialized: table
        +schema: ods_google_analytics_service
    stage_analytics:
      +materialized: table
      +schema: stage_analytics
    analytics:
      +materialized: table
      +schema: analytics
    reporting:
      +materialized: table
      +schema: reporting
    one_off_load_scripts:
      +materialized: table
      +schema: stage_analytics
      
seeds:
  dbt_core_course:
    +schema: external_sources