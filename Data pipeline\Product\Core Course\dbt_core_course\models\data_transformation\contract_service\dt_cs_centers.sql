{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ref('ods_cs_centers') }}
)

SELECT {{etl_load_date()}},
    id,
    center_reference_id,
    name,
    territory_id,
    has_pilot,
    time_zone_id,
    has_digital_validation
from ods_data