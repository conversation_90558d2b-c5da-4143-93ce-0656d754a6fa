{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_dsw_review_activity'
        ) }}

    {% if is_incremental() %}
        where last_updated > ((select max(last_updated) from {{ this }}))
    {% endif %}
)

select 
    {{etl_load_date()}}
    ,review_activity.id
    ,review_activity.student_id
    ,course_contents.level as course_contents_level
    ,course_contents.unit as course_contents_unit
    ,course_contents.lesson as course_contents_lesson
    ,activity_reference_id
    ,activity_sequence
    ,activity_url
    ,is_completed
    ,duration
    ,reset_count
    ,CASE 
            when category_type = 1 Then 'communication'
            when category_type = 2 Then 'vocabulary'
            when category_type = 3 then 'grammar'
            ELSE CAST(category_type as varchar)
        end as category_type
    ,created
    ,last_updated
FROM 
    ods_data as review_activity
        Left Join (
                select id,
                    level,
                    unit,
                    lesson
                from {{ref('ods_dsw_course_contents')}}
        ) AS course_contents ON course_contents.id = review_activity.course_content_id
        Left Join (
                select ssds_id,
                    center_id
                from {{ref('ods_ls_user')}}
        ) AS user ON user.ssds_id = review_activity.student_id
        Left Join(
                select id,
                    reference_center_id
                from {{ref('ods_ls_center')}}
        ) as center ON user.center_id = center.id
        Left Join (
                select center_reference_id,
                    time_zone_id
                from {{ ref ('ods_cc_center') }}
    ) as tz on center.reference_center_id = tz.center_reference_id