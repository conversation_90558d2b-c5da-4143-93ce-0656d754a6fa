{{
    config(
        tags=["incremental","init","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}

    {{gsc_select("it","ita")}}
    {{source("gsc_it",'it')}}
    {{gsc_where("2023-04-01")}} 

    union all

    {{gsc_select("de","deu")}}
    {{source("gsc_de",'de')}}
    {{gsc_where("2023-04-01")}} 

    union all

    {{gsc_select("es","esp")}}
    {{source("gsc_es",'es')}}
    {{gsc_where("2023-04-01")}} 

    union all

    {{gsc_select("pe","per")}}
    {{source("gsc_pe",'pe')}}
    {{gsc_where("2023-04-01")}} 

    union all

    {{gsc_select("sa","sau")}}
    {{source("gsc_sa",'sa')}}
    {{gsc_where("2023-04-01")}} 


    union all

    {{gsc_select("mn","mng")}}
    {{source("gsc_mn",'mn')}}
    {{gsc_where("2023-04-01")}} 

    union all

    {{gsc_select("tn","tun")}}
    {{source("gsc_tn",'tn')}}
    {{gsc_where("2023-04-01")}} 

    union all

    {{gsc_select("dz","dza")}}
    {{source("gsc_dz",'dz')}}
    {{gsc_where("2023-04-01")}}

    union all

    {{gsc_select("com","")}}
    {{source("gsc_com",'com')}}
    {{gsc_where("2023-04-01")}}    

    union all

    {{gsc_select("mx","mex")}}
    {{source("gsc_mx",'mx')}}
    {{gsc_where("2023-04-01")}}   

    union all

    {{gsc_select("kz","kaz")}}
    {{source("gsc_kz",'kz')}}
    {{gsc_where("2023-04-01")}}      

    union all

    {{gsc_select("ve","ven")}}
    {{source("gsc_ve",'ve')}}
    {{gsc_where("2023-04-01")}}      

    union all

    {{gsc_select("vn","vnm")}}
    {{source("gsc_vn",'vn')}}
    {{gsc_where("2023-04-25")}}      

    union all

    {{gsc_select("ec","ecu")}}
    {{source("gsc_ec",'ec')}}
    {{gsc_where("2023-04-25")}}        

    union all

    {{gsc_select("pa","pan")}}
    {{source("gsc_pa",'pa')}}
    {{gsc_where("2023-04-25")}}         

    union all

    {{gsc_select("il","isr")}}
    {{source("gsc_il",'il')}}
    {{gsc_where("2023-07-25")}}             

    union all

    {{gsc_select("la","lao")}}
    {{source("gsc_la",'la')}}
    {{gsc_where("2023-12-22")}}    

    union all

    {{gsc_select("ma","mar")}}
    {{source("gsc_ma",'ma')}}
    {{gsc_where("2023-12-22")}}    