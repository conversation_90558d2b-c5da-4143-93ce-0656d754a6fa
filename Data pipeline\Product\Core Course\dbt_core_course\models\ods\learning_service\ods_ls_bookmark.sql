{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'dbt_unique_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
    (
            registrationid || cast(contentitemtypeid as varchar)
        ) as dbt_unique_id,
        contentitemtypeid,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        registrationid,
        contentitemid,
        centerid,
        ROW_NUMBER() over (
            PARTITION BY registrationid,contentitemtypeid
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'bookmark'
        ) }}
)
SELECT
    {{etl_load_date()}},
    dbt_unique_id,
    contentitemtypeid as content_item_type_id,
    created,
    lastupdated as last_updated,
    registrationid as registration_id,
    contentitemid as content_item_id,
    centerid as center_id
FROM
    rankedrecords
WHERE
    rn = 1;
