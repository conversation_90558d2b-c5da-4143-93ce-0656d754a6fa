{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ps_prospect') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT 
    {{etl_load_date()}},
    registered_on,
    show_test_result, 
    has_accepted_privacy_policy,
    settled_level,
    is_timeout, 
    test_completed_on,
    created,
    last_updated,
    placement_test_entry_point,
    id,
    first_name,
    last_name,
    email,
    phone_number,
    center_reference_id,
    company_id,
    source,
    student_reference_id,
    created_by,
    last_updated_by
from
    ods_data as prospect
