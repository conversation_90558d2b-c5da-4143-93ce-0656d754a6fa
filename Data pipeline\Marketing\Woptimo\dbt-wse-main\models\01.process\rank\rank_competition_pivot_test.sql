{{
    config(
        tags=["incremental","rank","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}

SELECT * FROM 
(SELECT query,
event_date,
location_name,
language_name,
rank_organic,
competitor_field 
FROM 
{{ ref('rank_competition_test') }}) 

PIVOT(min(rank_organic) FOR competitor_field in ('ef',"italki","babbel","amazingtalker","berlitz_corporation","rosetta_stone","british_council"))