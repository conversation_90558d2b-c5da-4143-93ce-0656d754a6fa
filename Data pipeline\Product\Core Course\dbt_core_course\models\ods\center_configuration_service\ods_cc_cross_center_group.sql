{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('created') }} as created,
        isactive,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        name,
        territoryid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_center_configuration_service',
            'crosscentergroup'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    created,
    isactive as is_active,
    lastupdated as last_updated,
    id,
    name,
    territoryid as territory_id
FROM
    rankedrecords
WHERE
    rn = 1;
