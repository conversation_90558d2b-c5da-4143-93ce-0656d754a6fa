import sys
import requests
import json
import time
import pandas as pd
import boto3
import datetime as dt
from datetime import datetime, timedelta
from google.oauth2 import service_account
from googleapiclient.discovery import build
from awsglue.utils import getResolvedOptions
import cloud_operations
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Print the version of the requests library
logging.info("Requests Version: %s", requests.__version__)

# Define constants
BUCKET_NAME = "etl-dev-gsc-extract"

# Retrieve arguments
args = getResolvedOptions(sys.argv, ['site_url', 'territory_input', 'load_date'])
site_url = args['site_url']
territory_code_input = args['territory_input']
load_date = args['load_date']

# Initialize S3 connection
s3_connect = cloud_operations.S3

def save_response_data(response, bucket_name, territory, current_date, file_suffix):
    """
    Save response data to S3 bucket.
    """
    all_data = [{
        'clicks': resp['clicks'],
        'impressions': resp['impressions'],
        'ctr': resp['ctr'],
        'position': resp['position'],
        'query': resp['keys'][3],
        'device': resp['keys'][2],
        'date': resp['keys'][1],
        'page': resp['keys'][4],
        'country': resp['keys'][0],
        'territory': territory
    } for resp in response]

    file_name = f'analytics_data_{file_suffix}_{territory}.json'
    file_path = f'raw_files/{current_date}/{territory}/{file_name}'
    s3_connect.WriteJsonFile(bucket_name, file_path, all_data)
    logging.info("File has been uploaded to the folder")

def json_serial(obj):
    """
    JSON serializer for objects not serializable by default json code.
    """
    if isinstance(obj, (datetime, dt.datetime)):
        return obj.isoformat()
    raise TypeError("Type %s not serializable" % type(obj))

def get_service_account_credentials(bucket_name, file_key):
    """
    Retrieve service account credentials from S3.
    """
    service_account_json = s3_connect.ReadJsonFile(bucket_name, file_key)
    return service_account.Credentials.from_service_account_info(service_account_json)

def query_athena(client, query, database, output_location):
    """
    Execute an Athena query and return the results.
    """
    response = client.start_query_execution(
        QueryString=query,
        QueryExecutionContext={'Database': database},
        ResultConfiguration={'OutputLocation': output_location}
    )

    query_execution_id = response['QueryExecutionId']
    state = 'QUEUED'

    while state in ['QUEUED', 'RUNNING']:
        response = client.get_query_execution(QueryExecutionId=query_execution_id)
        state = response['QueryExecution']['Status']['State']
        time.sleep(5)  # Check every 5 seconds until query finishes

    if state == 'SUCCEEDED':
        result_response = client.get_query_results(QueryExecutionId=query_execution_id)
        if 'Rows' in result_response['ResultSet']:
            columns = [col['Label'] for col in result_response['ResultSet']['ResultSetMetadata']['ColumnInfo']]
            data = [[field.get('VarCharValue', 'NULL') for field in row['Data']] for row in result_response['ResultSet']['Rows'][1:]]
            return pd.DataFrame(data, columns=columns)
        else:
            return pd.DataFrame()
    else:
        logging.error("Query failed with state: %s", state)
        return pd.DataFrame()

def fetch_incremental_data(client, territory_code_input, load_date, site_url):
    """
    Fetch incremental data from Athena and return relevant parameters.
    """
    query = f"""
        SELECT territory_code, MAX(data_date) AS max_date 
        FROM ods_marketing_gsc 
        WHERE territory_code = '{territory_code_input}' 
        GROUP BY territory_code
    """
    logging.info(query)

    df = query_athena(client, query, 'dev_ods_marketing_gsc', 's3://etl-dev-gsc-extract/read_test/')
    if df.empty:
        territory_code = territory_code_input
        incremental_date = load_date
        date_obj = datetime.strptime(incremental_date, '%Y-%m-%d')
    else:
        territory_code = df.iloc[0]['territory_code']
        incremental_date = df.iloc[0]['max_date']
        date_obj = datetime.strptime(incremental_date, '%Y-%m-%d') + timedelta(days=1)

    year = int(date_obj.strftime('%Y'))
    month = int(date_obj.strftime('%m'))
    day = int(date_obj.strftime('%d'))

    return territory_code, date_obj, year, month, day

def extract_incremental_data(webmasters_service, site_url, start_date, end_date, territory_code, bucket_name):
    """
    Extract incremental data from Google Search Console and save to S3.
    """
    row_limit = 25000

    while start_date <= end_date:
        start_row = 0
        all_rows = []

        while True:
            request = {
                'startDate': start_date.strftime('%Y-%m-%d'),
                'endDate': ((start_date + dt.timedelta(days=32)).replace(day=1) - dt.timedelta(days=1)).strftime('%Y-%m-%d'),
                'dimensions': ['country', 'date', 'device', 'query', 'page'],
                'rowLimit': row_limit,
                'startRow': start_row
            }
            response = webmasters_service.searchanalytics().query(siteUrl=site_url, body=request).execute()
            if 'rows' in response:
                all_rows.extend(response['rows'])
            if 'rows' not in response or len(response['rows']) < row_limit:
                break
            else:
                start_row += row_limit

        current_date = dt.date.today()
        file_suffix = start_date.strftime('%Y_%m')
        save_response_data(all_rows, bucket_name, territory_code, current_date, file_suffix)
        start_date = (start_date + dt.timedelta(days=32)).replace(day=1)

def main():
    """
    Main function to fetch incremental data and save it to S3.
    """
    # Load service account credentials
    credentials = get_service_account_credentials(BUCKET_NAME, 'service_account.json')

    # Build the Google Search Console service
    webmasters_service = build('searchconsole', 'v1', credentials=credentials)

    # Fetch incremental data parameters
    client = boto3.client('athena')
    territory_code, date_obj, year, month, day = fetch_incremental_data(client, territory_code_input, load_date, site_url)

    logging.info("territory_code: %s", territory_code)
    logging.info("incremental_date: %s", date_obj)
    logging.info("url: %s", site_url)
    logging.info("Year: %d, Month: %d, Day: %d", year, month, day)

    # Set start and end dates for data extraction
    start_date = datetime(year, month, day)
    end_date = datetime.now()

    logging.info("Extracting incremental data from %s to %s", start_date, end_date)

    # Extract incremental data and save to S3
    extract_incremental_data(webmasters_service, site_url, start_date, end_date, territory_code, BUCKET_NAME)

if __name__ == "__main__":
    main()
