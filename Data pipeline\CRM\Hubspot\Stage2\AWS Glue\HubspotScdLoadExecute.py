import sys
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark.context import SparkContext
from awsglue.context import GlueContext
from awsglue.job import Job
import Queries
import ast
import LogFileGeneration

from pyspark.sql.functions import from_unixtime, to_timestamp, when

from pyspark.sql import SparkSession

QueryConnect = Queries.Stage2Queries


spark = SparkSession.builder.appName("MultipleSQLStatements").getOrCreate()
import json
import cloud_operations
import Queries
import logging

s3_connect = cloud_operations.S3
QueryConnect = Queries.Stage2Queries
Logs = LogFileGeneration.LogFile


ConfigInfo = getResolvedOptions(sys.argv, ['ScdTable','Status','Stage','Function','Operation','TablePath','TempTableQuery','TempTable','TempTablePath','RawTable','Properties','OrderProperties', 'DistinctProperties', 'PartitionProperties', 'Condition', 'Bucket'])
Bucket = ConfigInfo['Bucket']
Executionjson = s3_connect.read_json_file(Bucket, 'ExecutionCheck.json')
Cycleid = Executionjson['CycleId']
Properties = ast.literal_eval(ConfigInfo['Properties'])
PropertiesTransformed = (','.join(str(values) for values in Properties))
ScdTable = ConfigInfo['ScdTable']
RawTable = ConfigInfo['RawTable']
OrderProperties = ConfigInfo['OrderProperties']
Function  = ConfigInfo['Function']
DistinctProperties = ConfigInfo['DistinctProperties']
PartitionProperties = ConfigInfo['PartitionProperties']
Condition = ConfigInfo['Condition']
Query = QueryConnect.scdqueries(ScdTable=ScdTable, cycleid=Cycleid, properties=PropertiesTransformed, function=Function,RawTable=RawTable, Orderproperties=OrderProperties, DistinctProperties=DistinctProperties, PartitionProperties=PartitionProperties, Condition=Condition)
logging.warning(Query)
df = spark.sql(Query)
TempTable = ConfigInfo['TempTable']
TempTablePath = ConfigInfo['TempTablePath']
ScdTablePath = ConfigInfo['TablePath']
if Function in ['associationsupdated', 'contacts_updated'] :
    df.write.mode("overwrite").saveAsTable(ScdTable,path=ScdTablePath)
    logging.warning("scd tables created successfully")
    LogFile = Logs.ScdLogFile(ScdTable=ScdTable, Status=200,
                                               Stage=ConfigInfo['Stage'],
                                               Operation=ConfigInfo['Operation'], TablePath=ScdTablePath,
                                               CycleId=Cycleid, Bucket=Bucket)
    logging.warning("Log file has been generated")
else:
    df.write.mode("overwrite").saveAsTable(TempTable,path=TempTablePath)
    TempQuery = ConfigInfo['TempTableQuery']
    df_temp=spark.sql(TempQuery)
    ScdTablePath = ConfigInfo['TablePath']
    df_temp.write.mode("overwrite").saveAsTable(ScdTable,path=ScdTablePath)
    logging.warning("scd tables created successfully")
    LogFile = Logs.ScdLogFile(ScdTable=ScdTable, Status=200,
                                                   Stage=ConfigInfo['Stage'],
                                                   Operation=ConfigInfo['Operation'], TablePath=ScdTablePath,
                                                   CycleId=Cycleid, Bucket=Bucket)
    logging.warning("Log file has been generated")
