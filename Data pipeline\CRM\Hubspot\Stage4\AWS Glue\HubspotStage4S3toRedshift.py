import sys
from awsglue.utils import getResolvedOptions
import  logging

# Import necessary modules

# Insert the path for the required libraries
sys.path.insert(0, '/glue/lib/installation')

# Remove previously loaded 'boto' modules to ensure fresh import
keys = [k for k in sys.modules.keys() if 'boto' in k]
for k in keys:
    if 'boto' in k:
        del sys.modules[k]

# Import necessary modules

# Import CloudOperations module
import CloudOperations

# Import DbOperations module
import DbOperations

# Import LogFileGeneration module
import LogFileGeneration

# Access the LogFile class from LogFileGeneration module
logs = LogFileGeneration.LogFile

# Access the S3 class from CloudOperations module
s3_connect = CloudOperations.S3

# Access the Database class from DbOperations module
redshift_execute = DbOperations.Database

# Get the resolved options and configuration information
config_info = getResolvedOptions(sys.argv, ['Object', 'Status', 'Stage', 'Operation', 'Table', 'Filepath', 'Bucket'])

# Define the S3 bucket name
bucket = config_info['Bucket']

# Read the execution check JSON file from the S3 bucket
execution_json = s3_connect.ReadJsonFile(bucket, 'ExecutionCheck.json')

# Extract the cycle ID from the execution check
cycle_id = execution_json['CycleId']
table_name = config_info['Table']
file_path = config_info['Filepath']

# Generate the prerequisite query to truncate the table
pre_requisite_query = """TRUNCATE TABLE {} """.format(table_name)

# Define the statement for truncating the table
truncate_statement = "truncating the table " + table_name + " before copying the latest data"

# Execute the truncate query
truncate_execute = redshift_execute.Execution('WriteTable', pre_requisite_query, truncate_statement)

# Define the statement name for the copy command
statement_name = "This query is enrichment copy of table " + table_name + " to redshift"

# Generate the copy command query to load data from S3 to Redshift
copy_command_query = """COPY {}
FROM '{}'
IAM_ROLE 'arn:aws:iam::262158335980:role/RedshitS3access'
PARQUET;""".format(table_name, file_path)

# Print the copy command query
logging.warning(copy_command_query)

# Execute the copy command query
copy_execute = redshift_execute.Execution('WriteTable', copy_command_query, statement_name)
logging.warning(copy_execute)

# Print a success message for the data enrichment copy
logging.warning("Data enrichment copy from S3 to Redshift completed for table '%s'", table_name)

# Generate a log file for the Redshift enrichment copy
log_file = logs.RedshiftEnrichedCopy(
    Filepath=file_path,
    Object=config_info['Object'],
    Operation=config_info['Operation'],
    Stage=config_info['Stage'],
    Status=200,
    Table=table_name,
    Cycleid=cycle_id,
    Bucket=bucket
)
logging.warning("Log file has been generated")
