{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH placement_test_levels as (
    select 
        id,
        placement_test_content_id,
        "order",
        is_active
    from {{ ref("dt_ls_placement_test_levels") }}
    union
    select 
        id,
        placement_test_content_id,
        "order",
        is_active
    from {{ ref("dt_ps_placement_test_levels") }}

),
placement_test_activity as (
    select 
        id,
        placement_test_level_id,
        title,
        duration,
        is_active 
    from {{ref("dt_ls_placement_test_activity")}}
    union
    select 
        id,
        placement_test_level_id,
        title,
        duration,
        is_active 
    from {{ref("dt_ps_placement_test_activity")}}
),
placement_test_interaction as(
    select 
        id,
        placement_test_activity_id,
        "order",
        "type",
        is_active 
    from {{ref("dt_ls_placement_test_interaction")}}
    union
    select 
        id,
        placement_test_activity_id,
        "order",
        "type",
        is_active 
    from {{ref("dt_ps_placement_test_interaction")}}

),
placement_test_result as (
    select 
        id,
        prospect_id,
        created,
        last_updated,
        interaction_id,
        score
        -- placement_test_interaction_result_id 
    from {{ref("dt_ls_placement_test_result")}}
    union
    select 
        id,
        prospect_id,
        created,
        last_updated,
        interaction_id,
        score
        -- placement_test_interaction_result_id -- removed from application table
    from {{ref("dt_ps_placement_test_result")}}
),
aggregated_test_score as (
    select
        ptr.prospect_id,
        ptr.created as created_at,
        ptr.last_updated as updated_at,
        ptl."order" as level_number,
        -- ptl.candos as level_can_dos, # if needed by business we can un-comment
        pta.title as activity_title,
        -- pta.url as activity_url, # if needed by business we can un-comment
        pta.duration as activity_duration,
        pti."order" as activity_interaction_row_number,
        pti."type" as interaction_type,
        -- pti.url as interaction_url, #if needed by business we can un-comment
        ROW_NUMBER() OVER ( PARTITION BY ptr.prospect_id ORDER BY ptr.created  ASC) AS test_row_number,
        ptr.score as result_score,
        ptl.is_active as level_is_active,
        pta.is_active as activity_is_active,
        -- pta.is_skippable as activity_is_skippable, -- removed from application table
        pti.is_active as interaction_is_active,
        -- Foreign keys that dimension tables are not in analytics layer
        ptl.id as level_id,
        ptl.placement_test_content_id as content_id,
        pta.id as activity_id,
        pti.id as interaction_id,
        ptr.id as result_id
        -- ptr.placement_test_interaction_result_id as interaction_result_id -- removed from application table

    from placement_test_levels as ptl
        left join placement_test_activity as pta
                on ptl.id =pta.placement_test_level_id
        left join placement_test_interaction as pti
                on pta.id =pti.placement_test_activity_id
        left join placement_test_result  as ptr
            on pti.id =ptr.interaction_id
)

select 
prospect_id,
created_at,
updated_at,
level_number,
activity_title,
activity_duration,
activity_interaction_row_number,
interaction_type,
test_row_number,
result_score,
level_is_active,
activity_is_active,
-- activity_is_skippable, -- removed from application table
interaction_is_active,
level_id,
content_id,
activity_id,
interaction_id,
result_id
-- interaction_result_id -- removed from application table
from aggregated_test_score