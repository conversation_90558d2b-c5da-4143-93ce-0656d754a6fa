{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        score,
        {{ cast_to_timestamp('datecompleted') }} as datecompleted,
        {{ cast_to_timestamp('assignmentdate') }} as assignmentdate,
        istimeout,
        {{ cast_to_timestamp('startdate') }} as startdate,
        timeremaining,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        comment,
        status,
        prospectid,
        settledlevelid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'prospectgradebook'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    score,
    datecompleted as date_completed,
    assignmentdate  as assignment_date,
    istimeout as is_time_out,
    startdate as start_date,
    timeremaining as time_remaining,
    created,
    lastupdated as last_updated,
    id,
    comment,
    status,
    prospectid as prospect_id,
    settledlevelid as settled_level_id
FROM
    rankedrecords
WHERE
    rn = 1;
