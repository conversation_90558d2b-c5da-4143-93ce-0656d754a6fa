create table if not exists sales_planning_tool.planning_tool
(
    CycleId   VARCHAR(15),
    Month     date,
    Source    VARCHAR(64),
    Leads     VARCHAR(24),
    "MQL / Leads"  VARCHAR(24),
    MQL    VARCHAR(24),
    "Contacted / Leads"   VARCHAR(24),
    Contacted   VARCHAR(24),
    "Booked / Contacted"   VARCHAR(24),
    Booked    VARCHAR(24),
    "Shows / Booked"    VARCHAR(24),
    Shows    VARCHAR(24),
    "Closing Rate"    VARCHAR(24),
    "Closing Ratio on Shows"    VARCHAR(24),
    Contracts   VARCHAR(24),
    "Contracts / Leads"   VARCHAR(24),
    "Closing Ratio on Leads"   VARCHAR(24),
    "Average Contract Price"   VARCHAR(24),
    Sales   VARCHAR(24),
    "Individual Target"  VARCHAR(24),
    "vs Individual Target"   VARCHAR(24),
    "B2B Target"     VARCHAR(24),
    "Working days"    VARCHAR(24),
    "Daily booked"     VARCHAR(24),
    "Daily Shows"    VARCHAR(24),
    "Daily contracts"   VARCHAR(24),
    "Daily Sales"       VARCHAR(24),
    Territory         VARCHAR(5),
    ReferenceCenterId   VARCHAR(24),
    TerritoryReferenceId  VARCHAR(24)
);
