version: 2

sources:

  - name: stage_prospect_service
    description: >
      Source data from the Prospect Service which manages potential student information,
      placement test results, and related activities for prospective students.
    database: awsdatacatalog
    schema: stg_prospect_service
    tables:
      - name: prospect
        description: Information about prospective students including contact details and test status.
        columns:
          - name: id
            description: Primary key for the prospect record
          - name: registeredon
            description: Timestamp when the prospect registered
          - name: showtestresult
            description: Boolean flag indicating if test results should be shown to the prospect
          - name: hasacceptedprivacypolicy
            description: Boolean flag indicating if the prospect has accepted the privacy policy
          - name: settledlevel
            description: Settled language level for the prospect
          - name: istimeout
            description: Boolean flag indicating if the test timed out
          - name: testcompletedon
            description: Timestamp when the test was completed
          - name: created
            description: Timestamp when the record was created
          - name: lastupdated
            description: Timestamp when the record was last updated
          - name: placementtestentrypoint
            description: Entry point for the placement test
          - name: firstname
            description: First name of the prospect
          - name: lastname
            description: Last name of the prospect
          - name: email
            description: Email address of the prospect
          - name: phonenumber
            description: Phone number of the prospect
          - name: centerreferenceid
            description: Reference ID for the center
          - name: companyid
            description: ID of the company if applicable
          - name: source
            description: Source of the prospect record
          - name: studentreferenceid
            description: Reference ID if the prospect becomes a student
          - name: createdby
            description: ID of the user who created the record
          - name: lastupdatedby
            description: ID of the user who last updated the record

      - name: offlinetestresults
        description: Results from offline tests taken by prospects.
        columns:
          - name: id
            description: Primary key for the offline test result
          - name: prospectid
            description: Foreign key reference to the prospect
          - name: score
            description: Score achieved on the offline test
          - name: level
            description: Level determined by the offline test
          - name: created
            description: Timestamp when the record was created
          - name: lastupdated
            description: Timestamp when the record was last updated

      - name: prospectgradebook
        description: Gradebook records for prospect tests and assessments.
        columns:
          - name: id
            description: Primary key for the gradebook record
          - name: prospectid
            description: Foreign key reference to the prospect
          - name: testcompletedon
            description: Timestamp when the test was completed
          - name: istimeout
            description: Boolean flag indicating if the test timed out
          - name: teststartedon
            description: Timestamp when the test was started
          - name: timeremaining
            description: Time remaining when the test ended
          - name: created
            description: Timestamp when the record was created
          - name: createdby
            description: ID of the user who created the record
          - name: lastupdated
            description: Timestamp when the record was last updated
          - name: lastupdatedby
            description: ID of the user who last updated the record
          - name: nextactivityid
            description: ID of the next activity in the sequence
          - name: status
            description: Status of the gradebook record (0=initialized, 1=in_progress, 2=final)
          - name: scoremode
            description: Mode used for scoring
          - name: settledlevel
            description: Settled language level from the test

      - name: preplacementtestresult
        description: Results from pre-placement tests that determine initial level assessment.
        columns:
          - name: id
            description: Primary key for the pre-placement test result
          - name: prospectid
            description: Foreign key reference to the prospect
          - name: startlevel
            description: Starting level determined by the pre-placement test
          - name: result
            description: Result of the pre-placement test
          - name: preplacementtestactivityid
            description: ID of the pre-placement test activity
          - name: preplacementtestinteractionid
            description: ID of the pre-placement test interaction
          - name: created
            description: Timestamp when the record was created
          - name: createdby
            description: ID of the user who created the record
          - name: lastupdated
            description: Timestamp when the record was last updated
          - name: lastupdatedby
            description: ID of the user who last updated the record

      - name: placementtestresult
        description: Results from placement tests that determine final level placement.
        columns:
          - name: id
            description: Primary key for the placement test result
          - name: prospectid
            description: Foreign key reference to the prospect
          - name: interactionid
            description: ID of the interaction during the test
          - name: score
            description: Score achieved on the placement test
          - name: result
            description: Result of the placement test
          - name: created
            description: Timestamp when the record was created
          - name: createdby
            description: ID of the user who created the record
          - name: lastupdated
            description: Timestamp when the record was last updated
          - name: lastupdatedby
            description: ID of the user who last updated the record

      - name: placementtestinteraction
        description: Interactions during placement tests, tracking user responses and progress.
        columns:
          - name: id
            description: Primary key for the placement test interaction
          - name: prospectid
            description: Foreign key reference to the prospect
          - name: placementtestactivityid
            description: ID of the placement test activity
          - name: answer
            description: Answer provided by the prospect
          - name: created
            description: Timestamp when the record was created
          - name: createdby
            description: ID of the user who created the record
          - name: lastupdated
            description: Timestamp when the record was last updated
          - name: lastupdatedby
            description: ID of the user who last updated the record

      - name: placementtestactivity
        description: Activities that make up placement tests, including questions and exercises.
        columns:
          - name: id
            description: Primary key for the placement test activity
          - name: placementtestlevelid
            description: ID of the placement test level
          - name: title
            description: Title of the activity
          - name: url
            description: URL for the activity content
          - name: duration
            description: Duration of the activity in seconds
          - name: isactive
            description: Boolean flag indicating if the activity is active
          - name: created
            description: Timestamp when the record was created
          - name: lastupdated
            description: Timestamp when the record was last updated

      - name: placementtestlevels
        description: Levels used in placement tests to categorize language proficiency.
        columns:
          - name: id
            description: Primary key for the placement test level
          - name: name
            description: Name of the level
          - name: description
            description: Description of the level
          - name: sequence
            description: Sequence order of the level
          - name: isactive
            description: Boolean flag indicating if the level is active
          - name: created
            description: Timestamp when the record was created
          - name: lastupdated
            description: Timestamp when the record was last updated
