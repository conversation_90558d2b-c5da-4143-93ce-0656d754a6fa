{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_cc_center') }}
)
SELECT 
    {{etl_load_date()}},
    created_date,
    Case
        When first_day_of_the_week = 0 Then 'sunday'
        When first_day_of_the_week = 1 Then 'monday'
        When first_day_of_the_week = 2 Then 'tuesday'
        When first_day_of_the_week = 3 Then 'wednesday'
        When first_day_of_the_week = 4 Then 'thursday'
        When first_day_of_the_week = 5 Then 'friday'
        When first_day_of_the_week = 6 Then 'saturday'
        Else cast(
            first_day_of_the_week as varchar
        )
    End As first_day_of_the_week,
    has_deluxe_service,
    has_vip_service,
    is_active,
    is_day_light_saving,
    is_online_center,
    is_twenty_four_hour_format,
    not_allow_pn_b_on_holidays,
    has_pilot,
    has_digital_validation,
    is_new_digital_work_book,
    is_survey_enabled,
    is_teens_enabled,
    has_d2c_product,
    id,
    address_id,
    center_reference_id,
    center_director_name,
    email,
    mobile,
    name,
    phone,
    territory_id,
    time_zone_id,
    navision_client
FROM
    ods_data as center
