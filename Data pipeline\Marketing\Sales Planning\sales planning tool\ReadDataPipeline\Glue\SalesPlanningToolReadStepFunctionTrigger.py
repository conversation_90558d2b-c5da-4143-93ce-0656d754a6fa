import CloudOperations
import logging

StepFunction = CloudOperations.StepFunction
ExecutionInput = "SalesPlanningToolRead"
StateMachineArn = 'arn:aws:states:eu-west-1:262158335980:stateMachine:SalesPlanningToolReadWorkFlow'
CheckStateMachine = StepFunction.CheckStepFunctionsRunning(StateMachineArn)
if len(CheckStateMachine['executions']) == 0:
    logging.warning("There is no running step function proceed to trigger the step function")
    StepFunction.StartStepFunction(ExecutionInput, StateMachineArn)
else:
    logging\
        .warning("There is currently step function in running status please verify for long run of step function")
