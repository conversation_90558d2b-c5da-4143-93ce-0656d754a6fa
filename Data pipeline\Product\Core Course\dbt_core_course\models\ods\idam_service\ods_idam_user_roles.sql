{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        id,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        isactive,
        userbasicinfoid,
        roleid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_idam_service',
            'userroles'
        ) }}
)
SELECT
    {{etl_load_date()}},
    id,
    created,
    lastupdated as last_updated,
    isactive as is_active,
    userbasicinfoid as user_basic_info_id,
    roleid as role_id
FROM
    rankedrecords
WHERE
    rn = 1;
