{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('createddate') }} as createddate,
        firstdayoftheweek,
        hasdeluxeservice,
        hasvipservice,
        isactive,
        isdaylightsaving,
        isonlinecenter,
        istwentyfourhourformat,
        notallowpnbonholidays,
        haspilot,
        hasdigitalvalidation,
        isnewdigitalworkbook,
        issurveyenabled,
        isteensenabled,
        hasd2cproduct,
        id,
        addressid,
        centerreferenceid,
        centerdirectorname,
        email,
        mobile,
        name,
        phone,
        territoryid,
        timezoneid,
        navisionclient,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                id
        ) AS rn
    FROM
        {{ source(
            'stage_center_configuration_service',
            'center'
        ) }}
)
SELECT
    {{etl_load_date()}},
    createddate as created_date,
    firstdayoftheweek as first_day_of_the_week,
    hasdeluxeservice as has_deluxe_service,
    hasvipservice as has_vip_service,
    isactive as is_active,
    isdaylightsaving as is_day_light_saving,
    isonlinecenter as is_online_center,
    istwentyfourhourformat as is_twenty_four_hour_format,
    notallowpnbonholidays as not_allow_pn_b_on_holidays,
    haspilot as has_pilot,
    hasdigitalvalidation as has_digital_validation,
    isnewdigitalworkbook as is_new_digital_work_book,
    issurveyenabled as is_survey_enabled,
    isteensenabled as is_teens_enabled,
    hasd2cproduct as has_d2c_product,
    id,
    addressid as address_id,
    centerreferenceid as center_reference_id,
    centerdirectorname as center_director_name,
    email,
    mobile,
    name,
    phone,
    territoryid as territory_id,
    timezoneid as time_zone_id,
    navisionclient as navision_client
FROM
    rankedrecords
WHERE
    rn = 1;
