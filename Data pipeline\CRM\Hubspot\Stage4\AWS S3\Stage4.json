{"Stage4": [{"Object": "contacts", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot.contactsenriched", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/Contacts_Dataenriched/"}, {"Object": "deals", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot.dealsenriched", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/Deals_Dataenriched/"}, {"Object": "owners", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot.ownersenriched", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/Owners_Dataenriched/"}, {"Object": "companies", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot.companiesenriched", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/Companies_Dataenriched/"}, {"Object": "teams", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot.teamsenriched", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/Teams_Dataenriched/"}, {"Object": "customobject", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot.customobjectenriched", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/Customobject_Dataenriched/"}, {"Object": "contactstodeals", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot.associationcontactstodeals", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/AssociationContactstoDealsEnriched/"}, {"Object": "contactstocompanies", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot.associationcontactstocompanies", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/AssociationContactstoCompaniesEnriched/"}, {"Object": "dealstocontacts", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot.associationdealstocontacts", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/AssociationDealstoContactsEnriched/"}, {"Object": "dealstocompanies", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot.associationdealstocompanies", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/AssociationDealstoCompaniesEnriched/"}, {"Object": "companiestocontacts", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot.associationcompaneistocontacts", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/AssociationCompaniestoContactsEnriched/"}, {"Object": "companiestodeals", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot.associationcompaneistodeals", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/AssociationCompaniesToDealsEnriched/"}, {"Object": "mergedcontacts", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot_crm.merged_contacts", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/MergedContacts/"}, {"Object": "mergeddeals", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot_crm.merged_deals", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/MergedDeals/"}, {"Object": "dealpipeline", "Status": 404, "Stage": 4, "Operation": "redshiftmigration", "Table": "hubspot_crm.sales_pipeline", "Filepath": "s3://etl-hubspot-prod/DataEnrichmentLayer/Salespipeline_Dataenriched/"}]}