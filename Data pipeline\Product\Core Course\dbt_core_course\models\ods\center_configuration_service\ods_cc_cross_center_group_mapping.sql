{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        isactive,
        id,
        centerid,
        crosscentergroupid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                id
        ) AS rn
    FROM
        {{ source(
            'stage_center_configuration_service',
            'crosscentergroupmapping'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    isactive as is_active,
    id,
    centerid as center_id,
    crosscentergroupid as cross_center_group_id
FROM
    rankedrecords
WHERE
    rn = 1;
