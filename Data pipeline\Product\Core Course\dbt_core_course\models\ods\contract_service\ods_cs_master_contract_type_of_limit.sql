{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        {{cast_to_int('id')}},
        limittype
    FROM 
        {{source('stage_contract_service', 'mastercontracttypeoflimit')}}
)

SELECT
    {{etl_load_date()}},
    {{cast_to_int('id')}},
    limittype as limit_type
FROM
    RankedRecords