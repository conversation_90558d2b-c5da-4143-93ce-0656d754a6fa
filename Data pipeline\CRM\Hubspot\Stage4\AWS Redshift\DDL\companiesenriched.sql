create table if not exists hubspot.companiesenriched
(
    annualrevenue                          double precision,
    booked_date                            timestamp encode az64,
    domain                                 varchar(128),
    hs_object_id                           bigint encode az64,
    name                                   varchar(248),
    hubspot_owner_id                       bigint encode az64,
    company_source                         varchar(64),
    contacted_date                         timestamp encode az64,
    contract_date                          timestamp encode az64,
    createdate                             timestamp encode az64,
    create_deal                            varchar(42),
    hs_date_entered_customer               timestamp encode az64,
    hs_date_entered_evangelist             timestamp encode az64,
    hs_date_entered_lead                   timestamp encode az64,
    hs_date_entered_marketingqualifiedlead timestamp encode az64,
    hs_date_entered_opportunity            timestamp encode az64,
    hs_date_entered_other                  timestamp encode az64,
    hs_date_entered_salesqualifiedlead     timestamp encode az64,
    hs_date_entered_subscriber             timestamp encode az64,
    decision_maker_date                    timestamp encode az64,
    hubspot_team_id                        bigint encode az64,
    industry                               varchar(48),
    lead_date                              date encode az64,
    lifecyclestage                         varchar(22),
    lost_date                              date encode az64,
    mql_date                               date encode az64,
    hs_createdate                          varchar(48),
    hs_analytics_source_data_1             varchar(64),
    hs_analytics_source_data_2             varchar(48),
    hs_analytics_source                    varchar(17),
    potential                              double precision,
    qualifying_stage_company               varchar(64),
    segment                                varchar(13),
    show_date                              date encode az64,
    state                                  varchar(64),
    value_per_employee                     varchar(22),
    hs_lastmodifieddate                    timestamp encode az64,
    id                                     bigint encode az64,
    createdat                              timestamp encode az64,
    updatedat                              timestamp encode az64,
    territory_code                         varchar(4),
    archived                               varchar(8),
    cycleid                                bigint encode az64,
    territory_name                         varchar(32),
    deleteflag                             varchar(2),
    archiveddate                           timestamp encode az64,
    restoreddate                           timestamp encode az64
);



