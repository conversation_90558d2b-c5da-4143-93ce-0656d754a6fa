{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        code,
        location,
        categorytype,
        hasdescription,
        acceptsstandby,
        hastobeprebooked,
        isactive,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        title,
        color,
        courseid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_schedule_and_booking_service',
            'classtype'
        ) }}
)
SELECT
    {{etl_load_date()}},
    code as code,
    location as location,
    categorytype as category_type,
    hasdescription as has_description,
    acceptsstandby as accepts_standby,
    hastobeprebooked as has_to_be_pre_booked,
    isactive as is_active,
    {{ cast_to_timestamp('created') }} as created,
    {{ cast_to_timestamp('lastupdated') }} as last_updated,
    id,
    title as title,
    color as color,
    courseid as course_id
FROM
    rankedrecords
WHERE
    rn = 1;
