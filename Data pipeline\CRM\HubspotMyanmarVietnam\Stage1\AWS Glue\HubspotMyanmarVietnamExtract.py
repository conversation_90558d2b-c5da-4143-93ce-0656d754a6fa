import sys
from awsglue.transforms import *
from awsglue.context import <PERSON>lue<PERSON>ontext
from awsglue.dynamicframe import <PERSON><PERSON><PERSON><PERSON><PERSON>, DynamicFrameReader, DynamicFrameWriter, DynamicFrameCollection
from awsglue.dynamicframe import DynamicFrame
from pyspark import SparkConf, SparkContext
from pyspark.sql import SparkSession
from awsglue.utils import getResolvedOptions
from pyspark.sql.functions import col, lit
import logging
import ast
# import s3fs
from Packages import date, s3_resource, datetime, pandas as pd
import LogFileGeneration

logs = LogFileGeneration.LogFile
# create spark configuration object
conf = SparkConf()
conf.setMaster("local").setAppName("My app")

# create spark context and sparksession
sc = SparkContext(conf=conf)
glueContext = GlueContext(sc)
# glueContext.getJobRun().setTimeout(3600)
spark = SparkSession(sc)
args = getResolvedOptions(sys.argv, ['JOB_NAME'])
job_run_id = args['JOB_RUN_ID']

config_info = getResolvedOptions(sys.argv,
                                 ['Filter', 'Object', 'Territory', 'Status', 'Stage', 'Properties', 'Operation',
                                  'FolderKey', 'PrefixValue', 'CycleId', 'source_bucket', 'destination_bucket'])

present_day = date.today()
year = present_day.year
month = present_day.strftime('%m')
day = present_day.strftime('%d')

bucket_name = config_info['source_bucket']
destination_bucket = config_info['destination_bucket']

bucket = s3_resource.Bucket(bucket_name)

prefix_filter = config_info['PrefixValue']
folder_key = config_info['FolderKey']
properties = ast.literal_eval(config_info['Properties'])
cycle_id = config_info['CycleId']
stage = config_info['Stage']

# for prefix_filter in prefix_list:
prefix_value = prefix_filter + f"{year}" + f"/month={month}" + f"/day={day}"
for file in bucket.objects.filter(Prefix=prefix_value):
    month_int = int(month)
    day_int = int(day)
    if (file.last_modified).replace(tzinfo=None) > datetime.datetime(year, month_int, day_int, tzinfo=None):
        if ('Vietnam/Association' not in file.key) and ('Vietnam/Deleted_records' not in file.key):
            if folder_key in file.key:
                logging.warning(file.key)
                file_path_split = file.key.split('/')
                logging.warning(file_path_split)
                table_keyword = file_path_split[2]
                logging.warning(table_keyword)
                territory_keyword = file_path_split[1]
                logging.warning(territory_keyword)
                df = spark.read.parquet("s3://" + bucket_name + f"/{file.key}")
                new_df = df.select("*", lit(config_info["CycleId"]).alias("CycleId"))
                new_df.show()
                df_reordered = new_df.select(*properties)
                logging.warning("Printing below the reordered dataframe")
                df_reordered.show()
                file_location = "s3://" + destination_bucket + f"/MyanmarVietnamRawFiles" + f"/{cycle_id}" + f"/Stage{stage}" + f"/{table_keyword}" + f"/{territory_keyword}"
                df_reordered.repartition(1).write.parquet(file_location)
                log_file = logs.LogFileGenerate(Status=200, Stage=config_info['Stage'],
                                                CutOffDate=None,
                                                Operation=config_info['Operation'], Territory=config_info['Territory'],
                                                RecordsProcessed=None, NoOfApiCall=None,
                                                CycleId=cycle_id, Object=config_info['Object'],
                                                Bucket=destination_bucket)

logging.warning("All file have been completed reading")