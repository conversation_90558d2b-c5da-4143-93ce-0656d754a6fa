{"Comment": "A description of my state machine", "StartAt": "ReadStage1ConfigFile", "States": {"ReadStage1ConfigFile": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:core_course_data_ingestion_input:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Stage1"}, "Stage1": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Glue StartJobRun", "States": {"Glue StartJobRun": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "core_course_data_ingestion", "Arguments": {"--Object.$": "$.Object", "--SecretManager.$": "$.SecretManager", "--LoadType.$": "$.LoadType", "--FilterColumn.$": "$.FilterColumn", "--DatabaseConnection.$": "$.DatabaseConnection", "--AthenaDatabase.$": "$.AthenaDatabase", "--OdsObject.$": "$.OdsObject", "--OdsDatabase.$": "$.OdsDatabase", "--HistDatabase.$": "$.HistDatabase"}}, "End": true}}}, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "FailCase"}], "ItemsPath": "$.Stage1", "MaxConcurrency": 25, "Next": "<PERSON><PERSON><PERSON>"}, "Parallel": {"Type": "<PERSON><PERSON><PERSON>", "Branches": [{"StartAt": "conversation_ai_data_ingestion", "States": {"conversation_ai_data_ingestion": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "conversation_ai_data_ingestion"}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "conversation_ai_data_ingestion fail case"}]}, "conversation_ai_data_ingestion fail case": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:core_course_data_ingestion_alert:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 1, "MaxAttempts": 3, "BackoffRate": 2, "JitterStrategy": "FULL"}], "End": true}}}, {"StartAt": "onboarding_data_ingestion", "States": {"onboarding_data_ingestion": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "onboarding_data_ingestion"}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "onboarding_data_ingestion fail case"}]}, "onboarding_data_ingestion fail case": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:core_course_data_ingestion_alert:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 1, "MaxAttempts": 3, "BackoffRate": 2, "JitterStrategy": "FULL"}], "End": true}}}], "End": true}, "FailCase": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:core_course_data_ingestion_alert:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "OutputPath": "$.Payload", "End": true}}}