import logging
import datetime
from datetime import date


class Tools:
    @staticmethod
    def GetMonthRanges(InputStartDate, InputEndDate):
        """this function with list of date ranges based the input start and end date"""
        CurrentDay = date.today()
        StartDate = datetime.datetime.strptime(InputStartDate, '%Y-%m-%d')
        EndDate = datetime.datetime.strptime(InputEndDate, '%Y-%m-%d')
        """input-checkpoint"""
        if EndDate < StartDate:
            logging.warning("Error. Start Date of {0} is greater than End Date of {1}".format(StartDate, EndDate))
            return None
        if EndDate.strftime("%Y-%m-%d") > CurrentDay.strftime("%Y-%m-%d"):
            logging.warning("Error. End Date of {0} is greater than Current Date of {1}".format(EndDate, CurrentDay))
            return None
        """list of dict date range"""
        DateRanges = []
        CurrentYear = StartDate.year
        CurrentMonth = StartDate.month
        while CurrentYear <= EndDate.year:
            NextMonth = CurrentMonth + 1
            NextYear = CurrentYear
            if NextMonth > 12:
                NextMonth = 1
                NextYear = CurrentYear + 1
            """start of month"""
            if StartDate.month == CurrentMonth and StartDate.year == CurrentYear:
                MonthStart = StartDate.strftime("%Y-%m-%d")
            else:
                MonthStart = datetime.datetime.strptime("{0}/{1}/01".format(CurrentYear, CurrentMonth),
                                                        "%Y/%m/%d").date()
            """start of next month"""
            MonthEnd = datetime.datetime.strptime("{0}/{1}/01".format(NextYear, NextMonth), "%Y/%m/%d").date()
            """start of next month less one day"""
            if EndDate.month == CurrentMonth and EndDate.year == CurrentYear:
                MonthEnd = EndDate.strftime("%Y-%m-%d")
            else:
                MonthEnd = MonthEnd + datetime.timedelta(days=-1)
            FilterRange = {"FromDate": str(MonthStart), "ToDate": str(MonthEnd)}
            DateRanges.append(FilterRange)
            """condition to change next year/month """
            if CurrentMonth == 12:
                CurrentMonth = 1
                CurrentYear += 1
            else:
                CurrentMonth += 1
            """to stop the loop based on end date"""
            if CurrentYear == EndDate.year and CurrentMonth > EndDate.month:
                break
        return DateRanges
