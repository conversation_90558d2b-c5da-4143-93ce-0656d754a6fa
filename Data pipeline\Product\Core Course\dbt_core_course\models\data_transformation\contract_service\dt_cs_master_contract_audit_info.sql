{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_master_contract_audit_info'
        ) }}
    {% if is_incremental() %}
        where created_date > ((select max(created_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    mcauditinfo.id as id,
    mcauditinfo.master_contract_id as master_contract_id,
    cauditfields.field_name as modified_field,
    mcauditinfo.previous_value as previous_value,
    mcauditinfo.present_value as present_value,
    cchange_types.name as change_type,
    mcauditinfo.reason as reason,
    mcauditinfo.modified_by_id as modified_by_id,
    mcauditinfo.created_date as created_date,
    {{convert_to_local_timestamp('mcauditinfo.created_date','time_zone_id')}} as local_created_date,
    mcauditinfo.effective_date as effective_date,
    {{convert_to_local_timestamp('mcauditinfo.effective_date','time_zone_id')}} as local_effective_date
from ods_data as mcauditinfo
    left join (
        select id,
            field_name
        from {{ ref( 'ods_cs_contract_audit_fields' ) }}
    ) as cauditfields on mcauditinfo.modified_field_id = cauditfields.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_contract_change_types' ) }}
    ) as cchange_types on mcauditinfo.change_type = cchange_types.id
    left join (
        select id,
            center_id
        from {{ ref( 'ods_cs_master_contracts' ) }}
    ) as mcontracts on mcauditinfo.master_contract_id = mcontracts.id
    left join (
        select id,
            center_reference_id 
        from {{ ref( 'ods_cs_centers' ) }}
    ) as center on center.id = mcontracts.center_id
    left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on center.center_reference_id = tz.center_reference_id