{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}
 
 SELECT 
    bookmark.registration_id
    ,registration.user_id as student_reference_id
    ,registration.contract_id as contract_reference_id
    ,bookmark.content_item_id
    ,bookmark.content_item_type_id 
    ,bookmark.content_item_type 
    ,bookmark.content_item as description
    ,category."path"
    ,CASE
            WHEN LENGTH(
                REGEXP_REPLACE(SPLIT_PART(category."path", '.', 1), '[^0-9]', '')
            ) = 0 THEN NULL
            ELSE REGEXP_REPLACE(SPLIT_PART(category."path", '.', 1), '[^0-9]', '')
        END AS level,
        CASE
            WHEN LENGTH(
                REGEXP_REPLACE(SPLIT_PART(category."path", '.', 2), '[^0-9]', '')
            ) = 0 THEN NULL
            ELSE REGEXP_REPLACE(SPLIT_PART(category."path", '.', 2), '[^0-9]', '')
        END AS unit,
        CASE
            WHEN LENGTH(
                REGEXP_REPLACE(SPLIT_PART(category."path", '.', 3), '[^0-9]', '')
            ) = 0 THEN NULL
            ELSE REGEXP_REPLACE(SPLIT_PART(category."path", '.', 3), '[^0-9]', '')
        END AS lesson
    ,registration.course_status as course_level
from {{ref('dt_ls_bookmark')}} as bookmark
left join {{ref('dt_ls_registration')}} as registration on bookmark.registration_id = registration.id
left join {{ref('dt_ls_content_item')}} as content_item on bookmark.content_item_id = content_item.id 
left join {{ref('dt_ls_category')}} as category on category.Id = content_item.ancestor_category_id 