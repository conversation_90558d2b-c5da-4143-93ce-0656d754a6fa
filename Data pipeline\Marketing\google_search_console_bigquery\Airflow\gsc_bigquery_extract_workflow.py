import os
import json
from dependencies import glue_trigger
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.operators.bash import BashOperator
from dependencies.slack_alerts import task_failure_callback
from datetime import datetime
from dependencies.cloud_operations import S3
from dependencies import db_operations
from airflow.providers.amazon.aws.operators.s3 import S3DeleteObjectsOperator
from airflow.providers.amazon.aws.operators.glue import GlueJobOperator
from airflow.providers.amazon.aws.operators.s3 import S3DeleteObjectsOperator
from airflow.hooks.S3_hook import S3Hook
from dependencies import cloud_operations

# Define the DAG
default_args = {
    'owner': 'wse_data_team',
    'start_date': datetime(2024, 5, 28),
    # Add other default_args as needed
}
redshift_execute = db_operations.Database
s3 = cloud_operations.S3

file_info = s3.read_json_file(bucket="etl-dev-gsc-extract", file_path="config/bigquery_searchconsole_config.json")

dag = DAG('gsc_bigquery_extract_workflow', default_args=default_args, schedule_interval=None, catchup=False)

def checkpoint():
    print("Execution Staring")

def redshift_copy_command(**kwargs):
    table_name,layer = kwargs['table_name'],kwargs['layer']
    file_path = 's3://dev-google-search-console/'+S3.dynamic_file_path('dev-google-search-console','s3_data_dir/'+layer+'/'+table_name+'/')+'data/'
    redshift_table_name = f"google.{table_name}"
    
    pre_reqisite_query = """TRUNCATE TABLE {} """.format(redshift_table_name)
    truncate_statement = "truncating the table " + redshift_table_name + " before copying the latest data"
    truncate_execute = redshift_execute.execution('WriteTable', pre_reqisite_query, truncate_statement)
    print(truncate_execute)
    
    statement_name = "This query is "+ layer +" copy of table " + redshift_table_name + " to redshift"
    copy_command_query = """COPY {}
    FROM '{}'
    IAM_ROLE 'arn:aws:iam::262158335980:role/RedshitS3access'
    PARQUET;""".format(redshift_table_name, file_path)
    print(copy_command_query)
    copy_execute = redshift_execute.execution('WriteTable', copy_command_query, statement_name)
    print(copy_execute)
    print("Data ",layer," copy from S3 to redshift completed for table", redshift_table_name)


def redshift_query_execute(**kwargs):
    execution_query,query_statement = kwargs['execution_query'],kwargs['query_statement']
    query_execute = redshift_execute.execution('WriteTable', execution_query, query_statement)
    print(query_execute)

checkpoint = PythonOperator(
    task_id='checkpoint',
    python_callable=checkpoint,
    dag=dag
)

checkpoint

s3_page_delete = S3DeleteObjectsOperator(
    task_id='s3_page_delete',
    bucket='etl-dev-gsc-extract',
    keys=['url_json_response_raw/*'],  # Replace with your S3 folder path
    aws_conn_id='aws_default',  # Replace with your AWS connection ID
    dag=dag,
)

for input_data in file_info:
    op_kwargs = {
        "glue_job_name": input_data['glue_job_name'],
        "glue_args": input_data['glue_args']
        }
    glue_extract = PythonOperator(
        task_id=str(input_data['task_id']),
        python_callable=glue_trigger.run_glue_job,
        op_kwargs=op_kwargs,
        dag=dag
        )
    checkpoint >> s3_page_delete >> glue_extract

glue_s3file_savetable = GlueJobOperator(
        task_id="save_s3file_table",
        job_name='s3_file_bigquery_gsc_athena',
        iam_role_name='AWSGlueServiceRole-AZURE',
        dag=dag
    )

glue_extract >> glue_s3file_savetable

HOME = os.environ["HOME"]  # retrieve the location of your home folder
dbt_path = os.path.join(HOME, "dbt/dbt_marketing_bigquery_gsc")  # path to your dbt project

ods_searchconsole_url_impression = BashOperator(
    task_id="ods_searchconsole_url_impression",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models ods.ods_searchconsole_url_impression",  # run the model!
    # on_failure_callback=task_failure_callback,
    dag=dag
)

glue_s3file_savetable >> ods_searchconsole_url_impression

dt_search_console = BashOperator(
    task_id="dt_search_console",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models data_transformation.dt_search_console",  # run the model!
    # on_failure_callback=task_failure_callback,
    dag=dag
)

analytics_organic_search = BashOperator(
    task_id="analytics_organic_search",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run --models analytics.organic_search",  # run the model!
    # on_failure_callback=task_failure_callback,
    dag=dag
)

analytics_table = 'organic_search'
op_kwargs = {'table_name': analytics_table,'layer' :'analytics_marketing_bigquery_gsc'}
dt_to_redshift = PythonOperator(
    task_id=f"dt_to_redshift_{analytics_table}",
    python_callable=redshift_copy_command,  
    # on_failure_callback=task_failure_callback,
    op_kwargs=op_kwargs,
    dag=dag
    )

op_kwargs = {'execution_query': "call hubspot_crm.sp_organic_compiled();",'query_statement' : "Sp execution in Redshift"}
sp_redshift_execute = PythonOperator(
        task_id="sp_redshift_execute",
        python_callable=redshift_query_execute,  
        # on_failure_callback=task_failure_callback,
        op_kwargs=op_kwargs,
        dag=dag
)


ods_searchconsole_url_impression >> dt_search_console >> analytics_organic_search >> dt_to_redshift >> sp_redshift_execute

if __name__ == "__main__":
    dag.cli()