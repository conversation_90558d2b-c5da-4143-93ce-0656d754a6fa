{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_snb_scheduled_class_property'
        ) }}
)

Select
    {{etl_load_date()}},
    Id,
    Scheduled_Class_Id,
    Key,
    Value,
    Created,
    Last_Updated
From
    ods_data