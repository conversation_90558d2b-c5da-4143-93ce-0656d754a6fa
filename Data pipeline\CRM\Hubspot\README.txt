1.place the below-mentioned files in the folder "EtlDependencies" in gluejob-dependencies-production s3 bucket
CloudOperations.py
DbOperations.py
Utils.py

SourceSystemConfigInfo.json
SourceSystemConfigUpdate.json

2.place the below-mentioned file in the folder "LambdaDependencies" in gluejob-dependencies-production s3 bucket
SourceSystemBucketInfo.json

3.place the below-mentioned file in the folder "Hubspot" in gluejob-dependencies-production s3 bucket
AssociationFormatting.py
Authentication.py
cloud_operations.py
CloudOperations.py
CredentialSecretManager.py
DataFetchFormatting.py
DataFormatingTeams.py
LogFileGeneration.py
ObjectDataFetch.py
OwnersDataExtract.py
PostApiCall.py
TimeConversion.py
Queries.py

4.place the below-mentioned file in the folder "Config" in hubspot-datawarehouse-production s3 bucket.
Stage1.json
Stage2.json
Stage3.json
Stage4.json