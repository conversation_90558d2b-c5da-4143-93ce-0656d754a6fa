version: 2

models:
  - name: speak_billing
    columns:
      - name: center_reference_id
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        tests:
          - not_null:
              severity: error
      - name: product_type
        tests:
          - accepted_values:
              values: ['Core Course+', 'Speak+']
              severity: error
      - name: contract_type
        tests:
          - accepted_values:
              values: ['private', 'b2b']
              severity: error
      - name: is_promotional
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: start_date
        tests:
          - not_null:
              severity: error
      - name: billing_date
        tests:
          - not_null:
              severity: error
      - name: billing_month
        tests:
          - not_null:
              severity: error
      - name: dbt_unique_id
        tests:
          - not_null:
              severity: error