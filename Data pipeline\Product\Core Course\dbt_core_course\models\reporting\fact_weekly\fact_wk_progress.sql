{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet',
    unique_key = 'dbt_unique_id'
) }}

-- getting last 5 weeks based on current date
{% set end_date = var('end_date', modules.datetime.date.today()) %}
{% set start_date = var('start_date', (end_date - modules.datetime.timedelta(weeks=5))) %}



with calendar as 
    (
        select
            "date",
            first_week_date,
            last_week_date
        from
            reporting.dim_calendar
        where
            first_week_date = "date"
            AND "date" <= CURRENT_DATE
            AND "date" <= DATE '{{ end_date }}'
            AND "date" >= DATE '{{ start_date }}'
    )
,
fact_act_cut as
    (
        select *,
               cast(completed_date as date) as date_completed_date
        from {{ ref('fact_activity')}}
        where completed_date between date (date_add('week', -7,date('{{ start_date }}'))) and date(date_add('day',3,date('{{ end_date }}')))
    )
,
fact_sessions as
    (
        select 
               student_id,
               Session,
               min(cast(completed_date as date)) as session_start_date,
               SUM(duration_cap_mins) AS duration_mins
        from {{ ref('activities')}}
        where completed_date between date (date_add('week', -7,date('{{ start_date }}'))) and date(date_add('day',3,date('{{ end_date }}')))
        group by 
               student_id,
               Session
    )
,
f_bookings as 
    (
        select *
        , date(student_local_start_datetime) as student_local_start_date
        from {{ ref('class_bookings')}}
        where attended_flag = true
        and date(student_local_start_datetime) between date (date_add('week', -7,date('{{ start_date }}'))) and date(date_add('day',3,date('{{ end_date }}')))
    )
,
levels_started as
    (
        select *
        , student_reference_id as student_id
        , date(date_granted) as date_granted_date
        from {{ ref('levels_started')}}
        where date_granted between date (date_add('week', -7,date('{{ start_date }}'))) and date(date_add('day',3,date('{{ end_date }}')))
    )
,
session_agg as (
           select
                cal.first_week_date,
                student_id,
                count(session) filter (where session_start_date between date_add('week', -3, first_week_date) and last_week_date)               AS sessions_4wk,
                sum(duration_mins) filter (where session_start_date between date_add('week', -3, first_week_date) and last_week_date)/
                count(session) filter (where session_start_date between date_add('week', -3, first_week_date) and last_week_date)               as duration_per_session_4wk
           from
                calendar cal
                left join fact_sessions on fact_sessions.session_start_date between date_add('week', -3, cal.first_week_date) and cal.last_week_date
           group by
                cal.first_week_date,
                student_id
),
act_agg as (
           select
               cal.first_week_date,
               student_id,
               sum(multimedia_activities) filter (where date_completed_date between first_week_date and last_week_date) as multimedia_activities_completed_1wk,
               sum(workbook_activities) filter (where date_completed_date between first_week_date and last_week_date) as workbook_activities_completed_1wk,
               sum(duration_mm_mins) filter (where date_completed_date between first_week_date and last_week_date) as duration_mm_1wk,
               sum(duration_wb_mins) filter (where date_completed_date between first_week_date and last_week_date) as duration_wb_1wk,
               sum(duration_mins) filter (where date_completed_date between first_week_date and last_week_date) as duration_1wk,
               sum(lesson_exercises_complete) filter (where date_completed_date between first_week_date and last_week_date) as lessons_complete_1wk,
               sum(multimedia_activities) filter (where date_completed_date between date_add('week', -1, first_week_date) and last_week_date) as multimedia_activities_completed_2wk,
               sum(workbook_activities) filter (where date_completed_date between date_add('week', -1, first_week_date) and last_week_date) as workbook_activities_completed_2wk,
               sum(duration_mm_mins) filter (where date_completed_date between date_add('week', -1, first_week_date) and last_week_date) as duration_mm_2wk,
               sum(duration_wb_mins) filter (where date_completed_date between date_add('week', -1, first_week_date) and last_week_date) as duration_wb_2wk,
               sum(duration_mins) filter (where date_completed_date between date_add('week', -1, first_week_date) and last_week_date) as duration_2wk,
               sum(lesson_exercises_complete) filter (where date_completed_date between date_add('week', -1, first_week_date) and last_week_date) as lessons_complete_2wk,
               sum(multimedia_activities) filter (where date_completed_date between date_add('week', -3, first_week_date) and last_week_date) as multimedia_activities_completed_4wk,
               sum(workbook_activities) filter (where date_completed_date between date_add('week', -3, first_week_date) and last_week_date) as workbook_activities_completed_4wk,
               sum(duration_mm_mins) filter (where date_completed_date between date_add('week', -3, first_week_date) and last_week_date) as duration_mm_4wk,
               sum(duration_wb_mins) filter (where date_completed_date between date_add('week', -3, first_week_date) and last_week_date) as duration_wb_4wk,
               sum(duration_mins) filter (where date_completed_date between date_add('week', -3, first_week_date) and last_week_date) as duration_4wk,
               sum(lesson_exercises_complete) filter (where date_completed_date between date_add('week', -3, first_week_date) and last_week_date) as lessons_complete_4wk,
               sum(multimedia_activities) filter (where date_completed_date between date_add('week', -7, first_week_date) and last_week_date) as multimedia_activities_completed_8wk,
               sum(workbook_activities) filter (where date_completed_date between date_add('week', -7, first_week_date) and last_week_date) as workbook_activities_completed_8wk,
               sum(duration_mm_mins) filter (where date_completed_date between date_add('week', -7, first_week_date) and last_week_date) as duration_mm_8wk,
               sum(duration_wb_mins) filter (where date_completed_date between date_add('week', -7, first_week_date) and last_week_date) as duration_wb_8wk,
               sum(duration_mins) filter (where date_completed_date between date_add('week', -7, first_week_date) and last_week_date) as duration_8wk,
               sum(lesson_exercises_complete) filter (where date_completed_date between date_add('week', -7, first_week_date) and last_week_date) as lessons_complete_8wk
           from
              calendar cal
                left join fact_act_cut on fact_act_cut.date_completed_date between date_add('week', -7, cal.first_week_date) and cal.last_week_date
           group by
               cal.first_week_date,
               student_id
)
,
bookings_agg as (
           select
               cal.first_week_date,
               student_reference_id as student_id,
               count(booking_id) filter (where class_type like '%encounter%' and student_local_start_date between first_week_date and last_week_date) as enc_attended_1wk,
               count(booking_id) filter (where class_type like '%complementary class%' and student_local_start_date between first_week_date and last_week_date) as cc_attended_1wk,
               count(booking_id) filter (where class_type like '%social club%' and student_local_start_date between first_week_date and last_week_date) as sc_attended_1wk,
               count(booking_id) filter (where class_type like '%encounter%' and student_local_start_date between date_add('week', -1, first_week_date) and last_week_date) as enc_attended_2wk,
               count(booking_id) filter (where class_type like '%complementary class%' and student_local_start_date between date_add('week', -1, first_week_date) and last_week_date) as cc_attended_2wk,
               count(booking_id) filter (where class_type like '%social club%' and student_local_start_date between date_add('week', -1, first_week_date) and last_week_date) as sc_attended_2wk,
               count(booking_id) filter (where class_type like '%encounter%' and student_local_start_date between date_add('week', -3, first_week_date) and last_week_date) as enc_attended_4wk,
               count(booking_id) filter (where class_type like '%complementary class%' and student_local_start_date between date_add('week', -3, first_week_date) and last_week_date) as cc_attended_4wk,
               count(booking_id) filter (where class_type like '%social club%' and student_local_start_date between date_add('week', -3, first_week_date) and last_week_date) as sc_attended_4wk,
               count(booking_id) filter (where class_type like '%encounter%' and student_local_start_date between date_add('week', -7, first_week_date) and last_week_date) as enc_attended_8wk,
               count(booking_id) filter (where class_type like '%complementary class%' and student_local_start_date between date_add('week', -7, first_week_date) and last_week_date) as cc_attended_8wk,
               count(booking_id) filter (where class_type like '%social club%' and student_local_start_date between date_add('week', -7, first_week_date) and last_week_date) as sc_attended_8wk
           from
               calendar cal
                left join f_bookings on f_bookings.student_local_start_date between date_add('week', -7, cal.first_week_date) and cal.last_week_date
           group by
               cal.first_week_date,
               student_reference_id
)
,
levels_agg as (
           select
               cal.first_week_date,
               student_id,
               sum(levels_started) filter (where date_granted_date between first_week_date and last_week_date) as lvls_started_1wk,
               sum(levels_started) filter (where first_later = 'first' and date_granted_date between first_week_date and last_week_date) as lvls_started_first_1wk,
               sum(levels_started) filter (where first_later = 'later' and date_granted_date between first_week_date and last_week_date) as lvls_started_later_1wk,
               sum(levels_started) filter (where date_granted_date between date_add('week', -1, first_week_date) and last_week_date) as lvls_started_2wk,
               sum(levels_started) filter (where first_later = 'first' and date_granted_date between date_add('week', -1, first_week_date) and last_week_date) as lvls_started_first_2wk,
               sum(levels_started) filter (where first_later = 'later' and date_granted_date between date_add('week', -1, first_week_date) and last_week_date) as lvls_started_later_2wk,
               sum(levels_started) filter (where date_granted_date between date_add('week', -3, first_week_date) and last_week_date) as lvls_started_4wk,
               sum(levels_started) filter (where first_later = 'first' and date_granted_date between date_add('week', -3, first_week_date) and last_week_date) as lvls_started_first_4wk,
               sum(levels_started) filter (where first_later = 'later' and date_granted_date between date_add('week', -3, first_week_date) and last_week_date) as lvls_started_later_4wk,
               sum(levels_started) filter (where date_granted_date between date_add('week', -7, first_week_date) and last_week_date) as lvls_started_8wk,
               sum(levels_started) filter (where first_later = 'first' and date_granted_date between date_add('week', -7, first_week_date) and last_week_date) as lvls_started_first_8wk,
               sum(levels_started) filter (where first_later = 'later' and date_granted_date between date_add('week', -7, first_week_date) and last_week_date) as lvls_started_later_8wk
           from
               calendar cal
                left join levels_started on levels_started.date_granted_date between date_add('week', -7, cal.first_week_date) and cal.last_week_date
           group by
               cal.first_week_date,
               student_id
)

select     
    coalesce(act_agg.student_id,bookings_agg.student_id,levels_agg.student_id,session_agg.student_id) as student_id,
    coalesce(act_agg.first_week_date,bookings_agg.first_week_date,levels_agg.first_week_date,session_agg.first_week_date) as first_week_date,
    act_agg.multimedia_activities_completed_1wk,
    act_agg.workbook_activities_completed_1wk,
    act_agg.duration_mm_1wk,
    act_agg.duration_wb_1wk,
    act_agg.duration_1wk,
    act_agg.lessons_complete_1wk,    
    act_agg.multimedia_activities_completed_2wk,
    act_agg.workbook_activities_completed_2wk,
    act_agg.duration_mm_2wk,
    act_agg.duration_wb_2wk,
    act_agg.duration_2wk,
    act_agg.lessons_complete_2wk,  
    act_agg.multimedia_activities_completed_4wk,
    act_agg.workbook_activities_completed_4wk,
    act_agg.duration_mm_4wk,
    act_agg.duration_wb_4wk,
    act_agg.duration_4wk,
    act_agg.lessons_complete_4wk, 
    act_agg.multimedia_activities_completed_8wk,
    act_agg.workbook_activities_completed_8wk,
    act_agg.duration_mm_8wk,
    act_agg.duration_wb_8wk,
    act_agg.duration_8wk,
    act_agg.lessons_complete_8wk,
    session_agg.sessions_4wk,
    session_agg.duration_per_session_4wk,
    CASE
    WHEN sessions_4wk > 0 AND sessions_4wk <= 8 THEN 1
    WHEN sessions_4wk > 8 AND sessions_4wk <= 16 THEN 2
    WHEN sessions_4wk > 16 THEN 3
 ELSE -1
END AS segment_sessions,
    CASE
    WHEN duration_per_session_4wk <= 25 THEN 1
    WHEN duration_per_session_4wk > 25 AND duration_per_session_4wk <= 35 THEN 2
    WHEN duration_per_session_4wk > 35 AND duration_per_session_4wk <= 45 THEN 3
    WHEN duration_per_session_4wk > 45  THEN 4
 ELSE -1
END AS segment_avg_duration,
CASE
    WHEN sessions_4wk > 0 AND sessions_4wk <= 8
            AND duration_per_session_4wk < 45
    THEN 1
    WHEN sessions_4wk > 0 AND sessions_4wk <= 8
            AND duration_per_session_4wk >= 45
    THEN 2
    WHEN sessions_4wk > 8 
            AND duration_per_session_4wk < 25
    THEN 3
    WHEN sessions_4wk > 8
            AND duration_per_session_4wk >= 25  AND duration_per_session_4wk < 45
    THEN 4
    WHEN sessions_4wk > 8
            AND duration_per_session_4wk >= 45
    THEN 5
    ELSE -1
END AS segment,
    bookings_agg.enc_attended_1wk,
    bookings_agg.cc_attended_1wk,
    bookings_agg.sc_attended_1wk,
    bookings_agg.enc_attended_2wk,
    bookings_agg.cc_attended_2wk,
    bookings_agg.sc_attended_2wk,
    bookings_agg.enc_attended_4wk,
    bookings_agg.cc_attended_4wk,
    bookings_agg.sc_attended_4wk,
    bookings_agg.enc_attended_8wk,
    bookings_agg.cc_attended_8wk,
    bookings_agg.sc_attended_8wk,
    levels_agg.lvls_started_1wk,
    levels_agg.lvls_started_first_1wk,
    levels_agg.lvls_started_later_1wk,
    levels_agg.lvls_started_2wk,
    levels_agg.lvls_started_first_2wk,
    levels_agg.lvls_started_later_2wk,
    levels_agg.lvls_started_4wk,
    levels_agg.lvls_started_first_4wk,
    levels_agg.lvls_started_later_4wk,
    levels_agg.lvls_started_8wk,
    levels_agg.lvls_started_first_8wk,
    levels_agg.lvls_started_later_8wk,
    (
        cast(coalesce(act_agg.first_week_date,bookings_agg.first_week_date,levels_agg.first_week_date,session_agg.first_week_date) as varchar) ||
        cast(coalesce(act_agg.student_id,bookings_agg.student_id,levels_agg.student_id,session_agg.student_id) as varchar) ||
        'core course'
        ) as dbt_unique_id
    ,CAST(current_timestamp AS TIMESTAMP(6)) AS load_date
from act_agg
left join bookings_agg on act_agg.student_id = bookings_agg.student_id 
                        and act_agg.first_week_date = bookings_agg.first_week_date
left join levels_agg on coalesce(act_agg.student_id,bookings_agg.student_id) = levels_agg.student_id
                        and coalesce(act_agg.first_week_date,bookings_agg.first_week_date) = levels_agg.first_week_date
left join session_agg on coalesce(act_agg.student_id,bookings_agg.student_id,levels_agg.student_id) = session_agg.student_id
                        and coalesce(act_agg.first_week_date,bookings_agg.first_week_date,levels_agg.first_week_date) = session_agg.first_week_date

