{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH RankedRecords AS (
    SELECT 
        {{cast_to_timestamp("created")}} as created,
        {{cast_to_timestamp("lastupdated")}} as lastupdated,
        id,
        scheduledclassid,
        "key",
        "value",
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdated DESC) AS rn
    FROM 
        {{source('stage_schedule_and_booking_service', 'scheduledclassproperty')}}
)

SELECT
    {{etl_load_date()}},
    created,
    lastupdated as last_updated,
    id,
    scheduledclassid as scheduled_class_id,
    "key",
    "value"
FROM
    RankedRecords
WHERE
    rn = 1