{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = ['id'],
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}



SELECT
    {{etl_load_date()}},
    id,
    messagetemplateid AS message_template_id,
    templateid as template_id,
    subscriberid as subscriber_id,
    type as channel,
    identifier as title,
    CAST(from_iso8601_timestamp(createdat) AS timestamp(6)) AS created_at,
    CAST(from_iso8601_timestamp(updatedat) AS timestamp(6)) AS updated_at
FROM
{{ source('stage_novu_service','jobs') }}