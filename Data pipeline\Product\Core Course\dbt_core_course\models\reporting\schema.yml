version: 2

models:
  - name: dim_centers
    description: >
      Dimension table for centers with standardized naming and grouping.
      Used for consistent center reporting across the platform.
    columns:
      - name: center_id
        description: Unique identifier for the center
        tests:
          - unique:
              severity: error
          - not_null:
              severity: error
      - name: territory_id
        description: ID of the territory the center belongs to
        tests:
          - not_null:
              severity: error
      - name: center_name
        description: Properly capitalized name of the center
        tests:
          - not_null:
              severity: error
      - name: group_name
        description: Properly capitalized name of the center group
      - name: time_zone
        description: Time zone of the center
        tests:
          - not_null:
              severity: error

  - name: dim_classes
    description: >
      Dimension table for classes with aggregated metrics and classifications.
      Used for class scheduling analysis and capacity planning.
    columns:
      - name: class_id
        description: Unique identifier for the class
        tests:
          - unique:
              severity: error
          - not_null:
              severity: error
      - name: center_id
        description: ID of the center where the class is held
        tests:
          - not_null:
              severity: error
      - name: class_teacher_user_reference_id
        description: Reference ID for the teacher of the class
      - name: class_local_start_date
        description: Local start date and time of the class
        tests:
          - not_null:
              severity: error
      - name: class_date
        description: Date of the class
        tests:
          - not_null:
              severity: error
      - name: ClassHourBand
        description: Hour band of the class (e.g., 9-10, 10-11)
      - name: time_of_day
        description: Time of day classification (morning, afternoon, evening)
      - name: Class_date_hour_band
        description: Combined date and hour band
      - name: class_code
        description: Code identifier for the class
      - name: class_type
        description: Type of class (encounter, conversation class, etc.)
      - name: class_technology_type
        description: Technology used for the class
      - name: class_type_category
        description: Category of the class type
      - name: class_service_type
        description: Service type of the class
        tests:
          - not_null:
              severity: error
      - name: number_of_seats
        description: Number of available seats in the class
        tests:
          - not_null:
              severity: error
      - name: class_number_of_seats_in_stand_by
        description: Number of standby seats available
      - name: number_of_students
        description: Number of students booked in the class
      - name: categories_abbreviations
        description: Abbreviations of categories associated with the class
      - name: class_online_flag
        description: Flag indicating if the class is online
      - name: class_b2b_flag
        description: Flag indicating if the class is for B2B students
      - name: teen_flag
        description: Flag indicating if the class is for teen students
        tests:
         - accepted_values:
              values: ['Adults', 'Teens']
              severity: error
      - name: class_cancelled_flag
        description: Flag indicating if the class is cancelled
      - name: class_type_billable_flag
        description: Flag indicating if the class type is billable
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: ClassCode
        description: Alternative code for the class
      - name: DupeClassCount
        description: Count of duplicate classes
      - name: DupeClassStudents
        description: Count of students in duplicate classes
      - name: DupeKey
        description: Key used to identify duplicate classes
      - name: Classes
        description: Count of classes
      - name: FullyBooked
        description: Flag indicating if the class is fully booked
      - name: NoBooked
        description: Flag indicating if the class has no bookings
      - name: opportunity
        description: Opportunity classification for the class

  - name: fact_bookings
    description: >
      Fact table for class bookings with detailed flags and metrics.
      Used for booking analysis, attendance tracking, and student engagement.
    columns:
      - name: attended_flag
        description: Flag indicating if the student attended the class
      - name: billable_flag
        description: Flag indicating if the booking is billable
      - name: standby_flag
        description: Flag indicating if the booking is on standby
      - name: booked_by_role
        description: Role of the user who made the booking
      - name: booking_cancelled_by_role
        description: Role of the user who cancelled the booking
      - name: booking_cancelled_by
        description: ID of the user who cancelled the booking
      - name: booking_created_datetime
        description: Timestamp when the booking was created
      - name: last_updated_datetime
        description: Timestamp when the booking was last updated
      - name: booking_cancelled_datetime
        description: Timestamp when the booking was cancelled
      - name: booking_id
        description: Unique identifier for the booking
      - name: cancelled_flag
        description: Flag indicating if the booking was cancelled
      - name: class_booking_id
        description: ID for the class booking
      - name: class_date
        description: Date of the class
      - name: class_id
        description: ID of the class
      - name: class_type
        description: Type of class
      - name: class_type_billable
        description: Flag indicating if the class type is billable
      - name: class_month
        description: Month of the class
      - name: class_price
        description: Price of the class
      - name: class_result
        description: Result of the class
      - name: compound_key
        description: Compound key for the booking
      - name: late_cancelled_flag
        description: Flag indicating if the booking was cancelled late
      - name: no_show_flag
        description: Flag indicating if the student did not show up
      - name: student_reference_id
        description: Reference ID for the student
      - name: student_center_id
        description: ID of the center where the student is registered
      - name: technology_platform_staff_flag
        description: Flag indicating if technology platform was used by staff
      - name: technology_staff_flag
        description: Flag indicating if technology was used by staff
      - name: technology_student_flag
        description: Flag indicating if technology was used by student
      - name: rescheduled_flag
        description: Flag indicating if the booking was rescheduled
      - name: ready_flag
        description: Flag indicating if the student was ready for the class
      - name: lead_booked_datetime
        description: Lead time between booking and class
      - name: mm_ready_flag
        description: Flag indicating if the student completed multimedia preparation
      - name: wb_ready_flag
        description: Flag indicating if the student completed workbook preparation
      - name: load_date
        description: Date when the record was loaded

  - name: fact_speaking_ai_beta
    description: >
      Fact table for Speaking AI Beta service usage with detailed metrics.
      Used for analyzing speaking practice patterns, costs, and effectiveness.
    columns:
      - name: chat_id
        description: Unique identifier for the chat session
      - name: user_id
        description: ID of the user participating in the session
      - name: user_type
        description: Type of user (e.g., student, prospect)
      - name: level
        description: Language proficiency level for the session
      - name: feedback
        description: Feedback provided by the user
      - name: duration
        description: Duration of the session
      - name: start_date
        description: UTC timestamp when the session started
      - name: end_date
        description: UTC timestamp when the session ended
      - name: completed_date
        description: UTC timestamp when the feedback was completed
      - name: chat_start_date
        description: Local timestamp when the session started
      - name: chat_end_date
        description: Local timestamp when the session ended
      - name: local_completed_date
        description: Local timestamp when the feedback was completed
      - name: total_cost
        description: Total cost of the session
      - name: chat_ended
        description: Boolean flag indicating if the chat has ended
      - name: total_no_of_interactions
        description: Total number of interactions in the session
      - name: total_input_tokens
        description: Total number of input tokens used
      - name: total_output_tokens
        description: Total number of output tokens generated
      - name: total_tokens
        description: Total number of tokens (input + output)
      - name: chat_duration_seconds
        description: Duration of the chat in seconds
      - name: total_messages
        description: Total number of messages exchanged
      - name: user_messages
        description: Number of messages sent by the user
      - name: assistant_messages
        description: Number of messages sent by the assistant
      - name: user_speaking_time
        description: Total time the user spent speaking
      - name: assistant_speaking_time
        description: Total time the assistant spent speaking
      - name: total_output_tokens_per_message
        description: Average output tokens per message
      - name: total_input_tokens_per_message
        description: Average input tokens per message
      - name: max_output_tokens
        description: Maximum output tokens in a single message
      - name: max_input_tokens
        description: Maximum input tokens in a single message
      - name: min_output_tokens
        description: Minimum output tokens in a single message
      - name: min_input_tokens
        description: Minimum input tokens in a single message
      - name: total_message_length
        description: Total length of all messages
      - name: total_message_audio_duration
        description: Total duration of audio in messages
      - name: max_message_length
        description: Maximum length of a single message
      - name: min_message_length
        description: Minimum length of a single message
      - name: understanding_gap_count
        description: Count of instances where the AI didn't understand the user
      - name: date
        description: Date of the session
      - name: group_id
        description: ID of the group associated with the user
      - name: dbt_unique_id
        description: Unique identifier for the record in DBT

  - name: fact_activity
    description: >
      Fact table for student activities with aggregated metrics by date and level.
      Used for tracking student progress and engagement with learning content.
    columns:
      - name: student_id
        description: ID of the student
        tests:
          - not_null:
              severity: error
      - name: completed_date
        description: Date when the activities were completed
        tests:
          - not_null:
              severity: error
      - name: level
        description: Level of the content
        tests:
          - not_null:
              severity: error
      - name: unit
        description: Unit within the level
      - name: lesson
        description: Lesson within the unit
      - name: multimedia_activities
        description: Count of multimedia activities completed
        tests:
          - not_null:
              severity: error
      - name: lesson_exercises_complete
        description: Count of lesson exercises completed
      - name: mini_cycles
        description: Count of distinct mini-cycles completed
        tests:
          - not_null:
              severity: error
      - name: mini_cycles_stages
        description: Count of distinct mini-cycle stages completed
        tests:
          - not_null:
              severity: error
      - name: workbook_activities
        description: Count of workbook activities completed
        tests:
          - not_null:
              severity: error
      - name: duration_wb_mins
        description: Total duration of workbook activities in minutes
        tests:
          - not_null:
              severity: error
      - name: duration_mm_mins
        description: Total duration of multimedia activities in minutes

  - name: fact_classes
    description: >
      Fact table for classes with aggregated booking and attendance metrics.
      Used for class utilization analysis and attendance tracking.
    columns:
      - name: class_id
        description: Unique identifier for the class
      - name: class_date
        description: Date of the class
      - name: student_center_id
        description: ID of the center where the student is registered
      - name: dbt_unique_id
        description: Unique identifier for the record in DBT
      - name: load_date
        description: Date when the record was loaded
      - name: bookings
        description: Count of bookings for the class
      - name: ready
        description: Count of students who were ready for the class
      - name: attendance
        description: Count of students who attended the class
      - name: billable
        description: Count of billable bookings
      - name: booked_by_staff
        description: Count of bookings made by staff
      - name: booked_by_student
        description: Count of bookings made by students

  - name: fact_daily_novu_results
    description: >
      Daily fact table for Novu notification results with campaign metrics.
      Used for analyzing notification effectiveness and engagement.
    columns:
      - name: campaign
        description: Name of the notification campaign
        tests:
          - not_null:
              severity: error
      - name: channel
        description: Communication channel used (email, SMS, push, etc.)
        tests:
          - not_null:
              severity: error
      - name: message
        description: Message content or description
        tests:
          - not_null:
              severity: error
      - name: date
        description: Date of the notification
        tests:
          - not_null:
              severity: error
      - name: test_start_date
        description: Start date of the A/B test
      - name: test_end_date
        description: End date of the A/B test
      - name: outcome_1_name
        description: Name of the first outcome metric
      - name: outcome_2_name
        description: Name of the second outcome metric
      - name: outcome_3_name
        description: Name of the third outcome metric
      - name: outcome_4_name
        description: Name of the fourth outcome metric
      - name: outcome_5_name
        description: Name of the fifth outcome metric
      - name: outcome_6_name
        description: Name of the sixth outcome metric
      - name: total_messages_a
        description: Total messages sent for treatment A
      - name: total_messages_b
        description: Total messages sent for treatment B
      - name: outcome_1_count_a
        description: Count of outcome 1 for treatment A
      - name: outcome_1_count_b
        description: Count of outcome 1 for treatment B
      - name: outcome_2_count_a
        description: Count of outcome 2 for treatment A
      - name: outcome_2_count_b
        description: Count of outcome 2 for treatment B
      - name: outcome_3_count_a
        description: Count of outcome 3 for treatment A
      - name: outcome_3_count_b
        description: Count of outcome 3 for treatment B
      - name: outcome_4_count_a
        description: Count of outcome 4 for treatment A
      - name: outcome_4_count_b
        description: Count of outcome 4 for treatment B
      - name: outcome_5_count_a
        description: Count of outcome 5 for treatment A
      - name: outcome_5_count_b
        description: Count of outcome 5 for treatment B
      - name: outcome_6_count_a
        description: Count of outcome 6 for treatment A
      - name: outcome_6_count_b
        description: Count of outcome 6 for treatment B

  - name: fact_wk_activity
    description: >
      Weekly fact table for activities with percentile duration metrics.
      Used for tracking activity patterns and duration benchmarks over time.
    columns:
      - name: weekcommencing
        description: Start date of the week
      - name: path
        description: Path or identifier for the activity
      - name: level
        description: Level of the content
      - name: unit
        description: Unit within the level
      - name: lesson
        description: Lesson within the unit
      - name: mini_cycle
        description: Mini-cycle number
      - name: mini_cycle_stage
        description: Stage within the mini-cycle
      - name: description
        description: Description of the activity
      - name: content_item_id
        description: ID of the content item
      - name: Minimum
        description: Minimum duration for the activity
      - name: D1
        description: 10th percentile duration for the activity
      - name: Q1
        description: 25th percentile duration for the activity
      - name: Median
        description: Median duration for the activity
      - name: Q3
        description: 75th percentile duration for the activity
      - name: D9
        description: 90th percentile duration for the activity
      - name: Maximum
        description: Maximum duration for the activity
      - name: activity_type
        description: Type of activity (MM for multimedia, DW for digital workbook)
      - name: count_act
        description: Count of activities
      - name: score
        description: Average score for the activity

  - name: fact_wk_students
    description: >
      Weekly fact table for student metrics with contract and validity flags.
      Used for tracking student status and contract validity over time.
    columns:
      - name: date
        description: Date of the record
      - name: first_week_date
        description: First date of the week
      - name: last_month_date
        description: Last date of the month
      - name: year_month_key
        description: Year and month key (YYYYMM)
      - name: student_reference_id
        description: Reference ID for the student
      - name: contract_reference_id
        description: Reference ID for the contract
      - name: group_id
        description: ID of the group
      - name: lab_teacher_id
        description: ID of the lab teacher
      - name: personal_tutor
        description: ID or name of the personal tutor
      - name: consultant_id
        description: ID of the consultant
      - name: center_reference_id
        description: Reference ID for the center
      - name: product_type
        description: Type of product
      - name: study_plan_type
        description: Type of study plan
      - name: contract_type
        description: Type of contract
      - name: course_level
        description: Level of the course
      - name: status
        description: Status of the contract
      - name: location
        description: Location where the service is provided
      - name: class_access_type
        description: Type of class access
      - name: service_type
        description: Type of service
      - name: is_membership
        description: Flag indicating if the contract is a membership
      - name: is_teen
        description: Flag indicating if the student is a teen
      - name: is_promotional
        description: Flag indicating if the contract is promotional
      - name: self_booking_access_flag
        description: Flag indicating if the student has self-booking access
      - name: start_date
        description: Start date of the contract
      - name: end_date
        description: End date of the contract
      - name: first_21d_end_date
        description: End date of the first 21 days
      - name: first_contract
        description: Flag indicating if this is the first contract
      - name: valid_completed21days
        description: Flag indicating if the student completed 21 days
      - name: valid_month_to_date
        description: Flag indicating if the contract is valid month to date
      - name: valid_rolling30
        description: Flag indicating if the contract is valid for the last 30 days
      - name: valid_current_date
        description: Flag indicating if the contract is valid on the current date
      - name: valid_rolling_7days
        description: Flag indicating if the contract is valid for the last 7 days
      - name: valid_rolling_14days
        description: Flag indicating if the contract is valid for the last 14 days
      - name: valid_rolling_21days
        description: Flag indicating if the contract is valid for the last 21 days
      - name: valid_rolling_28days
        description: Flag indicating if the contract is valid for the last 28 days

  - name: fact_wk_progress
    description: >
      Weekly fact table for student progress metrics across different time periods.
      Used for tracking student engagement and activity over time.
    columns:
      - name: date
        description: Date of the record
      - name: first_week_date
        description: First date of the week
      - name: last_month_date
        description: Last date of the month
      - name: year_month_key
        description: Year and month key (YYYYMM)
      - name: student_reference_id
        description: Reference ID for the student
      - name: contract_reference_id
        description: Reference ID for the contract
      - name: center_reference_id
        description: Reference ID for the center
      - name: mm_activities_1wk
        description: Count of multimedia activities in the last week
      - name: mm_activities_2wk
        description: Count of multimedia activities in the last 2 weeks
      - name: mm_activities_4wk
        description: Count of multimedia activities in the last 4 weeks
      - name: mm_activities_8wk
        description: Count of multimedia activities in the last 8 weeks
      - name: wb_activities_1wk
        description: Count of workbook activities in the last week
      - name: wb_activities_2wk
        description: Count of workbook activities in the last 2 weeks
      - name: wb_activities_4wk
        description: Count of workbook activities in the last 4 weeks
      - name: wb_activities_8wk
        description: Count of workbook activities in the last 8 weeks
      - name: cc_booked_1wk
        description: Count of conversation classes booked in the last week
      - name: cc_booked_2wk
        description: Count of conversation classes booked in the last 2 weeks
      - name: cc_booked_4wk
        description: Count of conversation classes booked in the last 4 weeks
      - name: cc_booked_8wk
        description: Count of conversation classes booked in the last 8 weeks
      - name: cc_attended_1wk
        description: Count of conversation classes attended in the last week
      - name: cc_attended_2wk
        description: Count of conversation classes attended in the last 2 weeks
      - name: cc_attended_4wk
        description: Count of conversation classes attended in the last 4 weeks
      - name: cc_attended_8wk
        description: Count of conversation classes attended in the last 8 weeks
      - name: sc_attended_1wk
        description: Count of social clubs attended in the last week
      - name: sc_attended_2wk
        description: Count of social clubs attended in the last 2 weeks
      - name: sc_attended_4wk
        description: Count of social clubs attended in the last 4 weeks
      - name: sc_attended_8wk
        description: Count of social clubs attended in the last 8 weeks
      - name: lvls_started_1wk
        description: Count of levels started in the last week
      - name: lvls_started_first_1wk
        description: Count of first levels started in the last week
      - name: lvls_started_later_1wk
        description: Count of later levels started in the last week
      - name: lvls_started_2wk
        description: Count of levels started in the last 2 weeks
      - name: lvls_started_first_2wk
        description: Count of first levels started in the last 2 weeks
      - name: lvls_started_later_2wk
        description: Count of later levels started in the last 2 weeks
      - name: lvls_started_4wk
        description: Count of levels started in the last 4 weeks
      - name: lvls_started_first_4wk
        description: Count of first levels started in the last 4 weeks
      - name: lvls_started_later_4wk
        description: Count of later levels started in the last 4 weeks
      - name: lvls_started_8wk
        description: Count of levels started in the last 8 weeks
      - name: lvls_started_first_8wk
        description: Count of first levels started in the last 8 weeks
      - name: lvls_started_later_8wk
        description: Count of later levels started in the last 8 weeks

  - name: course_completed_metrics
    description: >
      Monthly fact table for course completion metrics with detailed dimensions.
      Used for analyzing course completion rates and student progression.
    columns:
      - name: dbt_unique_key
        description: Unique identifier for the record in DBT
      - name: Report_start_date
        description: Start date of the reporting period
      - name: center_id
        description: ID of the center
      - name: consultant_id
        description: ID of the consultant
      - name: class_access_type
        description: Type of class access
      - name: Course
        description: Course name or identifier
      - name: location
        description: Location where the service is provided
      - name: services
        description: Services associated with the course
      - name: group
        description: Group name or identifier
      - name: is_promotional
        description: Flag indicating if the course is promotional
      - name: is_teen
        description: Flag indicating if the course is for teens
      - name: is_b2_b
        description: Flag indicating if the course is B2B
      - name: is_membership
        description: Flag indicating if the course is a membership
      - name: course_level
        description: Level of the course
      - name: personal_tutor
        description: ID or name of the personal tutor
      - name: total_students
        description: Total number of students
      - name: total_students_completed
        description: Total number of students who completed the course
      - name: total_students_not_completed
        description: Total number of students who did not complete the course
      - name: total_students_completed_perc
        description: Percentage of students who completed the course
      - name: total_students_not_completed_perc
        description: Percentage of students who did not complete the course
      - name: total_students_completed_on_time
        description: Total number of students who completed the course on time
      - name: total_students_completed_late
        description: Total number of students who completed the course late
      - name: total_students_completed_on_time_perc
        description: Percentage of students who completed the course on time
      - name: total_students_completed_late_perc
        description: Percentage of students who completed the course late

  - name: dim_calendar
    description: >
      Calendar dimension table with date attributes and business calendar information.
      Used for time-based analysis and reporting across all business metrics.
    columns:
      - name: date
        description: Calendar date
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: year
        description: Year (YYYY)
        tests:
          - not_null:
              severity: error
      - name: month
        description: Month number (1-12)
        tests:
          - not_null:
              severity: error
      - name: day
        description: Day of month (1-31)
        tests:
          - not_null:
              severity: error
      - name: quarter
        description: Quarter number (1-4)
        tests:
          - not_null:
              severity: error
      - name: week_of_year
        description: Week number of the year (1-53)
      - name: day_of_week
        description: Day of week number (1-7, where 1=Sunday)
        tests:
          - not_null:
              severity: error
      - name: day_name
        description: Name of the day (Monday, Tuesday, etc.)
        tests:
          - not_null:
              severity: error
      - name: month_name
        description: Name of the month (January, February, etc.)
        tests:
          - not_null:
              severity: error
      - name: quarter_name
        description: Quarter name (Q1, Q2, Q3, Q4)
        tests:
          - not_null:
              severity: error
      - name: is_weekend
        description: Flag indicating if the date is a weekend
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: is_holiday
        description: Flag indicating if the date is a holiday
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: first_week_date
        description: First date of the week containing this date
        tests:
          - not_null:
              severity: error
      - name: last_week_date
        description: Last date of the week containing this date
        tests:
          - not_null:
              severity: error
      - name: first_month_date
        description: First date of the month containing this date
        tests:
          - not_null:
              severity: error
      - name: last_month_date
        description: Last date of the month containing this date
        tests:
          - not_null:
              severity: error
      - name: first_quarter_date
        description: First date of the quarter containing this date
        tests:
          - not_null:
              severity: error
      - name: last_quarter_date
        description: Last date of the quarter containing this date
        tests:
          - not_null:
              severity: error
      - name: first_year_date
        description: First date of the year containing this date
        tests:
          - not_null:
              severity: error
      - name: last_year_date
        description: Last date of the year containing this date
        tests:
          - not_null:
              severity: error

  - name: dim_contracts
    description: >
      Dimension table for contracts with detailed contract attributes.
      Used for contract analysis and student enrollment tracking.
    columns:
      - name: contract_id
        description: Unique identifier for the contract
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: student_id
        description: ID of the student associated with the contract
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
      - name: center_id
        description: ID of the center associated with the contract
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: territory_id
        description: ID of the territory associated with the center
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: contract_number
        description: Contract number
      - name: crm_contract_number
        description: Contract number in the CRM system
      - name: status
        description: Current status of the contract
      - name: start_date
        description: Start date of the contract
        tests:
          - not_null:
              severity: error
      - name: end_date
        description: End date of the contract
        tests:
          - not_null:
              severity: error
      - name: created_date
        description: Date when the contract was created
        tests:
          - not_null:
              severity: error
      - name: sale_date
        description: Date when the contract was sold
      - name: price
        description: Price of the contract
      - name: service_type
        description: Type of service provided under the contract
        tests:
          - accepted_values:
              values: ['standard', 'vip']
              severity: error
      - name: location
        description: Location where the service is provided
        tests:
          - accepted_values:
              values: ['incenter', 'outcenter']
              severity: error
      - name: class_access_type
        description: Type of class access granted by the contract
      - name: product_type
        description: Type of product associated with the contract
      - name: contract_type
        description: Type of contract
      - name: is_membership
        description: Flag indicating if the contract is a membership
      - name: is_promotional
        description: Flag indicating if the contract is promotional
      - name: is_transfer_in
        description: Flag indicating if the contract is a transfer in
      - name: is_cross_center_booking
        description: Flag indicating if the contract allows cross-center booking
      - name: is_teen
        description: Flag indicating if the contract is for a teen student

  - name: dim_daily_bookmark
    description: >
      Daily dimension table for student bookmarks and progress tracking.
      Used for analyzing daily student progression through learning content.
    columns:
      - name: student_id
        description: ID of the student
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: date
        description: Date of the bookmark record
        tests:
          - not_null:
              severity: error
      - name: registration_id
        description: ID of the student registration
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: mm_level
        description: Multimedia level bookmark
      - name: mm_unit
        description: Multimedia unit bookmark
      - name: mm_lesson
        description: Multimedia lesson bookmark
      - name: wb_level
        description: Workbook level bookmark
      - name: wb_unit
        description: Workbook unit bookmark
      - name: wb_lesson
        description: Workbook lesson bookmark
      - name: dbt_unique_id
        description: Unique identifier for the record in DBT
        tests:
          - unique:
              severity: error

  - name: dim_students
    description: >
      Dimension table for students with comprehensive student information.
      Used for student analysis and demographic reporting.
    columns:
      - name: student_id
        description: Unique identifier for the student
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: center_id
        description: ID of the center where the student is registered
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: territory_id
        description: ID of the territory associated with the student
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: is_active
        description: Flag indicating if the student is active
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: user_name
        description: Username for system login
        tests:
          - not_null:
              severity: warn
      - name: first_name
        description: Student's first name
      - name: last_name
        description: Student's last name
      - name: full_name
        description: Student's full name (first + last)
      - name: birth_date
        description: Student's date of birth
      - name: age
        description: Student's age in years
      - name: email
        description: Student's email address
      - name: mobile_telephone
        description: Student's mobile telephone number
      - name: created_at
        description: Timestamp when the student record was created
        tests:
          - not_null:
              severity: warn
      - name: updated_at
        description: Timestamp when the student record was last updated
      - name: deleted_at
        description: Timestamp when the student record was deleted
      - name: role
        description: Student's role in the system
        tests:
          - accepted_values:
              values: ['student', 'prospect']
              severity: warn
      - name: role_type
        description: Type of role
      - name: is_teen
        description: Flag indicating if the student is a teen
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: gender
        description: Student's gender
      - name: nationality
        description: Student's nationality
      - name: preferred_language
        description: Student's preferred language for communication
      - name: enrollment_date
        description: Date when the student first enrolled
      - name: last_login_date
        description: Date of the student's last login
      - name: total_contracts
        description: Total number of contracts the student has had
      - name: current_level
        description: Student's current learning level
      - name: placement_test_score
        description: Student's placement test score
      - name: placement_test_date
        description: Date when the student took the placement test

  - name: dim_territory
    description: >
      Dimension table for territories with geographical and organizational information.
      Used for territorial analysis and regional reporting.
    columns:
      - name: territory_id
        description: Unique identifier for the territory
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: territory_reference_id
        description: External reference ID for the territory
        tests:
          - not_null:
              severity: error
      - name: territory_name
        description: Name of the territory
        tests:
          - not_null:
              severity: error
      - name: territory_code
        description: Code identifier for the territory
      - name: region
        description: Region where the territory is located
      - name: country
        description: Country where the territory is located
        tests:
          - not_null:
              severity: error
      - name: country_code
        description: ISO country code
      - name: currency
        description: Currency used in the territory
      - name: time_zone
        description: Time zone of the territory
      - name: language
        description: Primary language spoken in the territory
      - name: territory_manager
        description: Manager responsible for the territory
      - name: territory_status
        description: Current status of the territory
        tests:
          - accepted_values:
              values: ['active', 'inactive', 'developing']
              severity: warn
      - name: territory_opening_date
        description: Date when the territory was established
      - name: total_centers
        description: Total number of centers in the territory
      - name: total_students
        description: Total number of students in the territory
      - name: territory_revenue
        description: Total revenue generated by the territory

  - name: dim_users
    description: >
      Dimension table for all users in the system including students, staff, and teachers.
      Used for user management and role-based analysis.
    columns:
      - name: user_id
        description: Unique identifier for the user
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student (if applicable)
      - name: center_id
        description: ID of the center where the user is associated
      - name: center_reference_id
        description: External reference ID for the center
      - name: territory_id
        description: ID of the territory associated with the user
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: is_active
        description: Flag indicating if the user is active
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: user_name
        description: Username for system login
        tests:
          - not_null:
              severity: warn
      - name: first_name
        description: User's first name
      - name: last_name
        description: User's last name
      - name: full_name
        description: User's full name (first + last)
      - name: birth_date
        description: User's date of birth
      - name: email
        description: User's email address
      - name: mobile_telephone
        description: User's mobile telephone number
      - name: created_at
        description: Timestamp when the user record was created
        tests:
          - not_null:
              severity: warn
      - name: updated_at
        description: Timestamp when the user record was last updated
      - name: deleted_at
        description: Timestamp when the user record was deleted
      - name: role
        description: User's role in the system
        tests:
          - accepted_values:
              values: ['student', 'staff', 'teacher', 'consultant', 'admin', 'prospect']
              severity: warn
      - name: role_type
        description: Type of role
      - name: department
        description: Department the user belongs to (for staff)
      - name: job_title
        description: Job title of the user (for staff)
      - name: hire_date
        description: Date when the user was hired (for staff)
      - name: manager_id
        description: ID of the user's manager (for staff)
      - name: salary
        description: User's salary (for staff)
      - name: employment_status
        description: Employment status (active, inactive, terminated)
        tests:
          - accepted_values:
              values: ['active', 'inactive', 'terminated', 'on_leave']
              severity: warn

  - name: fact_conversation_ai
    description: >
      Fact table for AI-powered conversation sessions with detailed metrics.
      Used for analyzing conversation AI usage, costs, and effectiveness.
    columns:
      - name: chat_id
        description: Unique identifier for the chat session
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: user_id
        description: ID of the user participating in the conversation
        tests:
          - not_null:
              severity: error
      - name: contract_id
        description: ID of the contract associated with the user
      - name: content_id
        description: ID of the content being discussed
      - name: start_date
        description: UTC timestamp when the conversation started
        tests:
          - not_null:
              severity: error
      - name: end_date
        description: UTC timestamp when the conversation ended
      - name: chat_start_date
        description: Local timestamp when the conversation started
      - name: chat_end_date
        description: Local timestamp when the conversation ended
      - name: total_cost
        description: Total cost of the conversation
      - name: chat_ended
        description: Boolean flag indicating if the chat has ended
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: total_no_of_interactions
        description: Total number of interactions in the conversation
      - name: total_input_tokens
        description: Total number of input tokens used
      - name: total_output_tokens
        description: Total number of output tokens generated
      - name: total_tokens
        description: Total number of tokens (input + output)
      - name: chat_duration_seconds
        description: Duration of the chat in seconds
      - name: total_messages
        description: Total number of messages exchanged
      - name: user_messages
        description: Number of messages sent by the user
      - name: assistant_messages
        description: Number of messages sent by the assistant
      - name: understanding_gap_count
        description: Count of instances where the AI didn't understand the user
      - name: average_response_time
        description: Average response time of the AI in seconds
      - name: user_satisfaction_score
        description: User satisfaction score for the conversation
      - name: conversation_topic
        description: Main topic or theme of the conversation
      - name: language_used
        description: Language used in the conversation
      - name: device_type
        description: Type of device used for the conversation
      - name: platform
        description: Platform used for the conversation (web, mobile, etc.)

  - name: fact_levels_started
    description: >
      Fact table for tracking when students start new levels in their learning journey.
      Used for progression analysis and level completion tracking.
    columns:
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: digital_books_log_id
        description: ID of the digital books log record
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: registration_id
        description: ID of the student registration
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: date_granted
        description: UTC timestamp when the level was granted
        tests:
          - not_null:
              severity: error
      - name: local_date_granted
        description: Local timestamp when the level was granted
        tests:
          - not_null:
              severity: error
      - name: category
        description: Category of the level
        tests:
          - accepted_values:
              values: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20']
              severity: warn
      - name: category_type
        description: Type of category
        tests:
          - accepted_values:
              values: ['level']
              severity: warn
      - name: unlock_type
        description: Type of unlock (standard, free, etc.)
        tests:
          - accepted_values:
              values: ['standard', 'promo']
              severity: warn
      - name: operation_type
        description: Type of operation (bill, refund, etc.)
        tests:
          - accepted_values:
              values: ['refund', 'bill']
              severity: warn
      - name: workbook_type
        description: Type of workbook
        tests:
          - accepted_values:
              values: ['digital', 'printed']
              severity: warn
      - name: is_teen
        description: Flag indicating if the student is a teen
        tests:
          - not_null:
              severity: warn
      - name: config_value
        description: Configuration value
      - name: sequence
        description: Sequence number
        tests:
          - not_null:
              severity: warn
      - name: is_restart
        description: Flag indicating if this is a restart
        tests:
          - not_null:
              severity: warn
      - name: first_later
        description: Indicates if this is a first or later level
      - name: levels_started
        description: Count of levels started (1 for bill, -1 for refund)

  - name: fact_wk_student_progress
    description: >
      Weekly aggregated student progress metrics grouped by various dimensions.
      Used for comprehensive weekly reporting and analysis of student engagement and performance across different segments.
    columns:
      - name: first_week_date
        description: First date of the week
        tests:
          - not_null:
              severity: error
      - name: group_id
        description: ID of the group
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: contract_type
        description: Type of contract
      - name: study_plan_type
        description: Type of study plan
      - name: bookmark_mm_level
        description: Current multimedia level bookmark
      - name: location
        description: Location where the service is provided
        tests:
          - accepted_values:
              values: ['incenter', 'outcenter']
              severity: error
      - name: class_access_type
        description: Type of class access
      - name: service_type
        description: Type of service (standard, vip)
        tests:
          - accepted_values:
              values: ['standard', 'vip']
              severity: error
      - name: is_membership
        description: Flag indicating if the contract is a membership
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: is_teen
        description: Flag indicating if the student is a teen
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: is_promotional
        description: Flag indicating if the contract is promotional
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: contract_inclusions
        description: Contract inclusions details
      - name: adjusted_segment
        description: Current adjusted student segment based on activity patterns
        tests:
          - accepted_values:
              values: ['Not Valid', 'Onboarding', 'Reactivated', 'Inactive', 'Infrequent Short', 'Infrequent Long', 'Frequent Very Short', 'Frequent Short', 'Frequent Long']
              severity: warn
      - name: last_adjusted_segment
        description: Previous adjusted student segment (4 weeks prior)
        tests:
          - accepted_values:
              values: ['Not Valid', 'Onboarding', 'Reactivated', 'Inactive', 'Infrequent Short', 'Infrequent Long', 'Frequent Very Short', 'Frequent Short', 'Frequent Long']
              severity: warn
      - name: "Total Students Serviced 1wk"
        description: Total number of students serviced in the last 1 week
      - name: "Total Students Serviced 2wk"
        description: Total number of students serviced in the last 2 weeks
      - name: "Total Students Serviced 4wk"
        description: Total number of students serviced in the last 4 weeks
      - name: "Total Students Period 1wk"
        description: Total students in the period for 1 week
      - name: "Total Students Period 2wk"
        description: Total students in the period for 2 weeks
      - name: "Total Students Period 4wk"
        description: Total students in the period for 4 weeks
      - name: "Total Students Period 8wk"
        description: Total students in the period for 8 weeks
      - name: "Active Students Serviced 1wk"
        description: Number of active students serviced in the last 1 week
      - name: "Active Students Serviced 2wk"
        description: Number of active students serviced in the last 2 weeks
      - name: "Active Students Serviced 4wk"
        description: Number of active students serviced in the last 4 weeks
      - name: "Studied 15 mins 4wk"
        description: Number of students who studied 15 minutes or less in 4 weeks
      - name: "Studied 15 to 60 mins 4wk"
        description: Number of students who studied between 15 and 60 minutes in 4 weeks
      - name: "Studied 1 to 3 hours 4wk"
        description: Number of students who studied between 1 and 3 hours in 4 weeks
      - name: "Studied 3 to 6 hours 4wk"
        description: Number of students who studied between 3 and 6 hours in 4 weeks
      - name: "Studied 6 to 10 hours 4wk"
        description: Number of students who studied between 6 and 10 hours in 4 weeks
      - name: "Studied 10+ hours 4wk"
        description: Number of students who studied more than 10 hours in 4 weeks
      - name: "Engaged Students 4wk"
        description: Number of engaged students in 4 weeks (more than 6 hours total study time)
      - name: "Long-term Engaged Students 8wk"
        description: Number of long-term engaged students in 8 weeks (more than 10 hours total study time)
      - name: "Total Encounters 1wk"
        description: Total number of encounters attended in 1 week
      - name: "Total Encounters 2wk"
        description: Total number of encounters attended in 2 weeks
      - name: "Total Encounters 4wk"
        description: Total number of encounters attended in 4 weeks
      - name: "Total complementary classes attended 1wk"
        description: Total complementary classes attended in 1 week
      - name: "Total complementary classes attended 2wk"
        description: Total complementary classes attended in 2 weeks
      - name: "Total complementary classes attended 4wk"
        description: Total complementary classes attended in 4 weeks
      - name: "Total social clubs attended 1wk"
        description: Total social clubs attended in 1 week
      - name: "Total social clubs attended 2wk"
        description: Total social clubs attended in 2 weeks
      - name: "Total social clubs attended 4wk"
        description: Total social clubs attended in 4 weeks
      - name: "Total time studied MM 1wk"
        description: Total time studied multimedia in 1 week (minutes)
      - name: "Total time studied MM 2wk"
        description: Total time studied multimedia in 2 weeks (minutes)
      - name: "Total time studied MM 4wk"
        description: Total time studied multimedia in 4 weeks (minutes)
      - name: "Levels started 1wk"
        description: Total levels started in 1 week
      - name: "Levels started first 1wk"
        description: First levels started in 1 week
      - name: "Levels started later 1wk"
        description: Later levels started in 1 week
      - name: "Levels started 2wk"
        description: Total levels started in 2 weeks
      - name: "Levels started first 2wk"
        description: First levels started in 2 weeks
      - name: "Levels started later 2wk"
        description: Later levels started in 2 weeks
      - name: "Levels started 4wk"
        description: Total levels started in 4 weeks
      - name: "Levels started first 4wk"
        description: First levels started in 4 weeks
      - name: "Levels started later 4wk"
        description: Later levels started in 4 weeks
      - name: total_duration_mm_1wk
        description: Total multimedia duration in 1 week (minutes)
      - name: total_duration_wb_1wk
        description: Total workbook duration in 1 week (minutes)
      - name: total_duration_1wk
        description: Total study duration in 1 week (minutes)
      - name: total_lessons_complete_1wk
        description: Total lessons completed in 1 week
      - name: total_duration_mm_2wk
        description: Total multimedia duration in 2 weeks (minutes)
      - name: total_duration_wb_2wk
        description: Total workbook duration in 2 weeks (minutes)
      - name: total_duration_2wk
        description: Total study duration in 2 weeks (minutes)
      - name: total_lessons_complete_2wk
        description: Total lessons completed in 2 weeks
      - name: total_duration_mm_4wk
        description: Total multimedia duration in 4 weeks (minutes)
      - name: total_duration_wb_4wk
        description: Total workbook duration in 4 weeks (minutes)
      - name: total_duration_4wk
        description: Total study duration in 4 weeks (minutes)
      - name: total_lessons_complete_4wk
        description: Total lessons completed in 4 weeks
      - name: total_duration_mm_8wk
        description: Total multimedia duration in 8 weeks (minutes)
      - name: total_duration_wb_8wk
        description: Total workbook duration in 8 weeks (minutes)
      - name: total_duration_8wk
        description: Total study duration in 8 weeks (minutes)
      - name: total_lessons_complete_8wk
        description: Total lessons completed in 8 weeks
      - name: dbt_unique_id
        description: Unique identifier for the record in DBT
        tests:
          - unique:
              severity: error
      - name: load_date
        description: Timestamp when the record was loaded

  - name: dim_centers
    description: >
      Dimension table for centers with detailed center information and attributes.
      Used for center-based analysis and geographical reporting.
    columns:
      - name: center_id
        description: Unique identifier for the center
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: territory_id
        description: ID of the territory associated with the center
        tests:
          - not_null:
              severity: error
      - name: territory_reference_id
        description: External reference ID for the territory
        tests:
          - not_null:
              severity: error
      - name: center_name
        description: Name of the center
        tests:
          - not_null:
              severity: error
      - name: center_code
        description: Code identifier for the center
      - name: center_address
        description: Physical address of the center
      - name: center_city
        description: City where the center is located
      - name: center_state
        description: State or province where the center is located
      - name: center_postal_code
        description: Postal code of the center
      - name: center_phone
        description: Phone number of the center
      - name: center_email
        description: Email address of the center
      - name: center_status
        description: Current status of the center
        tests:
          - accepted_values:
              values: ['active', 'inactive', 'closed', 'opening_soon']
              severity: warn
      - name: center_opening_date
        description: Date when the center opened
      - name: center_closing_date
        description: Date when the center closed (if applicable)
      - name: center_manager
        description: Manager responsible for the center
      - name: center_capacity
        description: Maximum capacity of the center
      - name: center_type
        description: Type of center (flagship, standard, mini, etc.)
      - name: is_franchise
        description: Flag indicating if the center is a franchise
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: franchise_owner
        description: Owner of the franchise (if applicable)
      - name: total_classrooms
        description: Total number of classrooms in the center
      - name: total_students
        description: Total number of students enrolled at the center
      - name: center_revenue
        description: Total revenue generated by the center

  - name: dim_classes
    description: >
      Dimension table for classes with comprehensive class information and scheduling details.
      Used for class management and scheduling analysis.
    columns:
      - name: class_id
        description: Unique identifier for the class
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: center_id
        description: ID of the center where the class is held
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: territory_id
        description: ID of the territory associated with the center
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: class_teacher_user_reference_id
        description: Reference ID for the teacher of the class
      - name: class_local_start_date
        description: Local start date and time of the class
        tests:
          - not_null:
              severity: error
      - name: class_date
        description: Date of the class
        tests:
          - not_null:
              severity: error
      - name: class_hour_band
        description: Hour band of the class (e.g., 9-10, 10-11)
      - name: time_of_day
        description: Time of day classification (morning, afternoon, evening)
      - name: class_date_hour_band
        description: Combined date and hour band
      - name: class_code
        description: Code identifier for the class
      - name: class_type
        description: Type of class (encounter, conversation class, etc.)
      - name: class_technology_type
        description: Technology used for the class
      - name: class_type_category
        description: Category of the class type
      - name: class_service_type
        description: Service type of the class
        tests:
          - not_null:
              severity: error
      - name: number_of_seats
        description: Number of available seats in the class
        tests:
          - not_null:
              severity: error
      - name: class_number_of_seats_in_stand_by
        description: Number of standby seats available
      - name: number_of_students
        description: Number of students booked in the class
      - name: categories_abbreviations
        description: Abbreviations of categories associated with the class
      - name: class_online_flag
        description: Flag indicating if the class is online
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: class_b2b_flag
        description: Flag indicating if the class is for B2B students
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: teen_flag
        description: Flag indicating if the class is for teen students
        tests:
          - accepted_values:
              values: ['Adults', 'Teens']
              severity: error
      - name: class_cancelled_flag
        description: Flag indicating if the class is cancelled
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: class_type_billable_flag
        description: Flag indicating if the class type is billable
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error

  - name: fact_activity
    description: >
      Fact table for all student activities including multimedia, workbook, and class activities.
      Used for comprehensive activity analysis and student engagement tracking.
    columns:
      - name: activity_id
        description: Unique identifier for the activity
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: student_id
        description: ID of the student who completed the activity
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: registration_id
        description: ID of the student registration
        tests:
          - not_null:
              severity: error
      - name: activity_type
        description: Type of activity (multimedia, workbook, class)
        tests:
          - not_null:
              severity: error
          - accepted_values:
              values: ['multimedia', 'workbook', 'conversation_class', 'social_club', 'encounter']
              severity: error
      - name: content_item_id
        description: ID of the content item
      - name: content_item_type
        description: Type of content item
      - name: level
        description: Level of the content
        tests:
          - not_null:
              severity: error
      - name: unit
        description: Unit within the level
      - name: lesson
        description: Lesson within the unit
      - name: activity_date
        description: Date when the activity was completed
        tests:
          - not_null:
              severity: error
      - name: activity_start_time
        description: Start time of the activity
      - name: activity_end_time
        description: End time of the activity
      - name: duration_minutes
        description: Duration of the activity in minutes
      - name: score
        description: Score achieved on the activity
      - name: total_questions
        description: Total number of questions in the activity
      - name: correct_answers
        description: Number of correct answers
      - name: completion_status
        description: Status of activity completion
        tests:
          - accepted_values:
              values: ['completed', 'in_progress', 'not_started', 'abandoned']
              severity: warn
      - name: device_type
        description: Type of device used for the activity
      - name: platform
        description: Platform used for the activity

  - name: fact_bookings
    description: >
      Fact table for class bookings with detailed booking information and outcomes.
      Used for booking analysis, attendance tracking, and capacity planning.
    columns:
      - name: booking_id
        description: Unique identifier for the booking
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: class_id
        description: ID of the class that was booked
        tests:
          - not_null:
              severity: error
      - name: student_id
        description: ID of the student who booked the class
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: center_id
        description: ID of the center where the class is held
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: class_date
        description: Date of the class
        tests:
          - not_null:
              severity: error
      - name: class_type
        description: Type of class
        tests:
          - not_null:
              severity: error
      - name: book_mode
        description: Mode of booking (book or standby)
        tests:
          - accepted_values:
              values: ['book', 'standby']
              severity: error
      - name: book_date
        description: Date when the booking was made
        tests:
          - not_null:
              severity: error
      - name: booking_lead_time_hours
        description: Lead time between booking and class in hours
      - name: attended_flag
        description: Flag indicating if the student attended the class
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: cancelled_flag
        description: Flag indicating if the booking was cancelled
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: no_show_flag
        description: Flag indicating if the student did not show up
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: late_cancelled_flag
        description: Flag indicating if the booking was cancelled late
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: standby_to_booked_flag
        description: Flag indicating if the booking was changed from standby to booked
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: booked_by_role
        description: Role of the user who made the booking
      - name: cancelled_by_role
        description: Role of the user who cancelled the booking
      - name: billable_flag
        description: Flag indicating if the booking is billable
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error

  - name: fact_classes
    description: >
      Fact table for classes with comprehensive class metrics and attendance data.
      Used for class performance analysis and operational reporting.
    columns:
      - name: class_id
        description: Unique identifier for the class
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: center_id
        description: ID of the center where the class is held
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: territory_id
        description: ID of the territory associated with the center
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: class_date
        description: Date of the class
        tests:
          - not_null:
              severity: error
      - name: class_start_time
        description: Start time of the class
        tests:
          - not_null:
              severity: error
      - name: class_end_time
        description: End time of the class
      - name: class_duration_minutes
        description: Duration of the class in minutes
      - name: class_type
        description: Type of class
        tests:
          - not_null:
              severity: error
      - name: class_level
        description: Level of the class
      - name: teacher_id
        description: ID of the teacher conducting the class
      - name: teacher_reference_id
        description: External reference ID for the teacher
      - name: total_seats
        description: Total number of seats available in the class
        tests:
          - not_null:
              severity: error
      - name: standby_seats
        description: Number of standby seats available
      - name: total_bookings
        description: Total number of bookings for the class
      - name: total_attendees
        description: Total number of students who attended the class
      - name: total_no_shows
        description: Total number of students who did not show up
      - name: total_cancellations
        description: Total number of cancelled bookings
      - name: total_late_cancellations
        description: Total number of late cancelled bookings
      - name: utilization_rate
        description: Percentage of seats utilized (attendees/total_seats)
      - name: attendance_rate
        description: Percentage of booked students who attended
      - name: no_show_rate
        description: Percentage of booked students who did not show up
      - name: cancellation_rate
        description: Percentage of bookings that were cancelled
      - name: class_status
        description: Status of the class
        tests:
          - accepted_values:
              values: ['scheduled', 'completed', 'cancelled', 'in_progress']
              severity: error
      - name: is_online
        description: Flag indicating if the class is online
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: is_b2b
        description: Flag indicating if the class is for B2B students
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: is_teen
        description: Flag indicating if the class is for teen students
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: is_billable
        description: Flag indicating if the class is billable
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error

  - name: fact_daily_class_aggregation
    description: >
      Daily aggregated metrics for class performance and utilization.
      Used for daily operational reporting and class efficiency analysis.
    columns:
      - name: date
        description: Date of the aggregation
        tests:
          - not_null:
              severity: error
      - name: center_id
        description: ID of the center
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: territory_id
        description: ID of the territory
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: class_type
        description: Type of class
        tests:
          - not_null:
              severity: error
      - name: total_classes_scheduled
        description: Total number of classes scheduled for the day
      - name: total_classes_completed
        description: Total number of classes completed
      - name: total_classes_cancelled
        description: Total number of classes cancelled
      - name: total_seats_available
        description: Total number of seats available across all classes
      - name: total_bookings
        description: Total number of bookings made
      - name: total_attendees
        description: Total number of students who attended classes
      - name: total_no_shows
        description: Total number of no-shows
      - name: total_cancellations
        description: Total number of booking cancellations
      - name: average_utilization_rate
        description: Average utilization rate across all classes
      - name: average_attendance_rate
        description: Average attendance rate across all classes
      - name: average_no_show_rate
        description: Average no-show rate across all classes
      - name: peak_hour_utilization
        description: Utilization rate during peak hours
      - name: off_peak_utilization
        description: Utilization rate during off-peak hours

  - name: fact_daily_class_students
    description: >
      Daily fact table tracking individual student class participation.
      Used for detailed student attendance analysis and engagement tracking.
    columns:
      - name: date
        description: Date of the class
        tests:
          - not_null:
              severity: error
      - name: student_id
        description: ID of the student
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: class_id
        description: ID of the class
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: class_type
        description: Type of class attended
        tests:
          - not_null:
              severity: error
      - name: class_level
        description: Level of the class
      - name: attended_flag
        description: Flag indicating if the student attended
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: booked_flag
        description: Flag indicating if the student had a booking
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: standby_flag
        description: Flag indicating if the student was on standby
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: cancelled_flag
        description: Flag indicating if the booking was cancelled
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: no_show_flag
        description: Flag indicating if the student did not show up
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: late_cancelled_flag
        description: Flag indicating if the booking was cancelled late
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: booking_lead_time_hours
        description: Lead time between booking and class in hours
      - name: class_duration_minutes
        description: Duration of the class in minutes
      - name: participation_score
        description: Score indicating level of participation in the class

  - name: fact_daily_novu_results
    description: >
      Daily aggregated results from Novu notification campaigns.
      Used for analyzing daily notification performance and engagement metrics.
    columns:
      - name: date
        description: Date of the notification campaign
        tests:
          - not_null:
              severity: error
      - name: campaign_name
        description: Name of the notification campaign
        tests:
          - not_null:
              severity: error
      - name: channel
        description: Communication channel used
        tests:
          - not_null:
              severity: error
          - accepted_values:
              values: ['email', 'sms', 'push', 'in_app', 'whatsapp']
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: total_messages_sent
        description: Total number of messages sent
      - name: total_messages_delivered
        description: Total number of messages delivered
      - name: total_messages_opened
        description: Total number of messages opened
      - name: total_messages_clicked
        description: Total number of messages clicked
      - name: total_messages_bounced
        description: Total number of messages bounced
      - name: total_messages_failed
        description: Total number of messages failed
      - name: delivery_rate
        description: Percentage of messages delivered
      - name: open_rate
        description: Percentage of delivered messages opened
      - name: click_rate
        description: Percentage of delivered messages clicked
      - name: bounce_rate
        description: Percentage of messages bounced
      - name: conversion_rate
        description: Percentage of messages that led to desired action
      - name: total_cost
        description: Total cost of the campaign
      - name: cost_per_message
        description: Average cost per message sent
      - name: cost_per_conversion
        description: Average cost per conversion

  - name: fact_daily_student_progress
    description: >
      Daily student progress metrics with comprehensive activity tracking.
      Used for daily student engagement analysis and progress monitoring.
    columns:
      - name: date
        description: Date of the progress record
        tests:
          - not_null:
              severity: error
      - name: student_id
        description: ID of the student
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: registration_id
        description: ID of the student registration
        tests:
          - not_null:
              severity: error
      - name: multimedia_activities_completed
        description: Number of multimedia activities completed on this date
      - name: workbook_activities_completed
        description: Number of workbook activities completed on this date
      - name: conversation_classes_attended
        description: Number of conversation classes attended on this date
      - name: social_clubs_attended
        description: Number of social clubs attended on this date
      - name: encounters_attended
        description: Number of encounters attended on this date
      - name: levels_started
        description: Number of levels started on this date
      - name: total_activities_completed
        description: Total number of activities completed on this date
      - name: total_duration_minutes
        description: Total duration of all activities in minutes
      - name: multimedia_duration_minutes
        description: Total duration of multimedia activities in minutes
      - name: workbook_duration_minutes
        description: Total duration of workbook activities in minutes
      - name: average_activity_score
        description: Average score across all activities completed
      - name: current_mm_level
        description: Current multimedia level
      - name: current_wb_level
        description: Current workbook level
      - name: is_active
        description: Flag indicating if the student was active on this date
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: engagement_score
        description: Calculated engagement score for the day
      - name: study_streak_days
        description: Number of consecutive days of study activity
      - name: login_count
        description: Number of times the student logged in on this date
      - name: session_duration_minutes
        description: Total session duration in minutes

  - name: fact_daily_progress
    description: >
      Daily progress metrics aggregated across all students and activities.
      Used for high-level daily progress reporting and trend analysis.
    columns:
      - name: date
        description: Date of the progress record
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: total_active_students
        description: Total number of active students on this date
      - name: total_multimedia_activities
        description: Total multimedia activities completed across all students
      - name: total_workbook_activities
        description: Total workbook activities completed across all students
      - name: total_conversation_classes
        description: Total conversation classes attended across all students
      - name: total_social_clubs
        description: Total social clubs attended across all students
      - name: total_encounters
        description: Total encounters attended across all students
      - name: total_levels_started
        description: Total levels started across all students
      - name: total_study_duration_minutes
        description: Total study duration across all students in minutes
      - name: average_engagement_score
        description: Average engagement score across all active students
      - name: new_student_registrations
        description: Number of new student registrations on this date
      - name: contract_renewals
        description: Number of contract renewals on this date
      - name: contract_cancellations
        description: Number of contract cancellations on this date
      - name: peak_activity_hour
        description: Hour of the day with highest activity
      - name: total_logins
        description: Total number of student logins on this date

  - name: fact_daily_students
    description: >
      Daily student status and contract validity tracking.
      Used for student lifecycle analysis and contract management reporting.
    columns:
      - name: date
        description: Date of the record
        tests:
          - not_null:
              severity: error
      - name: student_id
        description: ID of the student
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: contract_status
        description: Status of the contract on this date
        tests:
          - accepted_values:
              values: ['active', 'expired', 'cancelled', 'suspended', 'pending']
              severity: error
      - name: is_contract_valid
        description: Flag indicating if the contract is valid on this date
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: days_until_expiry
        description: Number of days until contract expiry
      - name: contract_start_date
        description: Start date of the contract
      - name: contract_end_date
        description: End date of the contract
      - name: product_type
        description: Type of product
      - name: service_type
        description: Type of service
        tests:
          - accepted_values:
              values: ['standard', 'vip']
              severity: error
      - name: location
        description: Location of service delivery
        tests:
          - accepted_values:
              values: ['incenter', 'outcenter']
              severity: error
      - name: is_membership
        description: Flag indicating if the contract is a membership
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: is_teen
        description: Flag indicating if the student is a teen
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: is_promotional
        description: Flag indicating if the contract is promotional
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: current_mm_level
        description: Current multimedia level
      - name: current_wb_level
        description: Current workbook level
      - name: enrollment_date
        description: Date when the student first enrolled
      - name: last_activity_date
        description: Date of the student's last activity
      - name: days_since_last_activity
        description: Number of days since the student's last activity

  - name: fact_monthly_student_progress
    description: >
      Monthly aggregated student progress metrics with comprehensive performance indicators.
      Used for monthly reporting and long-term trend analysis.
    columns:
      - name: month_date
        description: First date of the month
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: total_multimedia_activities
        description: Total multimedia activities completed in the month
      - name: total_workbook_activities
        description: Total workbook activities completed in the month
      - name: total_conversation_classes
        description: Total conversation classes attended in the month
      - name: total_social_clubs
        description: Total social clubs attended in the month
      - name: total_encounters
        description: Total encounters attended in the month
      - name: total_levels_started
        description: Total levels started in the month
      - name: total_study_duration_minutes
        description: Total study duration in minutes for the month
      - name: average_daily_activity
        description: Average daily activity count for the month
      - name: active_days_count
        description: Number of days the student was active in the month
      - name: monthly_engagement_score
        description: Overall engagement score for the month
      - name: levels_completed
        description: Number of levels completed in the month
      - name: progress_rate
        description: Rate of progress through the curriculum
      - name: retention_status
        description: Student retention status for the month
        tests:
          - accepted_values:
              values: ['retained', 'at_risk', 'churned', 'new']
              severity: warn

  - name: course_completed_metrics
    description: >
      Metrics for students who have completed their courses.
      Used for course completion analysis and success rate reporting.
    columns:
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: course_start_date
        description: Date when the course was started
        tests:
          - not_null:
              severity: error
      - name: course_completion_date
        description: Date when the course was completed
        tests:
          - not_null:
              severity: error
      - name: planned_completion_date
        description: Originally planned completion date
      - name: actual_duration_days
        description: Actual duration of the course in days
      - name: planned_duration_days
        description: Planned duration of the course in days
      - name: completion_variance_days
        description: Difference between actual and planned duration
      - name: total_levels_completed
        description: Total number of levels completed
      - name: total_activities_completed
        description: Total number of activities completed
      - name: total_study_hours
        description: Total study hours for the course
      - name: average_weekly_study_hours
        description: Average weekly study hours
      - name: completion_rate
        description: Percentage of course content completed
      - name: final_assessment_score
        description: Score on the final assessment
      - name: course_satisfaction_score
        description: Student satisfaction score for the course
      - name: completed_on_time
        description: Flag indicating if the course was completed on time
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error
      - name: completion_status
        description: Status of course completion
        tests:
          - accepted_values:
              values: ['completed', 'partially_completed', 'discontinued']
              severity: error

  - name: fact_progress_cycle
    description: >
      Progress tracking through learning cycles and milestones.
      Used for curriculum progression analysis and learning path optimization.
    columns:
      - name: cycle_id
        description: Unique identifier for the learning cycle
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: cycle_start_date
        description: Start date of the learning cycle
        tests:
          - not_null:
              severity: error
      - name: cycle_end_date
        description: End date of the learning cycle
      - name: cycle_type
        description: Type of learning cycle
        tests:
          - accepted_values:
              values: ['foundation', 'intermediate', 'advanced', 'specialization']
              severity: warn
      - name: level_range_start
        description: Starting level of the cycle
      - name: level_range_end
        description: Ending level of the cycle
      - name: total_activities_in_cycle
        description: Total number of activities in the cycle
      - name: completed_activities
        description: Number of activities completed
      - name: cycle_completion_percentage
        description: Percentage of cycle completed
      - name: cycle_duration_days
        description: Duration of the cycle in days
      - name: average_daily_progress
        description: Average daily progress rate
      - name: milestone_achievements
        description: Number of milestones achieved in the cycle
      - name: cycle_status
        description: Current status of the cycle
        tests:
          - accepted_values:
              values: ['in_progress', 'completed', 'paused', 'discontinued']
              severity: error