{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_snb_user'
        ) }}
)

SELECT {{etl_load_date()}}, 
    User.Id as Id,
    User_Reference_Id,
    First_Name,
    Last_Name,
    User_Name,
    Email,
    Photo_Uri,
    Role.description as <PERSON>,
    Center_Reference_Id,
    Is_Active,
    Created,
    Last_Updated
from ods_data as User
Left Join (
    select id,
    description
    from {{ ref('ods_snb_role') }}
) as Role ON User.role_id = Role.id