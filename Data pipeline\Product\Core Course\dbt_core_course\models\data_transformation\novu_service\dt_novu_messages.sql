{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}


Select 
    mess.id as message_id,
    subs.subscriber_id as student_reference_id,
    mess.channel,
    mess.title,
    mess.template_id as notification_id,
    nt.notification_name as notification_type,
    mess.message_template_id as variant_id,
    nt.variant_name as variant,
    mess.created_at,
    mess.updated_at
FROM 
    {{ ref('ods_novu_messages') }} as mess
LEFT JOIN 
    {{ ref('ods_novu_notification_templates') }} as nt 
    ON mess.template_id = nt.id and mess.message_template_id = nt.template_id
LEFT JOIN
    {{ ref('ods_novu_subscribers') }} as subs
    ON mess.subscriber_id = subs.id
