create or replace procedure hubspot_crm.sp_dimcentersource()
    language plpgsql
as
$$

BEGIN

--- dim center -----
TRUNCATE TABLE hubspot_crm.dim_center;

INSERT INTO hubspot_crm.dim_center
SELECT center_name, MD5(center_name || territory_name) AS center_key, Region, territory_name
FROM (SELECT DISTINCT center_name
                    , CASE
                          WHEN de.territory_name IN ('Hong Kong',
                                                     'Indonesia',
                                                     'Korea',
                                                     'Macau',
                                                     'Malaysia',
                                                     'Thailand',
                                                     'Vietnam',
                                                     'Myanmar',
                                                     'Mongolia',
                                                     'Laos',
                                                    'Kazakhstan') THEN 'Asia'
                          WHEN de.territory_name IN ('Angola',
                                                     'Czech Republic',
                                                     'France',
                                                     'Germany',
                                                     'Israel',
                                                     'Italy',
                                                     'Morocco',
                                                     'Portugal',
                                                     'Russian Federation',
                                                     'Saudi Arabia',
                                                     'Spain',
                                                     'Switzerland',
                                                     'Tunisia',
                                                     'Turkey',
                                                     'Algeria') THEN 'EMEA'
                          WHEN de.territory_name IN ('Argentina',
                                                     'Chile',
                                                     'Colombia',
                                                     'Dominican Republic',
                                                     'Ecuador',
                                                     'Guatemala',
                                                     'Mexico',
                                                     'Uruguay',
                                                     'Peru',
                                                     'Panama',
                                                     'Venezuela') THEN 'Latam'
                          WHEN de.territory_name IN ('Virtual Territory',
                                                     'Second Virtual Territory') THEN 'GOC'
                          WHEN de.territory_name IN ('Master from Test_Country',
                                                     'Xanadu',
                                                     'USA') THEN 'Other'
        END                             AS Region
                    , de.territory_name AS territory_name
      FROM hubspot_crm.contactsdeals de) AB;



TRUNCATE TABLE hubspot_crm.dim_source;

-- alter table hubspot_crm.dim_source alter column landing_page type varchar(448);

INSERT INTO hubspot_crm.dim_source
SELECT DISTINCT source,
                channel,
                channel_drill_down_1,
                channel_drill_down_2,
                campaign,
                landing_page,
                sub_source_group,
                MD5(source ||
                    channel || channel_drill_down_1 || channel_drill_down_2
                        ||
                    campaign ||
                    landing_page || sub_source_group ) AS source_key
FROM hubspot_crm.contactsdeals;

UPDATE hubspot_crm.contactsdeals
SET source_key_fk = ds.source_key
FROM hubspot_crm.dim_source ds
         JOIN hubspot_crm.contactsdeals cd
              ON cd.source = ds.source
                  AND cd.campaign = ds.campaign
                  AND cd.channel = ds.channel
                  AND cd.channel_drill_down_1 = ds.channel_drill_down_1
                  AND cd.channel_drill_down_2 = ds.channel_drill_down_2
                  AND cd.landing_page = ds.landing_page
                  AND cd.sub_source_group = ds.sub_source_group;

UPDATE hubspot_crm.contactsdeals
SET center_key_fk= dc.center_key
FROM hubspot_crm.dim_center dc
         JOIN hubspot_crm.contactsdeals cd
              ON cd.center_name = dc.center_name
                  AND cd.territory_name = dc.territory_name;





    COMMIT;


END;

$$;

