{{ config(
    materialized = 'incremental',
    incremental_strategy = 'append',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH gsc_raw as 
(
    select
        clicks,
        country,
        ctr,
        "date" as data_date,
        device,
        impressions,
        "page",
        position,
        query,
        territory
    FROM
      {{source('stage_marketing_bigquery_gsc', 'gsc_raw_extract')}}
    {% if is_incremental() %}
        where {{safe_cast_string_to_tz_date("date") }} > ((select max(data_date) from {{ this }}))
    {% endif %}
)
select
    clicks,
    country,
    ctr,
    {{safe_cast_string_to_tz_date('data_date') }} as data_date,
    device,
    impressions,
    "page" as page_url,
    position,
    query,
    territory as territory_code,
    'API' as table_source,
    {{etl_load_date()}}

FROM
gsc_raw;