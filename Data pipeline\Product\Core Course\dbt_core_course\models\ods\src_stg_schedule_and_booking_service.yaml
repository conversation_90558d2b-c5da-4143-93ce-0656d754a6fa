version: 2

sources:

  - name: stage_schedule_and_booking_service
    description: >
      Source data from the Schedule and Booking Service which manages class scheduling,
      bookings, and related configurations for education centers.
    database: awsdatacatalog
    schema: stg_schedule_and_booking_service
    tables:
      - name: category
        description: Categories used for classifying scheduled classes and other entities in the system.
        columns:
          - name: id
            description: Primary key for the category record
          - name: type
            description: Type identifier for the category (e.g., unit, level, stage)
          - name: sequence
            description: Ordering sequence for the category
          - name: created
            description: Timestamp when the category was created
          - name: lastupdated
            description: Timestamp when the category was last updated
          - name: value
            description: Display value or name of the category

      - name: classtype
        description: Types of classes that can be scheduled, with their properties and configurations.
        columns:
          - name: id
            description: Primary key for the class type record
          - name: code
            description: Unique code identifier for the class type
          - name: location
            description: Location type for the class (e.g., in-center, online)
          - name: categorytype
            description: Category type associated with this class type
          - name: hasdescription
            description: Boolean flag indicating if the class type has a description
          - name: acceptsstandby
            description: Boolean flag indicating if standby is accepted for this class type
          - name: hastobeprebooked
            description: Boolean flag indicating if pre-booking is required
          - name: isactive
            description: Boolean flag indicating if the class type is active
          - name: created
            description: Timestamp when the class type was created
          - name: lastupdated
            description: Timestamp when the class type was last updated
          - name: title
            description: Display title for the class type
          - name: color
            description: Color code used for visual representation
          - name: courseid
            description: Foreign key reference to the course

      - name: classtypeconfigurationforservicetype
        description: Configuration settings that link class types to service types with specific parameters.
        columns:
          - name: id
            description: Primary key for the configuration record
          - name: classtypeid
            description: Foreign key reference to the class type
          - name: servicetypeid
            description: Foreign key reference to the service type
          - name: duration
            description: Duration of the class in minutes
          - name: maxnumberofstudents
            description: Maximum number of students allowed in the class
          - name: created
            description: Timestamp when the configuration was created
          - name: lastupdated
            description: Timestamp when the configuration was last updated

      - name: course
        description: Courses offered by the education centers.
        columns:
          - name: id
            description: Primary key for the course record
          - name: name
            description: Name of the course
          - name: created
            description: Timestamp when the course was created
          - name: lastupdated
            description: Timestamp when the course was last updated

      - name: digitalmeetinginformation
        description: Information about digital meetings associated with scheduled classes.
        columns:
          - name: id
            description: Primary key for the digital meeting record
          - name: scheduledclassid
            description: Foreign key reference to the scheduled class
          - name: hosturl
            description: URL for the host to access the digital meeting
          - name: participanturl
            description: URL for participants to access the digital meeting
          - name: vendorid
            description: Identifier for the digital meeting vendor
          - name: password
            description: Password for accessing the digital meeting
          - name: created
            description: Timestamp when the digital meeting was created
          - name: lastupdated
            description: Timestamp when the digital meeting was last updated

      - name: role
        description: User roles within the scheduling and booking system.
        columns:
          - name: id
            description: Primary key for the role record
          - name: description
            description: Description of the role
          - name: created
            description: Timestamp when the role was created
          - name: lastupdated
            description: Timestamp when the role was last updated

      - name: scheduledclass
        description: Scheduled classes with their timing, location, and configuration details.
        columns:
          - name: id
            description: Primary key for the scheduled class record
          - name: starttime
            description: Start time of the scheduled class
          - name: endtime
            description: End time of the scheduled class
          - name: numberofseats
            description: Number of available seats in the class
          - name: categoryfrombooking
            description: Category associated with the booking
          - name: iscancelled
            description: Boolean flag indicating if the class is cancelled
          - name: communicationaccounttype
            description: Type of communication account used (e.g., webex, zoom)
          - name: created
            description: Timestamp when the scheduled class was created
          - name: lastupdated
            description: Timestamp when the scheduled class was last updated
          - name: noofseatsinstandby
            description: Number of seats available for standby
          - name: centerreferenceid
            description: Reference ID for the center where the class is scheduled
          - name: classtypeid
            description: Foreign key reference to the class type
          - name: description
            description: Description of the scheduled class
          - name: teacherid
            description: ID of the teacher assigned to the class
          - name: source
            description: Source of the scheduled class record
          - name: createdby
            description: ID of the user who created the class
          - name: lastupdatedby
            description: ID of the user who last updated the class

      - name: scheduledclasscategory
        description: Categories assigned to scheduled classes for classification and filtering.
        columns:
          - name: id
            description: Primary key for the scheduled class category record
          - name: scheduledclassid
            description: Foreign key reference to the scheduled class
          - name: categoryid
            description: Foreign key reference to the category
          - name: created
            description: Timestamp when the record was created
          - name: lastupdated
            description: Timestamp when the record was last updated

      - name: scheduledclassproperty
        description: Additional properties and attributes for scheduled classes.
        columns:
          - name: id
            description: Primary key for the property record
          - name: scheduledclassid
            description: Foreign key reference to the scheduled class
          - name: propertykey
            description: Key name of the property
          - name: propertyvalue
            description: Value of the property
          - name: created
            description: Timestamp when the property was created
          - name: lastupdated
            description: Timestamp when the property was last updated

      - name: user
        description: Users of the scheduling and booking system, including teachers and administrators.
        columns:
          - name: id
            description: Primary key for the user record
          - name: userreferenceid
            description: External reference ID for the user
          - name: firstname
            description: First name of the user
          - name: lastname
            description: Last name of the user
          - name: username
            description: Username for system login
          - name: email
            description: Email address of the user
          - name: photouri
            description: URI for the user's photo
          - name: roleid
            description: Foreign key reference to the user's role
          - name: centerreferenceid
            description: Reference ID for the user's assigned center
          - name: isactive
            description: Boolean flag indicating if the user is active
          - name: created
            description: Timestamp when the user record was created
          - name: lastupdated
            description: Timestamp when the user record was last updated