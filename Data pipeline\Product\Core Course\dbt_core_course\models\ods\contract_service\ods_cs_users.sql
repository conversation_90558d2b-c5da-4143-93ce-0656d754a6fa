{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        userreferenceid,
        username,
        firstname,
        lastname,
        studentcode,
        centerid,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
        {{cast_to_int('usertype')}},
        employeecode,
        userexternalid,
        territoryid,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'users')}}
)

SELECT
    {{etl_load_date()}},
    id,
    userreferenceid as user_reference_id,
    username as user_name,
    firstname as first_name,
    lastname as last_name,
    studentcode as student_code,
    centerid as center_id,
    createddate as created_date,
    lastupdateddate as last_updated_date,
    usertype as user_type,
    employeecode as employee_code,
    userexternalid as user_external_id,
    territoryid as territory_id
FROM 
    RankedRecords
WHERE 
    rn = 1;