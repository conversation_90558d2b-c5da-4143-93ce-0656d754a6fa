{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_groups'
        ) }}

    {% if is_incremental() %}
        where last_updated_date > ((select max(last_updated_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    grps.id  as id,
    grps.name  as name,
    loc.name as location,
    center_id,
    company_id,
    has_address_same_as_company,
    country,
    state,
    city,
    address_line1,
    address_line2,
    postal_code,
    prdlevelsstart."order" as start_level,
    prdlevelsend."order" as end_level,
    start_date,
    end_date,
    prdtypes.name as producttype,
    created_by_id,
    modified_by_id,
    created_date,
    {{convert_to_local_timestamp('created_date','time_zone_id')}} as local_created_date,
    last_updated_date,
    {{convert_to_local_timestamp('last_updated_date','time_zone_id')}} as local_last_updated_date,
    group_ref_id,
    is_transfer_in,
    transstatuses.name as transfer_status,
    b2_b_contract_number,
    total_number_of_hours,
    master_contract_course_id,
    is_membership,
    max_no_of_cc_and_sc_classes
from ods_data as grps
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_locations' ) }}
    ) as loc on grps.location = loc.id
    left join (
        select id,
            "order"
        from {{ ref( 'ods_cs_product_levels' ) }}
    ) as prdlevelsstart on grps.start_level_id = prdlevelsstart.id
    left join (
        select id,
            "order"
        from {{ ref( 'ods_cs_product_levels' ) }}
    ) as prdlevelsend on grps.end_level_id = prdlevelsend.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_product_types' ) }}
    ) as prdtypes on grps.product_type_id = prdtypes.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_transfer_statuses' ) }}
    ) as transstatuses on grps.transfer_status = transstatuses.id
    left join (
        select id,
            center_reference_id 
        from {{ ref( 'ods_cs_centers' ) }}
    ) as center on center.id = grps.center_id
    left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on center.center_reference_id = tz.center_reference_id