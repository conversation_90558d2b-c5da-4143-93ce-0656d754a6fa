{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH RankedRecords AS (
    SELECT 
        id
        ,coursecontentid
        ,activity
        ,activityurl
        ,noofattempts
        ,iscompleted
        ,duration
        ,categorytype
        ,score
        ,isactive
        ,activityreferenceid
        ,studentid
        ,{{cast_to_timestamp('created')}} as created
        ,{{cast_to_timestamp('lastupdated')}} as lastupdated
        ,studymode
        ,ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdated DESC) AS rn
    FROM 
        {{source('stage_digital_student_workbook_service', 'activityprogress')}}
)

SELECT
    {{etl_load_date()}}
    ,id
    ,coursecontentid as course_content_id
    ,activity
    ,activityurl as activity_url
    ,noofattempts as no_of_attempts
    ,iscompleted as is_completed
    ,duration
    ,categorytype as category_type
    ,score
    ,isactive as is_active
    ,activityreferenceid as activity_reference_id
    ,studentid as student_id
    ,created
    ,lastupdated as last_updated
    ,studymode as study_mode
FROM 
    RankedRecords
WHERE 
    rn = 1;