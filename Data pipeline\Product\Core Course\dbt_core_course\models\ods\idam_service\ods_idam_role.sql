{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        {{cast_to_int('usertypeid')}},
        isactive,
        id,
        loweredrolename,
        description,
        rolecode,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_idam_service',
            'role'
        ) }}
)
SELECT
    {{etl_load_date()}},
    created as created,
    lastupdated as last_updated,
    usertypeid as user_type_id,
    isactive as is_active,
    id,
    loweredrolename as lowered_role_name,
    description,
    rolecode as role_code
FROM
    rankedrecords
WHERE
    rn = 1;
