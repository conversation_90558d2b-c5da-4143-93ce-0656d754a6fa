import sys
from awsglue.utils import getResolvedOptions
sys.path.insert ( 0 , '/glue/lib/installation' )
keys = [ k for k in sys.modules.keys () if 'boto' in k ]
for k in keys:
    if 'boto' in k:
        del sys.modules [ k ]
import boto3
import CloudOperations
import DbOperations
import LogFileGeneration
import logging

Logs = LogFileGeneration.LogFile
s3_connect = CloudOperations.S3

config_info = getResolvedOptions(sys.argv, ['Bucket'])

Bucket = config_info['Bucket']
Executionjson = s3_connect.ReadJsonFile(Bucket, 'ExecutionCheck.json')
Cycleid = Executionjson['CycleId']

RedshiftExecute = DbOperations.Database
StatementName = "This is the Query of Stored Procedure for Hubspot Stage 5 and Stage 6"
SPQuery = """call hubspot_crm.sp_compiled()"""
logging.warning(SPQuery)
CopyExecute = RedshiftExecute.Execution('WriteTable',SPQuery,StatementName)
logging.warning(CopyExecute)