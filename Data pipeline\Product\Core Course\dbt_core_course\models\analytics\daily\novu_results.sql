{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet',
    unique_key = ['message_id','message_abbreviated'],
) }}

with
mess as (
    SELECT
        message_id,
        student_reference_id,
        channel,
        title,
        notification_id,
        notification_type,
        variant_id,
        variant,
        created_at,
        updated_at,
        ROW_NUMBER() over ( PARTITION BY notification_id,student_reference_id, date(created_at) ORDER BY channel DESC ) AS rn
    FROM 
        {{ ref('novu_messages') }}
    {% if is_incremental() %}
        WHERE 
            date(created_at) >= current_date - interval '16' day
    {% endif %}
),

activities as 
(
    Select 
        MIN(act.completed_date) as date_completed
        , mess.student_reference_id
        , mess.message_id
    from 
        mess
    LEFT JOIN
        {{ ref('activities') }} as act
    ON act.student_id = mess.student_reference_id
    AND act.completed_date > mess.created_at
    GROUP BY
        mess.student_reference_id,
        mess.message_id
),

sessions as 
(
    Select 
        Max(act.session) - Min(act.session) as session_count
        , mess.student_reference_id
        , mess.message_id
    FROM
        mess
    LEFT JOIN
        {{ ref('activities') }} as act
    ON act.student_id = mess.student_reference_id
    AND (act.completed_date > mess.created_at and act.completed_date <= DATE_ADD('day', 7,mess.created_at))
    GROUP BY
        mess.student_reference_id,
        mess.message_id
),

classes as 
(
    SELECT
        MIN(cb.class_start_datetime) filter (where cb.class_type IN ('encounter', 'online encounter')
                                                    AND cb.class_result = 'continue') as passed_enc
        ,MIN(cb.class_start_datetime) filter (where cb.class_type IN ('encounter', 'online encounter')
                                                    AND cb.attended_flag = true) as attended_enc
        ,MIN(cb.class_start_datetime) filter (where cb.class_type IN ('complementary class','online complementary class')
                                                    AND cb.attended_flag = true) as attended_cc
        ,MIN(cb.class_start_datetime) filter (where cb.class_type IN ( 'social club','online social club')
                                                    AND cb.attended_flag = true) as attended_sc
        ,MIN(cb.class_start_datetime) filter (where cb.class_type = 'online complementary class'
                                                    AND cb.attended_flag = true) as attended_onl_cc
        ,MIN(cb.class_start_datetime) filter (where cb.class_type = 'online social club'
                                                    AND cb.attended_flag = true) as attended_onl_sc
        ,MIN(cb.class_start_datetime) filter (where cb.class_type IN ('encounter', 'online encounter')
                                                    AND cb.attended_flag = true
                                                    AND (mm_ready_flag = true and wb_ready_flag = true)) as attended_ready_enc
        , mess.student_reference_id
        , mess.message_id
    from
        mess
    INNER JOIN 
        {{ ref('class_bookings') }} as cb
    ON cb.student_reference_id = mess.student_reference_id
    AND ( cb.class_start_datetime > mess.created_at)  
    Where mess.rn = 1
    GROUP BY
        mess.student_reference_id,
        mess.message_id
),

bookings as 
(
    SELECT
        MIN(cb.booking_datetime) filter (where cb.class_type IN ('complementary class','online complementary class')
                                                    AND cb.booking_id is not null) as booked_cc
        ,MIN(cb.booking_datetime) filter (where cb.class_type IN ( 'social club','online social club')
                                                    AND cb.booking_id is not null) as booked_sc
        ,MIN(cb.booking_datetime) filter (where cb.class_type = 'online complementary class'
                                                    AND cb.booking_id is not null) as booked_onl_cc
        ,MIN(cb.booking_datetime) filter (where cb.class_type ='online social club'
                                                    AND cb.booking_id is not null) as booked_onl_sc
        , mess.student_reference_id
        , mess.message_id
    from
        mess
    INNER JOIN 
        {{ ref('class_bookings') }} as cb
    ON cb.student_reference_id = mess.student_reference_id
    AND ( cb.booking_datetime > mess.created_at)  
    Where mess.rn = 1
    GROUP BY
        mess.student_reference_id,
        mess.message_id
),

messages as (
Select 
    mess.* ,
    DATE_DIFF('day', mess.created_at, activities.date_completed) as days_to_act,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, activities.date_completed) <= 6 THEN 1
        else 0
    END AS act_in_7_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, activities.date_completed) <= 2 THEN 1
        else 0
    END AS act_in_3_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, activities.date_completed) <= 0 THEN 1
        else 0
    END AS act_in_1_day,
    CASE 
        WHEN session_count >= 3 THEN 1
        else 0
    END AS session_in_7_day,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, classes.passed_enc) <= 13 THEN 1
        ELSE 0
    END AS enc_pass_14_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, classes.attended_ready_enc) <= 7 THEN 1
        ELSE 0
    END AS enc_att_8_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, classes.attended_ready_enc) <= 5 THEN 1
        ELSE 0
    END AS enc_att_6_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, classes.attended_onl_cc) <= 2 THEN 1
        else 0
    END AS cc_onl_att_in_3_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, classes.attended_onl_cc) <= 6 THEN 1
        else 0
    END AS cc_onl_att_in_7_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, classes.attended_onl_cc) <= 9 THEN 1
        else 0
    END AS cc_onl_att_in_10_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, bookings.booked_onl_cc) <= 2 THEN 1
        else 0
    END AS cc_onl_book_in_3_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, bookings.booked_onl_cc) <= 6 THEN 1
        else 0
    END AS cc_onl_book_in_7_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, bookings.booked_onl_cc) <= 9 THEN 1
        else 0
    END AS cc_onl_book_in_10_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, classes.attended_onl_sc) <= 2 THEN 1
        else 0
    END AS sc_onl_att_in_3_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, classes.attended_onl_sc) <= 6 THEN 1
        else 0
    END AS sc_onl_att_in_7_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, classes.attended_onl_sc) <= 9 THEN 1
        else 0
    END AS sc_onl_att_in_10_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, bookings.booked_onl_sc) <= 2 THEN 1
        else 0
    END AS sc_onl_book_in_3_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, bookings.booked_onl_sc) <= 6 THEN 1
        else 0
    END AS sc_onl_book_in_7_days,
    CASE 
        WHEN DATE_DIFF('day', mess.created_at, bookings.booked_onl_sc) <= 9 THEN 1
        else 0
    END AS sc_onl_book_in_10_days
FROM 
    mess
LEFT JOIN
    activities
ON  activities.message_id = mess.message_id
and activities.student_reference_id = mess.student_reference_id
LEFT JOIN
    sessions
ON  sessions.message_id = mess.message_id
and sessions.student_reference_id = mess.student_reference_id
LEFT JOIN
    classes 
ON  classes.message_id = mess.message_id
and classes.student_reference_id = mess.student_reference_id
LEFT JOIN
    bookings 
ON  bookings.message_id = mess.message_id
and bookings.student_reference_id = mess.student_reference_id
)

SELECT
    ref.campaign,
    ref.channel,
    ref.message,
    ref.message_abbreviated,
    ref.treatment,
    ref.treatment_group,
    date(ref.start_date) as test_start_date,
    date(ref.end_date) as test_end_date,
    mess.message_id,
    mess.created_at,
    ref.outcome_1_name,
    ref.outcome_1_days,
    CASE 
        WHEN ref.outcome_measures_1 = 'days_to_act' THEN mess.days_to_act
        WHEN ref.outcome_measures_1 = 'act_in_1_day' THEN mess.act_in_1_day
        WHEN ref.outcome_measures_1 = 'act_in_3_days' THEN mess.act_in_3_days
        WHEN ref.outcome_measures_1 = 'act_in_7_days' THEN mess.act_in_7_days
        WHEN ref.outcome_measures_1 = 'session_in_7_day' THEN mess.session_in_7_day
        WHEN ref.outcome_measures_1 = 'enc_att_8_days' THEN mess.enc_att_8_days
        WHEN ref.outcome_measures_1 = 'enc_att_6_days' THEN mess.enc_att_6_days
        WHEN ref.outcome_measures_1 = 'enc_pass_14_days' THEN mess.enc_pass_14_days
        WHEN ref.outcome_measures_1 = 'cc_onl_att_in_3_days' THEN mess.cc_onl_att_in_3_days
        WHEN ref.outcome_measures_1 = 'cc_onl_att_in_7_days' THEN mess.cc_onl_att_in_7_days
        WHEN ref.outcome_measures_1 = 'cc_onl_att_in_10_days' THEN mess.cc_onl_att_in_10_days
        WHEN ref.outcome_measures_1 = 'cc_onl_book_in_3_days' THEN mess.cc_onl_book_in_3_days
        WHEN ref.outcome_measures_1 = 'cc_onl_book_in_7_days' THEN mess.cc_onl_book_in_7_days
        WHEN ref.outcome_measures_1 = 'cc_onl_book_in_10_days' THEN mess.cc_onl_book_in_10_days
        WHEN ref.outcome_measures_1 = 'sc_onl_att_in_3_days' THEN mess.sc_onl_att_in_3_days
        WHEN ref.outcome_measures_1 = 'sc_onl_att_in_7_days' THEN mess.sc_onl_att_in_7_days
        WHEN ref.outcome_measures_1 = 'sc_onl_att_in_10_days' THEN mess.sc_onl_att_in_10_days
        WHEN ref.outcome_measures_1 = 'sc_onl_book_in_3_days' THEN mess.sc_onl_book_in_3_days
        WHEN ref.outcome_measures_1 = 'sc_onl_book_in_7_days' THEN mess.sc_onl_book_in_7_days
        WHEN ref.outcome_measures_1 = 'sc_onl_book_in_10_days' THEN mess.sc_onl_book_in_10_days
    END AS outcome_1_value,
    ref.outcome_2_name,
    ref.outcome_2_days,
    CASE 
        WHEN ref."outcome_measures_2" = 'days_to_act' THEN mess.days_to_act
        WHEN ref."outcome_measures_2" = 'act_in_1_day' THEN mess.act_in_1_day
        WHEN ref."outcome_measures_2" = 'act_in_3_days' THEN mess.act_in_3_days
        WHEN ref."outcome_measures_2" = 'act_in_7_days' THEN mess.act_in_7_days
        WHEN ref."outcome_measures_2" = 'session_in_7_day' THEN mess.session_in_7_day
        WHEN ref."outcome_measures_2" = 'enc_att_8_days' THEN mess.enc_att_8_days
        WHEN ref."outcome_measures_2" = 'enc_att_6_days' THEN mess.enc_att_6_days
        WHEN ref."outcome_measures_2" = 'enc_pass_14_days' THEN mess.enc_pass_14_days
        WHEN ref."outcome_measures_2" = 'cc_onl_att_in_3_days' THEN mess.cc_onl_att_in_3_days
        WHEN ref."outcome_measures_2" = 'cc_onl_att_in_7_days' THEN mess.cc_onl_att_in_7_days
        WHEN ref."outcome_measures_2" = 'cc_onl_att_in_10_days' THEN mess.cc_onl_att_in_10_days
        WHEN ref."outcome_measures_2" = 'cc_onl_book_in_3_days' THEN mess.cc_onl_book_in_3_days
        WHEN ref."outcome_measures_2" = 'cc_onl_book_in_7_days' THEN mess.cc_onl_book_in_7_days
        WHEN ref."outcome_measures_2" = 'cc_onl_book_in_10_days' THEN mess.cc_onl_book_in_10_days
        WHEN ref."outcome_measures_2" = 'sc_onl_att_in_3_days' THEN mess.sc_onl_att_in_3_days
        WHEN ref."outcome_measures_2" = 'sc_onl_att_in_7_days' THEN mess.sc_onl_att_in_7_days
        WHEN ref."outcome_measures_2" = 'sc_onl_att_in_10_days' THEN mess.sc_onl_att_in_10_days
        WHEN ref."outcome_measures_2" = 'sc_onl_book_in_3_days' THEN mess.sc_onl_book_in_3_days
        WHEN ref."outcome_measures_2" = 'sc_onl_book_in_7_days' THEN mess.sc_onl_book_in_7_days
        WHEN ref."outcome_measures_2" = 'sc_onl_book_in_10_days' THEN mess.sc_onl_book_in_10_days
    END AS outcome_2_value,
    ref.outcome_3_name,
    ref.outcome_3_days,
    CASE 
        WHEN ref."outcome_measures_3" = 'days_to_act' THEN mess.days_to_act
        WHEN ref."outcome_measures_3" = 'act_in_1_day' THEN mess.act_in_1_day
        WHEN ref."outcome_measures_3" = 'act_in_3_days' THEN mess.act_in_3_days
        WHEN ref."outcome_measures_3" = 'act_in_7_days' THEN mess.act_in_7_days
        WHEN ref."outcome_measures_3" = 'session_in_7_day' THEN mess.session_in_7_day
        WHEN ref."outcome_measures_3" = 'enc_att_8_days' THEN mess.enc_att_8_days
        WHEN ref."outcome_measures_3" = 'enc_att_6_days' THEN mess.enc_att_6_days
        WHEN ref."outcome_measures_3" = 'enc_pass_14_days' THEN mess.enc_pass_14_days
        WHEN ref."outcome_measures_3" = 'cc_onl_att_in_3_days' THEN mess.cc_onl_att_in_3_days
        WHEN ref."outcome_measures_3" = 'cc_onl_att_in_7_days' THEN mess.cc_onl_att_in_7_days
        WHEN ref."outcome_measures_3" = 'cc_onl_att_in_10_days' THEN mess.cc_onl_att_in_10_days
        WHEN ref."outcome_measures_3" = 'cc_onl_book_in_3_days' THEN mess.cc_onl_book_in_3_days
        WHEN ref."outcome_measures_3" = 'cc_onl_book_in_7_days' THEN mess.cc_onl_book_in_7_days
        WHEN ref."outcome_measures_3" = 'cc_onl_book_in_10_days' THEN mess.cc_onl_book_in_10_days
        WHEN ref."outcome_measures_3" = 'sc_onl_att_in_3_days' THEN mess.sc_onl_att_in_3_days
        WHEN ref."outcome_measures_3" = 'sc_onl_att_in_7_days' THEN mess.sc_onl_att_in_7_days
        WHEN ref."outcome_measures_3" = 'sc_onl_att_in_10_days' THEN mess.sc_onl_att_in_10_days
        WHEN ref."outcome_measures_3" = 'sc_onl_book_in_3_days' THEN mess.sc_onl_book_in_3_days
        WHEN ref."outcome_measures_3" = 'sc_onl_book_in_7_days' THEN mess.sc_onl_book_in_7_days
        WHEN ref."outcome_measures_3" = 'sc_onl_book_in_10_days' THEN mess.sc_onl_book_in_10_days
    END AS outcome_3_value,
    ref.outcome_4_name,
    ref.outcome_4_days,
    CASE 
        WHEN ref."outcome_measures_4" = 'days_to_act' THEN mess.days_to_act
        WHEN ref."outcome_measures_4" = 'act_in_1_day' THEN mess.act_in_1_day
        WHEN ref."outcome_measures_4" = 'act_in_3_days' THEN mess.act_in_3_days
        WHEN ref."outcome_measures_4" = 'act_in_7_days' THEN mess.act_in_7_days
        WHEN ref."outcome_measures_4" = 'session_in_7_day' THEN mess.session_in_7_day
        WHEN ref."outcome_measures_4" = 'enc_att_8_days' THEN mess.enc_att_8_days
        WHEN ref."outcome_measures_4" = 'enc_att_6_days' THEN mess.enc_att_6_days
        WHEN ref."outcome_measures_4" = 'enc_pass_14_days' THEN mess.enc_pass_14_days
        WHEN ref."outcome_measures_4" = 'cc_onl_att_in_3_days' THEN mess.cc_onl_att_in_3_days
        WHEN ref."outcome_measures_4" = 'cc_onl_att_in_7_days' THEN mess.cc_onl_att_in_7_days
        WHEN ref."outcome_measures_4" = 'cc_onl_att_in_10_days' THEN mess.cc_onl_att_in_10_days
        WHEN ref."outcome_measures_4" = 'cc_onl_book_in_3_days' THEN mess.cc_onl_book_in_3_days
        WHEN ref."outcome_measures_4" = 'cc_onl_book_in_7_days' THEN mess.cc_onl_book_in_7_days
        WHEN ref."outcome_measures_4" = 'cc_onl_book_in_10_days' THEN mess.cc_onl_book_in_10_days
        WHEN ref."outcome_measures_4" = 'sc_onl_att_in_3_days' THEN mess.sc_onl_att_in_3_days
        WHEN ref."outcome_measures_4" = 'sc_onl_att_in_7_days' THEN mess.sc_onl_att_in_7_days
        WHEN ref."outcome_measures_4" = 'sc_onl_att_in_10_days' THEN mess.sc_onl_att_in_10_days
        WHEN ref."outcome_measures_4" = 'sc_onl_book_in_3_days' THEN mess.sc_onl_book_in_3_days
        WHEN ref."outcome_measures_4" = 'sc_onl_book_in_7_days' THEN mess.sc_onl_book_in_7_days
        WHEN ref."outcome_measures_4" = 'sc_onl_book_in_10_days' THEN mess.sc_onl_book_in_10_days
    END AS outcome_4_value,
    ref.outcome_5_name,
    ref.outcome_5_days,
    CASE 
        WHEN ref."outcome_measures_5" = 'days_to_act' THEN mess.days_to_act
        WHEN ref."outcome_measures_5" = 'act_in_1_day' THEN mess.act_in_1_day
        WHEN ref."outcome_measures_5" = 'act_in_3_days' THEN mess.act_in_3_days
        WHEN ref."outcome_measures_5" = 'act_in_7_days' THEN mess.act_in_7_days
        WHEN ref."outcome_measures_5" = 'session_in_7_day' THEN mess.session_in_7_day
        WHEN ref."outcome_measures_5" = 'enc_att_8_days' THEN mess.enc_att_8_days
        WHEN ref."outcome_measures_5" = 'enc_att_6_days' THEN mess.enc_att_6_days
        WHEN ref."outcome_measures_5" = 'enc_pass_14_days' THEN mess.enc_pass_14_days
        WHEN ref."outcome_measures_5" = 'cc_onl_att_in_3_days' THEN mess.cc_onl_att_in_3_days
        WHEN ref."outcome_measures_5" = 'cc_onl_att_in_7_days' THEN mess.cc_onl_att_in_7_days
        WHEN ref."outcome_measures_5" = 'cc_onl_att_in_10_days' THEN mess.cc_onl_att_in_10_days
        WHEN ref."outcome_measures_5" = 'cc_onl_book_in_3_days' THEN mess.cc_onl_book_in_3_days
        WHEN ref."outcome_measures_5" = 'cc_onl_book_in_7_days' THEN mess.cc_onl_book_in_7_days
        WHEN ref."outcome_measures_5" = 'cc_onl_book_in_10_days' THEN mess.cc_onl_book_in_10_days
        WHEN ref."outcome_measures_5" = 'sc_onl_att_in_3_days' THEN mess.sc_onl_att_in_3_days
        WHEN ref."outcome_measures_5" = 'sc_onl_att_in_7_days' THEN mess.sc_onl_att_in_7_days
        WHEN ref."outcome_measures_5" = 'sc_onl_att_in_10_days' THEN mess.sc_onl_att_in_10_days
        WHEN ref."outcome_measures_5" = 'sc_onl_book_in_3_days' THEN mess.sc_onl_book_in_3_days
        WHEN ref."outcome_measures_5" = 'sc_onl_book_in_7_days' THEN mess.sc_onl_book_in_7_days
        WHEN ref."outcome_measures_5" = 'sc_onl_book_in_10_days' THEN mess.sc_onl_book_in_10_days
    END AS outcome_5_value,
    ref.outcome_6_name,
    ref.outcome_6_days,
    CASE 
        WHEN ref."outcome_measures_6" = 'days_to_act' THEN mess.days_to_act
        WHEN ref."outcome_measures_6" = 'act_in_1_day' THEN mess.act_in_1_day
        WHEN ref."outcome_measures_6" = 'act_in_3_days' THEN mess.act_in_3_days
        WHEN ref."outcome_measures_6" = 'act_in_7_days' THEN mess.act_in_7_days
        WHEN ref."outcome_measures_6" = 'session_in_7_day' THEN mess.session_in_7_day
        WHEN ref."outcome_measures_6" = 'enc_att_8_days' THEN mess.enc_att_8_days
        WHEN ref."outcome_measures_6" = 'enc_att_6_days' THEN mess.enc_att_6_days
        WHEN ref."outcome_measures_6" = 'enc_pass_14_days' THEN mess.enc_pass_14_days
        WHEN ref."outcome_measures_6" = 'cc_onl_att_in_3_days' THEN mess.cc_onl_att_in_3_days
        WHEN ref."outcome_measures_6" = 'cc_onl_att_in_7_days' THEN mess.cc_onl_att_in_7_days
        WHEN ref."outcome_measures_6" = 'cc_onl_att_in_10_days' THEN mess.cc_onl_att_in_10_days
        WHEN ref."outcome_measures_6" = 'cc_onl_book_in_3_days' THEN mess.cc_onl_book_in_3_days
        WHEN ref."outcome_measures_6" = 'cc_onl_book_in_7_days' THEN mess.cc_onl_book_in_7_days
        WHEN ref."outcome_measures_6" = 'cc_onl_book_in_10_days' THEN mess.cc_onl_book_in_10_days
        WHEN ref."outcome_measures_6" = 'sc_onl_att_in_3_days' THEN mess.sc_onl_att_in_3_days
        WHEN ref."outcome_measures_6" = 'sc_onl_att_in_7_days' THEN mess.sc_onl_att_in_7_days
        WHEN ref."outcome_measures_6" = 'sc_onl_att_in_10_days' THEN mess.sc_onl_att_in_10_days
        WHEN ref."outcome_measures_6" = 'sc_onl_book_in_3_days' THEN mess.sc_onl_book_in_3_days
        WHEN ref."outcome_measures_6" = 'sc_onl_book_in_7_days' THEN mess.sc_onl_book_in_7_days
        WHEN ref."outcome_measures_6" = 'sc_onl_book_in_10_days' THEN mess.sc_onl_book_in_10_days
    END AS outcome_6_value
FROM
    messages as mess
LEFT JOIN
    {{ ref('novu_reference') }} as ref
ON  mess.notification_id = ref.workflow_id
AND (mess.variant_id = ref.variant_id or ref.variant_id is null)
WHERE mess.rn = 1