create table if not exists hubspot_crm.contactsenriched
(
    actual_status                                 varchar(28),
    address                                       varchar(260),
    agreement_type                                varchar(15),
    booked_date                                   timestamp encode az64,
    brand_name                                    varchar(8),
    call_campaign                                 varchar(200),
    call_count                                    bigint encode az64,
    campana_mql                                   varchar(64),
    center_name                                   varchar(64),
    channel                                       varchar(400),
    channel_drill_down_1                          varchar(200),
    channel_drill_down_2                          varchar(200),
    city                                          varchar(248),
    contract_date                                 date encode az64,
    core_course_idam_userid                       varchar(10),
    core_course_student_id                        varchar(56),
    course_age_group                              varchar(8),
    course_type                                   varchar(16),
    createdate                                    timestamp encode az64,
    decision_maker_date                           timestamp encode az64,
    email                                         varchar(148),
    excludefromstats__c                           varchar(8),
    first_contract_amount                         double precision,
    first_contract_center                         varchar(36),
    first_contract_owner                          varchar(12),
    first_conversion_date                         timestamp encode az64,
    first_conversion_event_name                   varchar(443),
    first_source                                  varchar(22),
    first_sub_source                              varchar(36),
    firstname                                     varchar(264),
    gender                                        varchar(17),
    how_did_you_hear_about_us                     varchar(38),
    hs_analytics_first_url                        varchar(4400),
    hs_analytics_source                           varchar(15),
    hs_analytics_source_data_1                    varchar(800),
    hs_analytics_source_data_2                    varchar(400),
    hs_createdate                                 timestamp encode az64,
    hs_lastmodifieddate                           timestamp encode az64,
    hs_lifecyclestage_customer_date               timestamp encode az64,
    hs_lifecyclestage_evangelist_date             timestamp encode az64,
    hs_lifecyclestage_lead_date                   timestamp encode az64,
    hs_lifecyclestage_marketingqualifiedlead_date timestamp encode az64,
    hs_lifecyclestage_opportunity_date            timestamp encode az64,
    hs_lifecyclestage_other_date                  timestamp encode az64,
    hs_lifecyclestage_salesqualifiedlead_date     timestamp encode az64,
    hs_object_id                                  bigint encode az64,
    hubspot_owner_id                              bigint encode az64,
    hubspotscore                                  integer encode az64,
    individual_corporate                          varchar(12),
    last_touch_utm_campaign                       varchar(2),
    last_touch_utm_medium                         varchar(2),
    last_touch_utm_referral                       varchar(2),
    last_touch_utm_source                         varchar(2),
    lastmodifieddate                              timestamp encode az64,
    lastname                                      varchar(400),
    latest_source                                 varchar(10),
    latest_source_drill_down_1                    varchar(12),
    latest_source_drill_down_2                    varchar(12),
    lead_date                                     timestamp encode az64,
    lead_source                                   varchar(64),
    lifecyclestage                                varchar(22),
    lost_date                                     timestamp encode az64,
    medio_landing_mql                             varchar(64),
    mql_date                                      timestamp encode az64,
    not_interested_suitable_reason                varchar(66),
    notes_last_updated                            timestamp encode az64,
    notes_next_activity_date                      timestamp encode az64,
    phone                                         varchar(100),
    provedor_mql                                  varchar(64),
    province                                      varchar(48),
    qu_te_motiva_a_aprender_ingl_s_               varchar(14),
    qualifying_stage                              varchar(64),
    reason_for_learning_english                   varchar(120),
    recent_conversion_date                        timestamp encode az64,
    recent_conversion_event_name                  varchar(443),
    show_date                                     timestamp encode az64,
    source                                        varchar(42),
    sub_source                                    varchar(86),
    tmk_owner                                     varchar(9),
    useful_contact_date                           date encode az64,
    where_did_you_hear_about_us                   varchar(64),
    zip                                           varchar(168),
    id                                            bigint encode az64,
    createdat                                     timestamp encode az64,
    updatedat                                     timestamp encode az64,
    territory_code                                varchar(2),
    archived                                      varchar(5),
    cycleid                                       bigint encode az64,
    deleteflag                                    varchar(1),
    deleteddate                                   timestamp encode az64,
    restoreddate                                  timestamp encode az64,
    archiveddate                                  timestamp encode az64,
    timezonecreatedate                            timestamp encode az64,
    territory_name                                varchar(48),
    hs_merged_object_ids                          varchar(424)
);


