{{ config(
    tags=["speaking_ai_beta"],
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'chat_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}


with ods_data as (
    select * 
    from 
    {{ ref('ods_sai_feedback') }}

    {% if is_incremental() %}
        where completed_date > ((select max(completed_date) from {{ this }}))
    {% endif %}
)

Select
  {{etl_load_date()}},
  ods.chat_id,
  ods.user_id,
  ods.user_type,
  ods.feedback,
  ods.completed_date,
  CASE 
    WHEN DATE(ods.completed_date) != DATE('0001-01-01') 
    THEN {{convert_to_local_timestamp('ods.completed_date','tz.time_zone_id')}}
    ELSE ods.completed_date
  END AS local_completed_date
  FROM
  ods_data as ods
  left join (
    select 
    ssds_id,
    center_id
    from {{ref('ods_ls_user')}}
  ) as user on user.ssds_id = ods.user_id
  left join(
    select
    id,
    reference_center_id
    from {{ ref('ods_ls_center') }}
  )as ls_center on user.center_id = ls_center.id
  left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on ls_center.reference_center_id = tz.center_reference_id
