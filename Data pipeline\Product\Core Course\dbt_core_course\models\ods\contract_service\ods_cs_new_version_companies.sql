{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        name,
        code,
        centerowned,
        territoryid,
        licensenumber,
        createdbyid,
        modifiedbyid,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'newversioncompanies')}}
)

SELECT
    {{etl_load_date()}},
    id,
    name,
    code,
    centerowned as center_owned,
    territoryid as territory_id,
    licensenumber as license_number,
    createdbyid as created_by_id,
    modifiedbyid as modified_by_id,
    {{cast_to_timestamp('createddate')}} as created_date,
    {{cast_to_timestamp('lastupdateddate')}} as last_updated_date
FROM
    RankedRecords