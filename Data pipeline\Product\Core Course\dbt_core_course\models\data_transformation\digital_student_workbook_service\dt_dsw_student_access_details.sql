{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_dsw_student_access_details'
        ) }}

    {% if is_incremental() %}
        where last_updated > ((select max(last_updated) from {{ this }}))
    {% endif %}
)

SELECT
    {{etl_load_date()}}
    ,id
    ,contract_id
    ,student_id
    ,level_number
    ,has_digital_work_book_access
    ,is_editable
    ,release_workbook
    ,CASE 
        When operation_type = 0 then 'none'
        When operation_type = 1 then 'bill'
        When operation_type = 2 then 'refund'
        When operation_type = 3 then 'migrated'
        Else CAST(operation_type as varchar)
    end as operation_type
    ,created
    ,{{convert_to_local_timestamp('created','time_zone_id')}} as local_created
    ,last_updated 
    ,{{convert_to_local_timestamp('last_updated','time_zone_id')}} as local_last_updated
    ,is_active
    ,unlocked_date
    ,{{convert_to_local_timestamp('unlocked_date','time_zone_id')}} as local_unlocked_date
    ,std_acc_details.center_reference_id
    ,CASE
        When review_Mode = 0 then 'none'
        When review_Mode = 1 then 'migrated'
        Else CAST(review_Mode as varchar)
    end as review_Mode
FROM 
    ods_data as std_acc_details
    Left Join (
                select center_reference_id,
                    time_zone_id
                from {{ ref ('ods_cc_center') }}
    ) as tz on std_acc_details.center_reference_id = tz.center_reference_id