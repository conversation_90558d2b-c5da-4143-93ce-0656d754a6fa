{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet',
) }}


WITH base_data AS (
  SELECT
      *,
      ROW_NUMBER() OVER (PARTITION BY user_id, class_id ORDER BY event_date, event_timestamp) AS rn
  FROM {{ source('bigquery', 'camera_usage_events') }}
),
 
class_group AS (
  SELECT
      event_date,
      event_timestamp,
      event_name,
      user_id,
      platform,
      user_level,
      user_origin_level,
      user_center,
      user_role,
      class_id,
      class_access,
      camera_usage_time,
      SUM(CASE WHEN class_id IS NOT NULL THEN 1 ELSE 0 END)
          OVER (PARTITION BY user_id, event_date ORDER BY event_date, event_timestamp) AS class_group
  FROM base_data
  WHERE rn = 1
),
daily_user_classes AS (
  SELECT
      event_date,
      user_id,
      platform,
      user_role,
      class_id,
      SUM(camera_usage_time) AS total_camera_usage_time,
      CASE WHEN SUM(camera_usage_time) > 0 THEN 1 ELSE 0 END AS user_turned_camera,
      CASE WHEN SUM(camera_usage_time) = 0 THEN 1 ELSE 0 END AS user_not_turned_camera,
      COUNT(class_id) AS events,
      MIN(event_timestamp) AS start_class_timestamp,
      MAX(event_timestamp) AS last_event_of_the_class
  FROM class_group
  GROUP BY
      event_date,
      user_id,
      platform,
      user_role,
      class_id
),
-- Aggregate student rows for the same event_date and class_id
student_data AS (
  SELECT 
    event_date,
    class_id,
    user_id,
    platform,
    SUM(total_camera_usage_time) AS students_total_camera_usage_time,
    SUM(CASE WHEN user_turned_camera = 1 THEN user_turned_camera ELSE 0 END) AS user_turned_camera,
    SUM(CASE WHEN user_not_turned_camera = 1 THEN user_not_turned_camera ELSE 0 END) AS user_not_turned_camera,
    MAX(events) AS events,  -- Assuming all student rows share the same event value
    MIN(start_class_timestamp) AS start_class_timestamp,
    MAX(last_event_of_the_class) AS last_event_of_the_class
  FROM daily_user_classes
  WHERE user_role = 'student'
  GROUP BY event_date, class_id, user_id, platform
)

SELECT
    fds."date",
    CASE WHEN fds.group_id = 'Individual' then 'Individual' else 'Group' end as group_id,
    fds.center_reference_id,
    fds.contract_type,
    fds.location,
    fds.class_access_type,
    fds.service_type,
    fds.is_membership,
    fds.is_teen,
    fds.is_promotional,
    s.class_id,
    platform,
    -- Aggregate student metrics
    COUNT(DISTINCT CASE WHEN s.user_turned_camera > 0 THEN s.user_id END) AS students_with_camera_on,
    COUNT(DISTINCT CASE WHEN s.user_not_turned_camera > 0 THEN s.user_id END) AS students_without_camera,
    SUM(s.students_total_camera_usage_time) AS total_camera_usage_time
    
FROM student_data s
LEFT JOIN {{ ref('fact_daily_students') }} fds 
ON s.event_date = fds."date" 
AND s.user_id = fds.student_reference_id 
-- Group by center and contract details
GROUP BY 
    fds."date",
    CASE WHEN fds.group_id = 'Individual' then 'Individual' else 'Group' end,
    fds.center_reference_id,
    fds.contract_type,
    fds.location,
    fds.class_access_type,
    fds.service_type,
    fds.is_membership,
    fds.is_teen,
    fds.is_promotional,
    s.class_id,
    platform