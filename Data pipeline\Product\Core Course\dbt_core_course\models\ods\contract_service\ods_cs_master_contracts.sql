{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        mastercontractnumber,
        centerid,
        territoryid,
        companyid,
        {{cast_to_timestamp('startdate')}} as startdate,
        {{cast_to_timestamp('enddate')}} as enddate,
        {{cast_to_timestamp('saledate')}} as saledate,
        consultantid,
        state,
        status,
        {{cast_to_timestamp('canceldate')}} as canceldate,
        reason,
        modifiedbyid,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
        currentvalidationstate,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'mastercontracts')}}
)

SELECT
    {{etl_load_date()}},
    id,
    mastercontractnumber as master_contract_number,
    centerid as center_id,
    territoryid as territory_id,
    companyid as company_id,
    startdate as start_date,
    enddate as end_date,
    saledate as sale_date,
    consultantid as consultant_id,
    state,
    status,
    {{cast_to_timestamp('canceldate')}} as cancel_date,
    reason,
    modifiedbyid as modified_by_id,
    {{cast_to_timestamp('createddate')}} as created_date,
    {{cast_to_timestamp('lastupdateddate')}} as last_updated_date,
    currentvalidationstate as current_validation_state
FROM 
    RankedRecords
WHERE 
    rn = 1;