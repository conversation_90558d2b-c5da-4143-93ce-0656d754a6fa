{{
    config(
        tags=["test"],
    )
}}

SELECT 
_Address_ as page,
MAX(crawl_date) crawl_date,
STRING_AGG(CAST(status_code AS STRING) order by crawl_timestamp desc LIMIT 1) as status_code,
string_agg(title_1 order by crawl_timestamp desc LIMIT 1) AS page_title,
string_agg(cast(Title_1_Pixel_Width as string) order by crawl_timestamp desc LIMIT 1) AS page_title_length_pixel,
string_agg(Meta_Description_1 order by crawl_timestamp desc LIMIT 1) AS meta_description,
string_agg(cast(Meta_Description_1_Pixel_Width as string) order by crawl_timestamp desc LIMIT 1) AS meta_description_length_pixel,
string_agg(body_1 order by crawl_timestamp desc LIMIT 1) AS page_content,
string_agg(Canonical_Link_Element_1 order by crawl_timestamp desc LIMIT 1) AS canonical_page,
string_agg(Meta_Robots_1 order by crawl_timestamp desc LIMIT 1) as meta_robots,
FROM {{ source('crawl', 'wse') }}
where (content_type LIKE "%text/html%" or content_type LIKE "")
GROUP BY page
