{"Comment": "A description of my state machine", "StartAt": "ExecutionPlanner", "States": {"ExecutionPlanner": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "ExecutionPlanner", "Arguments": {"--input.$": "$.input"}}, "Next": "ReadExecutionPlannerFile", "ResultPath": null}, "ReadExecutionPlannerFile": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:ReadExecutionPlannerFile:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "ExecutionInfo"}, "ExecutionInfo": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "StageDecide", "States": {"StageDecide": {"Type": "Choice", "Choices": [{"Variable": "$.Stage1", "IsPresent": true, "Next": "Stage1"}, {"Variable": "$.Stage2", "IsPresent": true, "Next": "Stage2"}, {"Variable": "$.Stage3", "IsPresent": true, "Next": "Stage3"}, {"Variable": "$.Stage4", "IsPresent": true, "Next": "Stage4"}, {"Variable": "$.Stage5", "IsPresent": true, "Next": "Stage5"}]}, "Stage1": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "DataExtraction", "States": {"DataExtraction": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "GoogleTrendsDataExtaction", "Arguments": {"--Status.$": "States.Format('{}', $.Status)", "--Stage.$": "States.Format('{}', $.Stage)", "--Operation.$": "$.Operation", "--Territory.$": "$.Territory", "--ServiceType.$": "$.ServiceType", "--Competitors.$": "States.Format('{}',$.Competitors)", "--CycleId.$": "States.Format('{}', $.CycleId)", "--CutoffDate.$": "States.Format('{}', $.CutoffDate)", "--LoadType.$": "$.LoadType"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "ErrorNotification"}]}, "ErrorNotification": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:fail_case"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "ItemsPath": "$.Stage1", "MaxConcurrency": 40, "Next": "GoogleTrendsCheckStage1"}, "GoogleTrendsCheckStage1": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:GoogleTrendsCheckStage1"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}, "Stage2": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "stage_2_lambda", "States": {"stage_2_lambda": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:pass_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "MaxConcurrency": 1, "ItemsPath": "$.Stage2"}, "Stage3": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "stage_3_lambda", "States": {"stage_3_lambda": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:pass_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "MaxConcurrency": 1, "ItemsPath": "$.Stage3"}, "Stage4": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "stage_4_lambda", "States": {"stage_4_lambda": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:pass_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "MaxConcurrency": 1, "ItemsPath": "$.Stage4"}, "Stage5": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "stage_5_lambda", "States": {"stage_5_lambda": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:pass_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "MaxConcurrency": 1, "ItemsPath": "$.Stage5"}}}, "MaxConcurrency": 1, "ItemsPath": "$.ExecutionInfo", "Next": "PassCase", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "FailCase"}]}, "FailCase": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:fail_case"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "GoogleTrendsExecutionStatusUpdate", "OutputPath": "$.Payload"}, "GoogleTrendsExecutionStatusUpdate": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:GoogleTrendsExecutionStatusUpdate"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "ExecutionStatusUpdate"}, "ExecutionStatusUpdate": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "ExecutionStatusUpdate", "Arguments": {"--input.$": "$.input"}}, "End": true}, "PassCase": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-west-1:262158335980:function:pass_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "GoogleTrendsExecutionStatusUpdate"}}}