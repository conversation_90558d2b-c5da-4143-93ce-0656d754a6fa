import pandas as pd
import logging


class DataFrameCreate:
    @staticmethod
    def dataframe_module(DataFetchObject, NullDatafixConnect, spark, Bucket, CycleId, ConfigInfo, Object,
                         TerritoryCode):
        df = pd.DataFrame(DataFetchObject)
        df = NullDatafixConnect.null_data_fix(df)
        # Converting a pandas dataframe to spark dataframe
        spark_df = spark.createDataFrame(df)
        spark_df.show()
        logging.warning("The data frame has been created successfully")
        s3_path = "s3://" + Bucket + "/HubspotRawFiles/" + f"{CycleId}" + f"/Stage{ConfigInfo['Stage']}" + f"/{ConfigInfo['Operation']}" + f"/{Object}" + f"/{TerritoryCode}"
        spark_df.repartition(1).write.parquet(s3_path)
        return spark_df
