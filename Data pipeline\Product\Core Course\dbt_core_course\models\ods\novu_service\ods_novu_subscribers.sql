{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = ['id'],
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}



SELECT
    {{etl_load_date()}},
    id,
    subscriberid AS subscriber_id,
    CAST(json_extract_scalar(data, '$.userrange') AS INTEGER) AS user_range,
    CAST(json_extract_scalar(data, '$.platform') AS varchar) AS platform,
    CAST(from_iso8601_timestamp(createdat) AS timestamp(6)) AS created_at,
    CAST(from_iso8601_timestamp(updatedat) AS timestamp(6)) AS updated_at
FROM
{{ source('stage_novu_service','subscribers') }}