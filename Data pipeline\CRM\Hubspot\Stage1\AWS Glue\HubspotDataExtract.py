import sys
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark import SparkConf, SparkContext
from awsglue.context import GlueContext
from pyspark.sql import SparkSession
from awsglue.job import Job
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, TimestampType
import ast
import timeit
import logging
import pandas as pd
from pyspark.sql.functions import col, when

# Importing necessary modules
import OwnersDataExtract
import LogFileGeneration
import objects_data_fetch
import PostApiCall
import data_fetch_formatting
import NullDataFix
import LoggingStatements
import DataframeModule

# Getting configuration options
ConfigInfo = getResolvedOptions(sys.argv,
                                ['Stage', 'Territory', 'Object', 'Status', 'Properties', 'Operation', 'Filter', 'Url',
                                 'CutoffDate', 'DefaultProperties', 'CycleId','Bucket','contactsproperties','dealsproperties'])
if ConfigInfo['Object'] == 'associationcontacts':
    Properties = ast.literal_eval(ConfigInfo['contactsproperties'])
elif ConfigInfo['Object'] == 'associationdeals':
    Properties = ast.literal_eval(ConfigInfo['dealsproperties'])
else:
    Properties = ast.literal_eval(ConfigInfo['Properties'])
DefaultProperties = ast.literal_eval(ConfigInfo['DefaultProperties'])

logging.warning("Configinfo:'%s'", format(ConfigInfo))
logging.warning("properties:'%s'", format(Properties))
logging.warning("DefaultProperties:'%s'", format(DefaultProperties))

# Creating Spark context and session
conf = SparkConf()
conf.setMaster("local").setAppName("My app")
sc = SparkContext.getOrCreate(conf=conf)
spark = SparkSession(sc)
starttime = timeit.default_timer()

# Setting up necessary connections and variables
OwnersConnect = OwnersDataExtract.OwnersDataFetch
ObjectConnect = objects_data_fetch.ObjectFetch
DataFormatConnect = data_fetch_formatting.DataFormating
NullDatafixConnect = NullDataFix.NullDataFill
LogsStatementConnect = LoggingStatements.LogStatements
DataframeConnect = DataframeModule.DataFrameCreate
APICall = PostApiCall
Logs = LogFileGeneration.LogFile
Bucket = ConfigInfo['Bucket']

# Retrieving necessary information from the configuration
TerritoryCode = ConfigInfo['Territory']
Object = ConfigInfo['Object']
CycleId = ConfigInfo['CycleId']
logging.warning("Object:'%s'", format(Object))
logging.warning("Cycleid : '%s'", format(CycleId))
logging.warning(TerritoryCode)

# Checking if the Object is 'contacts' or 'deals'
if Object == 'contacts' or Object == 'deals':
    try:
        # Fetching incremental data for contacts or deals
        DataFetchObject = ObjectConnect.object_incremental(ConfigInfo, Bucket, Properties,
                                                           DefaultProperties, CycleId, Object)
        # logging.warning(DataFetchObject)
        ApiCalls = APICall.ApiCount()
        logstatements = LogsStatementConnect.logs_statements(ApiCalls, DataFetchObject)
        # Checking if there are no records to process
        if len(DataFetchObject) == 0:
            LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'], CutOffDate=ConfigInfo['CutoffDate'],
                                           Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                           RecordsProcessed=len(DataFetchObject), NoOfApiCall=ApiCalls,
                                           CycleId=CycleId, Object=Object, Bucket=Bucket)

        else:
            # Generating the cutoff date
            CutOffDate = DataFormatConnect.CutoffDateGenerator(DataFetchObject, FilterProperty=ConfigInfo['Filter'])

            # Converting the data to a DataFrame
            df = pd.DataFrame(DataFetchObject)
            # df.columns
            df.fillna('', inplace=True)

            if Object == 'contacts':
                df = df.rename(columns={'actualstatus__c': 'actual_status'})
                logging.warning("For contacts it has been renamed")

            spark_df = spark.createDataFrame(df)
            spark_df.show()
            logging.warning("The data frame has been created successfully")
            # Writing the DataFrame to Parquet format in S3
            s3_path = "s3:// " + Bucket + "/HubspotRawFiles/" + f"{CycleId}" + f"/Stage{ConfigInfo['Stage']}" + f"/{ConfigInfo['Operation']}" + f"/{Object}" + f"/{TerritoryCode}"
            spark_df.repartition(1).write.parquet(s3_path)
            # Generating log file
            LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'], CutOffDate=CutOffDate,
                                           Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                           RecordsProcessed=len(DataFetchObject), NoOfApiCall=ApiCalls,
                                           CycleId=CycleId, Object=Object, Bucket=Bucket)
    except Exception as error:
        raise error

if Object == 'owners':
    try:
        # Fetching data for owners
        DataFetchObject = OwnersConnect.GetExtract(ConfigInfo, Bucket, Properties, DefaultProperties, CycleId, Object)
        OwnersDataFetchObject = DataFetchObject[0]
        logging.warning("The Owners data is :'%s'", format(OwnersDataFetchObject))
        ApiCalls = APICall.ApiCount()

        # Checking if there are no owners data to process
        if len(OwnersDataFetchObject) == 0:
            LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'],
                                           CutOffDate=ConfigInfo['CutoffDate'],
                                           Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                           RecordsProcessed=len(OwnersDataFetchObject), NoOfApiCall=ApiCalls,
                                           CycleId=CycleId, Object=Object, Bucket=Bucket)
        else:
            # Cleaning and transforming the owners data
            json_data_cleaned = [{k: v if v is not None else "" for k, v in d.items()} for d in OwnersDataFetchObject]
            logging.warning(json_data_cleaned)
            df = pd.DataFrame(json_data_cleaned)
            df = NullDatafixConnect.null_data_fix(df)
            df['userId'] = df['userId'].astype('str')
            spark_df = spark.createDataFrame(df)
            spark_df = spark_df.withColumn('userId', col('userId').cast('long'))
            spark_df.show()
            logging.warning("The data frame has been created successfully")
            # Writing the DataFrame to Parquet format in S3

            s3_path = "s3://" + Bucket +"/HubspotRawFiles/" + f"{CycleId}" + f"/Stage{ConfigInfo['Stage']}" + f"/{ConfigInfo['Operation']}" + f"/{Object}" + f"/{TerritoryCode}"
            spark_df.repartition(1).write.parquet(s3_path)
            logstatements = LogsStatementConnect.logs_statements(ApiCalls, OwnersDataFetchObject)
            # Generating log file
            LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'], CutOffDate=None,
                                           Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                           RecordsProcessed=len(OwnersDataFetchObject), NoOfApiCall=ApiCalls,
                                           CycleId=CycleId, Object=Object, Bucket=Bucket)

            # Fetching data for teams
            TeamsDataFetchObject = DataFetchObject[1]
            logging.warning("The Teams data is :'%s'", format(TeamsDataFetchObject))

            # Checking if there are no teams data to process
            if len(TeamsDataFetchObject) == 0:
                logging.warning("There is no teams data")
            else:
                # Cleaning and transforming the teams data
                Df_Teams = pd.DataFrame(TeamsDataFetchObject)
                Df_Teams = NullDatafixConnect.null_data_fix(Df_Teams)
                Df_Teams['primary'].mask(Df_Teams['primary'] == True, 'True', inplace=True)
                Df_Teams['primary'].mask(Df_Teams['primary'] == False, 'False', inplace=True)
                spark_df = spark.createDataFrame(Df_Teams)
                logging.warning("The data frame has been created successfully")

                # Writing the DataFrame to Parquet format in S3
                s3_path = "s3://" + Bucket + "/HubspotRawFiles/" + f"{CycleId}" + f"/Stage{ConfigInfo['Stage']}" + f"/{ConfigInfo['Operation']}" + f"/Teams" + f"/{TerritoryCode}"
                spark_df.repartition(1).write.parquet(s3_path)
    except Exception as error:
        raise error

if Object in ['associationdeals', 'associationcontacts', 'associationcompanies']:
    try:
        # Fetching data based on the object type
        DataFetchObject = OwnersConnect.GetExtract(ConfigInfo, Bucket, Properties, DefaultProperties, CycleId, Object)
        # logging.warning("printing the data fetch object tuple format :'%s'", format(DataFetchObject))
        if Object == 'associationcontacts':
            Contacts = DataFetchObject[3]
            ObjectData = 'contacts'
            ApiCalls = APICall.ApiCount()
            logstatements = LogsStatementConnect.logs_statements(ApiCalls, Contacts)
            if len(Contacts) == 0:
                logging.warning("There is no data for Territory :'%s'", format(TerritoryCode))
                raise Exception
            else:
                spark_df = DataframeConnect.dataframe_module(Contacts, NullDatafixConnect, spark, Bucket,
                                                             CycleId,
                                                             ConfigInfo, ObjectData, TerritoryCode)
                LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'],
                                               CutOffDate=ConfigInfo['CutoffDate'],
                                               Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                               RecordsProcessed=len(Contacts), NoOfApiCall=ApiCalls,
                                               CycleId=CycleId, Object=ObjectData, Bucket=Bucket)
            # Processing association contacts
            ContactstoDeals = DataFetchObject[2]
            ObjectValue = 'ContactstoDeals'
            if len(ContactstoDeals) == 0:
                logging.warning("There is no data for Territory :'%s'", format(TerritoryCode))
            else:
                spark_df = DataframeConnect.dataframe_module(ContactstoDeals, NullDatafixConnect, spark, Bucket,
                                                             CycleId,
                                                             ConfigInfo, ObjectValue, TerritoryCode)

            ContactstoCompanies = DataFetchObject[1]
            ObjectValue = 'ContactstoCompanies'
            logging.warning("The length of companies is:'%s'", format(str(len(ContactstoCompanies))))
            if len(ContactstoCompanies) == 0:
                logging.warning("There is no data for territory :'%s'", format(TerritoryCode))
            else:
                spark_df = DataframeConnect.dataframe_module(ContactstoCompanies, NullDatafixConnect, spark, Bucket,
                                                             CycleId,
                                                             ConfigInfo, ObjectValue, TerritoryCode)

        ApiCalls = APICall.ApiCount()
        LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'],
                                       CutOffDate=ConfigInfo['CutoffDate'],
                                       Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                       RecordsProcessed=0, NoOfApiCall=ApiCalls,
                                       CycleId=CycleId, Object=Object, Bucket=Bucket)
        if Object == 'associationdeals':
            Deals = DataFetchObject[4]
            ObjectData = 'deals'
            ApiCalls = APICall.ApiCount()
            logstatements = LogsStatementConnect.logs_statements(ApiCalls, Deals)
            if len(Deals) == 0:
                logging.warning("There is no data for Territory :'%s'", format(TerritoryCode))
                raise Exception
            else:
                spark_df = DataframeConnect.dataframe_module(Deals, NullDatafixConnect, spark, Bucket,
                                                             CycleId,
                                                             ConfigInfo, ObjectData, TerritoryCode)
                LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'],
                                               CutOffDate=ConfigInfo['CutoffDate'],
                                               Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                               RecordsProcessed=len(Deals), NoOfApiCall=ApiCalls,
                                               CycleId=CycleId, Object=ObjectData, Bucket=Bucket)
            DealstoContacts = DataFetchObject[0]
            ObjectValue = 'DealstoContacts'
            if len(DealstoContacts) == 0:
                # Processing association deals
                logging.warning("There is no data for territory :'%s'", format(TerritoryCode))
            else:
                spark_df = DataframeConnect.dataframe_module(DealstoContacts, NullDatafixConnect, spark, Bucket,
                                                             CycleId,
                                                             ConfigInfo, ObjectValue, TerritoryCode)
            DealstoCompanies = DataFetchObject[1]
            ObjectValue = 'DealstoCompanies'
            logging.warning("The length of companies is :'%s'", format(str(len(DealstoCompanies))))
            if len(DealstoCompanies) == 0:
                logging.warning("There is no data for territory :'%s'", format(TerritoryCode))
            else:
                spark_df = DataframeConnect.dataframe_module(DealstoCompanies, NullDatafixConnect, spark, Bucket,
                                                             CycleId,
                                                             ConfigInfo, ObjectValue, TerritoryCode)
        ApiCalls = APICall.ApiCount()
        LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'],
                                       CutOffDate=ConfigInfo['CutoffDate'],
                                       Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                       RecordsProcessed=0, NoOfApiCall=ApiCalls,
                                       CycleId=CycleId, Object=Object, Bucket=Bucket)
        if Object == 'associationcompanies':
            Companies = DataFetchObject[5]
            ObjectData = 'companies'
            ApiCalls = APICall.ApiCount()
            logstatements = LogsStatementConnect.logs_statements(ApiCalls, Companies)
            if len(Companies) == 0:
                logging.warning("There is no data for Territory :'%s'", format(TerritoryCode))
                raise Exception
            else:
                spark_df = DataframeConnect.dataframe_module(Companies, NullDatafixConnect, spark, Bucket,
                                                             CycleId,
                                                             ConfigInfo, ObjectData, TerritoryCode)

                LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'],
                                               CutOffDate=ConfigInfo['CutoffDate'],
                                               Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                               RecordsProcessed=len(Companies), NoOfApiCall=ApiCalls,
                                               CycleId=CycleId, Object=ObjectData, Bucket=Bucket)
            # Processing association companies
            CompaniesToDeals = DataFetchObject[2]
            ObjectValue = 'CompaniesToDeals'
            if len(CompaniesToDeals) == 0:
                logging.warning("There is no data for territory :'%s'", format(TerritoryCode))
            else:
                spark_df = DataframeConnect.dataframe_module(CompaniesToDeals, NullDatafixConnect, spark, Bucket,
                                                             CycleId,
                                                             ConfigInfo, ObjectValue, TerritoryCode)
            CompaniesToContacts = DataFetchObject[0]
            ObjectValue = 'CompaniesToContacts'
            logging.warning("The length of companies is:'%s' ", format(str(len(CompaniesToContacts))))
            if len(CompaniesToContacts) == 0:
                logging.warning("There is no data for territory:'%s' ", format(TerritoryCode))
            else:
                spark_df = DataframeConnect.dataframe_module(CompaniesToContacts, NullDatafixConnect, spark, Bucket,
                                                             CycleId,
                                                             ConfigInfo, ObjectValue, TerritoryCode)
        ApiCalls = APICall.ApiCount()
        LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'],
                                       CutOffDate=ConfigInfo['CutoffDate'],
                                       Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                       RecordsProcessed=0, NoOfApiCall=ApiCalls,
                                       CycleId=CycleId, Object=Object, Bucket=Bucket)


    except Exception as error:
        raise error

if Object in ['salespipeline']:
    try:
        DataFetchObject = OwnersConnect.GetExtract(ConfigInfo, Bucket, Properties, DefaultProperties, CycleId, Object)
        if len(DataFetchObject) == 0:
            logging.warning("There is no data for Territory :'%s'", format(TerritoryCode))
        else:
            DF_SalesPipeline = pd.DataFrame(DataFetchObject)
            spark_df = DataframeConnect.dataframe_module(DF_SalesPipeline, NullDatafixConnect, spark, Bucket,
                                                             CycleId,
                                                             ConfigInfo, Object, TerritoryCode)
        ApiCalls = APICall.ApiCount()
        Duration = (timeit.default_timer() - starttime)/60
        LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'],
                                      CutOffDate=ConfigInfo['CutoffDate'],
                                      Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                      RecordsProcessed=0, NoOfApiCall=ApiCalls,
                                      CycleId=CycleId, Object=Object, Bucket=Bucket, Duration= Duration)
    except Exception as error:
        raise error
logging.warning("The duration of execution is :'%s'", format(timeit.default_timer() - starttime))

