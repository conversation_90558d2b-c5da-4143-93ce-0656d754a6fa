from airflow import DAG
from airflow.decorators import task
from airflow.utils.dates import days_ago
from airflow.operators.dummy import DummyOperator
from airflow.operators.python import <PERSON><PERSON><PERSON><PERSON>Operator, PythonOperator
from airflow.utils.task_group import TaskGroup
from datetime import datetime, date, timedelta
import requests, json, boto3
from airflow.operators.bash import Ba<PERSON><PERSON>perator
import os
from dependencies import glue_trigger
from dependencies.cloud_operations import SecretManager
import time
from dependencies.dbt_test_validator import check_dbt_output
from dependencies.slack_alerts import task_failure_callback, task_success_callback

HOME = os.environ["HOME"]
dbt_ga4_path = os.path.join(HOME, "dbt/dbt_core_course_ga4")  # For BigQuery tasks
dbt_core_path = os.path.join(HOME, "dbt/dbt_core_course")     # For ODS and DT tasks

default_args = {
    'owner': 'wse_data_team',
    'start_date': days_ago(1)
}

with DAG('ga4_events_pipeline',
         default_args=default_args,
         schedule_interval='0 5 * * *', # scheduled for 10:30 AM IST
         catchup=False) as dag:

    start = DummyOperator(task_id='start', dag=dag)
    
    @task
    def fetch_event_config():
        # Get Teable configuration from AWS Secrets Manager using CloudOperations
        secret = SecretManager.get_secret('ga4_events_teable', 'eu-west-1')
        
        teable_config = {
            "url": secret['api_url'],
            "token": secret['api_token'],
            "view_id": secret['view_id'],
            "field_ids": secret['field_ids']
        }
        
        headers = {
            "Authorization": f"Bearer {teable_config['token']}",
            "Accept": "application/json"
        }

        filter_full = {
            "conjunction": "and",
            "filterSet": [
                {"fieldId": teable_config['field_ids']['is_skipable'], "operator": "is", "value": False},
                {"fieldId": teable_config['field_ids']['is_full_refresh'], "operator": "is", "value": True}
            ]
        }

        filter_incr = {
            "conjunction": "and",
            "filterSet": [
                {"fieldId": teable_config['field_ids']['is_skipable'], "operator": "is", "value": False},
                {"fieldId": teable_config['field_ids']['is_full_refresh'], "operator": "is", "value": False}
            ]
        }

        def get_events(filter_payload):
            params = {
                "fieldKeyType": "name",
                "viewId": teable_config['view_id'],
                "filter": json.dumps(filter_payload),
                "cellFormat": "json"
            }
            response = requests.get(teable_config['url'], headers=headers, params=params)
            response.raise_for_status()
            return [rec['fields']['event_name'] for rec in response.json().get('records', [])]

        return {
            "full_refresh": get_events(filter_full),
            "incremental": get_events(filter_incr)
        }

    config = fetch_event_config()

    def branch_logic(ti):
        config = ti.xcom_pull(task_ids='fetch_event_config')
        has_full = bool(config['full_refresh'])
        has_incr = bool(config['incremental'])
        if has_full and has_incr:
            return ['full_refresh_group.full_load_config']
        elif has_full:
            return ['full_refresh_group.full_load_config']
        elif has_incr:
            return ['incremental_refresh_group.incr_load_config']
        else:
            return ['join']

    branch = BranchPythonOperator(
        task_id='branch_based_on_config',
        python_callable=branch_logic,
    )

    @task(trigger_rule='none_failed_min_one_success')
    def full_date_range():
        today = date.today()
        return {
            "start_date": date(today.year, 1, 1).isoformat(),
            "end_date": (today - timedelta(days=1)).isoformat(),
        }

    @task(trigger_rule='none_failed_min_one_success')
    def incr_date_range():
        client = boto3.client("athena", region_name="eu-west-1")
        sql = "SELECT max(event_date) AS max_dt FROM ods_google_analytics_service.ods_ga4_events"
        
        try:
            response = client.start_query_execution(
                QueryString=sql,
                WorkGroup="primary",
                QueryExecutionContext={"Database": "ods_google_analytics_service"},
                ResultConfiguration={"OutputLocation": "s3://aws-athena-query-results-pbi"}
            )
            query_execution_id = response['QueryExecutionId']

            while True:
                query_status = client.get_query_execution(QueryExecutionId=query_execution_id)
                state = query_status['QueryExecution']['Status']['State']
                if state in ['SUCCEEDED', 'FAILED', 'CANCELLED']:
                    break
                time.sleep(1)

            if state == 'FAILED':
                error_message = query_status['QueryExecution']['Status'].get('StateChangeReason', 'Unknown error')
                raise Exception(f"Query failed: {error_message}")
            elif state == 'CANCELLED':
                raise Exception("Query was cancelled")

            result = client.get_query_results(QueryExecutionId=query_execution_id)
            if len(result['ResultSet']['Rows']) < 2:
                raise Exception("No data found in the table")

            max_dt_str = result['ResultSet']['Rows'][1]['Data'][0]['VarCharValue']
            max_dt = datetime.strptime(max_dt_str, "%Y-%m-%d").date()
            today = date.today()
            return {
                "start_date": (today - timedelta(days=1) if max_dt >= today else max_dt + timedelta(days=1)).isoformat(),
                "end_date": (today - timedelta(days=1)).isoformat(),
            }
        except Exception as e:
            print(f"Error executing Athena query: {str(e)}")
            raise

    # ----- Full Refresh Group -----
    with TaskGroup("full_refresh_group") as full_refresh_group:
        @task
        def full_load_config(config: dict):
            return config['full_refresh']

        @task
        def get_full_dbt_vars(events: list, dr: dict):
            dbt_vars = {
                "start_date": dr["start_date"],
                "end_date": dr["end_date"],
                "event_names": events
            }
            print("DBT Vars:", dbt_vars)
            return json.dumps(dbt_vars)  # convert to string here

        full_events = full_load_config(config)
        dr_full = full_date_range()
        full_dbt_vars = get_full_dbt_vars(full_events, dr_full)

        full_bq = BashOperator(
            task_id="full_bq_dbt",
            bash_command="""
            cd /home/<USER>/dbt &&
            source dbt-venv/bin/activate &&
            cd {{ params.dbt_path }} &&
            dbt run --select ga4_events --vars '{{ ti.xcom_pull(task_ids="full_refresh_group.get_full_dbt_vars") }}'
            """,
            params={'dbt_path': dbt_ga4_path},
            on_failure_callback=task_failure_callback,
            dag=dag
        )

        full_staging = PythonOperator(
            task_id="full_staging_glue",
            python_callable=glue_trigger.run_glue_job,
            op_kwargs={"glue_job_name": "GA4 Events"},
            retries=1,
            retry_delay=timedelta(minutes=5),
            on_failure_callback=task_failure_callback,
            dag=dag
        )

        def full_delete_ods_dt_func(**context):
            client = boto3.client("athena", region_name="eu-west-1")
            events = context['ti'].xcom_pull(task_ids='full_refresh_group.full_load_config')
            
            # Get the date range from the full_date_range task
            date_range = context['ti'].xcom_pull(task_ids='full_refresh_group.full_date_range')
            start_date = date_range['start_date']
            end_date = date_range['end_date']
            
            # Delete from ODS table for the specific date range and events
            ods_sql = f"""
            DELETE FROM ods_google_analytics_service.ods_ga4_events 
            WHERE event_name IN ({','.join([f"'{event}'" for event in events])})
            AND event_date BETWEEN DATE '{start_date}' AND DATE '{end_date}'
            """
            
            # Delete from DT table for the specific date range and events
            dt_sql = f"""
            DELETE FROM dt_google_analytics_service.dt_ga4_events 
            WHERE event_name IN ({','.join([f"'{event}'" for event in events])})
            AND event_date BETWEEN DATE '{start_date}' AND DATE '{end_date}'
            """
            
            for sql in [ods_sql, dt_sql]:
                try:
                    # Start query execution
                    response = client.start_query_execution(
                        QueryString=sql,
                        WorkGroup="primary",
                        QueryExecutionContext={"Database": "ods_google_analytics_service"},
                        ResultConfiguration={"OutputLocation": "s3://aws-athena-query-results-pbi"}
                    )
                    query_execution_id = response['QueryExecutionId']

                    # Wait for query to complete
                    while True:
                        query_status = client.get_query_execution(QueryExecutionId=query_execution_id)
                        state = query_status['QueryExecution']['Status']['State']
                        
                        if state in ['SUCCEEDED', 'FAILED', 'CANCELLED']:
                            break
                            
                        time.sleep(1)

                    if state == 'FAILED':
                        error_message = query_status['QueryExecution']['Status'].get('StateChangeReason', 'Unknown error')
                        raise Exception(f"Query failed: {error_message}")
                    elif state == 'CANCELLED':
                        raise Exception("Query was cancelled")

                except Exception as e:
                    print(f"Error executing Athena query: {str(e)}")
                    raise
            
            print(f"Deleted events {events} from ODS and DT tables for date range {start_date} to {end_date}")

        full_delete_ods_dt = PythonOperator(
            task_id="full_delete_ods_dt",
            python_callable=full_delete_ods_dt_func,
            provide_context=True,
            dag=dag,
            do_xcom_push=False
        )

        full_ods = BashOperator(
            task_id="full_ods_dbt",
            bash_command="""
            cd /home/<USER>/dbt &&
            source dbt-venv/bin/activate &&
            cd {{ params.dbt_path }} &&
            dbt run --select ods.google_analytics_service.ods_ga4_events
            """,
            params={'dbt_path': dbt_core_path},
            on_failure_callback=task_failure_callback,
            dag=dag
        )

        full_dt = BashOperator(
            task_id="full_dt_dbt",
            bash_command="""
            cd /home/<USER>/dbt &&
            source dbt-venv/bin/activate &&
            cd {{ params.dbt_path }} &&
            dbt run --select data_transformation.google_analytics_service.dt_ga4_events
            """,
            params={'dbt_path': dbt_core_path},
            dag=dag
        )

        full_ods_test = BashOperator(
            task_id="full_ods_dbt_test",
            bash_command=f"""
                cd /home/<USER>/dbt &&
                source dbt-venv/bin/activate &&
                cd {dbt_core_path} &&
                dbt test --models ods.google_analytics_service.ods_ga4_events
            """,
            on_failure_callback=task_failure_callback,
            dag=dag
        )

        full_dt_test = BashOperator(
            task_id="full_dt_dbt_test",
            bash_command=f"""
                cd /home/<USER>/dbt &&
                source dbt-venv/bin/activate &&
                cd {dbt_core_path} &&
                dbt test --models data_transformation.google_analytics_service.dt_ga4_events
            """,
            on_failure_callback=task_failure_callback,
            dag=dag
        )

        full_dbt_test_check = PythonOperator(
            task_id="validate_full_tests",
            python_callable=check_dbt_output,
            op_kwargs={
                'task_ids': [
                    "full_refresh_group.full_ods_dbt_test", 
                    "full_refresh_group.full_dt_dbt_test"
                ]
            },
            provide_context=True,
            on_failure_callback=task_failure_callback,
            dag=dag
        )

        full_events >> dr_full >> full_dbt_vars >> full_bq >> full_staging >> full_delete_ods_dt >> full_ods >> full_dt >> full_ods_test >> full_dt_test >> full_dbt_test_check

    # ----- Incremental Refresh Group -----
    with TaskGroup("incremental_refresh_group") as incremental_refresh_group:
        @task(trigger_rule='none_failed_min_one_success')
        def incr_load_config(config: dict):
            return config['incremental']

        @task(trigger_rule='none_failed_min_one_success')
        def get_incr_dbt_vars(events: list, dr: dict):
            dbt_vars = {
                "start_date": dr["start_date"],
                "end_date": dr["end_date"],
                "event_names": events
            }
            print("DBT Vars:", dbt_vars)
            return json.dumps(dbt_vars)  # convert to string here
        
        incr_events = incr_load_config(config)
        dr_incr = incr_date_range()
        incr_dbt_vars = get_incr_dbt_vars(incr_events, dr_incr)

        incr_bq = BashOperator(
            task_id="incr_bq_dbt",
            bash_command="""
            cd /home/<USER>/dbt &&
            source dbt-venv/bin/activate &&
            cd {{ params.dbt_path }} &&
            dbt run --select ga4_events --vars '{{ ti.xcom_pull(task_ids="incremental_refresh_group.get_incr_dbt_vars") }}'
            """,
            params={'dbt_path': dbt_ga4_path},
            dag=dag,
            do_xcom_push=False
        )

        incr_staging = PythonOperator(
            task_id="incr_staging_glue",
            python_callable=glue_trigger.run_glue_job,
            op_kwargs={"glue_job_name": "GA4 Events"},
            retries=1,
            retry_delay=timedelta(minutes=5),
            on_failure_callback=task_failure_callback,
            dag=dag
        )

        incr_ods = BashOperator(
            task_id="incr_ods_dbt",
            bash_command="""
            cd /home/<USER>/dbt &&
            source dbt-venv/bin/activate &&
            cd {{ params.dbt_path }} &&
            dbt run --select ods.google_analytics_service.ods_ga4_events
            """,
            params={'dbt_path': dbt_core_path},
            dag=dag
        )

        incr_dt = BashOperator(
            task_id="incr_dt_dbt",
            bash_command="""
            cd /home/<USER>/dbt &&
            source dbt-venv/bin/activate &&
            cd {{ params.dbt_path }} &&
            dbt run --select data_transformation.google_analytics_service.dt_ga4_events
            """,
            params={'dbt_path': dbt_core_path},
            on_failure_callback=task_failure_callback,
            dag=dag
        )

        incr_ods_test = BashOperator(
            task_id="incr_ods_dbt_test",
            bash_command=f"""
                cd /home/<USER>/dbt &&
                source dbt-venv/bin/activate &&
                cd {dbt_core_path} &&
                dbt test --models ods.google_analytics_service.ods_ga4_events
            """,
            dag=dag
        )

        incr_dt_test = BashOperator(
            task_id="incr_dt_dbt_test",
            bash_command=f"""
                cd /home/<USER>/dbt &&
                source dbt-venv/bin/activate &&
                cd {dbt_core_path} &&
                dbt test --models data_transformation.google_analytics_service.dt_ga4_events
            """,
            on_failure_callback=task_failure_callback,
            dag=dag
        )

        incr_dbt_test_check = PythonOperator(
            task_id="validate_incr_tests",
            python_callable=check_dbt_output,
            op_kwargs={
                'task_ids': [
                    "incremental_refresh_group.incr_ods_dbt_test", 
                    "incremental_refresh_group.incr_dt_dbt_test"
                ]
            },
            provide_context=True,
            on_failure_callback=task_failure_callback,
            dag=dag
        )

        incr_events  >> dr_incr >> incr_dbt_vars >> incr_bq >> incr_staging >> incr_ods >> incr_dt  >> incr_ods_test >> incr_dt_test >> incr_dbt_test_check

    end = DummyOperator(task_id='end', dag=dag, on_success_callback=task_success_callback)
    join = DummyOperator(task_id='join', trigger_rule='none_failed_min_one_success', dag=dag)

    # DAG Flow with Sync and Conditional Incremental
    def branch_after_sync(config):
        has_incr = bool(config['incremental'])
        return 'incremental_refresh_group.incr_load_config' if has_incr else 'join'

    sync_point = DummyOperator(
        task_id='sync_point',
        trigger_rule='none_failed_min_one_success',
        on_failure_callback=task_failure_callback,
        dag=dag
    )

    branch_after_sync_point = BranchPythonOperator(
        task_id='branch_after_sync_point',
        python_callable=branch_after_sync,
        op_args=[config],
        on_failure_callback=task_failure_callback,
        dag=dag
    )

    # Initial branching
    start >> config >> branch

    # If both full + incr
    branch >> full_refresh_group >> sync_point >> branch_after_sync_point
    branch_after_sync_point >> incremental_refresh_group >> join
    branch_after_sync_point >> join

    # If only full
    branch >> full_refresh_group >> join

    # If only incr
    branch >> incremental_refresh_group >> join

    # If nothing to run
    branch >> join

    join >> end