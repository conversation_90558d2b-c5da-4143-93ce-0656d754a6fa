version: 2

sources:
  - name: stage_google_analytics_service
    description: >
      Source data from Google Analytics 4 (GA4) which provides user interaction and event tracking
      for web and mobile applications.
    database: awsdatacatalog
    schema: stg_google_analytics_service
    tables:
      - name: ga4_events
        description: Events tracked by Google Analytics 4 including user interactions and page views.
        columns:
          - name: event_date
            description: Date when the event occurred
          - name: event_timestamp
            description: Timestamp in microseconds when the event occurred
          - name: event_name
            description: Name of the event that was triggered
          - name: user_id
            description: ID of the user who triggered the event
          - name: event_params
            description: JSON array containing event parameters and their values
          - name: user_properties
            description: JSON array containing user properties and their values