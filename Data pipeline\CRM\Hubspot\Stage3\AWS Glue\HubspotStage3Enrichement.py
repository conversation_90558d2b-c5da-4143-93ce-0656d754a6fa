import sys
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
import logging

# Create a Spark session
from pyspark.sql import SparkSession

# Create a Spark session
spark = SparkSession.builder.appName("MultipleSQLStatements").getOrCreate()

# Set the configuration for Spark SQL legacy parquet int96RebaseModeInWrite, also this code helpe in avoiding issue for backdate calendar
spark.conf.set("spark.sql.legacy.parquet.int96RebaseModeInWrite", "LEGACY")

# Import necessary modules
import CloudOperations
import Queries
import LogFileGeneration

# Connect to S3 using CloudOperations module
s3_connect = CloudOperations.S3

# Define the queries from the Queries module
query_connect = Queries.Stage3

# Connect to LogFileGeneration module
logs_connect = LogFileGeneration.LogFile




# Get the resolved options and configuration information
config_info = getResolvedOptions(sys.argv, ['Object', 'Stage', 'Operation', 'Table', 'TablePath', 'Status', 'Bucket'])
object = config_info['Object']
# Define the S3 bucket name
bucket = config_info['Bucket']

# Read the execution check JSON file from the S3 bucket
execution_json = s3_connect.ReadJsonFile(bucket, 'ExecutionCheck.json')

# Extract the cycle ID from the execution check
cycle_id = execution_json['CycleId']

# Generate the enrichment query based on the provided configuration
query = query_connect.EnrichmentQueries(object=object)

# Print the generated query
logging.warning(query)

# Execute the enrichment query and retrieve the result dataframe
df = spark.sql(query)
df.show()

# Retrieve the table and table path from the configuration
table = config_info['Table']
table_path = config_info['TablePath']

# Write the result dataframe to the specified table path
df.write.mode("overwrite").saveAsTable(table, path=table_path)
logging.warning("enriched tables created successfully")

# Generate a log file for the enrichment operation
log_file = logs_connect.Enrichment(
    Table=table,
    TablePath=table_path,
    Status=200,
    Cycleid=cycle_id,
    Operation=config_info['Operation'],
    Object=object,
    Stage=config_info['Stage'],
    Bucket=bucket
)
logging.warning("Log file has been generated")
