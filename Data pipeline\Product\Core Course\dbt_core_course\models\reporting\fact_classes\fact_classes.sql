{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

SELECT
    class_id,
    class_date,
    student_center_id,
    CAST(class_id AS VARCHAR) || CAST(student_center_id AS VARCHAR) AS dbt_unique_id,
    CAST(current_timestamp AS TIMESTAMP(6)) AS load_date,
    -- bookings
    COUNT(booking_id) AS bookings,
    COUNT(booking_id) FILTER (WHERE ready_flag = true) AS ready,
    COUNT(booking_id) FILTER (WHERE attended_flag = true) AS attendance,
    COUNT(booking_id) FILTER (WHERE billable_flag = true) AS billable,
    COUNT(booking_id) FILTER (WHERE booked_by_role IS NOT NULL AND booked_by_role <> 'student') AS booked_by_staff,
    COUNT(booking_id) FILTER (WHERE booked_by_role = 'student') AS booked_by_student,
    -- cancellations
    COUNT(booking_id) FILTER (WHERE booking_cancelled_by IS NOT NULL AND booking_cancelled_by not in('student', 'auto cancel') ) AS cancelled_by_staff,
    COUNT(booking_id) FILTER (WHERE booking_cancelled_by = 'student') AS cancelled_by_student,
    COUNT(booking_id) FILTER (WHERE booking_cancelled_by = 'auto cancel') AS auto_cancelled,
    COUNT(booking_id) FILTER (WHERE cancelled_flag = true) AS cancelations,
    COUNT(booking_id) FILTER (WHERE rescheduled_flag = true AND cancelled_flag = true) AS rescheduled,
    COUNT(booking_id) FILTER (WHERE rescheduled_flag = true AND cancelled_flag = true AND ready_flag = true) AS cancelations_ready_rescheduled,
    -- early (not late) -> cancelations-late
    COUNT(booking_id) FILTER (WHERE (cancelled_flag = true) and date_diff('minute', booking_created_datetime, booking_cancelled_datetime) <= 30) AS cancelations_30_min,
    COUNT(booking_id) FILTER (WHERE (cancelled_flag = true) and date_diff('minute', booking_created_datetime, booking_cancelled_datetime) <= 30 AND booking_cancelled_by IS NOT NULL AND booking_cancelled_by <> 'student') AS cancelations_30_min_staff,
    COUNT(booking_id) FILTER (WHERE (cancelled_flag = true) and date_diff('minute', booking_created_datetime, booking_cancelled_datetime) <= 30 AND booking_cancelled_by IS NOT NULL AND booking_cancelled_by = 'student') AS cancelations_30_min_student,
    COUNT(booking_id) FILTER (WHERE (late_cancelled_flag = false AND cancelled_flag = true) AND ready_flag = true) AS early_cancelations_ready,
    COUNT(booking_id) FILTER (WHERE (late_cancelled_flag = false AND cancelled_flag = true) AND rescheduled_flag = true) AS early_cancelations_rescheduled,
    COUNT(booking_id) FILTER (WHERE (late_cancelled_flag = false AND cancelled_flag = true) AND ready_flag = true AND rescheduled_flag = true) AS early_cancelations_ready_rescheduled,
    -- late (in 12 hours before start)
    COUNT(booking_id) FILTER (WHERE late_cancelled_flag = true) AS late_cancelations,
    COUNT(booking_id) FILTER (WHERE late_cancelled_flag = true AND rescheduled_flag = true) AS late_cancelations_rescheduled,
    COUNT(booking_id) FILTER (WHERE late_cancelled_flag = true AND ready_flag = true) AS late_cancelations_ready, 
    --  late_cancelations - late_cancelations_ready -> late cancelations not ready
    COUNT(booking_id) FILTER (WHERE late_cancelled_flag = true AND mm_ready_flag = false AND wb_ready_flag = false) AS late_cancelations_both_not_ready,
    COUNT(booking_id) FILTER (WHERE late_cancelled_flag = true AND mm_ready_flag = false and wb_ready_flag = true) AS late_cancelations_mm_not_ready,
    COUNT(booking_id) FILTER (WHERE late_cancelled_flag = true AND wb_ready_flag = false and mm_ready_flag = true) AS late_cancelations_wb_not_ready,
    COUNT(booking_id) FILTER (WHERE late_cancelled_flag = true AND booking_cancelled_by IS NOT NULL AND booking_cancelled_by <> 'student') AS late_cancelations_by_staff,
    COUNT(booking_id) FILTER (WHERE late_cancelled_flag = true AND booking_cancelled_by IS NOT NULL AND booking_cancelled_by = 'student') AS late_cancelations_by_student,
    -- result
    COUNT(booking_id) FILTER (WHERE class_result IN ('passed', 'continue')) AS passed,
    COUNT(booking_id) FILTER (WHERE class_result IN ('passed', 'continue') AND ready_flag = true) AS passed_ready,
    --  passed - passed_ready -> passed not ready
    COUNT(booking_id) FILTER (WHERE class_result IN ('passed', 'continue') AND mm_ready_flag = false and wb_ready_flag = false) AS passed_both_not_ready,
    COUNT(booking_id) FILTER (WHERE class_result IN ('passed', 'continue') AND mm_ready_flag = false and wb_ready_flag = true) AS passed_mm_not_ready,
    COUNT(booking_id) FILTER (WHERE class_result IN ('passed', 'continue') AND wb_ready_flag = false and mm_ready_flag = true) AS passed_wb_not_ready,
    -- repeat
    COUNT(booking_id) FILTER (WHERE class_result IN ('failed', 'repeat')) AS failed,
    COUNT(booking_id) FILTER (WHERE class_result IN ('failed', 'repeat') AND ready_flag = true) AS failed_ready,
    COUNT(booking_id) FILTER (WHERE class_result IN ('failed', 'repeat') AND mm_ready_flag = false and wb_ready_flag = false) AS failed_both_not_ready,
    COUNT(booking_id) FILTER (WHERE class_result IN ('failed', 'repeat') AND mm_ready_flag = false and wb_ready_flag = true) AS failed_mm_not_ready,
    COUNT(booking_id) FILTER (WHERE class_result IN ('failed', 'repeat') AND wb_ready_flag = false and mm_ready_flag = true) AS failed_wb_not_ready,
    COUNT(booking_id) FILTER (WHERE class_result IS NULL AND cancelled_flag = false) AS no_result,
    -- no shows
    COUNT(booking_id) FILTER (WHERE no_show_flag = true AND cancelled_flag = false) AS no_shows,
    COUNT(booking_id) FILTER (WHERE (no_show_flag = true AND cancelled_flag = false) AND ready_flag = true) AS no_shows_ready,
    COUNT(booking_id) FILTER (WHERE (no_show_flag = true AND cancelled_flag = false) AND mm_ready_flag = false and wb_ready_flag = false) AS no_shows_both_not_ready,
    COUNT(booking_id) FILTER (WHERE (no_show_flag = true AND cancelled_flag = false)  AND mm_ready_flag = false and wb_ready_flag = true) AS no_shows_mm_not_ready,
    COUNT(booking_id) FILTER (WHERE (no_show_flag = true AND cancelled_flag = false)  AND wb_ready_flag = false and mm_ready_flag = true) AS no_shows_wb_not_ready,
    -- technology
    COUNT(booking_id) FILTER (WHERE technology_platform_staff_flag = true) AS tech_staff,
    COUNT(booking_id) FILTER (WHERE technology_staff_flag = true) AS tech_staff_no_platform,
    COUNT(booking_id) FILTER (WHERE technology_student_flag = true) AS tech_student,
    -- revenue
    SUM(class_price) FILTER (WHERE billable_flag = true) AS class_revenue,
    SUM(class_price) FILTER (WHERE billable_flag = true AND attended_flag = true) AS attendance_revenue,
    SUM(class_price) FILTER (WHERE billable_flag = true AND (no_show_flag = true AND cancelled_flag = false)) AS no_show_revenue,
    SUM(class_price) FILTER (WHERE billable_flag = true AND late_cancelled_flag = true) AS late_cancel_revenue,
    SUM(class_price) FILTER (WHERE billable_flag = true AND technology_student_flag = true) AS tech_revenue

FROM {{ref('fact_bookings')}}
where class_type in (   'class_type'
                        ,'online social club'
                        ,'online encounter'
                        ,'encounter'
                        ,'online complementary class'
                        ,'complementary class'
                        ,'social club'
                        ,'ghost encounter'
                        ,'freestyle advanced'
                        ,'freestyle elementary'
                        ,'freestyle intermediate'
                        ,'online english corner'
                        ,'speak+ intermediate'
                        ,'speak+ elementary'
                        ,'speak+ advanced'
                        )
    and booking_id is not null
    and standby_flag = FALSE

group by  class_id, class_date, student_center_id, cast(class_id as varchar) || cast(student_center_id as varchar), load_date