{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet',
    unique_key = 'dbt_unique_id'
) }}


{% set start_date = var('start_date', none) %}
{% set end_date = var('end_date', none) %}

with bookings_count as 
(
    Select 
        cb.booking_id
        ,count(b.booking_id) as bookings_when_booked
    FROM
        {{ ref('class_bookings') }} as cb
    LEFT JOIN
        {{ ref('bookings') }} as b
    ON
    cb.class_id = b.ref_class_id
    and b.booking_datetime < cb.booking_datetime
    and (b.booking_cancelled_flag = false or (b.booking_last_updated_datetime > cb.booking_datetime))
    where
        {% if is_incremental() %}
        date(cb.booking_datetime) >= date(date_add('month',-2,date_trunc('month',current_date)))
        {% else %}
            date(cb.booking_datetime) between date (date_add('day',-31,date('{{ start_date }}'))) and date(date_add('day',3,date('{{ end_date }}')))
        {% endif %}
        and cb.class_type_billable = true
    GROUP BY
        cb.booking_id
),
activities as
(
select act.student_id as student_reference_id
, act.center_reference_id
,'self study' as event_type
,act.activity_type as event_description
,cast(act.unit as varchar) as event_detail1
,cast(act.lesson as varchar) as event_detail2
,case when act.mini_cycles = 1 and mini_cycle_stage = 1 then 'lesson start'
 when act.description like '%Lesson Exercise%' then 'lesson exercise' else 'lesson content' end as event_detail3
,date(act.completed_date) as event_date
,date_trunc('month',act.completed_date) + interval '1' month - interval '1' day as event_month
,act.start_date as event_start_time
,act.completed_date as event_end_time
-- ,act.content_item_id as event_id
,act.duration_cap_mins as event_duration
from {{ ref('activities') }} act 
 where 
{% if start_date is not none and end_date is not none %}
    act.completed_date between date (date_add('day',-31,date('{{ start_date }}'))) and date(date_add('day',3,date('{{ end_date }}')))
{% else %}
    act.completed_date >= date(date_add('month',-2,date_trunc('month',current_date)))
{% endif %}
),
attendance as
(
select cb.student_reference_id
, cb.booking_center_reference_id as center_reference_id
,'attended class' as event_type
,cb.class_type as event_description
,cb.categories_abbreviations as event_detail1
,cb.class_result as event_detail2
,'' as event_detail3
,date(cb.class_local_start_datetime) as event_date
,date_trunc('month',cb.local_booking_datetime) + interval '1' month - interval '1' day as event_month
,cb.class_local_start_datetime as event_start_time
,cb.class_local_end_datetime as event_end_time
-- ,cb.class_id as event_id
,60 as event_duration
from {{ ref('class_bookings') }} cb
where 
{% if start_date is not none and end_date is not none %}
    date(cb.class_local_start_datetime) between date(date_add('day',-31,date('{{ start_date }}'))) and date(date_add('day',3,date('{{ end_date }}')))
{% else %}
    date(cb.class_local_start_datetime)  >=date(date_add('month',-2,date_trunc('month',current_date)))
{% endif %}
and cb.class_cancelled_flag = false 
and cb.class_type in ('encounter', 'online encounter', 'complementary class', 'online complementary class', 'social club', 'online social club')
and cb.class_result in ('passed','continue','repeat','technology student', 'technology staff', 'technology platform')
),
bookings as
(
    select 
        cb.student_reference_id
        , cb.booking_center_reference_id as center_reference_id
        ,concat('booked class',' - ',COALESCE(cb.booked_by,'unknown')) as event_type
        ,cb.class_type as event_description
        ,cb.categories_abbreviations as event_detail1
        ,cast(cast(cb.class_local_start_datetime as date) as varchar) as event_detail2
        ,cast(b.bookings_when_booked as varchar) as event_detail3
        ,date(cb.local_booking_datetime) as event_date
        ,date_trunc('month',cb.local_booking_datetime) + interval '1' month - interval '1' day as event_month
        ,cb.local_booking_datetime as event_start_time
        ,cb.local_booking_datetime as event_end_time
        ,0 as event_duration
        from {{ ref('class_bookings') }} cb 
        LEFT JOIN
            bookings_count b
        ON cb.booking_id = b.booking_id
        where
        {% if start_date is not none and end_date is not none %}
            date(cb.local_booking_datetime) between date(date_add('day',-31,date('{{ start_date }}'))) and date(date_add('day',3,date('{{ end_date }}')))
        {% else %}
            date(cb.local_booking_datetime)  >=date(date_add('month',-2,date_trunc('month',current_date)))
        {% endif %}
        and cb.class_type_billable = true
),
union_data AS 
(
SELECT
*
FROM activities
UNION ALL
SELECT
*
FROM bookings
UNION ALL
SELECT
*
FROM attendance
)
,data_agg as
(
SELECT
student_reference_id
,center_reference_id
,event_date
,event_month
,event_type
,event_description
,event_detail1
,event_detail2
,event_detail3
,min(event_start_time) as event_start_time
,max(event_end_time) as event_end_time
,sum(event_duration) as event_duration
,cast(date_diff('second',min(event_start_time),max(event_end_time))/60.00 as decimal(10,2)) as event_elapsed_time
,(
    COALESCE(CAST(student_reference_id AS VARCHAR), 'no_student_reference_id') ||
    COALESCE(CAST(center_reference_id AS VARCHAR), 'no_center_reference_id') ||
    COALESCE(CAST(event_description AS VARCHAR), 'no_event_description') ||
    COALESCE(CAST(event_month AS VARCHAR), 'no_event_month') ||
    COALESCE(CAST(event_date AS VARCHAR), 'no_event_date') ||
    COALESCE(CAST(event_type AS VARCHAR), 'no_event_type') ||
    COALESCE(CAST(event_detail1 AS VARCHAR), 'no_event_detail1') ||
    COALESCE(CAST(event_detail2 AS VARCHAR), 'no_event_detail2') ||
    COALESCE(CAST(event_detail3 AS VARCHAR), 'no_event_detail3')
) AS dbt_unique_id
,CAST(current_timestamp AS TIMESTAMP(6)) AS load_date
from union_data 
group by 
student_reference_id
,center_reference_id
,event_date
,event_month
,event_type
,event_description
,event_detail1
,event_detail2
,event_detail3
),
session_start_event AS (
    SELECT 
        student_reference_id
        ,center_reference_id
        ,event_date
        ,event_month
        ,event_type
        ,event_description
        ,event_detail1
        ,event_detail2
        ,event_detail3
        ,event_start_time
        ,event_end_time
        ,event_duration
        ,0 as event_elapsed_time
        ,(
            COALESCE(CAST(student_reference_id AS VARCHAR), 'no_student_reference_id') ||
            COALESCE(CAST(center_reference_id AS VARCHAR), 'no_center_reference_id') ||
            COALESCE(CAST(event_description AS VARCHAR), 'no_event_description') ||
            COALESCE(CAST(event_start_time AS VARCHAR), 'no_event_month') ||
            COALESCE(CAST(event_date AS VARCHAR), 'no_event_date') ||
            COALESCE(CAST(event_type AS VARCHAR), 'no_event_type') ||
            COALESCE(CAST(event_detail1 AS VARCHAR), 'no_event_detail1') ||
            COALESCE(CAST(event_detail2 AS VARCHAR), 'no_event_detail2') ||
            COALESCE(CAST(event_detail3 AS VARCHAR), 'no_event_detail3')
        ) AS dbt_unique_id
        ,CAST(current_timestamp AS TIMESTAMP(6)) AS load_date
    FROM (
        SELECT 
            act.student_id AS student_reference_id,
            act.center_reference_id,
            'study session start' AS event_type,
            act.activity_type AS event_description,
            CAST(act.unit AS VARCHAR) AS event_detail1,
            CAST(act.lesson AS VARCHAR) AS event_detail2,
            CASE 
                WHEN act.mini_cycles = 1 AND act.mini_cycle_stage = 1 THEN 'lesson start'
                WHEN act.description LIKE '%Lesson Exercise%' THEN 'lesson exercise'
                ELSE 'lesson content' 
            END AS event_detail3,
            DATE(act.completed_date) AS event_date,
            DATE_TRUNC('month', act.completed_date) + INTERVAL '1' MONTH - INTERVAL '1' DAY AS event_month,
            act.completed_date AS event_start_time,
            act.completed_date AS event_end_time,
            0 AS event_duration,
            ROW_NUMBER() OVER (PARTITION BY act.student_id, act.session ORDER BY act.completed_date ASC) AS rn
        FROM {{ ref('activities') }} act
        WHERE 
        {% if start_date is not none and end_date is not none %}
            act.completed_date BETWEEN DATE(DATE_ADD('day', -31, DATE('{{ start_date }}'))) 
                                  AND DATE(DATE_ADD('day', 3, DATE('{{ end_date }}')))
        {% else %}
            act.completed_date >= DATE(DATE_ADD('month', -2, DATE_TRUNC('month', CURRENT_DATE)))
        {% endif %}
    ) 
    WHERE rn = 1
)
SELECT
*
FROM data_agg
UNION ALL
SELECT
*
FROM session_start_event