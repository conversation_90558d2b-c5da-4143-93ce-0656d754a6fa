version: 2

semantic_models:
  - name: weekly_student_progress
    description: "Weekly student progress metrics across different time periods"
    model: ref('fact_wk_progress')
    defaults:
      agg_time_dimension: first_week_date

    dimensions:
      - name: student_id
        description: "Unique identifier for the student"

      - name: first_week_date
        description: "First date of the week"
        type: time
        type_params:
          time_granularity: day

      - name: dbt_unique_id
        description: "Unique identifier for the record in DBT"

      - name: segment
        description: "Student segment based on activity patterns"
        expr: "CASE
                WHEN sessions_4wk > 0 AND sessions_4wk <= 8 THEN 1
                WHEN sessions_4wk > 8 AND sessions_4wk <= 16 THEN 2
                WHEN sessions_4wk > 16 THEN 3
                ELSE -1
              END"

    measures:
      - name: total_students
        description: "Count of unique students"
        agg: count_distinct
        expr: student_id

      - name: multimedia_activities_1wk
        description: "Count of multimedia activities in the last week"
        agg: sum
        expr: multimedia_activities_completed_1wk

      - name: multimedia_activities_2wk
        description: "Count of multimedia activities in the last 2 weeks"
        agg: sum
        expr: multimedia_activities_completed_2wk

      - name: multimedia_activities_4wk
        description: "Count of multimedia activities in the last 4 weeks"
        agg: sum
        expr: multimedia_activities_completed_4wk

      - name: multimedia_activities_8wk
        description: "Count of multimedia activities in the last 8 weeks"
        agg: sum
        expr: multimedia_activities_completed_8wk

      - name: workbook_activities_1wk
        description: "Count of workbook activities in the last week"
        agg: sum
        expr: workbook_activities_completed_1wk

      - name: workbook_activities_2wk
        description: "Count of workbook activities in the last 2 weeks"
        agg: sum
        expr: workbook_activities_completed_2wk

      - name: workbook_activities_4wk
        description: "Count of workbook activities in the last 4 weeks"
        agg: sum
        expr: workbook_activities_completed_4wk

      - name: workbook_activities_8wk
        description: "Count of workbook activities in the last 8 weeks"
        agg: sum
        expr: workbook_activities_completed_8wk

      - name: cc_booked_1wk
        description: "Count of conversation classes booked in the last week"
        agg: sum

      - name: cc_booked_2wk
        description: "Count of conversation classes booked in the last 2 weeks"
        agg: sum

      - name: cc_booked_4wk
        description: "Count of conversation classes booked in the last 4 weeks"
        agg: sum

      - name: cc_booked_8wk
        description: "Count of conversation classes booked in the last 8 weeks"
        agg: sum

      - name: cc_attended_1wk
        description: "Count of conversation classes attended in the last week"
        agg: sum

      - name: cc_attended_2wk
        description: "Count of conversation classes attended in the last 2 weeks"
        agg: sum

      - name: cc_attended_4wk
        description: "Count of conversation classes attended in the last 4 weeks"
        agg: sum

      - name: cc_attended_8wk
        description: "Count of conversation classes attended in the last 8 weeks"
        agg: sum

      - name: sc_attended_1wk
        description: "Count of social clubs attended in the last week"
        agg: sum

      - name: sc_attended_2wk
        description: "Count of social clubs attended in the last 2 weeks"
        agg: sum

      - name: sc_attended_4wk
        description: "Count of social clubs attended in the last 4 weeks"
        agg: sum

      - name: sc_attended_8wk
        description: "Count of social clubs attended in the last 8 weeks"
        agg: sum

      - name: lvls_started_1wk
        description: "Count of levels started in the last week"
        agg: sum

      - name: lvls_started_2wk
        description: "Count of levels started in the last 2 weeks"
        agg: sum

      - name: lvls_started_4wk
        description: "Count of levels started in the last 4 weeks"
        agg: sum

      - name: lvls_started_8wk
        description: "Count of levels started in the last 8 weeks"
        agg: sum

      - name: duration_mm_1wk
        description: "Total duration of multimedia activities in the last week (minutes)"
        agg: sum

      - name: duration_mm_2wk
        description: "Total duration of multimedia activities in the last 2 weeks (minutes)"
        agg: sum

      - name: duration_mm_4wk
        description: "Total duration of multimedia activities in the last 4 weeks (minutes)"
        agg: sum

      - name: duration_mm_8wk
        description: "Total duration of multimedia activities in the last 8 weeks (minutes)"
        agg: sum

      - name: duration_wb_1wk
        description: "Total duration of workbook activities in the last week (minutes)"
        agg: sum

      - name: duration_wb_2wk
        description: "Total duration of workbook activities in the last 2 weeks (minutes)"
        agg: sum

      - name: duration_wb_4wk
        description: "Total duration of workbook activities in the last 4 weeks (minutes)"
        agg: sum

      - name: duration_wb_8wk
        description: "Total duration of workbook activities in the last 8 weeks (minutes)"
        agg: sum

      - name: sessions_4wk
        description: "Number of learning sessions in the last 4 weeks"
        agg: sum

      - name: duration_per_session_4wk
        description: "Average duration per session in the last 4 weeks (minutes)"
        agg: avg

  - name: weekly_student_details
    description: "Weekly student details with contract and validity information"
    model: ref('fact_wk_students')
    defaults:
      agg_time_dimension: first_week_date

    dimensions:
      - name: student_reference_id
        description: "Reference ID for the student"

      - name: first_week_date
        description: "First date of the week"
        type: time
        type_params:
          time_granularity: day

      - name: contract_reference_id
        description: "Reference ID for the contract"

      - name: center_reference_id
        description: "Reference ID for the center"

      - name: group_id
        description: "ID of the group"

      - name: personal_tutor
        description: "ID or name of the personal tutor"

      - name: consultant_id
        description: "ID of the consultant"

      - name: product_type
        description: "Type of product (core course, d2c)"

      - name: study_plan_type
        description: "Type of study plan"

      - name: contract_type
        description: "Type of contract"

      - name: course_level
        description: "Level of the course"

      - name: status
        description: "Status of the contract"

      - name: location
        description: "Location where the service is provided (incenter, outcenter)"

      - name: class_access_type
        description: "Type of class access"

      - name: service_type
        description: "Type of service (standard, vip)"

      - name: is_membership
        description: "Flag indicating if the contract is a membership"
        type: boolean

      - name: is_teen
        description: "Flag indicating if the student is a teen"
        type: boolean

      - name: is_promotional
        description: "Flag indicating if the contract is promotional"
        type: boolean

      - name: start_date
        description: "Start date of the contract"
        type: time
        type_params:
          time_granularity: day

      - name: end_date
        description: "End date of the contract"
        type: time
        type_params:
          time_granularity: day

      - name: first_contract
        description: "Flag indicating if this is the first contract"
        type: boolean

      - name: bookmark_mm_level
        description: "Current multimedia level bookmark"

      - name: dbt_unique_id
        description: "Unique identifier for the record in DBT"

    measures:
      - name: total_students
        description: "Count of unique students"
        agg: count_distinct
        expr: student_reference_id

      - name: valid_current_students
        description: "Count of students with valid contracts on the current date"
        agg: count_distinct
        expr: "CASE WHEN valid_current_date IS NOT NULL THEN student_reference_id END"

      - name: valid_rolling30_students
        description: "Count of students with valid contracts in the last 30 days"
        agg: count_distinct
        expr: "CASE WHEN valid_rolling30 IS NOT NULL THEN student_reference_id END"

      - name: new_students
        description: "Count of students with first contracts starting in this period"
        agg: count_distinct
        expr: "CASE WHEN first_contract = true THEN student_reference_id END"

  - name: daily_student_progress
    description: "Daily student progress metrics"
    model: ref('fact_daily_progress')
    defaults:
      agg_time_dimension: date

    dimensions:
      - name: student_id
        description: "Unique identifier for the student"

      - name: date
        description: "Date of the record"
        type: time
        type_params:
          time_granularity: day

      - name: dbt_unique_id
        description: "Unique identifier for the record in DBT"

    measures:
      - name: total_students
        description: "Count of unique students"
        agg: count_distinct
        expr: student_id

      - name: mm_activities_mtd
        description: "Count of multimedia activities month to date"
        agg: sum

      - name: mm_activities_30days
        description: "Count of multimedia activities in the last 30 days"
        agg: sum

      - name: encounters_attended_mtd
        description: "Count of encounters attended month to date"
        agg: sum

      - name: ccs_attended_mtd
        description: "Count of conversation classes attended month to date"
        agg: sum

      - name: scs_attended_mtd
        description: "Count of social clubs attended month to date"
        agg: sum

      - name: levels_started_mtd
        description: "Count of levels started month to date"
        agg: sum

      - name: staff_bookings
        description: "Count of bookings made by staff"
        agg: sum

      - name: student_bookings
        description: "Count of bookings made by students"
        agg: sum

metrics:
  - name: active_students_weekly
    description: "Count of students with any activity in the week"
    type: simple
    label: "Active Students (Weekly)"
    type_params:
      measure:
        model_name: weekly_student_progress
        measure_name: total_students
      filter: "multimedia_activities_1wk > 0 OR workbook_activities_1wk > 0 OR cc_attended_1wk > 0"
    meta:
      owner: "Data Team"

  - name: active_students_monthly
    description: "Count of students with any activity in the month"
    type: simple
    label: "Active Students (Monthly)"
    type_params:
      measure:
        model_name: daily_student_progress
        measure_name: total_students
      filter: "mm_activities_mtd > 0 OR encounters_attended_mtd > 0 OR ccs_attended_mtd > 0"
    meta:
      owner: "Data Team"

  - name: weekly_engagement_rate
    description: "Percentage of students who completed at least one activity in the week"
    type: ratio
    label: "Weekly Engagement Rate"
    type_params:
      numerator: active_students_weekly
      denominator:
        model_name: weekly_student_details
        measure_name: valid_current_students
    meta:
      owner: "Data Team"
      format: "percentage"

  - name: avg_weekly_activities
    description: "Average number of activities per student per week"
    type: ratio
    label: "Avg Weekly Activities"
    type_params:
      numerator:
        model_name: weekly_student_progress
        measure_name: multimedia_activities_1wk
        filter: null
      denominator:
        model_name: weekly_student_details
        measure_name: valid_current_students
    meta:
      owner: "Data Team"
      format: "decimal"

  - name: avg_weekly_duration
    description: "Average duration of activities per student per week (minutes)"
    type: ratio
    label: "Avg Weekly Duration (mins)"
    type_params:
      numerator:
        model_name: weekly_student_progress
        measure_name: duration_mm_1wk
        filter: null
      denominator:
        model_name: weekly_student_details
        measure_name: valid_current_students
    meta:
      owner: "Data Team"
      format: "decimal"
