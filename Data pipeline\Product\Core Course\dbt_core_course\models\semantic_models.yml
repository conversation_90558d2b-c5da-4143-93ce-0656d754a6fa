version: 2

semantic_models:
  - name: weekly_student_progress
    description: "Weekly student progress metrics across different time periods"
    model_name: fact_wk_progress
    primary_entity: student

    entities:
      - name: student
        type: student
      - name: week
        type: time

    dimensions:
      - name: student_id
        description: "Unique identifier for the student"
        entity: student

      - name: first_week_date
        description: "First date of the week"
        entity: week
        data_type: date
        is_partition: true

      - name: dbt_unique_id
        description: "Unique identifier for the record in DBT"
        entity: student

      - name: segment
        description: "Student segment based on activity patterns"
        entity: student
        expr: "CASE
                WHEN sessions_4wk > 0 AND sessions_4wk <= 8 THEN 1
                WHEN sessions_4wk > 8 AND sessions_4wk <= 16 THEN 2
                WHEN sessions_4wk > 16 THEN 3
                ELSE -1
              END"

    measures:
      - name: total_students
        description: "Count of unique students"
        agg: count_distinct
        expr: student_id

      - name: multimedia_activities_1wk
        description: "Count of multimedia activities in the last week"
        agg: sum
        expr: multimedia_activities_completed_1wk

      - name: multimedia_activities_2wk
        description: "Count of multimedia activities in the last 2 weeks"
        agg: sum
        expr: multimedia_activities_completed_2wk

      - name: multimedia_activities_4wk
        description: "Count of multimedia activities in the last 4 weeks"
        agg: sum
        expr: multimedia_activities_completed_4wk

      - name: multimedia_activities_8wk
        description: "Count of multimedia activities in the last 8 weeks"
        agg: sum
        expr: multimedia_activities_completed_8wk

      - name: workbook_activities_1wk
        description: "Count of workbook activities in the last week"
        agg: sum
        expr: workbook_activities_completed_1wk

      - name: workbook_activities_2wk
        description: "Count of workbook activities in the last 2 weeks"
        agg: sum
        expr: workbook_activities_completed_2wk

      - name: workbook_activities_4wk
        description: "Count of workbook activities in the last 4 weeks"
        agg: sum
        expr: workbook_activities_completed_4wk

      - name: workbook_activities_8wk
        description: "Count of workbook activities in the last 8 weeks"
        agg: sum
        expr: workbook_activities_completed_8wk

      - name: cc_booked_1wk
        description: "Count of conversation classes booked in the last week"
        agg: sum

      - name: cc_booked_2wk
        description: "Count of conversation classes booked in the last 2 weeks"
        agg: sum

      - name: cc_booked_4wk
        description: "Count of conversation classes booked in the last 4 weeks"
        agg: sum

      - name: cc_booked_8wk
        description: "Count of conversation classes booked in the last 8 weeks"
        agg: sum

      - name: cc_attended_1wk
        description: "Count of conversation classes attended in the last week"
        agg: sum

      - name: cc_attended_2wk
        description: "Count of conversation classes attended in the last 2 weeks"
        agg: sum

      - name: cc_attended_4wk
        description: "Count of conversation classes attended in the last 4 weeks"
        agg: sum

      - name: cc_attended_8wk
        description: "Count of conversation classes attended in the last 8 weeks"
        agg: sum

      - name: sc_attended_1wk
        description: "Count of social clubs attended in the last week"
        agg: sum

      - name: sc_attended_2wk
        description: "Count of social clubs attended in the last 2 weeks"
        agg: sum

      - name: sc_attended_4wk
        description: "Count of social clubs attended in the last 4 weeks"
        agg: sum

      - name: sc_attended_8wk
        description: "Count of social clubs attended in the last 8 weeks"
        agg: sum

      - name: lvls_started_1wk
        description: "Count of levels started in the last week"
        agg: sum

      - name: lvls_started_2wk
        description: "Count of levels started in the last 2 weeks"
        agg: sum

      - name: lvls_started_4wk
        description: "Count of levels started in the last 4 weeks"
        agg: sum

      - name: lvls_started_8wk
        description: "Count of levels started in the last 8 weeks"
        agg: sum

      - name: duration_mm_1wk
        description: "Total duration of multimedia activities in the last week (minutes)"
        agg: sum

      - name: duration_mm_2wk
        description: "Total duration of multimedia activities in the last 2 weeks (minutes)"
        agg: sum

      - name: duration_mm_4wk
        description: "Total duration of multimedia activities in the last 4 weeks (minutes)"
        agg: sum

      - name: duration_mm_8wk
        description: "Total duration of multimedia activities in the last 8 weeks (minutes)"
        agg: sum

      - name: duration_wb_1wk
        description: "Total duration of workbook activities in the last week (minutes)"
        agg: sum

      - name: duration_wb_2wk
        description: "Total duration of workbook activities in the last 2 weeks (minutes)"
        agg: sum

      - name: duration_wb_4wk
        description: "Total duration of workbook activities in the last 4 weeks (minutes)"
        agg: sum

      - name: duration_wb_8wk
        description: "Total duration of workbook activities in the last 8 weeks (minutes)"
        agg: sum

      - name: sessions_4wk
        description: "Number of learning sessions in the last 4 weeks"
        agg: sum

      - name: duration_per_session_4wk
        description: "Average duration per session in the last 4 weeks (minutes)"
        agg: avg

  - name: weekly_student_details
    description: "Weekly student details with contract and validity information"
    model_name: fact_wk_students
    primary_entity: student

    entities:
      - name: student
        type: student
      - name: week
        type: time
      - name: contract
        type: contract
      - name: center
        type: center

    dimensions:
      - name: student_reference_id
        description: "Reference ID for the student"
        entity: student

      - name: first_week_date
        description: "First date of the week"
        entity: week
        data_type: date
        is_partition: true

      - name: contract_reference_id
        description: "Reference ID for the contract"
        entity: contract

      - name: center_reference_id
        description: "Reference ID for the center"
        entity: center

      - name: group_id
        description: "ID of the group"
        entity: student

      - name: personal_tutor
        description: "ID or name of the personal tutor"
        entity: student

      - name: consultant_id
        description: "ID of the consultant"
        entity: student

      - name: product_type
        description: "Type of product (core course, d2c)"
        entity: contract

      - name: study_plan_type
        description: "Type of study plan"
        entity: contract

      - name: contract_type
        description: "Type of contract"
        entity: contract

      - name: course_level
        description: "Level of the course"
        entity: contract

      - name: status
        description: "Status of the contract"
        entity: contract

      - name: location
        description: "Location where the service is provided (incenter, outcenter)"
        entity: contract

      - name: class_access_type
        description: "Type of class access"
        entity: contract

      - name: service_type
        description: "Type of service (standard, vip)"
        entity: contract

      - name: is_membership
        description: "Flag indicating if the contract is a membership"
        entity: contract
        data_type: boolean

      - name: is_teen
        description: "Flag indicating if the student is a teen"
        entity: student
        data_type: boolean

      - name: is_promotional
        description: "Flag indicating if the contract is promotional"
        entity: contract
        data_type: boolean

      - name: start_date
        description: "Start date of the contract"
        entity: contract
        data_type: date

      - name: end_date
        description: "End date of the contract"
        entity: contract
        data_type: date

      - name: first_contract
        description: "Flag indicating if this is the first contract"
        entity: contract
        data_type: boolean

      - name: bookmark_mm_level
        description: "Current multimedia level bookmark"
        entity: student

      - name: dbt_unique_id
        description: "Unique identifier for the record in DBT"
        entity: student

    measures:
      - name: total_students
        description: "Count of unique students"
        agg: count_distinct
        expr: student_reference_id

      - name: valid_current_students
        description: "Count of students with valid contracts on the current date"
        agg: count_distinct
        expr: "CASE WHEN valid_current_date IS NOT NULL THEN student_reference_id END"

      - name: valid_rolling30_students
        description: "Count of students with valid contracts in the last 30 days"
        agg: count_distinct
        expr: "CASE WHEN valid_rolling30 IS NOT NULL THEN student_reference_id END"

      - name: new_students
        description: "Count of students with first contracts starting in this period"
        agg: count_distinct
        expr: "CASE WHEN first_contract = true THEN student_reference_id END"

  - name: daily_student_progress
    description: "Daily student progress metrics"
    model_name: fact_daily_progress
    primary_entity: student

    entities:
      - name: student
        type: student
      - name: day
        type: time

    dimensions:
      - name: student_id
        description: "Unique identifier for the student"
        entity: student

      - name: date
        description: "Date of the record"
        entity: day
        data_type: date
        is_partition: true

      - name: dbt_unique_id
        description: "Unique identifier for the record in DBT"
        entity: student

    measures:
      - name: total_students
        description: "Count of unique students"
        agg: count_distinct
        expr: student_id

      - name: mm_activities_mtd
        description: "Count of multimedia activities month to date"
        agg: sum

      - name: mm_activities_30days
        description: "Count of multimedia activities in the last 30 days"
        agg: sum

      - name: encounters_attended_mtd
        description: "Count of encounters attended month to date"
        agg: sum

      - name: ccs_attended_mtd
        description: "Count of conversation classes attended month to date"
        agg: sum

      - name: scs_attended_mtd
        description: "Count of social clubs attended month to date"
        agg: sum

      - name: levels_started_mtd
        description: "Count of levels started month to date"
        agg: sum

      - name: staff_bookings
        description: "Count of bookings made by staff"
        agg: sum

      - name: student_bookings
        description: "Count of bookings made by students"
        agg: sum

metrics:
  - name: active_students_weekly
    description: "Count of students with any activity in the week"
    calculation_method: count_distinct
    expression: student_id
    label: "Active Students (Weekly)"
    timestamp: first_week_date
    time_grains: [day, week, month, quarter, year]
    where: multimedia_activities_1wk > 0 OR workbook_activities_1wk > 0 OR cc_attended_1wk > 0
    model: ref('weekly_student_progress')
    meta:
      owner: "Data Team"

  - name: active_students_monthly
    description: "Count of students with any activity in the month"
    calculation_method: count_distinct
    expression: student_id
    label: "Active Students (Monthly)"
    timestamp: date
    time_grains: [day, week, month, quarter, year]
    where: mm_activities_mtd > 0 OR encounters_attended_mtd > 0 OR ccs_attended_mtd > 0
    model: ref('daily_student_progress')
    meta:
      owner: "Data Team"

  - name: weekly_engagement_rate
    description: "Percentage of students who completed at least one activity in the week"
    calculation_method: derived
    expression: "{{ metric('active_students_weekly') }} / {{ metric('valid_current_students') }}"
    label: "Weekly Engagement Rate"
    timestamp: first_week_date
    time_grains: [day, week, month, quarter, year]
    meta:
      owner: "Data Team"
      format: "percentage"

  - name: valid_current_students
    description: "Count of students with valid contracts on the current date"
    calculation_method: count_distinct
    expression: student_reference_id
    label: "Valid Current Students"
    timestamp: first_week_date
    time_grains: [day, week, month, quarter, year]
    where: valid_current_date IS NOT NULL
    model: ref('weekly_student_details')
    meta:
      owner: "Data Team"

  - name: avg_weekly_activities
    description: "Average number of activities per student per week"
    calculation_method: derived
    expression: "{{ metric('multimedia_activities_1wk') }} / {{ metric('valid_current_students') }}"
    label: "Avg Weekly Activities"
    timestamp: first_week_date
    time_grains: [day, week, month, quarter, year]
    meta:
      owner: "Data Team"
      format: "decimal"

  - name: multimedia_activities_1wk
    description: "Count of multimedia activities in the last week"
    calculation_method: sum
    expression: multimedia_activities_completed_1wk
    label: "Multimedia Activities (1wk)"
    timestamp: first_week_date
    time_grains: [day, week, month, quarter, year]
    model: ref('weekly_student_progress')
    meta:
      owner: "Data Team"

  - name: avg_weekly_duration
    description: "Average duration of activities per student per week (minutes)"
    calculation_method: derived
    expression: "{{ metric('duration_mm_1wk') }} / {{ metric('valid_current_students') }}"
    label: "Avg Weekly Duration (mins)"
    timestamp: first_week_date
    time_grains: [day, week, month, quarter, year]
    meta:
      owner: "Data Team"
      format: "decimal"

  - name: duration_mm_1wk
    description: "Total duration of multimedia activities in the last week (minutes)"
    calculation_method: sum
    expression: duration_mm_1wk
    label: "Multimedia Duration (1wk)"
    timestamp: first_week_date
    time_grains: [day, week, month, quarter, year]
    model: ref('weekly_student_progress')
    meta:
      owner: "Data Team"
