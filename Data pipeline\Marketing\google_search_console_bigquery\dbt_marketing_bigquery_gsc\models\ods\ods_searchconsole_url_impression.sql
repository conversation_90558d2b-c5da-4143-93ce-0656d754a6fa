{{ 
    config  (
    materialized = 'incremental',
    incremental_strategy = 'append',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) 
}}


WITH maximum_date_url AS (
    SELECT 
        site_url, 
        MAX(data_date) AS max_data_date
    FROM 
        {{ this }}
    GROUP BY 
        site_url
),

filtered_data AS (
    SELECT 
        h.*
    FROM 
       {{source('stage_marketing_bigquery_gsc', 'gsc_searchconsole_url_impression')}} h
    JOIN 
        maximum_date_url murl
    ON 
        h.site_url = murl.site_url
    WHERE 
        {{safe_cast_string_to_tz_date('h.data_date') }} > murl.max_data_date
)
select
    clicks,
    country,
    {{safe_cast_string_to_tz_date('data_date') }} as data_date,
    device,
    impressions,
    is_anonymized_query,
    query,
    search_type,
    site_url,
    sum_position,
    url,
    case when site_url like '%https://wse.edu.co/%' then 'co'
     when site_url like '%https://www.wallstreetenglish.de/%' then 'de'
     when site_url like '%https://www.wallstreet-english.co.il/%' then 'il'
     when site_url like '%https://www.wallstreetenglish.com.pa/%' then 'pa'
     when site_url like '%sc-domain:wallstreetenglish.com.ve%' then 've'
     when site_url like '%sc-domain:wallstreetenglish.dz%' then 'dz'
     when site_url like '%sc-domain:wallstreetenglish.mn%' then 'mn'
     when site_url like '%sc-domain:wallstreetenglish.tn%' then 'tn'
     when site_url like '%https://www.wallstreetenglish.com.mx/%' then 'mx'
     when site_url like '%https://www.wallstreetenglish.es/%' then 'es'
     when site_url like '%sc-domain:wallstreetenglish.com.ec%' then 'ec'
     when site_url like '%https://wallstreetenglish.edu.vn/%' then 'vn'
     when site_url like '%sc-domain:wallstreetenglish.co.in%' then 'in'
     when site_url like '%sc-domain:wallstreetenglish.edu.sa%' then 'sa'
     when site_url like '%sc-domain:wallstreet.it%' then 'it'
     when site_url like '%sc-domain:wallstreetenglish.edu.pe%' then 'pe'
     when site_url like '%sc-domain:wallstreetenglish.ly%' then 'ly'
     when site_url like '%sc-domain:wse.com.tr%' then 'tr'
     when site_url like '%sc-domain:wallstreetenglish.la%' then 'la'
     when site_url like '%sc-domain:wallstreetenglish.kz%' then 'kz'
     when site_url like '%sc-domain:wallstreetenglish.ma%' then 'ma'
     when site_url like 'sc-domain:wallstreetenglish.com'  then 'com'
     when site_url like 'sc-domain:wallstreetenglish.cr' then 'cr'
     when site_url like 'sc-domain:wallstreetenglish.com.gt' then 'gt'
     when site_url like 'sc-domain:wallstreetenglish.om' then 'om' else 'unknown'
    end as territory_code,
    'bigquery' as table_source,
    {{etl_load_date()}}

FROM
filtered_data;
