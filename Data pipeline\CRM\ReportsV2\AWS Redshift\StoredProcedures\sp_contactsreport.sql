create or replace procedure hubspot_crm.sp_contactsreport()
    language plpgsql
as
$$

BEGIN
    DROP TABLE IF EXISTS cutoff;
    DROP TABLE IF EXISTS #contactsupdated;
    DROP TABLE IF EXISTS #latestrecord;

    CREATE TEMP TABLE cutoff AS
    SELECT
        DATE_PART(DAY, GETDATE()) > 10 AS isbiggerthanten,
        CASE
            WHEN isbiggerthanten
                THEN CAST(DATEADD(DAY, 1, LAST_DAY(DATEADD(MONTH, -1, SYSDATE))) AS timestamp)
            ELSE CAST(DATEADD(DAY, 1, LAST_DAY(DATEADD(MONTH, -2, SYSDATE))) AS timestamp)
        END AS cutoff;


   
    SELECT *
    INTO #contactsupdated
    FROM
        (
        SELECT
            hs_object_id,
            COALESCE(c.lead_date, '1900-01-01') AS lead_date,
            mql_date,
            useful_contact_date,
            booked_date,
            show_date,
            contract_date,
            lost_date,
            first_contract_amount,
            call_campaign,
            CASE WHEN (center_name IS NULL OR center_name = '') AND
                      (first_contract_center IS NULL OR first_contract_center = '')
                     THEN 'No Center'
                 WHEN (center_name IS NULL OR center_name = '') AND
                      (first_contract_center IS NOT NULL AND first_contract_center <> '')
                     THEN first_contract_center
                 ELSE center_name
            END AS center_name,
            first_contract_center,
            hubspot_owner_id,
            CASE
                WHEN first_contract_owner = ''
                    THEN NULL
                ELSE CAST(first_contract_owner AS int)
            END AS first_contract_owner,
            createdate,
            email,
            CONCAT(CONCAT(firstname, ' '), lastname) AS contactname,
            how_did_you_hear_about_us,
            individual_corporate,
            channel,
            channel_drill_down_1,
            channel_drill_down_2,
            source,
            sub_source,
            tmk_owner,
            recent_conversion_date,
            recent_conversion_event_name,
            course_age_group,
            1 AS latestrecord,
            territory_code
                ,
                    ROW_NUMBER()
                    OVER (PARTITION BY territory_code, hs_object_id ORDER BY territory_code, hs_object_id, cycleid DESC) AS etl_latest
            --re_engaged_lead

        FROM
            devdwh.hubspot_crm.contactsenriched c
        WHERE
            territory_code IN ('MN', 'ES', 'KZ')
        ) a
    WHERE
        a.etl_latest = 1;


    --Insert into ContactsReport - records where Lead Date has changed or contact is new
-- I Believe here the Contacts report is already being updated. Inserts the new contacts to CR, so when is the cutoff applied?
-- inserts all contacts enriched that are latest = 1
    INSERT INTO
        devdwh.hubspot_crm.contactsreport

    SELECT
        n.*

    FROM
        #contactsupdated n
            LEFT JOIN
            devdwh.hubspot_crm.contactsreport a
            ON n.hs_object_id = a.hs_object_id
                AND n.territory_code = a.etl_account
                AND n.lead_date = a.lead_date
    WHERE
        a.hs_object_id IS NULL;

--Update LatestRecord on ContactsReport

    SELECT
        hs_object_id,
        lead_date,
        etl_account,
                ROW_NUMBER()
                OVER (PARTITION BY etl_account, hs_object_id ORDER BY etl_account, hs_object_id, lead_date DESC) AS latestrecord

    INTO #latestrecord
    FROM
        devdwh.hubspot_crm.contactsreport;


    UPDATE devdwh.hubspot_crm.contactsreport cr

    SET
        latestrecord = lr.latestrecord
    FROM
        #latestrecord AS lr
    WHERE
        cr.hs_object_id = lr.hs_object_id
        AND cr.lead_date = lr.lead_date
        AND cr.etl_account = lr.etl_account;




        UPDATE devdwh.hubspot_crm.contactsreport

        SET
            call_campaign = cte.call_campaign,
            center_name = cte.center_name,
            first_contract_center = cte.first_contract_center,
            hubspot_owner_id = cte.hubspot_owner_id,
            first_contract_owner = cte.first_contract_owner,
            how_did_you_hear_about_us = cte.how_did_you_hear_about_us,
            individual_corporate = cte.individual_corporate,
            channel = cte.channel,
            channel_drill_down_1 = cte.channel_drill_down_1,
            channel_drill_down_2 = cte.channel_drill_down_2,
            source = cte.source,
            sub_source = cte.sub_source,
            tmk_owner = cte.tmk_owner,
            course_age_group = cte.course_age_group,
            email = cte.email,
            --re_engaged_lead = CWD.re_engaged_lead,
            contactname = cte.contactname

        FROM
            (
            SELECT
                cwd.hs_object_id,
                cwd.call_campaign,
                cwd.center_name,
                cwd.first_contract_center,
                cwd.hubspot_owner_id,
                cwd.first_contract_owner,
                cwd.how_did_you_hear_about_us,
                cwd.individual_corporate,
                cwd.channel,
                cwd.channel_drill_down_1,
                cwd.channel_drill_down_2,
                cwd.source,
                cwd.sub_source,
                cwd.tmk_owner,
                cwd.course_age_group,
                cwd.email,
                --re_engaged_lead = CWD.re_engaged_lead,
                cwd.contactname,
                cwd.lead_date,
                cwd.territory_code

            FROM
                devdwh.hubspot_crm.contactsreport cr
                    INNER JOIN #contactsupdated cwd
                               ON cr.hs_object_id = cwd.hs_object_id
                                   AND cr.lead_date = cwd.lead_date
                                   AND cr.etl_account = cwd.territory_code
                                   AND cr.latestrecord = 1
            ) cte

        WHERE
            devdwh.hubspot_crm.contactsreport.hs_object_id = cte.hs_object_id
            AND devdwh.hubspot_crm.contactsreport.lead_date = cte.lead_date
            AND hubspot_crm.contactsreport.etl_account = cte.territory_code
            AND (contract_date >= (
                                  SELECT
                                      cutoff
                                  FROM
                                      cutoff
                                  )
            OR lost_date >= (
                            SELECT
                                cutoff
                            FROM
                                cutoff
                            )
            OR contract_date IS NULL
            OR lost_date IS NULL);


        UPDATE devdwh.hubspot_crm.contactsreport
        SET
            mql_date = cwd.mql_date
        FROM
            #contactsupdated AS cwd
        WHERE
            devdwh.hubspot_crm.contactsreport.hs_object_id = cwd.hs_object_id
            AND devdwh.hubspot_crm.contactsreport.lead_date = cwd.lead_date
            AND etl_account = cwd.territory_code
            AND devdwh.hubspot_crm.contactsreport.latestrecord = 1
            AND (devdwh.hubspot_crm.contactsreport.mql_date >= (
                                                               SELECT
                                                                   cutoff
                                                               FROM
                                                                   cutoff
                                                               )
            OR devdwh.hubspot_crm.contactsreport.mql_date IS NULL);


        UPDATE devdwh.hubspot_crm.contactsreport
        SET
            useful_contact_date = cwd.useful_contact_date
        FROM
            #contactsupdated AS cwd
        WHERE
            devdwh.hubspot_crm.contactsreport.hs_object_id = cwd.hs_object_id
            AND devdwh.hubspot_crm.contactsreport.lead_date = cwd.lead_date
            AND etl_account = cwd.territory_code
            AND devdwh.hubspot_crm.contactsreport.latestrecord = 1
            AND (devdwh.hubspot_crm.contactsreport.mql_date >= (
                                                               SELECT
                                                                   cutoff
                                                               FROM
                                                                   cutoff
                                                               )
            OR devdwh.hubspot_crm.contactsreport.mql_date IS NULL);

        UPDATE devdwh.hubspot_crm.contactsreport
        SET
            booked_date = cwd.booked_date
        FROM
            #contactsupdated AS cwd
        WHERE
            devdwh.hubspot_crm.contactsreport.hs_object_id = cwd.hs_object_id
            AND devdwh.hubspot_crm.contactsreport.lead_date = cwd.lead_date
            AND etl_account = cwd.territory_code
            AND devdwh.hubspot_crm.contactsreport.latestrecord = 1
            AND (devdwh.hubspot_crm.contactsreport.mql_date >= (
                                                               SELECT
                                                                   cutoff
                                                               FROM
                                                                   cutoff
                                                               )
            OR devdwh.hubspot_crm.contactsreport.mql_date IS NULL);


        UPDATE devdwh.hubspot_crm.contactsreport
        SET
            show_date = cwd.show_date
        FROM
            #contactsupdated AS cwd
        WHERE
            devdwh.hubspot_crm.contactsreport.hs_object_id = cwd.hs_object_id
            AND devdwh.hubspot_crm.contactsreport.lead_date = cwd.lead_date
            AND etl_account = cwd.territory_code
            AND devdwh.hubspot_crm.contactsreport.latestrecord = 1
            AND (devdwh.hubspot_crm.contactsreport.mql_date >= (
                                                               SELECT
                                                                   cutoff
                                                               FROM
                                                                   cutoff
                                                               )
            OR devdwh.hubspot_crm.contactsreport.mql_date IS NULL);

        UPDATE devdwh.hubspot_crm.contactsreport
        SET
            contract_date = cwd.contract_date
        FROM
            #contactsupdated AS cwd
        WHERE
            devdwh.hubspot_crm.contactsreport.hs_object_id = cwd.hs_object_id
            AND devdwh.hubspot_crm.contactsreport.lead_date = cwd.lead_date
            AND etl_account = cwd.territory_code
            AND devdwh.hubspot_crm.contactsreport.latestrecord = 1
            AND (devdwh.hubspot_crm.contactsreport.mql_date >= (
                                                               SELECT
                                                                   cutoff
                                                               FROM
                                                                   cutoff
                                                               )
            OR devdwh.hubspot_crm.contactsreport.mql_date IS NULL);


        UPDATE devdwh.hubspot_crm.contactsreport
        SET
            first_contract_amount = cwd.first_contract_amount
        FROM
            #contactsupdated AS cwd
        WHERE
            devdwh.hubspot_crm.contactsreport.hs_object_id = cwd.hs_object_id
            AND devdwh.hubspot_crm.contactsreport.lead_date = cwd.lead_date
            AND etl_account = cwd.territory_code
            AND devdwh.hubspot_crm.contactsreport.latestrecord = 1
            AND (devdwh.hubspot_crm.contactsreport.mql_date >= (
                                                               SELECT
                                                                   cutoff
                                                               FROM
                                                                   cutoff
                                                               )
            OR devdwh.hubspot_crm.contactsreport.mql_date IS NULL);


        UPDATE devdwh.hubspot_crm.contactsreport
        SET
            first_contract_center = cwd.first_contract_center
        FROM
            #contactsupdated AS cwd
        WHERE
            devdwh.hubspot_crm.contactsreport.hs_object_id = cwd.hs_object_id
            AND devdwh.hubspot_crm.contactsreport.lead_date = cwd.lead_date
            AND etl_account = cwd.territory_code
            AND devdwh.hubspot_crm.contactsreport.latestrecord = 1
            AND (devdwh.hubspot_crm.contactsreport.mql_date >= (
                                                               SELECT
                                                                   cutoff
                                                               FROM
                                                                   cutoff
                                                               )
            OR devdwh.hubspot_crm.contactsreport.mql_date IS NULL);


        UPDATE devdwh.hubspot_crm.contactsreport
        SET
            first_contract_owner = cwd.first_contract_owner
        FROM
            #contactsupdated AS cwd
        WHERE
            devdwh.hubspot_crm.contactsreport.hs_object_id = cwd.hs_object_id
            AND devdwh.hubspot_crm.contactsreport.lead_date = cwd.lead_date
            AND etl_account = cwd.territory_code
            AND devdwh.hubspot_crm.contactsreport.latestrecord = 1
            AND (devdwh.hubspot_crm.contactsreport.mql_date >= (
                                                               SELECT
                                                                   cutoff
                                                               FROM
                                                                   cutoff
                                                               )
            OR devdwh.hubspot_crm.contactsreport.mql_date IS NULL);





    COMMIT;


END;

$$;

