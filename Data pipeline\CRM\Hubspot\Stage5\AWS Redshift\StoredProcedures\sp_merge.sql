create or replace procedure hubspot_crm.sp_merge()
    language plpgsql
as
$$

BEGIN

UPDATE hubspot_crm.contactsenriched
SET deleteflag = 'M'
FROM hubspot_crm.merged_contacts mc
         JOIN hubspot_crm.contactsenriched ce
              ON mc.merged_contactid = ce.hs_object_id
                  AND mc.territory_code = ce.territory_code;

UPDATE hubspot_crm.dealsenriched
SET deleteflag = 'M'
FROM hubspot_crm.merged_deals md
         JOIN hubspot_crm.dealsenriched de
              ON md.merged_dealid = de.hs_object_id
                  AND md.territory_code = de.territory_code;

    COMMIT;


END

$$;

