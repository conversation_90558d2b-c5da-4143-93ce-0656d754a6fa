{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}
 
WITH
    class_result_flags as
    (
    Select
        coalesce((classes.class_id || bookings.booking_id), classes.class_id) as class_booking_id
        ,CASE
        WHEN bookings.booking_cancelled_flag = false AND
            bookings.class_result IN ('continue', 'repeat', 'passed') THEN true
        ELSE false
        END                                                                                                                     AS attended_flag
        ,CASE
            WHEN bookings.booking_cancelled_flag = false AND
                bookings.class_result IN ('technology platform','technology staff') THEN true
            ELSE false
        END                                                                                                                     AS technology_platform_staff_flag
        ,CASE
            WHEN bookings.booking_cancelled_flag = false AND
                bookings.class_result IN ('technology staff') THEN true
            ELSE false
        END                                                                                                                     AS technology_staff_flag
        ,CASE
            WHEN bookings.booking_cancelled_flag = false AND
                bookings.class_result IN ('technology','technology student') THEN true
            ELSE false
        END                                                                                                                     AS technology_student_flag
        ,CASE
            WHEN bookings.booking_cancelled_flag = false AND
                (bookings.standby_to_booked_24hrs IS NULL OR bookings.standby_to_booked_24hrs = 0) AND
                bookings.class_result IN ('no show', 'not present', 'failed')
            THEN true
            ELSE false
        END                                                                                                                     AS no_show_flag                                                                                                                  
        ,CASE
            WHEN class_local_start_datetime < TIMESTAMP '2021-05-01 00:00:00'
                AND cancellations_24hrs = 1
                AND (standby_to_booked_24hrs IS NULL OR standby_to_booked_24hrs = 0)
                AND booking_cancelled_role_title <> 'goc service manager'
                AND book_mode = 'book'
                AND booking_order_desc = 1
            THEN true
            WHEN class_local_start_datetime >= TIMESTAMP '2021-05-01 00:00:00'
                AND cancellations_12hrs = 1
                AND (standby_to_booked_24hrs IS NULL OR standby_to_booked_24hrs = 0)
                AND booking_cancelled_role_title <> 'goc service manager'
                AND book_mode = 'book'
                AND booking_order_desc = 1
            THEN true
        ELSE false
        END                                                                                                                     AS late_cancellation_flag
        ,CASE
                WHEN classes.class_type LIKE '%social%'
                    THEN 'Social Club'
                WHEN classes.class_type LIKE '%encounter%'
                    THEN 'Encounter'
                WHEN classes.class_type LIKE '%complementary%'
                    THEN 'Complementary Class'
                WHEN classes.class_type LIKE '%freesty%'
                    THEN 'Speak +'
                ELSE NULL END       AS class_type_category
    FROM
        {{ref('classes')}} as classes
    LEFT JOIN
        {{ref('bookings')}} as bookings
        ON classes.class_id = bookings.ref_class_id
    )

SELECT
    coalesce((classes.class_id || bookings.booking_id), classes.class_id) as class_booking_id
    ,classes.class_id as class_id
    ,classes.class_center_reference_id
    ,classes.class_start_datetime
    ,classes.class_local_start_datetime
    ,classes.class_end_datetime
    ,classes.class_local_end_datetime
    ,bookings.class_close_date
    ,{{ convert_to_local_timestamp(
        'classes.class_start_datetime',
        'student_center.timezone'
    ) }} as student_local_start_datetime
    ,classes.class_type
    ,classes.class_code
    ,classes.class_number_of_seats
    ,classes.class_number_of_seats_in_stand_by
    ,classes.class_description
    ,classes.class_teacher_user_reference_id
    ,classes.class_category_from_booking
    ,CASE
        WHEN classes.class_cancelled_flag is null
            AND classes.class_created_datetime < TIMESTAMP '2023-01-23 07:39:58.564' -- Timestamp when Scheduling and Booking Database is up
        THEN false
        ELSE classes.class_cancelled_flag
    END                                                                                                                     AS class_cancelled_flag
    ,classes.class_communication_account_type
    ,classes.class_source
    ,classes.class_created_datetime
    ,classes.class_local_created_datetime
    ,classes.class_last_updated_datetime
    ,classes.class_local_last_updated_datetime
    ,classes.class_created_by
    ,classes.class_created_by_role
    ,classes.class_last_updated_by
    ,classes.class_last_updated_by_role
    ,CASE
        WHEN classes.class_b2b_flag is null
            AND classes.class_created_datetime < TIMESTAMP '2018-01-27 01:55:34.650' -- Timestamp when is_b2b column introduced in Learning Service Class
        THEN false
        ELSE classes.class_b2b_flag
    END                                                                                                                     AS class_b2b_flag
    ,classes.class_visible_in_group
    ,classes.class_teen_flag
    ,classes.class_online_flag
    ,classes.class_service_type
    ,classes.class_category
    ,classes.class_category_type
    ,class_type_billable
    ,bookings.booking_id
    ,bookings.student_id
    ,bookings.student_reference_id
    ,bookings.center_reference_id                                                                                           AS booking_center_reference_id
    ,bookings.booking_territory_id
    ,bookings.book_mode
    ,bookings.booking_datetime
    ,bookings.local_booking_datetime
    ,bookings.booking_created_datetime
    ,bookings.booking_local_created_datetime
    ,bookings.booking_last_updated_datetime
    ,bookings.booking_local_last_updated_datetime
    ,CASE
        WHEN bookings.booking_cancelled_flag = true THEN bookings.booking_last_updated_datetime
    END                                                                                                                     AS booking_cancelled_datetime
    ,CASE
        WHEN bookings.booking_cancelled_flag = true THEN bookings.booking_local_last_updated_datetime
    END                                                                                                                     AS booking_local_cancelled_datetime
    ,bookings.booked_by                                                                                                     AS booked_by_id
    ,bookings.booked_role_title                                                                                             AS booked_by_role
    ,bookings.booking_person_type                                                                                           AS booked_by
    ,bookings.booking_cancelled_by                                                                                          AS booking_cancelled_by_id
    ,bookings.booking_cancelled_role_title                                                                                  AS booking_cancelled_by_role
    , CASE
        WHEN bookings.booking_cancelled_by = '45e963a0-8d29-4882-86b0-76a6ef79c9e3' THEN 'auto cancel'
        ELSE bookings.booking_cancelled_person_type
    END                                                                                                                   AS booking_cancelled_by
    ,bookings.booking_cancelled_flag
    ,bookings.booking_mode_modified_datetime
    ,bookings.booking_mode_modified_local_datetime
    ,bookings.booking_stand_by_notification_type
    --,bookings.booking_accessed_flag
    ,bookings.booked_student_contract_id
    ,bookings.booked_student_contract_reference_id
    ,bookings.standby_to_booked_flag
    ,bookings.standby_to_booked_24hrs
    ,CASE
        WHEN auto_cancel_type = '12' THEN true
        WHEN bookings.cancellations_12hrs = 1 THEN true
        ELSE false
    END                                                                                                                     AS cancellations_12hrs_flag
    ,CASE
        WHEN auto_cancel_type = '24' THEN true
        WHEN bookings.cancellations_24hrs = 1 THEN true
        ELSE false
    END                                                                                                                     AS cancellations_24hrs_flag
    ,bookings.cancellations_12hrs_not_sbtb_24hrs
    ,bookings.class_result_id
    ,bookings.class_encounter_level
    ,bookings.class_encounter_unit
    ,bookings.class_result_class_type
    ,bookings.class_result
    ,bookings.class_comments
    ,CASE
        WHEN class_center_reference_id LIKE 'v%goc'
            AND classes.class_type_billable = true
            AND
            (
                class_result_flags.attended_flag = true
                OR class_result_flags.technology_student_flag = true
                OR class_result_flags.no_show_flag = true
                OR class_result_flags.late_cancellation_flag = true
            )
        THEN coalesce(cp.price,cpt.price)
        ELSE NULL
    END                                                                                                                     AS class_price
    ,class_result_flags.attended_flag
    ,class_result_flags.technology_platform_staff_flag
    ,class_result_flags.technology_staff_flag
    ,class_result_flags.technology_student_flag
    ,class_result_flags.no_show_flag
    ,class_result_flags.late_cancellation_flag
    ,CASE WHEN
            classes.class_type_billable = true
        AND
            (
                class_result_flags.attended_flag                = true OR
                class_result_flags.technology_student_flag      = true OR
                class_result_flags.no_show_flag                 = true OR
                class_result_flags.late_cancellation_flag       = true
            )
        THEN true
        ELSE false END                                                                                                      AS billable_flag
    ,bookings.class_result_created_datetime
    ,bookings.class_access_type
    ,bookings.self_booking_access_flag
    ,classes.categories_abbreviations
    ,CASE
        WHEN mm_ready <= classes.class_local_start_datetime
        AND wb_ready_80 <= classes.class_local_start_datetime
        -- AND wb_ready_66 <= date_add('day', -1, bookings.local_booking_datetime)
        THEN TRUE
        ELSE FALSE
    END AS ready_flag
    ,LEAD(bookings.local_booking_datetime) OVER (
        PARTITION BY bookings.student_id, class_result_flags.class_type_category, classes.categories_abbreviations
        ORDER BY bookings.student_id, class_result_flags.class_type_category, bookings.local_booking_datetime
    ) AS lead_booked_datetime
    ,CASE 
        WHEN LEAD(bookings.local_booking_datetime) OVER (
            PARTITION BY bookings.student_id, class_result_flags.class_type_category, classes.categories_abbreviations
            ORDER BY bookings.student_id, class_result_flags.class_type_category, bookings.local_booking_datetime
        ) IS NOT NULL 
        THEN TRUE
        ELSE FALSE
    END AS rescheduled_flag
    ,CASE
            WHEN mm_ready <= classes.class_local_start_datetime
            THEN TRUE
            ELSE FALSE
        END AS mm_ready_flag
    ,CASE
            WHEN wb_ready_80 <= classes.class_local_start_datetime
            THEN TRUE
            ELSE FALSE
        END AS wb_ready_flag
    ,er.mm_ready
    ,er.mm_complete_66
    ,er.mm_complete_33
    ,er.mm_complete_0
    ,er.wb_ready_80
    ,er.wb_complete_66 
    ,er.wb_complete_33
    ,er.wb_complete_0

FROM
    {{ref('classes')}} as classes
LEFT JOIN
    {{ref('bookings')}} as bookings
    ON classes.class_id = bookings.ref_class_id
LEFT JOIN class_result_flags
    ON class_result_flags.class_booking_id = coalesce((classes.class_id || bookings.booking_id), classes.class_id)
LEFT JOIN {{ref('territory_centers')}} as student_center
    ON bookings.center_reference_id = student_center.center_reference_id
LEFT JOIN
    {{ref('class_pricing')}} as cpt
    ON booking_territory_id = lower(cpt.territoryid) AND classes.class_type = lower(cpt.classtype)
        AND classes.class_code = lower(cpt.classcode)
        AND date(classes.class_local_start_datetime) >= date(cpt.validfrom) AND date(classes.class_local_start_datetime)  <= date(cpt.validto)
LEFT JOIN
    {{ref('class_pricing_center')}} as cp
    ON bookings.center_reference_id = lower(cp.referencecenterid) AND classes.class_type = lower(cp.classtype)
        AND classes.class_code = lower(cp.classcode)
        AND date(classes.class_local_start_datetime) >= date(cp.validfrom) AND date(classes.class_local_start_datetime)  <= date(cp.validto)
LEFT JOIN {{ref("encounter_readiness")}} er
    ON bookings.student_reference_id = er.student_id
    AND CAST(er.unit AS VARCHAR) = classes.categories_abbreviations