{{ 
    config  (
    materialized = 'incremental',
    incremental_strategy = 'append',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) 
}}


WITH latest_dates AS (
    SELECT 
        territory_code, 
        MIN(data_date) AS max_data_date
    FROM 
        {{ ref('ods_searchconsole_url_impression') }}
    GROUP BY 
        territory_code
),
filtered_history AS (
    SELECT 
        h.*
    FROM 
        {{ ref('ods_gsc_api_history') }} h
    JOIN 
        latest_dates ld
    ON 
        h.territory_code = ld.territory_code
    WHERE 
        h.data_date < ld.max_data_date
)
SELECT 
     clicks,
     country,
     null as ctr,
     data_date,
     device,
     impressions,
     is_anonymized_query,
     query,
     search_type,
     site_url,
     null as position,
     sum_position,
     url as page_url,
     territory_code,
     table_source,
     etl_load_date
FROM 
    {{ ref('ods_searchconsole_url_impression') }}

UNION ALL

SELECT 
    clicks,
    country,
    ctr,
    data_date,
    device,
    impressions,
    null as is_anonymized_query,
    query,
    '' as search_type,
    '' as site_url,
    position,
    null as sum_position,
    page_url,
    territory_code,
    table_source,
    etl_load_date
FROM 
    filtered_history;
