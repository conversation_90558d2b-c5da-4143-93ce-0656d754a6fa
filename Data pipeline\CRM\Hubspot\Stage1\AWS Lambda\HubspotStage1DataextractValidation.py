import boto3
import json
import logging
import os





def lambda_handler(event, context):
    Bucket = os.environ.get('bucket')
    Boto3Resource = boto3.resource("s3")
    S3Bucket = Boto3Resource.Bucket(Bucket)
    ExecutionCheckFilePath = Boto3Resource.Object(Bucket, 'ExecutionCheck.json')
    ExecutionCheckFileContent = ExecutionCheckFilePath.get()['Body'].read().decode('utf-8')
    ExecutionCheck = json.loads(ExecutionCheckFileContent)
    logging.warning(ExecutionCheck)
    Folder = f"Logs/{ExecutionCheck['CycleId']}/Stage1"
    FilesInS3 = [f.key.split(Folder + "/")[1] for f in S3Bucket.objects.filter(Prefix=Folder).all()]
    logging.warning(FilesInS3)
    logging.warning(len(FilesInS3))

    if len(FilesInS3) == 171:
        logging.warning("Stage 1 completed")
        # FolderList = ['/Stage1/data_extract/contacts/','/Stage1/data_extract/deals/', '/Stage1/data_extract/owners/','/Stage1/data_extract/association/','/Stage1/data_extract_archive/contacts/','/Stage1/data_extract_archive/deals/','/Stage1/data_extract_archive/owners/','/Stage1/data_extract/companies/','/Stage1/data_extract/customobject/']
        FolderList = ['/Stage1/data_extract/owners/*/*.parquet',
                        '/Stage1/data_extract/deals/*/*.parquet',
                        '/Stage1/data_extract/customobject/*/*.parquet',
                        '/Stage1/data_extract/contacts/*/*.parquet',
                        '/Stage1/data_extract/companies/*/*.parquet',
                        '/Stage1/data_extract_archive/Archivecontacts/*/*.parquet',
                        '/Stage1/data_extract_archive/Archivedeals/*/*.parquet',
                        '/Stage1/data_extract_archive/Archiveowners/*/*.parquet',
                        '/Stage1/data_extract_archive/Archivecompanies/*/*.parquet',
                        '/Stage1/data_extract/Teams/*/*.parquet',
                        '/Stage1/data_extract/ContactstoDeals/*/*.parquet',
                        '/Stage1/data_extract/ContactstoCompanies/*/*.parquet',
                        '/Stage1/data_extract/DealstoCompanies/*/*.parquet',
                        '/Stage1/data_extract/DealstoContacts/*/*.parquet',
                        '/Stage1/data_extract/CompaniesToContacts/*/*.parquet',
                        '/Stage1/data_extract/CompaniesToDeals/*/*.parquet',
                        'Webhooks/*/*/*/*.json']
        CycleId = ExecutionCheck['CycleId']
        SaveTable=[]
        for path in FolderList:
            Table = path.split('/')[-3]
            Input = path.split('*')[-1]
            if Input == '.json':
                FilePath = "s3://" + Bucket + "/" + path
                Table = path.split('/')[-5]
            else:
                FilePath = "s3://"+ Bucket +"/HubspotRawFiles/" + str(CycleId) + path
            WritePath = "s3://" + Bucket + "/TableData/" + Table
            if Input == '.json':
                InputType = "json"
                WriteMode = "overwrite"
            else:
                InputType = "parquet"
                WriteMode = "overwrite"
            TableInfo = {
                "InputType": InputType,
                "FilePath" : FilePath,
                "TableName": Table,
                "WritePath": WritePath,
                "WriteMode": WriteMode
            }
            SaveTable.append(TableInfo)
        SaveAsTable = {"TableProperties": SaveTable}
    else:
        logging.warning("Stage 1 not completed")
        raise Exception
    return SaveAsTable