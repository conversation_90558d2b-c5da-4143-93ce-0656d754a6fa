{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_study_type'
        ) }}
    {% if is_incremental() %}
        where last_updated_date > ((select max(last_updated_date) from {{ this }}))
    {% endif %}
)


SELECT {{etl_load_date()}}, 
    id,
    name,
    code,
    product_type_id,
    is_active,
    created_date,
    last_updated_date
    FROM ods_data as tempTable