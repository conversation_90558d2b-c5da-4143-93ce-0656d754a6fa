{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('registrationdate') }} as registrationdate,
        {{ cast_to_timestamp('expirationdate') }} as expirationdate,
        latestplacementtestscore,
        source,
        trialenabled,
        showplacementtestresult,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        privacypolicyaccepted,
        id,
        ssdsreferenceid,
        centerid,
        firstname,
        lastname,
        email,
        companyid,
        studentid,
        phonenumber,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'prospect'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    registrationdate as registration_date,
    expirationdate as expiration_date,
    latestplacementtestscore as latest_placement_test_score,
    source,
    trialenabled as trial_enabled,
    showplacementtestresult as show_placement_test_result,
    created,
    lastupdated as last_updated,
    privacypolicyaccepted as privacy_policy_accepted,
    id,
    ssdsreferenceid as ssds_reference_id,
    centerid as center_id,
    firstname as first_name,
    lastname as last_name,
    email,
    companyid as company_id,
    studentid as student_id,
    phonenumber as phone_number
FROM
    rankedrecords
WHERE
    rn = 1;
