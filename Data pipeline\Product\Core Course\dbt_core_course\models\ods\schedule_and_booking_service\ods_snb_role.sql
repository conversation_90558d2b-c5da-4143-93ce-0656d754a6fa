{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH RankedRecords AS (
    SELECT 
        {{cast_to_timestamp("created")}} as created,
        {{cast_to_timestamp("lastupdated")}} as lastupdated,
        id,
        description,
        loweredrolename,
        code,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdated DESC) AS rn
    FROM 
        {{source('stage_schedule_and_booking_service', 'role')}}
)

SELECT
    {{etl_load_date()}},
    created as created,
    lastupdated as last_updated,
    id,
    description,
    loweredrolename as lowered_role_name,
    code
FROM 
    RankedRecords
WHERE
    rn = 1