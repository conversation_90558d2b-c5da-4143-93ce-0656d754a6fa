{{
    config(
        tags=["incremental","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}

select
SPLIT(url,"?")[SAFE_OFFSET(0)] as page,event_date,query,lang,
sum(clicks) clicks,
sum(impressions) impressions,
sum(sum_position) sum_position,
SUM(CASE WHEN country=gsc_country THEN clicks ELSE 0 END) AS local_clicks,
SUM(CASE WHEN country=gsc_country THEN impressions ELSE 0 END) AS local_impressions,
TRUE as is_keyword_source_gsc,
 from {{ ref('gsc_init_test') }} gsc
 where {{increment()}}
group by page,event_date,query,lang