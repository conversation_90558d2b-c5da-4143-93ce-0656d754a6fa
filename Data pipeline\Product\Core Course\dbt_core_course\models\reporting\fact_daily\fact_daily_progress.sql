{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet',
    unique_key = 'dbt_unique_id'
) }}

with cal as
    (
    select
    "date"
    ,date_add('day',-29,"date") as rolling30_start_date
    ,first_month_date
    ,least(date_add('day',-29,"date"),first_month_date) as earliest_date
    ,last_month_date
    ,year_month_key
    ,date_add('day',4,last_month_date) as next_month_4th
    ,case when current_date < last_month_date then "date"
          when current_date between last_month_date and date_add('day',3,last_month_date) then last_month_date
          when current_date >= date_add('day',4,last_month_date) then date_add('day',3,last_month_date) end as misentry_date
    from reporting.dim_calendar
    where "date" <= current_date
    {% set start_date = var('start_date', none) %}
    {% set end_date = var('end_date', none) %}
    {% if start_date is not none and end_date is not none %}
        and "date" between date('{{ start_date }}') and date('{{ end_date }}' )
    {% elif is_incremental() %}
        {% set current_date = modules.datetime.date.today() %}
        {% set current_day = current_date.day %}
        {% set current_hour = modules.datetime.datetime.now().hour %}
        {% set first_day_current_month = current_date.replace(day=1) %}
        {% set last_day_previous_month = first_day_current_month - modules.datetime.timedelta(days=1) %}
        {% set first_day_previous_month = last_day_previous_month.replace(day=1) %}

        {% if current_day > 4 or (current_day == 4 and current_hour >= 12) %}
            {% set cutoff_date = first_day_current_month %}
        {% else %}
            {% set cutoff_date = first_day_previous_month %}
        {% endif %}
        and "date" >= date '{{ cutoff_date.strftime('%Y-%m-%d') }}'
    {% else %}
        and 1=2 --This is to avoid the initial refresh without input parameters.
    {% endif %}
),

f_bookings as (
        select *
        from {{ ref('class_bookings')}}
        where attended_flag = true
        {% if start_date is not none and end_date is not none %}
            and date(student_local_start_datetime) between date(date_add('day',-31,date('{{ start_date }}'))) and date(date_add('day',3,date('{{ end_date }}')))
        {% else %}
            and date(student_local_start_datetime)  >=date(date_add('month',-2,date_trunc('month',current_date)))
        {% endif %}
)
  ,


future_bookings as (
        select *
        from {{ ref('class_bookings')}}
        where class_type like '%encounter%'
        {% if start_date is not none and end_date is not none %}
            and date(student_local_start_datetime)  between date('{{ start_date }}') and date (date_add('day',180,date('{{ end_date }}')))
        {% else %}
            and date(student_local_start_datetime)  >= date(date_add('month',-2,date_trunc('month',current_date)))
        {% endif %}
)
  ,

all_bookings as (
        select 
        student_reference_id
        ,booking_id
        ,booked_by as booked_person_type
        ,booking_created_datetime
        from {{ ref('class_bookings')}}
        where 1=1
        {% if start_date is not none and end_date is not none %}
            and date(local_booking_datetime)  between date (date_add('day',-31,date('{{ start_date }}'))) and date(date_add('day',3,date('{{ end_date }}')))
        {% else %}
            and date(local_booking_datetime)  >= date(date_add('month',-2,date_trunc('month',current_date)))
        {% endif %}
)
  ,

fact_act_cut as
    (
        select *
        from {{ ref('fact_activity')}}
        where 
        {% if start_date is not none and end_date is not none %}
            completed_date between date (date_add('day',-31,date('{{ start_date }}'))) and date(date_add('day',3,date('{{ end_date }}')))
        {% else %}
            completed_date >= date(date_add('month',-2,date_trunc('month',current_date)))
        {% endif %}
    )
,

levels_started as
    (
        select *
        from {{ ref('levels_started')}}
        where 
        {% if start_date is not none and end_date is not none %}
            date_granted between date (date_add('day',-31,date('{{ start_date }}'))) and date(date_add('day',3,date('{{ end_date }}')))
        {% else %}
            date_granted >= date(date_add('month',-2,date_trunc('month',current_date)))
        {% endif %}
    )
,

book_mtd as (
                select
                    student_reference_id as student_id,
                    cal."date",
                    sum(case when class_type like '%encounter%'
                                and student_local_start_datetime between cast (first_month_date AS TIMESTAMP(6))
                    and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
                    then 1 else 0 end) as encounters_attended_mtd,
                    sum(case when class_type like '%complementary class%'
                                and student_local_start_datetime between cast (first_month_date AS TIMESTAMP(6))
                    and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
                    then 1 else 0 end) as ccs_attended_mtd,
                    sum(case when class_type like '%social club%'
                                and student_local_start_datetime between cast (first_month_date AS TIMESTAMP(6))
                    and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
                    then 1 else 0 end) as scs_attended_mtd,
                    sum(case when class_type like '%encounter%'
                                and class_type like '%online%'
                                and student_local_start_datetime between cast (first_month_date AS TIMESTAMP(6))
                    and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
                    then 1 else 0 end) as onl_encounters_attended_mtd,
                    sum(case when class_type like '%complementary class%'
                                and class_type like '%online%'
                                and student_local_start_datetime between cast (first_month_date AS TIMESTAMP(6))
                    and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
                    then 1 else 0 end) as onl_ccs_attended_mtd,
                    sum(case when class_type like '%social club%'
                                and class_type like '%online%'
                                and student_local_start_datetime between cast (first_month_date AS TIMESTAMP(6))
                    and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
                    then 1 else 0 end) as onl_scs_attended_mtd,
                    sum(case when class_type like '%encounter%'
                                and student_local_start_datetime between cast(rolling30_start_date AS TIMESTAMP(6))
                    and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
                    then 1 else 0 end) as encounters_attended_30days,
                    sum(case when  class_type IN ('advising session','online advising session')
                            and class_result IN ('passed')
                            and student_local_start_datetime between cast (first_month_date AS TIMESTAMP(6))
                    and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
                    then 1 else 0 end) as advising_session,
                    sum(case when  class_type IN ('end of level advising session','online end of level advising session')
                            and class_result IN ('passed')
                            and student_local_start_datetime between cast (first_month_date AS TIMESTAMP(6))
                    and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
                    then 1 else 0 end) as eol_advising_session                 
                from
                    cal
                    left join f_bookings book
                        on student_local_start_datetime between cast(earliest_date AS TIMESTAMP(6))
                        and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
                        and (class_result_created_datetime <= next_month_4th
                        or class_result_created_datetime is null)
                group by
                    student_reference_id, cal."date"
    ),

book_first21days as (
                select
                    ds.student_reference_id as student_id,
                    ds."date",
                    sum(case when student_local_start_datetime between cast(start_date AS TIMESTAMP(6))
                    and CAST(CONCAT((CAST(CAST(first_21d_end_date AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
                    then 1 else 0 end) as encounters_attended_first_21days
                from
                    {{ ref('fact_daily_students')}} ds
                    left join f_bookings book
                        on ds.student_reference_id = book.student_reference_id
                    left join cal on ds."date" = cal."date"
                    where valid_completed21days is not null
                    and book.class_result = 'continue'
                    and (class_result_created_datetime <= next_month_4th
                        or class_result_created_datetime is null)
                group by
                    ds.student_reference_id, ds."date"
    ),

booked as (
                select
                    student_reference_id as student_id,
                    cal."date",
                    sum(1) as future_enc_bookings
                from
                    cal
                    left join future_bookings book
                        on booking_created_datetime <= CAST(CONCAT(cast("date" as varchar), ' 23:59:59.997') AS TIMESTAMP)
                           and student_local_start_datetime > CAST(CONCAT(cast("date" as varchar), ' 23:59:59.997') AS TIMESTAMP)
                           and ((booking_cancelled_flag = true and booking_last_updated_datetime > CAST(CONCAT(cast("date" as varchar), ' 23:59:59.997') AS TIMESTAMP))
                               or booking_cancelled_flag = false)
                group by
                    student_reference_id, cal."date"
    )
,

booked_by as (
                select
                    student_reference_id as student_id,
                    cal."date",
                    sum(case when booked_person_type = 'staff' then 1 else 0 end) as staff_bookings,
                    sum(case when booked_person_type = 'student' then 1 else 0 end) as student_bookings
                from
                    cal
                    left join all_bookings book
                        on booking_created_datetime between cast(first_month_date AS TIMESTAMP(6))
                        and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6))
                group by
                    student_reference_id, cal."date"
    )
,
activity as (
    select
        student_id,
        cal."date",
        sum(case when completed_date between cast (first_month_date AS TIMESTAMP(6))
        and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
        then multimedia_activities else null end) as mm_activities_mtd,
        sum(case when completed_date between cast(rolling30_start_date AS TIMESTAMP(6))
        and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
        then multimedia_activities else null end) as mm_activities_30days
    from
        cal
        left join fact_act_cut 
            on completed_date between cast (earliest_date AS TIMESTAMP(6)) 
            and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
    group by
        student_id, cal."date"
)
,
levels as (
    select
        student_reference_id as student_id,
        cal."date",
        sum(case when date_granted between cast (first_month_date AS TIMESTAMP(6))
        and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
        then levels_started else null end) as levels_started_mtd,
        sum(case when first_later = 'first' and date_granted between cast (first_month_date AS TIMESTAMP(6))
        and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
        then levels_started else null end) as levels_started_first_mtd,
        sum(case when first_later = 'later' and date_granted between cast (first_month_date AS TIMESTAMP(6))
        and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
        then levels_started else null end) as levels_started_later_mtd
    from
        cal
        left join levels_started 
            on date_granted between cast (earliest_date AS TIMESTAMP(6)) 
            and CAST(CONCAT((CAST(CAST("date"  AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) 
    group by
        student_reference_id, cal."date"
)



select coalesce(book_mtd.student_id,book_first21days.student_id,booked.student_id,activity.student_id,levels.student_id,booked_by.student_id) as student_id,
       coalesce(book_mtd.date,book_first21days.date,booked.date,activity.date,levels.date,booked_by.date) as date,
       book_mtd.encounters_attended_mtd,
       book_mtd.ccs_attended_mtd,
       book_mtd.scs_attended_mtd,
       book_mtd.onl_encounters_attended_mtd,
       book_mtd.onl_ccs_attended_mtd,
       book_mtd.onl_scs_attended_mtd,
       book_mtd.encounters_attended_30days,
       book_mtd.advising_session,
       book_mtd.eol_advising_session,
       book_first21days.encounters_attended_first_21days,
       booked.future_enc_bookings,
       activity.mm_activities_mtd,
       activity.mm_activities_30days,
       levels.levels_started_mtd,
       levels.levels_started_first_mtd,
       levels.levels_started_later_mtd,
       booked_by.staff_bookings,
       booked_by.student_bookings,
       (
            cast(coalesce(book_mtd.date,book_first21days.date,booked.date,activity.date,levels.date,booked_by.date) as varchar) ||
            cast(coalesce(book_mtd.student_id,book_first21days.student_id,booked.student_id,activity.student_id,levels.student_id,booked_by.student_id) as varchar) ||
            'core course'
        ) as dbt_unique_id
    ,CAST(current_timestamp AS TIMESTAMP(6)) AS load_date
from book_mtd

    full join book_first21days
        on book_mtd.student_id = book_first21days.student_id
        and book_mtd."date" = book_first21days."date"

    full join booked
        on coalesce(book_mtd.student_id,book_first21days.student_id) = booked.student_id
        and coalesce(book_mtd."date",book_first21days."date") = booked."date"

    full join activity
        on coalesce(book_mtd.student_id,book_first21days.student_id,booked.student_id) = activity.student_id
        and coalesce(book_mtd."date",book_first21days."date",booked."date") = activity."date"

    full join levels
        on coalesce(book_mtd.student_id,book_first21days.student_id,booked.student_id,activity.student_id) = levels.student_id
        and coalesce(book_mtd."date",book_first21days."date",booked."date",activity."date") = levels."date"

    full join booked_by
        on coalesce(book_mtd.student_id,book_first21days.student_id,booked.student_id,activity.student_id,levels.student_id) = booked_by.student_id
        and coalesce(book_mtd."date",book_first21days."date",booked."date",activity."date",levels."date") = booked_by."date"