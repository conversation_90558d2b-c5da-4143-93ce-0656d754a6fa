{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_prospect') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT 
    {{etl_load_date()}},
    registration_date,
    expiration_date,
    latest_placement_test_score,
    source,
    trial_enabled,
    show_placement_test_result,
    created,
    last_updated,
    privacy_policy_accepted,
    prospect.id,
    ssds_reference_id,
    User.ssds_id as user_reference_id,
    role.lowered_role_name  as user_type,
    center_id,
    first_name,
    last_name,
    prospect.email,
    company_id,
    student_id,
    phone_number
from
    ods_data as prospect
    Left Join (
        select
            role_id,
            ssds_id,
            email
        from
            {{ref('ods_ls_user')}}
    ) as User    
    ON prospect.email = User.email
    Left Join (
        select
            id,
            lowered_role_name
        from
            {{ref('ods_ls_role')}}
            where lowered_role_name  ='student'
    ) as role
    ON User.role_id= role.id
