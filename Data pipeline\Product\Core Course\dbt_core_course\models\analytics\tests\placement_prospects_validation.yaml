version: 2

models:
  - name: placement_prospects
    columns:
      - name: prospect_id
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: center_id
        tests:
          - not_null:
              severity: warn
      - name: center_reference_id
        tests:
          - not_null:
              severity: warn
      - name: prospect_registration_date
        tests:
          - not_null:
              severity: warn
      - name: prospect_created
        tests:
          - not_null:
              severity: warn
      - name: prospect_last_updated
        tests:
          - not_null:
              severity: warn
      - name: source
        tests:
          - accepted_values:
              values: [0, 1, 2]
              quote: false
              severity: warn
      - name: source_category
        tests:
          - accepted_values:
              values: ['crm', 'nse', apim]
              severity: warn
      - name: show_placement_test_result
        tests:
          - accepted_values:
              values: ['false', 'true']
              quote: false
              severity: warn
      - name: email
        tests:
          - not_null:
              severity: warn
      - name: placement_test_completed
        tests:
          - accepted_values:
              values: ['false', 'true']
              quote: false
              severity: warn
      - name: no_of_tests
        tests:
          - accepted_values:
              values: [0, 1]
              quote: false
              severity: warn
