{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with step_aggregation as (
SELECT
    user_reference_id
    ,regexp_replace(regexp_replace(regexp_replace(feature_type, '_', ' '), '(^|\\s)([a-z])', x -> upper(x[2])),'-',' ') AS feature_type
    ,CASE
        WHEN step_type IN ('ineractive_lesson', 'interactive-lesson', 'interactive_lesson') THEN 'Interactive Lesson'
        WHEN step_type IN ('Privacy Policy', 'privacy-policy') THEN 'Privacy Policy'
        WHEN step_type IN ('ready_for_encounter', 'ready_for_encounter_book_step', 'ready_for_encounter_booked_step') THEN 'Ready for Encounter'
        WHEN step_type IN ('Select Language', 'select-language') THEN 'Select Language'
        WHEN step_type IN ('study-planner', 'study_planner') THEN 'Study Planner'
        WHEN step_type IN ('what-social-experience', 'what-social-experience-1', 'what_social_experience') THEN 'What Social Experience'
        WHEN step_type IN ('iwhy-workbook') THEN 'Why workbook'
        ELSE regexp_replace(regexp_replace(regexp_replace(step_type, '_', ' '), '(^|\\s)([a-z])', x -> upper(x[2])),'-',' ')
    END AS step_type
    ,is_completed AS "Feature completed"
    ,is_skipped AS "Feature skipped"
    ,step_is_completed
    ,step_is_skipped
    ,step_updated_at
    ,created_at
    ,updated_at
FROM {{ ref('ods_ob_user_onboards') }}
)

select 
user_reference_id
,CONCAT(feature_type, step_type) AS combined_feature_step
,feature_type
,step_type
,"Feature completed"
,"Feature skipped"
,step_is_completed
,step_is_skipped
,step_updated_at
,created_at
,updated_at
from step_aggregation