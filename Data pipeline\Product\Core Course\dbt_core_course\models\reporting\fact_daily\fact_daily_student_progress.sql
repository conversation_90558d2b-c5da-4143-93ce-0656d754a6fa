{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet',
    unique_key = 'dbt_unique_id',
    pre_hook=[
        """
        {% if is_incremental() %}
            -- Delete records based on incremental
            delete from {{this}}
            where date >=  (
            select
                CASE
                WHEN (EXTRACT(DAY FROM CURRENT_DATE) > 4) OR
                    (EXTRACT(DAY FROM CURRENT_DATE) = 4 AND EXTRACT(HOUR FROM CURRENT_TIMESTAMP) >= 12) THEN
                    DATE_TRUNC('MONTH', CURRENT_DATE)
                ELSE
                    DATE_TRUNC('MONTH',  date_add('month',-1,CURRENT_DATE))
            end as date)
        {% endif %}
        """
    ]
) }}

WITH calendar as
    (
    select
    "date",
	last_month_date
    from reporting.dim_calendar
    where "date" <= current_date
    {% set start_date = var('start_date', none) %}
    {% set end_date = var('end_date', none) %}
    {% if start_date is not none and end_date is not none %}
        and "date" between date('{{ start_date }}') and date('{{ end_date }}' )
    {% elif is_incremental() %}
        {% set current_date = modules.datetime.date.today() %}
        {% set current_day = current_date.day %}
	{% set current_hour = modules.datetime.datetime.now().hour %}
        {% set first_day_current_month = current_date.replace(day=1) %}
        {% set last_day_previous_month = first_day_current_month - modules.datetime.timedelta(days=1) %}
        {% set first_day_previous_month = last_day_previous_month.replace(day=1) %}

        {% if current_day > 4 or (current_day == 4 and current_hour >= 12) %}
            {% set cutoff_date = first_day_current_month %}
        {% else %}
            {% set cutoff_date = first_day_previous_month %}
        {% endif %}
        and "date" >= date '{{ cutoff_date.strftime('%Y-%m-%d') }}'
    {% else %}
        and 1=2 --This is to avoid the initial refresh without input parameters.
    {% endif %}
    ),

agg_student_progress as (
    SELECT
    fds."date",
    fds.group_id,
    coalesce(fds.personal_tutor,'No Personal Tutor') as personal_tutor,
    fds.consultant_id,
    fds.center_reference_id,
    fds.contract_type,
    coalesce(fds.course_level,'No Course Level') as course_level,
    fds.location,
    fds.class_access_type,
    fds.class_access_plus_type,
    fds.service_type,
    fds.is_membership,
    fds.is_teen,
    fds.is_promotional,
    fds.contract_inclusions,
    count(fds.valid_current_date) as "Current Valid Students",
    count(fds.valid_month_to_date) as "Total Students Serviced",
    count(fds.valid_rolling30) as "Total Students Serviced 30days",
    count(fds.valid_completed21days) as "Total Students Completing 21 days",
    count(case when (fdp.mm_activities_mtd > 0 OR fdp.encounters_attended_mtd > 0) then fdp.student_id else null end) AS "Active Students Serviced",
    count(case when (fdp.mm_activities_30days > 0 OR fdp.encounters_attended_30days > 0) then fdp.student_id else null end) AS "Active Students Serviced 30days",
    count(case when (fdp.mm_activities_30days > 0 OR fdp.encounters_attended_30days > 0) and fdp.future_enc_bookings > 0 then fdp.student_id else null end) AS "Active Booked 30days",
    count(case when fdp.encounters_attended_mtd > 0 then fdp.student_id else null end) AS "Students done 1+",
    count(case when fdp.encounters_attended_first_21days > 0 then fdp.student_id else null end) AS "Students completed 21days",
    sum(fdp.levels_started_mtd) AS "Levels started",
    sum(fdp.levels_started_first_mtd) AS "Levels started first",
    sum(fdp.levels_started_later_mtd) AS "Levels started later",
    sum(fdp.ccs_attended_mtd) AS "Total CCs",
    sum(fdp.onl_ccs_attended_mtd) AS "Total CCs Onl",
    sum(fdp.encounters_attended_mtd) AS "Total Encounters",
    sum(fdp.onl_encounters_attended_mtd) AS "Total Encounters Onl",
    sum(fdp.scs_attended_mtd) AS "Total SCs",
    sum(fdp.onl_scs_attended_mtd) AS "Total SCs Onl",
    sum(fdp.advising_session) AS "Total Advising Session",
    sum(fdp.eol_advising_session) AS "Total Eol Advising Session",
    TO_HEX(SHA256(cast(
        cast(fds."date" as varchar)
        || cast(fds.group_id as varchar)
        || cast(fds.consultant_id as varchar)
        || cast(fds.center_reference_id as varchar)
        || cast(fds.contract_type as varchar)
        || cast(fds.location as varchar)
        || cast(fds.class_access_type as varchar)
        || cast(fds.service_type as varchar)
        || cast(fds.is_membership as varchar)
        || cast(fds.is_teen as varchar)
        || cast(fds.is_promotional as varchar)
        || cast(coalesce(fds.course_level,'No Course Level') as varchar)
        || cast(fds.contract_inclusions  as varchar)
        || cast(coalesce(fds.personal_tutor,'No Personal Tutor') as varchar)
        || cast(fds.class_access_plus_type as varchar)
    as varbinary))) as dbt_unique_id
    ,CAST(current_timestamp AS TIMESTAMP(6)) AS load_date
    FROM calendar cal
    left join {{ ref('fact_daily_students') }} fds on cal."date" = fds."date" and ((year(fds."date") < 2023 and fds."date" = cal.last_month_date) or year(fds."date") >= 2023)
    left join {{ ref('fact_daily_progress') }} fdp on fds."date" = fdp."date" and fds.student_reference_id = fdp.student_id
    
    WHERE 
    fds.product_type ='core course'
    group by
    fds."date",
    fds.group_id,
    fds.personal_tutor,
    fds.consultant_id,
    fds.center_reference_id,
    fds.contract_type,
    fds.course_level,
    fds.location,
    fds.class_access_type,
    fds.class_access_plus_type,
    fds.service_type,
    fds.is_membership,
    fds.is_teen,
    fds.is_promotional,
    fds.contract_inclusions
    )

select
    fds.dbt_unique_id
    ,fds."date"
    ,fds.group_id
    ,fds.personal_tutor
    ,fds.consultant_id
    ,fds.center_reference_id
    ,fds.contract_type
    ,fds.course_level
    ,fds.location
    ,fds.class_access_type
    ,fds.class_access_plus_type
    ,fds.service_type
    ,fds.is_membership
    ,fds.is_teen
    ,fds.is_promotional
    ,fds.contract_inclusions
    ,fds."Current Valid Students"
    ,fds."Total Students Serviced"
    ,fds."Total Students Serviced 30days"
    ,fds."Total Students Completing 21 days"
    ,fds."Active Students Serviced"
    ,fds."Active Students Serviced 30days"
    ,fds."Active Booked 30days"
    ,fds."Students done 1+"
    ,fds."Students completed 21days"
    ,fds."Levels started"
    ,fds."Levels started first"
    ,fds."Levels started later"
    ,(fds."Levels started")*p.value AS "DM revenue"
    ,(fds."Levels started first")*p.value AS "DM revenue first"
    ,(fds."Levels started later")*p.value AS "DM revenue later"
    ,fds."Total CCs"
    ,fds."Total CCs Onl"
    ,fds."Total Encounters"
    ,fds."Total Encounters Onl"
    ,fds."Total SCs"
    ,fds."Total SCs Onl"
    ,fds."Total Advising Session"
    ,fds."Total Eol Advising Session"
    ,fds.load_date
FROM agg_student_progress as fds
left join {{ref("territory_centers")}} tc on fds.center_reference_id = tc.center_reference_id
    left join {{ref("pricing")}} p on lower(tc.territory_name) = lower(p.territory_name) and 
        date_format(fds."date", '%Y') = CAST(p.year as varchar) and date_format(fds."date", '%b') = p.Month