import logging
import sys
from awsglue.utils import getResolvedOptions

sys.path.insert(0, '/glue/lib/installation')
keys = [k for k in sys.modules.keys() if 'boto' in k]
for k in keys:
    if 'boto' in k:
        del sys.modules[k]
import pymysql
import pandas as pd
import io
import boto3
import datetime
from datetime import datetime
from datetime import date
import time
import DbOperations
import credential_secret_manager

client_s3 = boto3.client('s3')
client = boto3.client('redshift-data')
todays_date = date.today()
start = time.perf_counter()
db_connect = DbOperations.Database
credential_connect = credential_secret_manager.SecretManager

config_info = getResolvedOptions(sys.argv, ['Bucket'])

jobstarted_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
truncate_table = "truncate table hubspot_crm.id_mapper_dw;"
truncate_table_response = db_connect.Execution ('WriteTable', truncate_table, 'truncating the id_mapper_dw table')
truncate_table_response = client.describe_statement(Id=truncate_table_response['Id'])
while str(truncate_table_response['Status']) != 'FINISHED':
    truncate_table_response = client.describe_statement(Id=truncate_table_response['Id'])
logging.warning('status of table truncate : ', truncate_table_response['Status'])

secret_name = 'mysql_dev_consultant'
mysql_connect = credential_connect.get_secret('mysql_dev_consultant', 'eu-north-1')

conn = pymysql.connect(
    host=mysql_connect['host'],
    user=mysql_connect['user'],
    password=mysql_connect['password'],
    db=mysql_connect['db'],
)
cur = conn.cursor()
cur.execute("select * from hubspot_prod.consultant")
res = [dict((cur.description[i][0], value) for i, value in enumerate(row)) for row in cur.fetchall()]
# print(res)
todays_date = date.today()
year = todays_date.year
month = todays_date.month
day = todays_date.day
# print(output)
bucket = config_info['Bucket']
csv_key = "id_mapper" + f"/year-{todays_date.year}" + f"/month-{todays_date.month}" + f"/day-{todays_date.day}" + "/id_mapper.csv"
df_consultant = pd.DataFrame(res)
df_consultant['role_id']=df_consultant['role_id'].fillna(0).astype(int)
# print(df_consultant)
logging.warning("data fame has been created successfully")
with io.StringIO() as csv_buffer:
    df_consultant.to_csv(csv_buffer, index=False, quoting=1)
    csv_response = client_s3.put_object(Bucket=bucket, Key=csv_key, Body=csv_buffer.getvalue())
    logging.warning("CSV file has been generated")
copy_key = "s3://" + bucket + "/" + csv_key
copy_command = "COPY hubspot_crm.id_mapper_dw FROM '{0}' iam_role 'arn:aws:iam::262158335980:role/RedshitS3access' region 'eu-west-1' IGNOREHEADER 1 CSV timeformat 'auto';".format (copy_key)
logging.warning(copy_command)
copy_query_response = db_connect.Execution('WriteTable', copy_command, 'copy command query from S3 to Redshift')
logging.warning(copy_query_response)
copy_query_response = client.describe_statement(Id=copy_query_response['Id'])
while (str(copy_query_response['Status']) != 'FINISHED'):
    copy_query_response = client.describe_statement(Id=copy_query_response['Id'])
logging.warning("The copy command has been completed")