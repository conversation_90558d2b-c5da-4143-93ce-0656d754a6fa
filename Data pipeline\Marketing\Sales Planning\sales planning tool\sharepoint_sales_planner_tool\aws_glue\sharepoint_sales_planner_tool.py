import packages

secret_instance = packages.secret_manager.SecretManager
sharepoint_credential = secret_instance.get_secret('sharepoint_authentication', 'eu-west-1')
bucket = "sharepoint-sales-manager-develop"
source = 'sales planner tool'
territory = 'MN'  # testing input
object = 'MNubn02'  # testing input
""" used in aws environment to get territory & center input
args = packages.getResolvedOptions(sys.argv, ['territory','object'])
territory = args['territory']
object = args['object']
"""
packages.logging.warning(territory)
packages.logging.warning(object)
operation_1 = 'data extraction'
operation_2 = 'delete query'
operation_3 = 'copy command'
"""update the execution date in execution planner table"""
to_date = packages.datetime.datetime.now()
"""filter the response"""
today = packages.datetime.datetime.today().date()
first_day = str(today.replace(day=1).strftime("%d-%m-%Y"))
packages.logging.warning("first_day:'%s'", format(first_day))
s3 = packages.cloud_operations.S3
redshift = packages.db_operations
iam_role = packages.db_operations.iam_role
region = packages.db_operations.region
"""Variable Declaration for Sharepoint authentication"""
url = sharepoint_credential['url']
username = sharepoint_credential['username']
password = sharepoint_credential['password']
site_url = sharepoint_credential['site_url']
"""Authentication to SharePoint"""
authcookie = packages.Office365(url, username=username, password=password).GetCookies()
site = packages.Site(site_url, version=packages.Version.v2016, authcookie=authcookie)
"""Variables for S3 Folders"""
today_date = packages.date.today()
year = today_date.year
month = today_date.month
"""functions to read & write in redshift"""


def read_table(source, territory, object, operation):
    """function to read table"""
    query = "select process_id, source, object, operation, load_type, status, execution_type, territory, " \
            "s3_path, to_char(cutoff_date,'yyyy-mm-dd') as cutoff_date " \
            "from sharepoint.execution_planner where source =" \
            + f"'{source}'" + " and execution_type ='true' and status = '0' and territory =" + f"'{territory}'" \
            + "and object =" + f"'{object}'" + "and operation =" + f"'{operation}'" + ";"
    packages.logging.warning("query:'%s'", format(query))
    query_execute = redshift.DbQueryExecution.execute_query(query, f"'{territory}'")
    query_describe = redshift.DbQueryExecution.describe_query(query_execute)
    query_response = redshift.DbQueryExecution.get_statement_result(query_describe['Id'])
    packages.logging.warning("length of response:'%s'", format(len(query_response)))
    return query_response


def write_table(update_query, territory):
    """function to write table"""
    packages.logging.warning("query:'%s'", format(update_query))
    update_query_execute = redshift.DbQueryExecution.execute_query(update_query, f"'{territory}'")
    update_query_describe = redshift.DbQueryExecution.describe_query(update_query_execute)
    packages.logging.warning("query_describe['Status']:'%s'", format(update_query_describe['Status']))
    return update_query_describe


def capture_response(file_key, data_request, territory, object, bucket):
    """function to response in s3 file and update table"""
    s3_write_table_response = s3.write_csv_file(file_key, bucket, data_request)
    packages.logging.warning("s3_write_table_response:'%s'", format(s3_write_table_response))
    if len(s3_write_table_response) is not None:
        operation_1_update_query = """update sharepoint.execution_planner set status = '1', cutoff_date = '{}', 
                                    s3_path = '{}', load_type = 'full load' where source = '{}' 
                                    and execution_type = 'true' and territory = '{}' and object = '{}'
                                    and operation = '{}';""". \
            format(to_date, s3_write_table_response, source, territory, object, operation_1)
        operation_3_s3_update_query = """update sharepoint.execution_planner set
                                        s3_path = '{}' where source = '{}' 
                                        and execution_type = 'true'
                                        and territory = '{}' and object = '{}'
                                        and operation = '{}';""". \
            format(s3_write_table_response, source, territory, object, operation_3)
        operation_1_update = write_table(operation_1_update_query, territory)
        operation_3_s3_path_update = write_table(operation_3_s3_update_query, territory)
        response_dict = {
            "execute_update_status": operation_1_update['Status'],
            "file_path_update_status": operation_3_s3_path_update['Status']
        }
        return response_dict


data_extraction_details = read_table(source, territory, object, operation_1)
if len(data_extraction_details) != 0:
    execution_type = data_extraction_details[0]['execution_type']
    status = data_extraction_details[0]['status']
    packages.logging.warning("status:'%s'", format(status))
    packages.logging.warning("execution_type:'%s'", format(execution_type))
    if execution_type == 'true' and status == '0':
        load_type = data_extraction_details[0]['load_type']
        packages.logging.warning("load_type:'%s'", format(load_type))
        if load_type == "full load":
            """--------------------- step 1 data extraction------------------ """
            """read file_path config file"""
            file_path_config_file = s3.connect(bucket, f'config/file_path_config.json')
            file_path_config_content = s3.read_json_file(file_path_config_file)
            for center in file_path_config_content[territory]['centers']:
                if object == center['reference_center_id']:
                    center_name = center['center']
                    reference_center_id = center['reference_center_id']
                    folder = center['folder']
                    packages.logging.warning(folder)
                    filename = center['file']
                    packages.logging.warning(filename)
                    key = "sales_planner_tool" + f"/{territory}" + f"/{center_name}" + f"/year-{year}" + \
                          f"/month-{month}" + f"/{territory}_{center_name}.csv"
                    """Get the File from Sharepoint"""
                    folder = site.Folder(folder)
                    response = folder.get_file(filename)
                    """Getting the Sheet name"""
                    workbook = packages.load_workbook(packages.io.BytesIO(response), data_only=True)
                    get_sheet_names = workbook.sheetnames
                    sheet_index = get_sheet_names.index("Planning tool")
                    workbook.active = sheet_index
                    sheet = workbook.active
                    packages.logging.warning("Sheet Names Available " + str(get_sheet_names))
                    packages.logging.warning("Sheet Active " + str(sheet))
                    """converting the sheet into DF"""
                    data_df = packages.pd.DataFrame(sheet.values)
                    new_header = data_df.iloc[0]  # grab the first row for the header
                    data_df = data_df[1:]  # take the data less the header row
                    data_df.columns = new_header  # set the header row as the df header
                    data_df['Month'] = packages.pd.to_datetime(data_df['Month'], infer_datetime_format=True)
                    filtered_df = data_df.loc[(data_df['Month'] >= first_day)]
                    filtered_df = filtered_df[filtered_df.Source != 'TOTAL']
                    filtered_df['territory_code'] = territory
                    """write the DF into CSV file in the same location"""
                    capture_response(key, filtered_df, territory, object, bucket)
                    packages.logging.warning("file_written_in s3")
                    """----------------- step 2 delete query ---------------------"""
                    """delete existing records in destination table"""
                    delete_query = """delete from sharepoint.sales_planner_tool
                    where territory_code = '{0}' 
                    and "reference center id" like '%{1}%' and month >='{2}';"""\
                        .format(territory, reference_center_id,first_day)
                    packages.logging.warning("delete_query:'%s'", format(delete_query))
                    execute_delete_query = write_table(delete_query, f"{territory}_{center_name}")
                    packages.logging.warning("execute_delete_query:'%s'", format(execute_delete_query))
                    """update status in execution planner table"""
                    operation_2_update_query = """update sharepoint.execution_planner 
                    set status ='1' where source = '{}' and execution_type = 'true' 
                    and territory = '{}' and object = '{}' and operation = '{}';""". \
                        format(source, territory, object, operation_2)
                    packages.logging.warning("operation_2_update_query:'%s'", format(operation_2_update_query))
                    operation_2_update = write_table(operation_2_update_query, territory)
                    packages.logging.warning("operation_2_update:'%s'", format(operation_2_update))
                    """--------------------- step 3 copy command -----------------------"""
                    """insert new records in table"""
                    """copy command query structure"""
                    copy_command_query = """COPY sharepoint.sales_planner_tool
                            FROM 's3://{0}/{1}'
                            iam_role '{2}'
                            region '{3}'
                            IGNOREHEADER 1
                            CSV
                            dateformat 'auto';""".format(bucket, key, iam_role, region)
                    packages.logging.warning("copy_command_query:'%s'", format(copy_command_query))
                    execute_copy_command = write_table(copy_command_query, f"{territory}_{center_name}")
                    packages.logging.warning("execute_copy_command:'%s'", format(execute_copy_command))
                    """update status in execution planner table"""
                    operation_3_update_query = """update sharepoint.execution_planner set 
                    status ='1' where source = '{}' and execution_type = 'true'
                    and territory = '{}' and object = '{}' and operation = '{}';""". \
                        format(source, territory, object, operation_3)
                    packages.logging.warning("operation_3_update_query:'%s'", format(operation_3_update_query))
                    operation_3_update = write_table(operation_3_update_query, territory)
                    packages.logging.warning("operation_3_update:'%s'", format(operation_3_update))
else:
    packages.logging.warning(" already  executed")
