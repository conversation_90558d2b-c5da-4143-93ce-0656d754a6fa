version: 2

models:
  - name: b2b_contracts
    columns:
      - name: company_id
        tests:
          - not_null:
              severity: error
      - name: company_name
        tests:
          - not_null:
              severity: error
      - name: center_owned
        tests:
          - not_null:
              severity: error
      - name: license_number
        tests:
          - not_null:
              severity: warn
      - name: company_created_date
        tests:
          - not_null:
              severity: warn
      - name: company_local_created_date
        tests:
          - not_null:
              severity: warn
      - name: company_last_updated_date
        tests:
          - not_null:
              severity: warn
      - name: company_local_last_updated_date
        tests:
          - not_null:
              severity: warn
      - name: master_contracts_id
        tests:
          - not_null:
              severity: error
      - name: master_contract_number
        tests:
          - not_null:
              severity: warn
      - name: master_contracts_start_date
        tests:
          - not_null:
              severity: warn
      - name: master_contracts_end_date
        tests:
          - not_null:
              severity: warn
      - name: master_contracts_sale_date
        tests:
          - not_null:
              severity: warn
      - name: master_contracts_state
        tests:
          - accepted_values:
              values: ['expired', 'future', 'cancelled', 'valid', 'misentry']
              severity: error
      - name: master_contracts_status
        tests:
          - accepted_values:
              values: ['valid', 'invalid', 'future']
              severity: error
      - name: master_contracts_created_date
        tests:
          - not_null:
              severity: warn
      - name: master_contracts_local_created_date
        tests:
          - not_null:
              severity: warn
      - name: master_contracts_last_updated_date
        tests:
          - not_null:
              severity: warn
      - name: master_contracts_local_last_updated_date
        tests:
          - not_null:
              severity: warn
