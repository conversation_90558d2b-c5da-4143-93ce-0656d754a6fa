create table if not exists hubspot.contactsenriched_mmvn
(
    id                                             bigint encode az64,
    createdate                                     timestamp encode az64,
    lastmodifieddate                               timestamp encode az64,
    channel                                        varchar(128),
    source                                         varchar(128),
    center_name                                    varchar(128),
    hubspot_owner_id                               bigint encode az64,
    hs_lifecyclestage_lead_date                    timestamp encode az64,
    hs_lifecyclestage_marketingqualified_lead_date timestamp encode az64,
    booked_date                                    date encode az64,
    territory_code                                 varchar(4),
    sub_source_level_1                             varchar(64),
    sub_source_level_2                             varchar(64),
    sub_source_level_3                             varchar(64),
    campaign                                       varchar(88),
    timezonecreatedate                             timestamp encode az64,
    territory_name                                 varchar(24),
    cycleid                                        bigint encode az64
);


