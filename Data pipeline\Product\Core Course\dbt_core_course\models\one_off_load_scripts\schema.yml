version: 2

models:
  - name: contracts_location_history
    description: >
      One-off script to generate historical data for contract location changes.
      Combines audit information with current contract data to create a complete history.
    columns:
      - name: contract_id
        description: Unique identifier for the contract
      - name: location
        description: Location where the service is provided
      - name: start_date
        description: Date from which this location value was effective
      - name: end_date
        description: Date until which this location value was effective
      - name: source
        description: Source of the data (1 = audit log, 2 = current contract)
      - name: row_num
        description: Row number for ordering within a contract
      - name: lag_end_date
        description: End date of the previous record for the same contract

  - name: contracts_service_type_history
    description: >
      One-off script to generate historical data for contract service type changes.
      Combines audit information with current contract data to create a complete history.
    columns:
      - name: contract_id
        description: Unique identifier for the contract
      - name: service_type
        description: Type of service provided under the contract
      - name: start_date
        description: Date from which this service type value was effective
      - name: end_date
        description: Date until which this service type value was effective
      - name: source
        description: Source of the data (1 = audit log, 2 = current contract)
      - name: row_num
        description: Row number for ordering within a contract
      - name: lag_end_date
        description: End date of the previous record for the same contract

  - name: contracts_start_date_history
    description: >
      One-off script to generate historical data for contract start date changes.
      Combines audit information with current contract data to create a complete history.
    columns:
      - name: contract_id
        description: Unique identifier for the contract
      - name: contract_start_date
        description: Start date of the contract
      - name: start_date
        description: Date from which this start date value was effective
      - name: end_date
        description: Date until which this start date value was effective
      - name: source
        description: Source of the data (1 = audit log, 2 = current contract)
      - name: row_num
        description: Row number for ordering within a contract
      - name: lag_end_date
        description: End date of the previous record for the same contract

  - name: contracts_end_date_history
    description: >
      One-off script to generate historical data for contract end date changes.
      Combines audit information with current contract data to create a complete history.
    columns:
      - name: contract_id
        description: Unique identifier for the contract
      - name: contract_end_date
        description: End date of the contract
      - name: start_date
        description: Date from which this end date value was effective
      - name: end_date
        description: Date until which this end date value was effective
      - name: source
        description: Source of the data (1 = audit log, 2 = current contract)
      - name: row_num
        description: Row number for ordering within a contract
      - name: lag_end_date
        description: End date of the previous record for the same contract

  - name: contracts_product_type_history
    description: >
      One-off script to generate historical data for contract product type changes.
      Combines audit information with current contract data to create a complete history.
    columns:
      - name: contract_id
        description: Unique identifier for the contract
      - name: contract_products
        description: Products associated with the contract
      - name: valid_from
        description: Date from which these products were effective
      - name: valid_to
        description: Date until which these products were effective
      - name: rn
        description: Row number for ordering within a contract

  - name: progress_dynamic_targets
    description: >
      One-off script to calculate dynamic targets for progress metrics.
      Combines actual performance with target adjustments to set expectations.
    columns:
      - name: territory
        description: Name of the territory or country
      - name: date
        description: Date of the data
      - name: total students serviced
        description: Total number of students serviced
      - name: total students completing 21 days
        description: Number of students who completed 21 days
      - name: active students serviced
        description: Number of active students serviced
      - name: students done 1+
        description: Number of students who completed at least one activity
      - name: students completed 21days
        description: Number of students who completed 21 days
      - name: total encounters
        description: Total number of encounters
      - name: activity rate
        description: Rate of activity participation
      - name: mpr
        description: Monthly Participation Rate
      - name: total done 1+
        description: Total number of activities completed
      - name: expected - active students serviced
        description: Expected number of active students based on targets
      - name: expected - total encounters
        description: Expected number of total encounters based on targets
      - name: expected - students done 1+
        description: Expected number of students completing at least one activity
