import boto3
import ast


class SecretManager:
    @staticmethod
    def get_secret(secret_name, region_name):
        session = boto3.session.Session()
        client = session.client(service_name='secretsmanager', region_name=region_name)
        try:
            get_secret_value_response = client.get_secret_value(SecretId=secret_name)
        except Exception:
            raise Exception
        secret = ast.literal_eval(get_secret_value_response['SecretString'])
        return secret
