{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'dbt_unique_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        (
            contentitemid || skillid
        ) as dbt_unique_id,
        contentitemid,
        skillid,
        ROW_NUMBER() over (
            PARTITION BY contentitemid,
            skillid
            ORDER BY
                contentitemid,
                skillid
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'contentitemskill'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    dbt_unique_id,
    contentitemid as content_item_id,
    skillid as skill_id
FROM
    rankedrecords
WHERE
    rn = 1;
