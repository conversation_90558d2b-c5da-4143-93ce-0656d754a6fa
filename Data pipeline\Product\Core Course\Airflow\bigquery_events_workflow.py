import os
from airflow import DAG
from airflow.operators.python_operator import Python<PERSON><PERSON><PERSON>
from datetime import timed<PERSON><PERSON>, datetime
from airflow.operators.bash import BashOperator
from airflow.providers.amazon.aws.operators.glue import GlueJobOperator
from dependencies.slack_alerts import task_failure_callback, task_warning_callback, task_success_callback
from dependencies import db_operations
from dependencies.pipeline_prerequisite import toggle_dag_state
import logging
import re

# Define your DAG
default_args = {
    'owner': 'wse_data_team',
    'start_date': datetime(2023, 10, 30)
}

redshift_execute = db_operations.Database

dag = DAG('bigquery_events_workflow',
        default_args=default_args,
        schedule_interval='30 0 * * 1',  # You can set your desired schedule_interval
        catchup=False
        )

HOME = os.environ["HOME"]  # retrieve the location of your home folder
dbt_path = os.path.join(HOME, "dbt/dbt_core_course_ga4") 

big_query_events = BashOperator(
    task_id="big_query_events",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                + f" && dbt run",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

page_navigaion_bigquery_data_ingestion = GlueJobOperator(
        task_id='page_navigaion_bigquery_data_ingestion',
        job_name='page_navigaion_bigquery_data_ingestion',
        iam_role_name='AWSGlueServiceRole-AZURE',
        dag = dag
        # Remove on_failure_callback here as well
    )

cue_card_bigquery_data_ingestion = GlueJobOperator(
        task_id='cue-card-bigquery-data-ingestion',
        job_name='cue-card-bigquery-data-ingestion',
        iam_role_name='AWSGlueServiceRole-AZURE',
        dag = dag
        # Remove on_failure_callback here as well
    )

camera_usage_bigquery_data_ingestion = GlueJobOperator(
        task_id='camera-usage-bigquery-data-ingestion',
        job_name='camera-usage-bigquery-data-ingestion',
        iam_role_name='AWSGlueServiceRole-AZURE',
        dag = dag
        # Remove on_failure_callback here as well
    )

big_query_events >> [page_navigaion_bigquery_data_ingestion,cue_card_bigquery_data_ingestion,camera_usage_bigquery_data_ingestion]