version: 2

models:
  - name: fact_daily_students
    columns:
      - name: date
        tests:
          - not_null:
              severity: error
      - name: year_month_key
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        tests:
          - not_null:
              severity: error
      - name: group_id
        tests:
          - not_null:
              severity: warn
      - name: lab_teacher_id
        tests:
          - not_null:
              severity: warn
      - name: consultant_id
        tests:
          - not_null:
              severity: warn
      - name: center_reference_id
        tests:
          - not_null:
              severity: error
      - name: product_type
        tests:
          - accepted_values:
              values: ['d2c', 'core course']
              severity: error
      - name: status
        tests:
          - accepted_values:
              values: ['valid']
              severity: error
      - name: location
        tests:
          - accepted_values:
              values: ['InCenter', 'OutCenter']
              severity: error
      - name: class_access_type
        tests:
          - accepted_values:
              values: ['No Access', 'In-Center', 'Online', 'Full Access']
              severity: error
      - name: service_type
        tests:
          - accepted_values:
              values: ['Standard', 'VIP', 'Teen Standard', 'Teen VIP']
              severity: error
      - name: is_membership
        tests:
          - accepted_values:
              values: ['Level', 'Membership']
              severity: error
      - name: is_teen
        tests:
          - accepted_values:
              values: ['false', 'true']
              quote: false
              severity: error
      - name: is_promotional
        tests:
          - accepted_values:
              values: ['false', 'true']
              quote: false
              severity: error
      - name: self_booking_access_flag
        tests:
          - accepted_values:
              values: ['false', 'true']
              quote: false
              severity: error
      - name: start_date
        tests:
          - not_null:
              severity: error
      - name: end_date
        tests:
          - not_null:
              severity: error
      - name: first_21d_end_date
        tests:
          - not_null:
              severity: error
      - name: rn
        tests:
          - accepted_values:
              values: [1]
              quote: false
              severity: error
      - name: dbt_unique_id
        tests:
          - not_null:
              severity: error
      - name: last_month_date
        tests:
          - not_null:
              severity: error
      - name: contract_type
        tests:
          - accepted_values:
              values: ['Private', 'B2B']
              severity: error
      - name: first_week_date
        tests:
          - not_null:
              severity: error
      - name: study_plan_type
        tests:
          - accepted_values:
              values: ['twelveweeks', 'eightweeks_default', 'fourweeks', 'eightweeks', 'not_applicable']
              severity: error
