{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH scheduledclassproperty AS (
    SELECT
        *
    FROM
        {{ ref(
            'dt_snb_scheduled_class_property'
        ) }})
, category_to_column AS (
    SELECT
        *,
        case when key = 'isb2b' then value end as is_b2_b,
        case when key = 'visibleingroup' then value end as visible_in_group,
        case when key = 'isteen' then value end as is_teen,
        case when key = 'servicetype' then value end as service_type
    FROM
        scheduledclassproperty
)
, align_table as (
    SELECT
        etl_load_date,
        scheduled_class_id as class_id,
        max(is_b2_b) as is_b2_b,
        max(visible_in_group) as visible_in_group,
        max(is_teen) as is_teen,
        max(service_type) as service_type
    FROM
        category_to_column
    GROUP BY
        etl_load_date,
        scheduled_class_id
)
, format_columns as (
    select etl_load_date,
        class_id,
        case when is_b2_b is null then false
            when is_b2_b like 'false' then false
            when is_b2_b like 'true' then true 
        end as is_b2_b,
        case when visible_in_group is null then false
            when visible_in_group like 'false' then false
            when visible_in_group like 'true' then true 
        end as visible_in_group,
        case when is_teen is null then false
            when is_teen like 'false' then false
            when is_teen like 'true' then true 
        end as is_teen,
        case when service_type is null then 'combined'
            else service_type
        end as service_type
    from align_table
)

select *
from format_columns