{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'dbt_unique_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        (
            studentid || categoryid
        ) as dbt_unique_id,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        studentid,
        categoryid,
        ROW_NUMBER() over (
            PARTITION BY studentid,
            categoryid
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'digitalbooksrestart'
        ) }}
)
SELECT
    {{etl_load_date()}},
    dbt_unique_id,
    created,
    lastupdated as last_updated,
    studentid as student_id,
    categoryid as category_id
FROM
    rankedrecords
WHERE
    rn = 1;
