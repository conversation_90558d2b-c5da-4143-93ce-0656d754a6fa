version: 2

models:
  - name: dim_students
    columns:
      - name: last_month_date
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        tests:
          - not_null:
              severity: error
      - name: group_id
        tests:
          - not_null:
              severity: error
      - name: lab_teacher_id
        tests:
          - not_null:
              severity: error
      - name: consultant_id
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        tests:
          - not_null:
              severity: error
      - name: product_type
        tests:
          - accepted_values:
              values: ['core course', 'd2c']
              severity: error
      - name: status
        tests:
          - accepted_values:
              values: ['valid']
              severity: error
      - name: location
        tests:
          - accepted_values:
              values: ['incenter', 'outcenter', 'no_location']
              severity: error
      - name: class_access_type
        tests:
          - accepted_values:
              values: ['in-center class access', 'online class access', 'full access', 'no_class_access_type']
              severity: error
      - name: service_type
        tests:
          - accepted_values:
              values: ['standard', 'vip', 'no_service_type']
              severity: error
      - name: is_membership
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: is_teen
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: is_promotional
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: contract_type
        tests:
          - accepted_values:
              values: ['private', 'b2b']
              severity: error
      - name: self_booking_access_flag
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: rn
        tests:
          - accepted_values:
              values: [1]
              quote: false
              severity: error
