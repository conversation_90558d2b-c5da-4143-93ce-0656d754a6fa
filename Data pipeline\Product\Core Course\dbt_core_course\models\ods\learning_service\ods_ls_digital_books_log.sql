{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('dategranted') }} as dategranted,
        unlocktype,
        operationtype,
        sequence,
        workbooktype,
        isrestart,
        studentid,
        categoryid,
        centerid,
        registrationid,
        id,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                dategranted DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'digitalbookslog'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    dategranted as date_granted,
    unlocktype as unlock_type,
    operationtype as operation_type,
    sequence,
    workbooktype as workbook_type,
    isrestart as is_restart,
    studentid as student_id,
    categoryid as category_id,
    centerid as center_id,
    registrationid as registration_id,
    id
FROM
    rankedrecords
WHERE
    rn = 1;
