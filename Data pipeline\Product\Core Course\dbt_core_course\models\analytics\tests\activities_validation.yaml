version: 2

models:
  - name: activities
    columns:
      - name: activity_type
        tests:
          - accepted_values:
              values: ['multimedia', 'digital_workbook']
              severity: warn
      - name: activity_id
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: student_id
        tests:
          - not_null:
              severity: error
      - name: content_item_id
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        tests:
          - not_null:
              severity: error
      - name: completed_date
        tests:
          - not_null:
              severity: warn
      - name: level
        tests:
          - not_null:
              severity: error
