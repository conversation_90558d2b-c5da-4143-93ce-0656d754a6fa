{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        beginningofdidacticyear,
        bookminutesuptoclassstart,
        cancelminutesuptoclassstart,
        carryovermaximum,
        {{ cast_to_timestamp('createddate') }} as createddate,
        isactive,
        hasonlineclassesvisibility,
        hasschedulingvisibility,
        iscarryoverenabled,
        isstandbyenabled,
        iswaitinglistenabled,
        {{ cast_to_timestamp('lastmodifieddate') }} as lastmodifieddate,
        noofbookingdaysinadvance,
        noofclassrooms,
        noofotherspaces,
        noofstudentsperencounter,
        noofstudentspercomplementaryclass,
        remindernotificationsbeforeclassstart,
        socialclassorcomplementaryclassperlevel,
        maximumstudentsinstandby,
        waitinglistencounteroronlineencounter,
        showunusedencounters,
        staffcancelminutesuptoclassstart,
        isstandbyconfigurable,
        isoneweekplanavailableinstudyplan,
        isstudentavailabilityenabled,
        id,
        centerid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastmodifieddate DESC
        ) AS rn
    FROM
        {{ source(
            'stage_center_configuration_service',
            'centerconfiguration'
        ) }}
)
SELECT
    {{etl_load_date()}},
    beginningofdidacticyear as beginning_of_didactic_year,
    bookminutesuptoclassstart as book_minutes_up_to_class_start,
    cancelminutesuptoclassstart as cancel_minutes_up_to_class_start,
    carryovermaximum as carry_over_maximum,
    createddate as created_date,
    isactive as is_active,
    hasonlineclassesvisibility as has_online_classes_visibility,
    hasschedulingvisibility as has_scheduling_visibility,
    iscarryoverenabled as is_carry_over_enabled,
    isstandbyenabled as is_stand_by_enabled,
    iswaitinglistenabled as is_waiting_list_enabled,
    lastmodifieddate as last_modified_date,
    noofbookingdaysinadvance as no_of_booking_days_in_advance,
    noofclassrooms as no_of_class_rooms,
    noofotherspaces as no_of_other_spaces,
    noofstudentsperencounter as no_of_students_per_encounter,
    noofstudentspercomplementaryclass as no_of_students_per_complementary_class,
    remindernotificationsbeforeclassstart as reminder_notifications_before_class_start,
    socialclassorcomplementaryclassperlevel as social_class_or_complementary_class_per_level,
    maximumstudentsinstandby as maximum_students_in_standby,
    waitinglistencounteroronlineencounter as waiting_list_encounter_or_online_encounter,
    showunusedencounters as show_unused_encounters,
    staffcancelminutesuptoclassstart as staff_cancel_minutes_upto_class_start,
    isstandbyconfigurable as is_stand_by_configurable,
    isoneweekplanavailableinstudyplan as is_one_week_plan_available_in_study_plan,
    isstudentavailabilityenabled as is_student_availability_enabled,
    id,
    centerid as center_id
FROM
    rankedrecords
WHERE
    rn = 1;
