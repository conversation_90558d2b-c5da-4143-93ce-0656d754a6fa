
--DROP TABLE hubspot_crm.external_data_definitions;
CREATE TABLE IF NOT EXISTS hubspot_crm.external_data_definitions
(
	area VARCHAR(24)   ENCODE lzo
	,subarea VARCHAR(128)   ENCODE lzo
	,data_name VARCHAR(128)   <PERSON>NCOD<PERSON> lzo
	,value_type VARCHAR(38)   ENCODE lzo
	,definition VARCHAR(648)   ENCODE lzo
	,notes VARCHAR(648)   ENCODE lzo
	,"priority" INTEGER   ENCODE az64
	,data_accesibility VARCHAR(8)   ENCODE lzo
	,"owner" VARCHAR(24)   ENCODE lzo
	,"input" VARCHAR(24)   ENCODE lzo
	,"jan-23" DOUBLE PRECISION   ENCODE RAW
	,"feb-23" DOUBLE PRECISION   ENCODE RAW
	,"mar-23" DOUBLE PRECISION   ENCODE RAW
	,"apr-23" DOUBLE PRECISION   ENCODE RAW
	,"may-23" DOUBLE PRECISION   ENCODE RAW
	,"jun-23" DOUBLE PRECISION   ENCODE RAW
	,"jul-23" DOUBLE PRECISION   ENCODE RAW
	,"aug-23" DOUBLE PRECISION   ENCODE RAW
	,"sep-23" DOUBLE PRECISION   ENCODE RAW
	,"oct-23" DOUBLE PRECISION   ENCODE RAW
	,"nov-23" DOUBLE PRECISION   ENCODE RAW
	,"dec-23" DOUBLE PRECISION   ENCODE RAW
	,"jan-24" DOUBLE PRECISION   ENCODE RAW
	,"feb-24" DOUBLE PRECISION   ENCODE RAW
	,"mar-24" DOUBLE PRECISION   ENCODE RAW
	,"apr-24" DOUBLE PRECISION   ENCODE RAW
	,"may-24" DOUBLE PRECISION   ENCODE RAW
	,"jun-24" DOUBLE PRECISION   ENCODE RAW
	,"jul-24" DOUBLE PRECISION   ENCODE RAW
	,"aug-24" DOUBLE PRECISION   ENCODE RAW
	,"sep-24" DOUBLE PRECISION   ENCODE RAW
	,"oct-24" DOUBLE PRECISION   ENCODE RAW
	,"nov-24" DOUBLE PRECISION   ENCODE RAW
	,"dec-24" DOUBLE PRECISION   ENCODE RAW
	,"jan-25" DOUBLE PRECISION   ENCODE RAW
	,"feb-25" DOUBLE PRECISION   ENCODE RAW
	,"mar-25" DOUBLE PRECISION   ENCODE RAW
	,"apr-25" DOUBLE PRECISION   ENCODE RAW
	,"may-25" DOUBLE PRECISION   ENCODE RAW
	,"jun-25" DOUBLE PRECISION   ENCODE RAW
	,"jul-25" DOUBLE PRECISION   ENCODE RAW
	,"aug-25" DOUBLE PRECISION   ENCODE RAW
	,"sep-25" DOUBLE PRECISION   ENCODE RAW
	,"oct-25" DOUBLE PRECISION   ENCODE RAW
	,"nov-25" DOUBLE PRECISION   ENCODE RAW
	,"dec-25" DOUBLE PRECISION   ENCODE RAW
	,uu INTEGER   ENCODE az64
)
