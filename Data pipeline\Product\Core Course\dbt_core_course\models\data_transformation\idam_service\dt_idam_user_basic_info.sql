{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'user_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_idam_user_basic_info') }}
)
select
    gender,
    birth_date,
    created,
    last_updated,
    is_active,
    enable_password_reset,
    is_prospect,
    user_id,
    user_name,
    password,
    first_name,
    last_name,
    email,
    ssds_id,
    center_id,
    territory_id,
    user_external_id
from
    ods_data as userbasicinfo
