{{
    config(
        tags=["incremental","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}

with a as (
select
page,event_date,query,lang,
clicks,
local_clicks,
impressions,
local_impressions,
sum_position,
"gsc" as data_source
from 
{{ ref('gsc_bypage_query_test') }}
 where  {{increment()}}
union all

select
page,event_date,query,lang,
sum(clicks) clicks,
sum(case when country=gsc_country THEN clicks ELSE 0 END) as local_clicks, 
sum(impressions) impressions,
sum(case when country=gsc_country THEN impressions ELSE 0 END) as local_impressions,  
SUM (impressions*(position-1)) as sum_position, 
"gsc history" as data_source
from 
{{ ref('gsc_history_init_test') }}
where  {{increment()}}
group by page,event_date,lang,query
),
b as (
SELECT 
a.page,a.event_date,a.lang,a.query,
ANY_VALUE(clicks) clicks,
ANY_VALUE(local_clicks) local_clicks,
any_value(sum_position) sum_position,
ANY_VALUE(impressions) impressions,
ANY_VALUE(local_impressions) local_impressions,
MIN(r.rank_organic) rank_organic,
ANY_VALUE(r.page) rank_page,
max (r.is_featured_snippet) is_featured_snippet,
string_agg(data_source) as data_sources,
CASE WHEN query LIKE "%wal%" or query LIKE "%wse%" or query LIKE "%وول ستريت%" THEN TRUE ELSE FALSE END AS is_branded,

from a
 left join {{ ref('rank_with_all_dates_test') }} r using (query,event_date)

group by page,event_date,lang,query
)

select 
b.*,
sum_top_position,
site_impressions

from b 
left join {{ ref('gsc_site_byquery_test') }} using (query,event_date,lang)
