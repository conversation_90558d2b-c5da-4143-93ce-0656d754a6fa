{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_idam_user_additional_info') }}
)
SELECT {{etl_load_date()}},
    id,
    is_email_verified,
    created,
    last_updated,
    is_active,
    send_mail_preference,
    preferred_contact_method,
    user_basic_info_id,
    social_network_id1,
    social_network_address1,
    social_network_id2,
    social_network_address2,
    social_network_id3,
    social_network_address3,
    social_network_id4,
    social_network_address4,
    photo_name,
    mobile_telephone,
    home_telephone,
    work_telephone,
    fax,
    address1,
    address2,
    city,
    state,
    postal_code,
    call_between_from,
    call_between_to,
    personal_profession_id,
    personal_motivation_id,
    personal_nationality_id,
    personal_native_language_id,
    personal_secondary_language_id,
    interest_hobbies,
    about_me,
    study_reason,
    why_study_english,
    work_motivation
from
    ods_data as useradditionalinfo
