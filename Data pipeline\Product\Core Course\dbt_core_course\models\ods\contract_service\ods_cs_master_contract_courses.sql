{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        mastercontractid,
        producttypeid,
        studytypeid,
        {{cast_to_int('b2bcontracttype')}},
        price,
        refund,
        {{cast_to_timestamp('startdate')}} as startdate,
        {{cast_to_timestamp('enddate')}} as enddate,
        state,
        status,
        {{cast_to_timestamp('canceldate')}} as canceldate,
        isactive,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
        ismembership,
        isnonmembership,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'mastercontractcourses')}}
)

SELECT
    {{etl_load_date()}},
    id,
    mastercontractid as master_contract_id,
    producttypeid as product_type_id,
    studytypeid as study_type_id,
    b2bcontracttype as b2_b_contract_type,
    price,
    refund,
    startdate as start_date,
    enddate as end_date,
    state,
    status,
    canceldate as cancel_date,
    isactive as is_active,
    createddate as created_date,
    lastupdateddate as last_updated_date,
    ismembership as is_membership,
    isnonmembership as is_non_membership
FROM 
    RankedRecords
WHERE 
    rn = 1;