{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_snb_scheduled_class_category'
        ) }}
)

SELECT {{etl_load_date()}},
    scheduledclasscategory.id as id,
    scheduled_class_id,
    category.value as category,
    case
        when category.type = 0 then 'none'
        when category.type = 1 then 'unit'
        when category.type = 2 then 'level'
        when category.type = 3 then 'stage' else cast(category.type as varchar)
    end as category_type,
    created,
    last_updated
From ods_data as ScheduledClassCategory
    Left Join (
        Select id,
            value,
            type
        from {{ ref("ods_snb_category") }}
    ) category On ScheduledClassCategory.category_id = category.Id