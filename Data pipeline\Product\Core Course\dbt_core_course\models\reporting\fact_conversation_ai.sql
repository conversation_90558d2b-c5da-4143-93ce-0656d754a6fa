{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet',
    unique_key = 'dbt_unique_id'
) }}


select 
    dc.chat_id,
    dc.user_id,
    dc.contract_id,
    dc.content_id,
    dc.start_date,
    dc.end_date,
    dc.chat_start_date,
    dc.chat_end_date,
    dc.total_cost,
    dc.chat_ended,
    dc.total_no_of_interactions,
    dc.total_input_tokens,
    dc.total_output_tokens,
    dc.total_tokens,
    dc.chat_duration_seconds,
    dc.total_messages,
    dc.user_messages,
    dc.assistant_messages,
    dc.user_speaking_time,
    dc.assistant_speaking_time,
    dc.total_output_tokens_per_message,
    dc.total_input_tokens_per_message,
    dc.max_output_tokens,
    dc.max_input_tokens,
    dc.min_output_tokens,
    dc.min_input_tokens,
    dc.total_message_length,
    dc.total_message_audio_duration,
    dc.max_message_length,
    dc.min_message_length,
    dc.understanding_gap_count,
    fds."date",
    fds.group_id,
    coalesce(fds.personal_tutor,'No Personal Tutor') as personal_tutor,
    fds.consultant_id,
    fds.center_reference_id,
    fds.contract_type,
    fds.product_type,
    fds.bookmark_mm_level,
    coalesce(fds.course_level,'No Course Level') as course_level,
    fds.location,
    fds.class_access_type,
    fds.service_type,
    fds.is_membership,
    fds.is_teen,
    fds.is_promotional,
    fds.contract_inclusions,
    (cast(dc.chat_id as varchar)
    || cast(coalesce(dc.user_id,'no_user_id') as varchar)
    || cast(coalesce(dc.contract_id,'no_contract_id') as varchar)
    || cast(coalesce(dc.content_id,'no_content_id') as varchar)
    ) as dbt_unique_id,
    cast(current_timestamp AS TIMESTAMP(6)) AS load_date
from {{ref('conversation_ai')}} as dc
left join {{ref('fact_daily_students')}} as fds on date(dc.chat_start_date) = fds."date"
and dc.user_id = fds.student_reference_id
and dc.contract_id = fds.contract_reference_id
{% if is_incremental() %}
    WHERE CAST(dc.start_date AS DATE) > (SELECT CAST(DATE_ADD('day', -2, MAX(start_date)) AS DATE) FROM {{ this }})
{% endif %}