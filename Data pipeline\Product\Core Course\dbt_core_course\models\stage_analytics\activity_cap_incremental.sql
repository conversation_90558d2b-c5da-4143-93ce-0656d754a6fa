{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='unique_id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH date_range AS (
    SELECT 
        {% if is_incremental() %}
            DATE_ADD('day', -9, (SELECT MAX(date_completed) FROM {{this}})) AS start_date, (SELECT MAX(date_completed) FROM {{this}}) as calc_date
        {% else %}
            DATE('2024-05-29')  AS start_date, DATE('2024-06-07') as calc_date
        {% endif %}
),
contentitemresult AS (
    SELECT 
        'multimedia' AS source,
        content_item_id,
        DATE(date_completed) AS date_completed,
        duration,
        content_itemresult_type
    FROM {{ ref('dt_ls_content_item_result') }}
    WHERE DATE(date_completed) >= (SELECT start_date FROM date_range)
    AND content_itemresult_type = 'passed'
),
digital_workbook AS (
    SELECT
        'digital_workbook' AS source,
        activity_reference_id AS content_item_id,
        DATE(last_updated) AS date_completed,
        duration
    FROM {{ ref('dt_dsw_activity_progress') }}
    WHERE is_completed = true
        AND DATE(last_updated) >= (SELECT start_date FROM date_range)
),
combined_data AS (
    SELECT
        source,
        content_item_id,
        date_completed,
        duration
    FROM contentitemresult
    
    UNION ALL
    
    SELECT
        source,
        content_item_id,
        date_completed,
        duration
    FROM digital_workbook

),
aggregated_data AS (
    SELECT
        source,
        content_item_id || CAST(dc."date" AS VARCHAR) AS unique_id,
        content_item_id,
        dc."date" AS date_completed,
        APPROX_PERCENTILE(duration, ARRAY[0, 0.1, 0.25, 0.5, 0.75, 0.9, 1]) AS percentiles
    FROM reporting.dim_calendar dc
    LEFT JOIN combined_data cd
        ON cd.date_completed BETWEEN DATE_ADD('day', -7, dc."date") AND DATE_ADD('day', -1, dc."date")
    WHERE dc."date" > (SELECT calc_date FROM date_range) AND dc."date" <= current_date
    GROUP BY source, content_item_id, dc."date"
)
SELECT
    unique_id,
    content_item_id,
    date_completed,
    percentiles[1] / 60.0 AS minimum_duration,
    percentiles[2] / 60.0 AS percentile_10_duration,
    percentiles[3] / 60.0 AS percentile_25_duration,
    percentiles[4] / 60.0 AS median_duration,
    percentiles[5] / 60.0 AS percentile_75_duration,
    percentiles[6] / 60.0 AS percentile_90_duration,
    percentiles[7] / 60.0 AS maximum_duration
FROM aggregated_data