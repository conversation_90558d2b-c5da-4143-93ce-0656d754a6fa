{{
    config(
        tags=["incremental","rank","init","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}
with a as(
SELECT  
r.keyword as query, 
tasks.data.language_name,
tasks.data.location_name,
tasks.data.device,
r.datetime, 
EXTRACT (date FROM datetime) AS event_date,
r.type AS result_type,
i.breadcrumb as snippet_breadcrumb, 
i.url,
i.title as snippet_title,
i.description as snippet_description,
i.rank_group,
i.domain,
i.type,
CASE WHEN i.type="organic" THEN rank_group ELSE NULL END AS rank_organic,
CASE WHEN i.type="paid" THEN rank_group ELSE NULL END AS rank_paid,
CASE WHEN i.type="featured_snippet" THEN rank_group ELSE NULL END AS rank_featured_snippet,
(SELECT STRING_AGG(item_types) FROM UNNEST(item_types) item_types) AS item_on_serp
FROM {{ source('rank', 'rank') }}, UNNEST (tasks) tasks, UNNEST(result) r, UNNEST (items) i
{% if is_incremental() %}
where EXTRACT (date FROM datetime)  {{daily_run()}}
{% endif %}  
),
tab_domain as (
select 
*,
  CASE
    WHEN REGEXP_CONTAINS(
    REGEXP_EXTRACT(url,"https?://([^/]+)")    
    ,"(free.fr|blogspot.com|blogspot.fr|canalblog.com|co.([a-z][a-z]])|gouv.fr|asso.fr|com?[.]([a-z][a-z])|ac.uk|[qg]c.ca|go.jp|over-blog.(com|fr)|eklablog.com|blogg.org|edu.([a-z][a-z]])|in.th)$") THEN REGEXP_EXTRACT(
        REGEXP_EXTRACT(url,"https?://([^/]+)")  
        ,"(?:^|[/.])([^.]+[.][^.]+[.][^.]+)$")
  ELSE
  REGEXP_EXTRACT(
  REGEXP_EXTRACT(url,"https?://([^/]+)")
  ,"(?:^|[/.])([^.]+[.][^.]+)$")
END
AS url_domain,
 from a
)

-- Best ranking per domain
select
url_domain,event_date,language_name,location_name,query,
STRING_AGG(snippet_title order by IFNULL(rank_featured_snippet,100) desc, IFNULL(rank_organic,100) desc LIMIT 1) snippet_title,
STRING_AGG(snippet_breadcrumb order by IFNULL(rank_featured_snippet,100) desc, IFNULL(rank_organic,100) desc LIMIT 1) snippet_breadcrumb,
STRING_AGG(url order by IFNULL(rank_featured_snippet,100) desc, IFNULL(rank_organic,100) desc LIMIT 1) url,
STRING_AGG(snippet_description order by IFNULL(rank_featured_snippet,100) desc, IFNULL(rank_organic,100) desc LIMIT 1) snippet_description,
MIN(rank_organic) rank_organic,
MIN(rank_featured_snippet) rank_featured_snippet

from tab_domain
group by url_domain,event_date,language_name,location_name,query


