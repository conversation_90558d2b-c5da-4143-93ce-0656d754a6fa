{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        score,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        prospectid,
        interactionid,
        placementtestinteractionresultid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'placementtestresult'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    score,
    created,
    lastupdated as last_updated,
    id,
    prospectid as prospect_id,
    interactionid as interaction_id,
    placementtestinteractionresultid as placement_test_interaction_result_id
FROM
    rankedrecords
WHERE
    rn = 1;
