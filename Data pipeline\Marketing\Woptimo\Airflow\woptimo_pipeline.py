import os
from airflow import DAG
from airflow.operators.python_operator import <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
from airflow.operators.bash import <PERSON><PERSON><PERSON>perator
from dependencies.slack_alerts import task_failure_callback
from dependencies.cloud_operations import S3
from dependencies import db_operations
from dependencies.pipeline_prerequisite import toggle_dag_state
from airflow.providers.amazon.aws.operators.s3 import S3DeleteObjectsOperator
from airflow.providers.amazon.aws.operators.glue import GlueJobOperator


# Define your DAG
default_args = {
    'owner': 'wse_data_team',
    'start_date': datetime(2023, 10, 30)
}

redshift_execute = db_operations.Database

dag = DAG('woptimo_pipeline',
        default_args=default_args,
        schedule_interval="0 4 * * *",  # You can set your desired schedule_interval
        catchup=False
        )


# function to call prerequisite
def execute_prerequisite():
    toggle_dag_state(dag)

#function to copy data from S3 to redshift
def redshift_copy_command():
    file_path = 's3://wse-seo-reporting/json_response/data-mart-page-test/'
    redshift_table_name = f"wse_seo_reporting.data_mart_page_test"
    
    pre_reqisite_query = """TRUNCATE TABLE {} """.format(redshift_table_name)
    truncate_statement = "truncating the table " + redshift_table_name + " before copying the latest data"
    truncate_execute = redshift_execute.execution('WriteTable', pre_reqisite_query, truncate_statement)
    print(truncate_execute)
    
    statement_name = "This query is copy of table " + redshift_table_name + " to redshift"
    copy_command_query = """COPY wse_seo_reporting.data_mart_page_test
    FROM 's3://wse-seo-reporting/json_response/data-mart-page-test/'
    IAM_ROLE 'arn:aws:iam::262158335980:role/RedshitS3access'
    REGION 'eu-west-1'
    JSON 's3://wse-seo-reporting/manifest_json/jsonpath-test.json';"""
    print(copy_command_query)
    copy_execute = redshift_execute.execution('WriteTable', copy_command_query, statement_name)
    print(copy_execute)
    print("Data  copy from S3 to redshift completed for table", redshift_table_name)

# basic prerequisite check
prerequisite_check = PythonOperator(
    task_id='prerequisite_check',
    python_callable=execute_prerequisite,
    provide_context=True,
    dag=dag
)

s3_page_delete = S3DeleteObjectsOperator(
    task_id='s3_page_delete',
    bucket='wse-seo-reporting',
    keys=['json_response/data-mart-page-test/*'],  # Replace with your S3 folder path
    aws_conn_id='aws_default',  # Replace with your AWS connection ID
    dag=dag,
)

trigger_dbt = BashOperator(
    task_id="trigger_dbt",
    bash_command="cd /home/<USER>/dbt"
                + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                + f" && cd dbt_woptimo"  # Go to the path containing your dbt project environment
                + f" && dbt run --select tag:test",  # run the model!
    on_failure_callback=task_failure_callback,
    dag=dag
)

# Task to trigger the AWS Glue job
glue_bigquery_copy_job = GlueJobOperator(
        task_id="glue_bigquery_copy_job",
        job_name='woptimo-data-mart-page-test',
        iam_role_name='AWSGlueServiceRole-AZURE',
        dag=dag,
    )

s3_to_redshift = PythonOperator(
    task_id=f"s3_to_redshift",
    python_callable=redshift_copy_command,
    on_failure_callback=task_failure_callback,
    dag=dag)


# Set up the task dependency
prerequisite_check >> s3_page_delete
s3_page_delete >> trigger_dbt
trigger_dbt >> glue_bigquery_copy_job
glue_bigquery_copy_job >> s3_to_redshift


if __name__ == "__main__":
    dag.cli()
