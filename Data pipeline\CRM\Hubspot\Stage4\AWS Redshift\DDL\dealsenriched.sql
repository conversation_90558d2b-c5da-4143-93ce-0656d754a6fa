create table if not exists hubspot_crm.dealsenriched
(
    dealid                                bigint encode az64,
    isdeleted                             varchar(10),
    associatedcompanyids                  varchar(13),
    associateddealids                     varchar(13),
    associated<PERSON><PERSON>                        varchar(13),
    amount                                double precision,
    amount_gifts                          double precision,
    amount_interest                       double precision,
    amount_in_home_currency               double precision,
    booked_date                           timestamp encode az64,
    business_partner_1_amount             integer encode az64,
    business_partner_2_hours              integer encode az64,
    business_partner_3_start_date         timestamp encode az64,
    business_partner_4_end_date           timestamp encode az64,
    business_partner_5_start_level        integer encode az64,
    business_partner_6_end_level          integer encode az64,
    business_partner_7_tags               varchar(12),
    center_name                           varchar(64),
    channel_drill_down_1                  varchar(128),
    channel_drill_down_2                  varchar(128),
    closedate                             timestamp encode az64,
    closed_lost_reason                    varchar(292),
    closed_won_reason                     varchar(124),
    coach                                 varchar(64),
    contacted_date                        timestamp encode az64,
    conversion                            varchar(300),
    conversion_date                       timestamp encode az64,
    contract_number                       bigint encode az64,
    core_course_7_class_access_type       varchar(9),
    core_course_amount                    double precision,
    core_course_end_date                  timestamp encode az64,
    core_course_levels                    integer encode az64,
    core_course_start_date                timestamp encode az64,
    core_course_contract_id               varchar(128),
    core_course_student_id                varchar(128),
    createdate                            timestamp encode az64,
    days_to_close                         bigint encode az64,
    dealname                              varchar(400),
    deal_source                           varchar(64),
    dealstage                             varchar(36),
    dealtype                              varchar(16),
    description                           varchar(400),
    dm_known                              varchar(128),
    interest_date                         timestamp encode az64,
    lead_date                             timestamp encode az64,
    likelihood_to_close                   varchar(13),
    lost_reason                           varchar(128),
    lost_stage                            varchar(128),
    hs_all_accessible_team_ids            varchar(84),
    hs_all_owner_ids                      varchar(48),
    hs_all_team_ids                       varchar(48),
    hs_analytics_source                   varchar(18),
    hs_analytics_source_data_1            varchar(342),
    hs_analytics_source_data_2            varchar(320),
    hs_campaign                           varchar(13),
    hs_createdate                         timestamp encode az64,
    hs_deal_amount_calculation_preference varchar(22),
    hs_lastmodifieddate                   timestamp encode az64,
    hs_object_id                          bigint encode az64,
    hs_sales_email_last_replied           varchar(24),
    hubspot_owner_assigneddate            timestamp encode az64,
    hubspot_owner_id                      bigint encode az64,
    hubspot_team_id                       bigint encode az64,
    individual_corporate                  varchar(18),
    market_leader_1_amount                varchar(12),
    market_leader_2_hours                 varchar(12),
    market_leader_3_start_date            timestamp encode az64,
    market_leader_4_end_date              timestamp encode az64,
    market_leader_5_start_level           varchar(36),
    market_leader_6_end_level             varchar(36),
    membership                            varchar(128),
    mql_date                              timestamp encode az64,
    notes_last_contacted                  timestamp encode az64,
    notes_last_updated                    timestamp encode az64,
    notes_next_activity_date              timestamp encode az64,
    num_associated_contacts               varchar(24),
    num_contacted_notes                   varchar(24),
    num_notes                             varchar(24),
    pe_end_level                          varchar(36),
    pe_start_level                        varchar(36),
    pipeline                              varchar(64),
    prospect_date                         timestamp encode az64,
    showed_date                           timestamp encode az64,
    source                                varchar(248),
    stage                                 varchar(64),
    sub_source                            varchar(32),
    test_prep_group_1_amount              varchar(48),
    test_prep_group_2_quantity            varchar(14),
    test_prep_group_3_start_date          timestamp encode az64,
    test_prep_group_4_end_date            timestamp encode az64,
    test_prep_types_1                     varchar(36),
    extraurlparameters                    varchar(100),
    channel                               varchar(400),
    show_location                         varchar(19),
    core_course_idam_userid               varchar(13),
    offer_date                            timestamp encode az64,
    offer_amount                          varchar(58),
    course_age_group                      varchar(15),
    core_course_7_product_type            varchar(19),
    amount_net                            double precision,
    call_campaign                         varchar(26),
    certifications_1_amount               double precision,
    certifications_2_quantity             bigint encode az64,
    certifications_3_type                 varchar(10),
    certifications_4_start_date           timestamp encode az64,
    certifications_5_end_date             timestamp encode az64,
    convenzioni                           varchar(132),
    core_course_7_cross_center_booking    varchar(14),
    core_course_7_type                    varchar(13),
    core_course_fit_0_group_name          varchar(20),
    core_course_fit_2_hours               varchar(6),
    core_course_fit_3_start_date          timestamp encode az64,
    core_course_fit_4_end_date            timestamp encode az64,
    core_course_fit_5_start_level         bigint encode az64,
    core_course_fit_6_end_level           bigint encode az64,
    core_course_fit_7_type                varchar(13),
    core_course_fit_amount                double precision,
    core_course_online_1_amount           double precision,
    core_course_online_2_levels           varchar(13),
    core_course_online_3_start_date       timestamp encode az64,
    core_course_online_4_end_date         timestamp encode az64,
    core_course_online_5_start_level      varchar(36),
    core_course_online_6_end_level        varchar(36),
    core_course_online_7_type             varchar(12),
    hs_merged_object_ids                  varchar(248),
    ilc_1_amount                          double precision,
    ilc_2_hours                           varchar(15),
    ilc_3_start_date                      timestamp encode az64,
    ilc_4_end_date                        timestamp encode az64,
    ilc_6_tags                            varchar(13),
    ilc_language                          varchar(66),
    market_leader_7_tags                  varchar(32),
    subsource                             varchar(86),
    test_prep_executive_1_amount          double precision,
    test_prep_executive_2_quantity        varchar(13),
    test_prep_executive_4_start_date      timestamp encode az64,
    test_prep_executive_5_end_date        timestamp encode az64,
    test_prep_executive_6_tags            varchar(13),
    test_prep_executive_types             varchar(36),
    test_prep_group_0_group_name          varchar(16),
    test_prep_group_2_tags                varchar(13),
    voucher_welfare                       varchar(48),
    id                                    bigint encode az64,
    createdat                             timestamp encode az64,
    updatedat                             timestamp encode az64,
    territory_code                        varchar(12),
    archived                              varchar(15),
    cycleid                               bigint encode az64,
    timezonecreatedate                    timestamp encode az64,
    timezoneclosedate                     timestamp encode az64,
    territory_name                        varchar(48),
    deleteflag                            varchar(2),
    archiveddate                          timestamp encode az64,
    restoreddate                          timestamp encode az64
);



