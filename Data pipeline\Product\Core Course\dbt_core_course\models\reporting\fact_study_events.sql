{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

SELECT
    student_reference_id AS user_id
    ,event_date
    ,CASE WHEN event_type LIKE 'booked class%' THEN 'booked class' ELSE event_type END AS event_type
    ,event_description
    ,CASE WHEN event_type = 'self study' THEN event_detail3
    WHEN event_type = 'attended class' THEN event_detail2
    ELSE NULL END AS event_detail
    ,CASE WHEN event_type LIKE '%staff' THEN 'staff' ELSE 'student' END AS event_user
    ,count(student_reference_id) AS event_count
    ,sum(event_duration) AS event_duration
    ,sum(try_cast(event_detail3 as integer)) filter (where event_type = 'booked class - student') AS event_detail_sum
FROM {{ref('study_events')}}
WHERE event_date >= {{start_of_last_year()}}
GROUP by
    student_reference_id
    ,event_date
    ,CASE WHEN event_type LIKE 'booked class%' THEN 'booked class' ELSE event_type END
    ,event_description
    ,CASE WHEN event_type LIKE '%staff' THEN 'staff' ELSE 'student' END
    ,CASE WHEN event_type = 'self study' THEN event_detail3
    WHEN event_type = 'attended class' THEN event_detail2
    ELSE NULL END