{{ config(
    MATERIALIZED = 'table',
    table_type = 'iceberg',
    FORMAT = 'parquet'
) }}

	SELECT		
        C.id AS contract_id,
        CE.center_reference_id,
        T.Name AS territory_name,
        CE.Name AS center_name,
        UR.student_code,
        C.Number AS contract_number,
        UR.last_name,
        UR.first_name,		
        date_format({{convert_to_local_timestamp('c.created_date','tz.time_zone_id')}}, '%b %d %Y') AS created_date,
        case when C.contract_type = 2 then C.created_date else C.sale_date end as sales_date,
        case when STDTC.previous_value is null then C.start_date else DATE(STDTC.previous_value) end as Orig_start_date,
        case when ENDTC.previous_value is null then C.end_date else DATE(ENDTC.previous_value) end as Orig_end_date,
        C.start_date,
        C.end_date,
        case when STLVLC.previous_value is null then REPLACE (SL.name, 'level ', '') else STLVLC.previous_value end as orig_start_level,
        case when ENLVLC.previous_value is null then REPLACE (EL.name, 'level ', '') else ENLVLC.previous_value end as orig_end_level,
        REPLACE (SL.name, 'Level ', '') start_level,
        REPLACE (EL.name, 'Level ', '') end_level,	
        C.status AS contract_status,
        C.contract_type,
        C.location,
        case 
            when C.is_teen = false and C.service_type = 1 then 'Standard'
            when C.is_teen = false and C.service_type = 2 then 'VIP'
            when C.is_teen = true and C.service_type = 1 then 'Teens Standard'
            when C.is_teen = true and C.service_type = 2 then 'Teens VIP' 
        end as service,
        C.product_type_id AS course_type,
        C.is_promotional,
        C.is_transfer_in,
        case 
            when CLAT.class_access_type = 'full access' and contract_products.product like '%11%' then 'Full Access +'
            when CLAT.class_access_type = 'full access' then 'Full Access'
            when CLAT.class_access_type = 'in-center class access' then 'In-Center'
            when CLAT.class_access_type = 'online class access' and contract_products.product like '%11%' then 'Online +'
            when CLAT.class_access_type = 'online class access' then 'Online'
            when CLAT.class_access_type = 'no_class_access_type' then 'No Access'
            else 'No Access' 
        end as access_type,
        PRC.previous_value AS audit_price,
        C.Price AS cont_price,
        CASE WHEN PRC.previous_value IS NULL THEN C.price ELSE CAST(PRC.previous_value as decimal(19,5)) END AS org_price,
        is_membership,
        is_renewed,
        --C.cancel_date,
        CC.created_date as cancel_date,
        C.refunded,
        CC.reason AS cancellation_reason,
        student_id,
        status,
        LAG(status,1) OVER(
            PARTITION BY student_id
            ORDER BY    student_id, start_date) AS lag_prev_contr,
        CASE WHEN LAG(status,1) OVER(
            PARTITION BY student_id
            ORDER BY    student_id, start_date) <> 2 AND status = 1
            THEN 'Future_not_valid_now'
            ELSE 'Other'
            END AS future_contract
FROM			
    {{ref('ods_cs_contracts')}} C 
INNER JOIN
    {{ref('ods_cs_centers')}} CE
    ON	C.center_id = CE.id
INNER JOIN
    {{ref('ods_cs_territory')}} T
    ON	CE.territory_id = T.id
LEFT JOIN
    {{ref('ods_ls_center')}} LSC
    ON	CE.center_reference_id = LSC.reference_center_id
LEFT JOIN  
    {{ref('ods_cc_center')}} tz 
    ON  tz.center_reference_id = CE.center_reference_id
INNER JOIN
    {{ref('ods_cs_users')}} UR
    ON	C.student_id = UR.id
LEFT JOIN
    {{ref('ods_cs_users')}} Const_Usr
    ON	C.consultant_id = Const_Usr.id
LEFT JOIN
    {{ref('ods_cs_product_levels')}} SL
    ON	C.start_level_id = SL.id
LEFT JOIN
    {{ref('ods_cs_product_levels')}} EL
    ON	C.end_level_id = EL.id
LEFT JOIN
    {{ref('ods_idam_user_basic_info')}} UBI
    ON	UR.user_reference_id = UBI.ssds_id	

LEFT JOIN
        (select contract_id,class_access_type from (
        select 
            contract_id, 
            class_access_type, 
            row_number() over(partition by contract_id order by valid_from) as rn 
        from (select contract_id, 
            class_access_type, valid_from  from analytics.contracts_changes 
        where 
             class_access_type != 'no_class_access_type')
        ) where rn = 1 )as CLAT
ON      C.id = CLAT.contract_id
LEFT JOIN
        (select contract_id,
        LISTAGG( cast(product_id as varchar) , ', ') WITHIN GROUP (ORDER BY product_id) as product
        from ods_contract_service.ods_cs_contract_products
        group by contract_id
        ) as contract_products
    ON C.id = contract_products.contract_id
LEFT JOIN	
        (
            SELECT	contract_id
                    ,modified_field_id
                    ,previous_value
                    ,change_type
                    ,created_date
                    ,ROW_NUMBER() OVER(PARTITION BY contract_id, modified_field_id ORDER BY created_date) AS RN				
            FROM	{{ref('ods_cs_contracts_audit_info')}}
            WHERE	modified_field_id = '4c38bdb1-e878-4661-86ae-53d9b347b565' --Price
        )	PRC
    ON  PRC.contract_id = C.id AND PRC.RN = 1
LEFT JOIN	
        (
            SELECT	contract_id
                    ,modified_field_id
                    ,REPLACE (L.name, 'level ', 'L') as previous_value
                    ,change_type
                    ,created_date
                    ,ROW_NUMBER() OVER(PARTITION BY contract_id, modified_field_id ORDER BY created_date) AS RN				
            FROM	{{ref('ods_cs_contracts_audit_info')}} C
            LEFT JOIN		{{ref('ods_cs_product_levels')}} L
            ON				C.previous_value = L.id

            WHERE	modified_field_id = 'f70d9ba0-290b-47a7-8e11-20f73a2c8824' --Start Level
        )	STLVLC
    ON	STLVLC.contract_id = C.id
    AND	STLVLC.RN = 1
LEFT JOIN	
        (
            SELECT	contract_id
                    ,modified_field_id
                    ,REPLACE (L.name, 'level ', 'L') as previous_value
                    ,change_type
                    ,created_date
                    ,ROW_NUMBER() OVER(PARTITION BY contract_id, modified_field_id ORDER BY created_date) AS RN				
            FROM	{{ref('ods_cs_contracts_audit_info')}} C
            LEFT JOIN		{{ref('ods_cs_product_levels')}} L
            ON				C.previous_value = L.id
            WHERE	modified_field_id = 'fa063854-be56-4e10-9444-abe4999cbfd2' --End Level
        )	ENLVLC
    ON	ENLVLC.contract_id = C.id
    AND	ENLVLC.RN = 1
LEFT JOIN	
        (
            SELECT	contract_id
                    ,modified_field_id
                    ,previous_value
                    ,change_type
                    ,created_date
                    ,ROW_NUMBER() OVER(PARTITION BY contract_id, modified_field_id ORDER BY created_date) AS RN				
            FROM	{{ref('ods_cs_contracts_audit_info')}}
            WHERE	modified_field_id = 'df7eb5dc-84db-4250-9157-c4843ec3df82' -- Start Date
        )	STDTC
    ON	STDTC.contract_id = C.id
    AND	STDTC.RN = 1
LEFT JOIN	
        (
            SELECT	contract_id
                    ,modified_field_id
                    ,previous_value
                    ,change_type
                    ,created_date
                    ,ROW_NUMBER() OVER(PARTITION BY contract_id, modified_field_id ORDER BY created_date) AS RN				
            FROM	{{ref('ods_cs_contracts_audit_info')}}
            WHERE	modified_field_id = 'a3efcb4b-e149-42c8-938f-e27935028669' --End Date
        )	ENDTC
    ON	ENDTC.contract_id = C.id
    AND	ENDTC.RN = 1
LEFT JOIN		{{ref('ods_cs_contracts_audit_info')}} CC
    ON	C.id = CC.contract_id
    AND	CC.change_type = 2 -- Cancellation change type
    AND	CC.modified_field_id = '4dda9d2b-f7fa-4cd5-8f38-d76985a63029'	-- Cancellation State change
WHERE 
	CASE 
		WHEN STDTC.previous_value IS NULL THEN C.start_date 
		ELSE DATE(STDTC.previous_value)
	END >= TIMESTAMP '2019-01-01 00:00:00.000'