{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        length,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        ancestorid,
        descendentid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'categoryhierarchy'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    length,
    created created,
    lastupdated as last_updated,
    id,
    ancestorid as ancestor_id,
    descendentid as descendent_id
FROM
    rankedrecords
WHERE
    rn = 1;
