{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

select 
 companies.id as company_id
,companies.name as company_name
,companies.code as company_code
,companies.center_owned
,companies.license_number
,created_user.role as company_created_by
,modified_user.role as company_modified_by
,companies.created_date as company_created_date
,companies.local_created_date as company_local_created_date
,companies.last_updated_date as company_last_updated_date
,companies.local_last_updated_date as company_local_last_updated_date
,master_contracts.id as master_contracts_id 
,master_contracts.master_contract_number
,master_contracts.start_date as master_contracts_start_date
,master_contracts.end_date as master_contracts_end_date
,master_contracts.sale_date as master_contracts_sale_date
,master_contracts.state as master_contracts_state
,master_contracts.status as master_contracts_status
,master_contracts.cancel_date as master_contracts_cancel_date
,master_contracts.reason as master_contracts_resaon
,mc_modified_user.role as master_contracts_modified_by
,master_contracts.created_date as master_contracts_created_date
,master_contracts.local_created_date as master_contracts_local_created_date
,master_contracts.last_updated_date as master_contracts_last_updated_date
,master_contracts.local_last_updated_date as master_contracts_local_last_updated_date
,master_contract_course.id as master_contract_course_id
,master_contract_course.product_type as mcc_product_type
,master_contract_course.study_type as mcc_study_type
,master_contract_course.b2_b_contract_type as mcc_b2_b_contract_type 
,master_contract_course.price as mcc_price
,master_contract_course.refund as mcc_refund
,master_contract_course.start_date as mcc_start_date
,master_contract_course.end_date as mcc_end_date
,master_contract_course.state as mcc_state
,master_contract_course.status as mcc_status
,master_contract_course.cancel_date as mcc_cancel_date
,master_contract_course.is_active as mcc_is_active
,master_contract_course.created_date as mcc_created_date
,master_contract_course.local_created_date as mcc_local_created_date
,master_contract_course.last_updated_date as mcc_last_updated_date
,master_contract_course.local_last_updated_date as mcc_local_last_updated_date
,master_contract_course.is_membership as mcc_is_membership
,master_contract_course.is_non_membership as mcc_is_non_membership
from  
	{{ref('dt_cs_new_version_companies')}} as companies
left join {{ref('dt_cs_master_contracts')}} as master_contracts
	on companies.id = master_contracts.company_id
left join {{ref('dt_cs_master_contract_courses')}} as master_contract_course
	on master_contracts.id = master_contract_course.master_contract_id
left join {{ref('dt_ls_user')}} as mc_modified_user
	on master_contracts.modified_by_id = mc_modified_user.user_id
left join {{ref('dt_ls_user')}} as created_user
	on companies.created_by_id = created_user.user_id
left join {{ref('dt_ls_user')}} as modified_user
	on companies.modified_by_id = modified_user.user_id
where master_contracts.id is not null