{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('registeredon') }} as registeredon,
        showtestresult, 
        hasacceptedprivacypolicy, 
        settledlevel, 
        istimeout, 
        {{ cast_to_timestamp('testcompletedon') }} as testcompletedon,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        placementtestentrypoint,
        id,
        firstname,
        lastname,
        email,
        phonenumber,
        centerreferenceid,
        companyid,
        source,
        studentreferenceid,
        createdby,
        lastupdatedby,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_prospect_service',
            'prospect'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    registeredon as registered_on,
    showtestresult as show_test_result, 
    hasacceptedprivacypolicy as has_accepted_privacy_policy,
    settledlevel as settled_level,
    istimeout as is_timeout, 
    testcompletedon as test_completed_on,
    created,
    lastupdated as last_updated,
    placementtestentrypoint as placement_test_entry_point,
    id,
    firstname as first_name,
    lastname as last_name,
    email,
    phonenumber as phone_number,
    centerreferenceid as center_reference_id,
    companyid as company_id,
    source,
    studentreferenceid as student_reference_id,
    createdby as created_by,
    lastupdatedby as last_updated_by
FROM
    rankedrecords
WHERE
    rn = 1;
