version: 2

sources:

  - name: stage_idam_service
    description: >
      Source data from the Identity and Access Management (IDAM) Service which manages user accounts,
      roles, permissions, and related center and territory information.
    database: awsdatacatalog
    schema: stg_idam_service
    tables:
      - name: center
        description: Centers registered in the IDAM system with their reference IDs and territory associations.
        columns:
          - name: id
            description: Primary key for the center record
          - name: centerreferenceid
            description: External reference ID for the center
          - name: name
            description: Name of the center
          - name: territoryid
            description: Foreign key reference to the territory
          - name: centercode
            description: Code identifier for the center
          - name: studentcode
            description: Code prefix used for student IDs at this center

      - name: role
        description: User roles that define permissions and access levels in the system.
        columns:
          - name: id
            description: Primary key for the role record
          - name: description
            description: Description or name of the role
          - name: created
            description: Timestamp when the role was created
          - name: lastupdated
            description: Timestamp when the role was last updated

      - name: territory
        description: Territories or regions where centers operate, with their reference IDs.
        columns:
          - name: id
            description: Primary key for the territory record
          - name: territoryreferenceid
            description: External reference ID for the territory
          - name: name
            description: Name of the territory
          - name: code
            description: Code identifier for the territory
          - name: isocode
            description: ISO country code for the territory
          - name: isactive
            description: Boolean flag indicating if the territory is active

      - name: useradditionalinfo
        description: Additional information and preferences for users in the system.
        columns:
          - name: id
            description: Primary key for the user additional info record
          - name: isemailverified
            description: Boolean flag indicating if the user's email is verified
          - name: created
            description: Timestamp when the record was created
          - name: lastupdated
            description: Timestamp when the record was last updated
          - name: isactive
            description: Boolean flag indicating if the record is active
          - name: sendmailpreference
            description: User's preference for receiving emails
          - name: preferredcontactmethod
            description: User's preferred method of contact
          - name: userbasicinfoid
            description: Foreign key reference to the user basic info
          - name: socialnetworkid1
            description: ID of the first social network
          - name: socialnetworkaddress1
            description: Address/handle for the first social network
          - name: socialnetworkid2
            description: ID of the second social network
          - name: socialnetworkaddress2
            description: Address/handle for the second social network
          - name: socialnetworkid3
            description: ID of the third social network
          - name: socialnetworkaddress3
            description: Address/handle for the third social network
          - name: socialnetworkid4
            description: ID of the fourth social network
          - name: socialnetworkaddress4
            description: Address/handle for the fourth social network
          - name: photoname
            description: Name of the user's photo file
          - name: mobiletelephone
            description: Mobile telephone number
          - name: hometelephone
            description: Home telephone number
          - name: worktelephone
            description: Work telephone number
          - name: fax
            description: Fax number
          - name: address1
            description: First line of address
          - name: address2
            description: Second line of address
          - name: city
            description: City
          - name: state
            description: State or province
          - name: postalcode
            description: Postal or ZIP code
          - name: callbetweenfrom
            description: Start time for preferred call hours
          - name: callbetweento
            description: End time for preferred call hours
          - name: personalprofessionid
            description: ID of the user's profession
          - name: personalmotivationid
            description: ID of the user's motivation for learning
          - name: personalnationalityid
            description: ID of the user's nationality
          - name: personalnativelanguageid
            description: ID of the user's native language
          - name: personalsecondarylanguageid
            description: ID of the user's secondary language
          - name: interesthobbies
            description: User's interests and hobbies
          - name: aboutme
            description: User's self-description
          - name: studyreason
            description: User's reason for studying
          - name: whystudyenglish
            description: User's reason for studying English
          - name: workmotivation
            description: User's work-related motivation

      - name: userbasicinfo
        description: Core user information including authentication details and basic profile data.
        columns:
          - name: userid
            description: Primary key for the user record
          - name: gender
            description: User's gender
          - name: birthdate
            description: User's date of birth
          - name: created
            description: Timestamp when the user record was created
          - name: lastupdated
            description: Timestamp when the user record was last updated
          - name: isactive
            description: Boolean flag indicating if the user is active
          - name: enablepasswordreset
            description: Boolean flag indicating if password reset is enabled
          - name: isprospect
            description: Boolean flag indicating if the user is a prospect
          - name: username
            description: Username for system login
          - name: password
            description: Hashed password for authentication
          - name: firstname
            description: User's first name
          - name: lastname
            description: User's last name
          - name: email
            description: User's email address
          - name: ssdsid
            description: SSDS identifier for the user
          - name: centerid
            description: Foreign key reference to the user's center
          - name: territoryid
            description: Foreign key reference to the user's territory
          - name: userexternalid
            description: External identifier for the user

      - name: userroles
        description: Mapping table that associates users with their assigned roles.
        columns:
          - name: id
            description: Primary key for the user role mapping
          - name: userbasicinfoid
            description: Foreign key reference to the user basic info
          - name: roleid
            description: Foreign key reference to the role
          - name: created
            description: Timestamp when the role assignment was created
          - name: lastupdated
            description: Timestamp when the role assignment was last updated
          - name: isactive
            description: Boolean flag indicating if the role assignment is active