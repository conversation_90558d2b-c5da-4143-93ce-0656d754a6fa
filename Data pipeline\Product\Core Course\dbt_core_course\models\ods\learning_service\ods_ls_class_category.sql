{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'dbt_unique_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        (
            classid || categoryid
        ) as dbt_unique_id,
        classid,
        categoryid,
        ROW_NUMBER() over (
            PARTITION BY classid,
            categoryid
            ORDER BY
                classid,
                categoryid
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'classcategory'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    dbt_unique_id,
    classid as class_id,
    categoryid as category_id
FROM
    rankedrecords
WHERE
    rn = 1;
