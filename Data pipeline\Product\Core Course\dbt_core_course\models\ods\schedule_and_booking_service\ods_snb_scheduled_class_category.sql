{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH RankedRecords AS (
    SELECT 
        {{cast_to_timestamp("created")}} as created,
        {{cast_to_timestamp("lastupdated")}} as lastupdated,
        id,
        scheduledclassid,
        categoryid,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdated DESC) AS rn
    FROM 
        {{source('stage_schedule_and_booking_service', 'scheduledclasscategory')}}
)

SELECT
    {{etl_load_date()}},
    created as created,
    lastupdated as last_updated,
    id,
    scheduledclassid as scheduled_class_id,
    categoryid as category_id
FROM
    RankedRecords
WHERE
    rn = 1