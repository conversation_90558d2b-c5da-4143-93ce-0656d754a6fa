import logging
import boto3
import time


def run_glue_job(glue_job_name, glue_args=None):
    """
    Run a Glue job and wait for its completion.

    Parameters:
        - glue_job_name: The name of the Glue job.
        - glue_args: (optional) A dictionary of arguments to be passed to the Glue job.

    Returns:
        None
    """
    glue_client = boto3.client('glue')

    try:
        # Start the Glue job
        response = glue_client.start_job_run(JobName=glue_job_name, Arguments=glue_args or {})
        job_run_id = response['JobRunId']
        logging.warning(f"Glue job run ID: {job_run_id}")

        # Wait for the job to complete
        while True:
            response = glue_client.get_job_run(JobName=glue_job_name, RunId=job_run_id)
            status = response['JobRun']['JobRunState']

            if status in ('SUCCEEDED', 'FAILED', 'STOPPED', 'TIMEOUT'):
                break

            time.sleep(10)  # Wait for 10 seconds before checking the status again

        # Check the final status
        if status != 'SUCCEEDED':
            raise Exception(f"Glue job {glue_job_name} failed with status: {status}")

        logging.warning(f"Glue job {glue_job_name} completed successfully.")

    except Exception as e:
        logging.warning(f"An error occurred: {e}")
        raise e
