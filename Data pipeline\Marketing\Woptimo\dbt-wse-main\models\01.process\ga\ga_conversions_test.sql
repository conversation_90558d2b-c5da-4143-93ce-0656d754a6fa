{{
    config(
        tags=["incremental","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}

select 
event_date,
landing_page as page,
min(lang) as lang,
SUM(pageviews) AS pageviews,
SUM(case when session_channel LIKE "%organic" THEN pageviews ELSE 0 END) AS pageviews_organic,
SUM(case when session_channel LIKE "google_organic" THEN pageviews ELSE 0 END) AS pageviews_ggorganic,
COUNT (DISTINCT session_id) sessions,
COUNT (DISTINCT (case when session_channel LIKE "%organic" THEN session_id ELSE null END)) sessions_organic,
COUNT (DISTINCT (case when session_channel LIKE "google_organic" THEN session_id ELSE null END)) sessions_ggorganic,

{% for conv in var('conversion_events') %}

sum(google_organic_{{conv}}_on_lp) conv_onlp_ggorganic_{{conv}},
sum(organic_session_{{conv}}) conv_session_organic_{{conv}},
sum(session_{{conv}}) conv_session_allsrc_{{conv}},
sum({{conv}}_on_lp) conv_onlp_allsrc_{{conv}},

{% endfor %}
from {{ ref('ga_conversions_sessions_test') }}
where landing_page is not null
and {{increment()}}
group by event_date, page