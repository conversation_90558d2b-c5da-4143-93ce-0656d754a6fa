{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        changetype as change_type
        ,startdate as start_date
        ,enddate as end_date
        ,createddate as created_date
        ,lastupdateddate as last_updated_date
        ,id 
        ,contractid as contract_id
        ,reason as reason
        ,startlevelid as start_level_id
        ,endlevelid as end_level_id
        ,modifiedbyid as modified_by_id
        ,ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'contractchanges')}}
)

Select
    change_type
    ,{{cast_to_timestamp('start_date')}} as start_date
    ,{{cast_to_timestamp('end_date')}} as end_date
    ,{{cast_to_timestamp('created_date')}} as created_date
    ,{{cast_to_timestamp('last_updated_date')}} as last_updated_date
    ,id 
    ,contract_id
    ,reason
    ,start_level_id
    ,end_level_id
    ,modified_by_id
FROM 
    RankedRecords
Where
    rn =1;