{{ config(
    MATERIALIZED = 'table',
    table_type = 'iceberg',
    FORMAT = 'parquet'
) }}

SELECT
    a.created_date,
    a.contract_id,
    a.crm_contract_number,
    a.last_name,
    a.first_name,
    a.student_code,
    a.center_reference_id,
    a.territory_name,
    a.formatted_mod_date,
    a.consultant,
    a.editor,
    a.mod_reason,
    a.course_type,
    a.access_type,
    a.service_type,
    a.is_membership,
    a.location,
    0 AS is_promotional,
    0 AS is_transferin,
    'private' AS private_master,
    MIN(a.org_start_date) AS org_start_date,
    MIN(a.org_end_date) AS org_end_date,
    MIN(a.mod_start_date) AS mod_start_date,
    MIN(a.mod_end_date) AS mod_end_date,
    MIN(CASE WHEN pl_os."order" = pl_ms."order" THEN NULL ELSE pl_ms."order" END) AS modified_start_level,
    MIN(CASE WHEN pl_os."order" = pl_ms."order" THEN NULL ELSE pl_os."order" END) AS original_start_level,
    MIN(CASE WHEN pl_oe."order" = pl_me."order" THEN NULL ELSE pl_me."order" END) AS modified_end_level,
    MIN(CASE WHEN pl_oe."order" = pl_me."order" THEN NULL ELSE pl_oe."order" END) AS original_end_level,
    MIN(a.modified_price) AS modified_price,
    MIN(a.original_price) AS original_price,
    SUM(
        COALESCE(
            date_diff('day', cast(a.mod_start_date as date),  cast(a.org_start_date as date)),
            0
        ) -
        COALESCE(
            date_diff('day', cast(a.mod_end_date as date),  cast(a.org_end_date as date)),
            0
        )
    ) AS diff_no_of_days,
    SUM(CASE WHEN pl_me."order" = pl_oe."order" OR pl_oe."order" IS NULL THEN 0 ELSE pl_me."order" - pl_oe."order" END) +
    SUM(CASE WHEN pl_ms."order" = pl_os."order" OR pl_os."order" IS NULL THEN 0 ELSE pl_os."order" - pl_ms."order" END) AS modified_no_of_levels,
    SUM(COALESCE(a.modified_price - a.original_price, 0)) AS price_difference
FROM (
	SELECT
        cc.created_date,
        c.center_reference_id,
        ls_tr.name AS territory_name,
        ct.id AS contract_id,
        ct.crm_contract_number,
        cc.reason AS mod_reason,
        COALESCE(cc.effective_date,{{convert_to_local_timestamp('cc.created_date','tz.time_zone_id')}}) AS formatted_mod_date,
        u.last_name,
        u.first_name,
        u.student_code,
        ct.is_teen,
        pt.id AS course_type,
        ct.is_membership,
        ct.location,
        CASE
            WHEN ct.service_type = 1 AND ct.is_teen = false THEN 'standard'
            WHEN ct.service_type = 2 AND ct.is_teen = false THEN 'vip'
            WHEN ct.service_type = 1 AND ct.is_teen = true THEN 'teens standard'
            WHEN ct.service_type = 2 AND ct.is_teen = true THEN 'teens vip'
        END AS service_type,
        case 
            WHEN cain.contract_id IS NOT NULL AND caon.contract_id IS NOT NULL AND contract_products.product LIKE '%11%' THEN 'Full Access +'
            WHEN cain.contract_id IS NOT NULL AND caon.contract_id IS NOT NULL THEN 'Full Access'
            WHEN cain.contract_id IS NOT NULL THEN 'In-Center'
            WHEN caon.contract_id IS NOT NULL AND contract_products.product LIKE '%11%' THEN 'Online +'
            WHEN caon.contract_id IS NOT NULL THEN 'Online'
            ELSE 'No Access'
        END AS access_type,
        CAST(CASE WHEN cc.modified_field_id = '4c38bdb1-e878-4661-86ae-53d9b347b565' THEN cc.present_value ELSE NULL END AS decimal(38, 2)) AS modified_price,
        CAST(CASE WHEN cc.modified_field_id = '4c38bdb1-e878-4661-86ae-53d9b347b565' THEN cc.previous_value ELSE NULL END AS decimal(38, 2)) AS original_price,
        CASE WHEN cc.modified_field_id = 'df7eb5dc-84db-4250-9157-c4843ec3df82' THEN cc.previous_value ELSE NULL END AS org_start_date,
        CASE WHEN cc.modified_field_id = 'df7eb5dc-84db-4250-9157-c4843ec3df82' THEN cc.present_value ELSE NULL END AS mod_start_date,
        CASE WHEN cc.modified_field_id = 'a3efcb4b-e149-42c8-938f-e27935028669' THEN cc.previous_value ELSE NULL END AS org_end_date,
        CASE WHEN cc.modified_field_id = 'a3efcb4b-e149-42c8-938f-e27935028669' THEN cc.present_value ELSE NULL END AS mod_end_date,
        CASE WHEN cc.modified_field_id = 'f70d9ba0-290b-47a7-8e11-20f73a2c8824' THEN cc.present_value ELSE NULL END AS modified_start_level_id,
        CASE WHEN cc.modified_field_id = 'f70d9ba0-290b-47a7-8e11-20f73a2c8824' THEN cc.previous_value ELSE NULL END AS original_start_level_id,
        CASE WHEN cc.modified_field_id = 'fa063854-be56-4e10-9444-abe4999cbfd2' THEN cc.present_value ELSE NULL END AS modified_end_level_id,
        CASE WHEN cc.modified_field_id = 'fa063854-be56-4e10-9444-abe4999cbfd2' THEN cc.previous_value ELSE NULL END AS original_end_level_id,
        CONCAT(u1.last_name, ' ', u1.first_name) AS consultant,
        CONCAT(u2.last_name, ' ', u2.first_name) AS editor
    FROM {{ref("ods_cs_centers")}} c
    INNER JOIN (SELECT * FROM {{ref("ods_cs_contracts")}} WHERE state <> 13) ct ON c.id = ct.center_id
    INNER JOIN {{ref("ods_ls_center")}} ls_cntr ON ls_cntr.reference_center_id = c.center_reference_id
    INNER JOIN {{ref("ods_ls_territory")}} ls_tr ON ls_cntr.territory_id = ls_tr.id
    INNER JOIN {{ref("ods_cc_center")}} tz ON tz.center_reference_id = c.center_reference_id
    INNER JOIN {{ref("ods_cs_contracts_audit_info")}} cc ON ct.id = cc.contract_id AND change_type IN (8, 13)
    LEFT JOIN {{ref("ods_cs_product_types")}} pt ON pt.id = ct.product_type_id
    LEFT JOIN (SELECT DISTINCT contract_id FROM {{ref("ods_cs_contract_class_access_type")}} WHERE class_access_type_id = '514e7cea-9ea7-4096-a050-c729466a6219' AND is_active = true) cain ON ct.id = cain.contract_id
    LEFT JOIN (SELECT DISTINCT contract_id FROM {{ref("ods_cs_contract_class_access_type")}} WHERE class_access_type_id = 'e802ae87-4e17-4d43-b88f-ea7f136cdf69' AND is_active = true) caon ON ct.id = caon.contract_id
    LEFT JOIN (select contract_id,LISTAGG( cast(product_id as varchar) , ', ') WITHIN GROUP (ORDER BY product_id) as product from ods_contract_service.ods_cs_contract_products group by contract_id) as contract_products
    ON ct.id = contract_products.contract_id
    LEFT JOIN (
        SELECT *,
			row_number() OVER (PARTITION BY contract_id ORDER BY created_date DESC) AS rn
        FROM {{ref("ods_cs_contract_validations")}}
        WHERE contract_id IS NOT NULL AND state = 2
    ) cv ON cv.contract_id = ct.id AND cv.rn = 1
    INNER JOIN {{ref("ods_cs_users")}} u ON ct.student_id = u.id AND u.user_type = 4
    INNER JOIN {{ref("ods_cs_users")}} u1 ON ct.consultant_id = u1.id AND u1.user_type = 3
    INNER JOIN {{ref("ods_cs_users")}} u2 ON cc.modified_by_id = u2.id
    LEFT JOIN {{ref("ods_cs_product_levels")}} pl_st ON ct.start_level_id = pl_st.id
    LEFT JOIN {{ref("ods_cs_product_levels")}} pl_ed ON ct.end_level_id = pl_ed.id
    WHERE ct.is_promotional = FALSE
	AND ct.contract_type = 1
	--AND cv.state IS NOT NULL
	AND cc.change_type = 8
	AND cc.modified_field_id IN (
		'f70d9ba0-290b-47a7-8e11-20f73a2c8824',
		'fa063854-be56-4e10-9444-abe4999cbfd2',
		'df7eb5dc-84db-4250-9157-c4843ec3df82',
		'a3efcb4b-e149-42c8-938f-e27935028669',
		'4c38bdb1-e878-4661-86ae-53d9b347b565'
	)
	AND cc.present_value <> '00000000-0000-0000-0000-000000000000'
	AND cc.modified_by_id IS NOT NULL
	AND cc.created_date >= timestamp '2019-01-01 00:00:00.000'
) a
LEFT JOIN {{ref("ods_cs_product_levels")}} pl_ms ON a.modified_start_level_id = pl_ms.id
LEFT JOIN {{ref("ods_cs_product_levels")}} pl_os ON a.original_start_level_id = pl_os.id
LEFT JOIN {{ref("ods_cs_product_levels")}} pl_me ON a.modified_end_level_id = pl_me.id
LEFT JOIN {{ref("ods_cs_product_levels")}} pl_oe ON a.original_end_level_id = pl_oe.id
GROUP BY
    a.created_date,
    a.contract_id,
    a.crm_contract_number,
    a.last_name,
    a.first_name,
    a.student_code,
    a.center_reference_id,
    a.territory_name,
    a.formatted_mod_date,
    a.consultant,
    a.editor,
    a.mod_reason,
    a.course_type,
    a.access_type,
    a.service_type,
    a.is_membership,
    a.location