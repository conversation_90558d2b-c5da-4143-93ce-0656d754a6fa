import sys
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark.context import Spark<PERSON>ontext
from awsglue.context import GlueContext
from awsglue.job import Job

args = getResolvedOptions(sys.argv, ["JOB_NAME"])
sc = SparkContext()
glueContext = GlueContext(sc)
spark = glueContext.spark_session
job = Job(glueContext)
job.init(args["JOB_NAME"], args)

# Script generated for node Google BigQuery Connector 0.24.2 for AWS Glue 3.0
GoogleBigQueryConnector0242forAWSGlue30_node1 = (
    glueContext.create_dynamic_frame.from_options(
        connection_type="marketplace.spark",
        connection_options={
            "table": "dbt_prod_datamart.mart_seo_page_test",
            "parentProject": "wse-marketing",
            "connectionName": "bigquery",
        },
        transformation_ctx="GoogleBigQueryConnector0242forAWSGlue30_node1",
    )
)

# Script generated for node BigQuery_S3
BigQuery_S3_node3 = glueContext.write_dynamic_frame.from_options(
    frame=GoogleBigQueryConnector0242forAWSGlue30_node1,
    connection_type="s3",
    format="json",
    connection_options={
        "path": "s3://wse-seo-reporting/json_response/data-mart-page-test/",
        "partitionKeys": [],
    },
    transformation_ctx="BigQuery_S3_node3",
)

job.commit()