import sys
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark import SparkConf, SparkContext
from awsglue.context import GlueContext
from pyspark.sql import SparkSession
from awsglue.job import Job
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, TimestampType
import ast
import timeit
import pandas as pd
import logging
import NullDataFix
import LoggingStatements
import DataframeModule

# Get configuration information from command-line arguments
ConfigInfo = getResolvedOptions(sys.argv, [
    'Stage', 'Territory', 'Object', 'Status', 'Properties', 'Operation', 'Filter', 'Url',
    'CutoffDate', 'DefaultProperties', 'CycleId', 'Bucket'
])
Properties = ast.literal_eval(ConfigInfo['Properties'])
DefaultProperties = ast.literal_eval(ConfigInfo['DefaultProperties'])

logging.warning("Configinfo:'%s'", format(ConfigInfo))
logging.warning("properties:'%s'", format(Properties))
logging.warning("DefaultProperties:'%s'", format(DefaultProperties))

# Import required modules and classes
import OwnersDataExtract
import LogFileGeneration
import ObjectDataFetch
import PostApiCall
import DataFetchFormatting

# Create Spark configuration and context
conf = SparkConf()
conf.setMaster("local").setAppName("My app")
sc = SparkContext.getOrCreate(conf=conf)
spark = SparkSession(sc)
starttime = timeit.default_timer()

# Initialize connections and instances
OwnersConnect = OwnersDataExtract.OwnersDataFetch
ObjectConnect = ObjectDataFetch.ObjectFetch
DataFormatConnect = DataFetchFormatting.DataFormating
APICall = PostApiCall
Logs = LogFileGeneration.LogFile
NullDatafixConnect = NullDataFix.NullDataFill
Bucket = ConfigInfo['Bucket']
LogsStatementConnect = LoggingStatements.LogStatements
DataframeConnect = DataframeModule.DataFrameCreate

TerritoryCode = ConfigInfo['Territory']
Object = ConfigInfo['Object']
CycleId = ConfigInfo['CycleId']

logging.warning("Cycleid : '%s'", format(CycleId))
logging.warning("The territory_code is :'%s'", format(TerritoryCode))

# Check the object type and perform actions accordingly
if Object in ['companies']:

    try:
        # Fetch data for the object type
        DataFetchObject = ObjectConnect.object_incremental(ConfigInfo, Bucket, Properties,
                                                           DefaultProperties, CycleId, Object)
        logging.warning(DataFetchObject)
        ApiCalls = APICall.ApiCount()
        logstatements = LogsStatementConnect.logs_statements(ApiCalls, DataFetchObject)
        if len(DataFetchObject) == 0:
            # Create a log file if no records are processed
            LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'], CutOffDate=ConfigInfo['CutOffDate'],
                                           Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                           RecordsProcessed=len(DataFetchObject), NoOfApiCall=ApiCalls,
                                           CycleId=CycleId, Object=Object, Bucket=Bucket)

        else:
            # Generate the cutoff date and perform further actions
            CutOffDate = DataFormatConnect.CutoffDateGenerator(DataFetchObject, FilterProperty=ConfigInfo['Filter'])
            # Creating a pandas dataframe on the object
            spark_df = DataframeConnect.dataframe_module(DataFetchObject, NullDatafixConnect, spark, Bucket, CycleId, ConfigInfo, Object, TerritoryCode )
            LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'], CutOffDate=CutOffDate,
                                           Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                           RecordsProcessed=len(DataFetchObject), NoOfApiCall=ApiCalls,
                                           CycleId=CycleId, Object=Object, Bucket=Bucket)
    except Exception as error:
        raise error
if Object == 'customobject':
    logging.warning(ConfigInfo)
    try:
        # Fetch data for the object type
        DataFetchObject = OwnersConnect.GetExtract(ConfigInfo, Bucket, Properties, DefaultProperties, CycleId, Object)
        logging.warning(DataFetchObject)
        ApiCalls = APICall.ApiCount()
        logstatements = LogsStatementConnect.logs_statements(ApiCalls, DataFetchObject)
        if len(DataFetchObject) == 0:
            # Create a log file if no records are processed
            LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'], CutOffDate=ConfigInfo['CutOffDate'],
                                           Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                           RecordsProcessed=len(DataFetchObject), NoOfApiCall=ApiCalls,
                                           CycleId=CycleId, Object=Object, Bucket=Bucket)
        else:
            # Creating a pandas dataframe on the object
            spark_df = DataframeConnect.dataframe_module(DataFetchObject[0], NullDatafixConnect, spark, Bucket, CycleId, ConfigInfo, Object, TerritoryCode )
            # Create a log file with processed records
            LogFile = Logs.LogFileGenerate(Status=200, Stage=ConfigInfo['Stage'], CutOffDate=None,
                                           Operation=ConfigInfo['Operation'], Territory=TerritoryCode,
                                           RecordsProcessed=len(DataFetchObject), NoOfApiCall=ApiCalls,
                                           CycleId=CycleId, Object=Object, Bucket=Bucket)
    except Exception as error:
        raise error
logging.warning("The duration of execution is :'%s'", format(timeit.default_timer() - starttime))

