{% snapshot contracts_product_type_changes %}
{{
 config(
 unique_key='contract_id',
 target_schema ='snapshot',
 strategy='check',
 check_cols=['contract_id','product'],
 )
}}

SELECT 
    a.contract_id, 
    ARRAY_JOIN(ARRAY_AGG(product_id), ',') AS product
FROM (
    SELECT DISTINCT 
        contract_id, 
        CAST(product_id AS VARCHAR) AS product_id
    FROM {{ref('ods_cs_contract_products')}}
    ORDER BY product_id
) a
GROUP BY a.contract_id
{% endsnapshot %}
