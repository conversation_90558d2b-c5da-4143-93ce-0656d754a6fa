{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH prospect AS (
    SELECT
        *
    FROM
        {{ ref("dt_ps_prospect") }}
),
prospect_start_level AS(         
    SELECT 
        id as prospect_id,
        placement_test_entry_point,
        CASE 
          WHEN placement_test_entry_point = 0 THEN 0
          WHEN placement_test_entry_point = 1 THEN 3
          WHEN placement_test_entry_point = 2 THEN 10
          WHEN placement_test_entry_point = 3 THEN 17
          ELSE NULL
        END as start_level,
        CASE 
          WHEN placement_test_entry_point = 0 THEN 'auto'
          WHEN placement_test_entry_point = 1 THEN 'beginner'
          WHEN placement_test_entry_point = 2 THEN 'intermediate'
          WHEN placement_test_entry_point = 3 THEN 'advanced'
          ELSE NULL
        END as start_level_category,
        center_reference_id
    from prospect
)

select 
    prospect_id,
    placement_test_entry_point,
    start_level,
    start_level_category,
    center_reference_id
from prospect_start_level
