{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_product_levels'
        ) }}
)

SELECT {{etl_load_date()}}, 
    Id,
    Name,
    "Order",
    Product_Type_Id,
    Study_Type_Id
    FROM ods_data as tempTable