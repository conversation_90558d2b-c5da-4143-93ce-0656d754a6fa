{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        type,
        sequence,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        value,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_schedule_and_booking_service',
            'category'
        ) }}
)

SELECT
    {{etl_load_date()}},
    type,
    sequence,
    {{ cast_to_timestamp('created') }} as created,
    {{ cast_to_timestamp('lastupdated') }} as last_updated,
    id,
    value
FROM
    rankedrecords
WHERE
    rn = 1;
