{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    on_schema_change = 'append_new_columns',
    format = 'parquet'
) }}

select data_date,
       country_geo_loc,
       device,
       page_url,
       page_path,
       hostname as host_name,
       page_type,
       SUM(position) as position,
       query,
       SUM(sum_position) AS sum_position,
       SUM(impressions) AS impressions,
       SUM(clicks) AS clicks,
       territory_code,
       case when (hostname in ('wallstreetenglish.com','www.wallstreetenglish.com') and territory_name ='Global') then 'Global' 
       when territory_name not in ('Global') then territory_name
       else '' end as territory_name,
       table_source
from
    {{ref('dt_search_console')}}
    group by data_date,country_geo_loc,device,page_url,page_path,hostname,page_type,query,territory_code,territory_name,table_source;