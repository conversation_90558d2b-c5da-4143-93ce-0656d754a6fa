{"Comment": "A description of my state machine", "StartAt": "ExecutionPlanner", "States": {"ExecutionPlanner": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "ExecutionPlanner", "Arguments": {"--input.$": "$.input"}}, "ResultPath": null, "Next": "ReadExecutionJsonFile"}, "ReadExecutionJsonFile": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:ReadExecutionPlannerFile:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Map"}, "Map": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Choice", "States": {"Choice": {"Type": "Choice", "Choices": [{"Variable": "$.Stage1", "IsPresent": true, "Next": "Stage1Subprocess"}, {"Variable": "$.Stage2", "IsPresent": true, "Next": "Stage2map"}, {"Variable": "$.Stage3", "IsPresent": true, "Next": "Stage3Map"}, {"Variable": "$.Stage4", "IsPresent": true, "Next": "Stage4Map"}]}, "Stage1Subprocess": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:HubspotSubstage1Process:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "SubStage1Map"}, "SubStage1Map": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Sub-Stage1", "States": {"Sub-Stage1": {"Type": "Choice", "Choices": [{"Variable": "$.Contacts", "IsPresent": true, "Next": "ContactsStage1"}, {"Variable": "$.Deals", "IsPresent": true, "Next": "DealsStage1"}, {"Variable": "$.Owners", "IsPresent": true, "Next": "OwnersStage1"}, {"Variable": "$.AssociationDeals", "IsPresent": true, "Next": "AssociationDealsStage1"}, {"Variable": "$.ArchiveContacts", "IsPresent": true, "Next": "ArchiveContactsStage1"}, {"Variable": "$.Companies", "IsPresent": true, "Next": "CompaniesStage1"}, {"Variable": "$.CustomObject", "IsPresent": true, "Next": "CustomObjectStage1"}, {"Variable": "$.ArchiveDeals", "IsPresent": true, "Next": "ArchiveDealsmap"}, {"Variable": "$.ArchiveOwners", "IsPresent": true, "Next": "Archiveownersmap"}, {"Variable": "$.ArchiveCompanies", "IsPresent": true, "Next": "ArchiveCompaniesStage1"}, {"Variable": "$.AssociationContacts", "IsPresent": true, "Next": "AssociationContactsMap"}, {"Variable": "$.AssociationCompanies", "IsPresent": true, "Next": "AssociationCompaniesMap"}, {"Variable": "$.france_associations", "IsPresent": true, "Next": "Wait (6)"}]}, "Wait (6)": {"Type": "Wait", "Seconds": 1025, "Next": "FranceAssociationmap"}, "ContactsStage1": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "GlueDataExtractContacts", "States": {"GlueDataExtractContacts": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotDataExtractProd", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--Url.$": "$.Url", "--CutoffDate.$": "States.Format('{}',$.CutoffDate)", "--DefaultProperties.$": "States.Format('{}',$.DefaultProperties)", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (14)"}]}, "Lambda Invoke (14)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "ItemsPath": "$.Contacts", "MaxConcurrency": 39}, "DealsStage1": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "DealsWait", "States": {"DealsWait": {"Type": "Wait", "Seconds": 5, "Next": "GlueDataExtractDeals"}, "GlueDataExtractDeals": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotDataExtractProd", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--Url.$": "$.Url", "--CutoffDate.$": "States.Format('{}',$.CutoffDate)", "--DefaultProperties.$": "States.Format('{}',$.DefaultProperties)", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (1)"}]}, "Lambda Invoke (1)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "MaxConcurrency": 39, "ItemsPath": "$.Deals"}, "OwnersStage1": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "OwnersWait", "States": {"OwnersWait": {"Type": "Wait", "Seconds": 10, "Next": "GlueDataextractOwners"}, "GlueDataextractOwners": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotDataExtractProd", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--Url.$": "$.Url", "--CutoffDate.$": "States.Format('{}',$.CutoffDate)", "--DefaultProperties.$": "States.Format('{}',$.DefaultProperties)", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (2)"}]}, "Lambda Invoke (2)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "ItemsPath": "$.Owners"}, "AssociationDealsStage1": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "AssociationDealsWait", "States": {"AssociationDealsWait": {"Type": "Wait", "Seconds": 15, "Next": "AssociationDealsExtract"}, "AssociationDealsExtract": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotDataExtractProd", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--Url.$": "$.Url", "--CutoffDate.$": "States.Format('{}',$.CutoffDate)", "--DefaultProperties.$": "States.Format('{}',$.DefaultProperties)", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (3)"}]}, "Lambda Invoke (3)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "ItemsPath": "$.AssociationDeals", "MaxConcurrency": 39}, "ArchiveContactsStage1": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "ArchiveContactsWait", "States": {"ArchiveContactsWait": {"Type": "Wait", "Seconds": 20, "Next": "ArchiveContactsExtract"}, "ArchiveContactsExtract": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotArchiveDataextract", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--Url.$": "$.Url", "--CutoffDate.$": "States.Format('{}',$.CutoffDate)", "--DefaultProperties.$": "States.Format('{}',$.DefaultProperties)", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (4)"}]}, "Lambda Invoke (4)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "ItemsPath": "$.ArchiveContacts", "MaxConcurrency": 39}, "CompaniesStage1": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Wait", "States": {"Wait": {"Type": "Wait", "Seconds": 25, "Next": "HubspotCustomCompaniesExtract"}, "HubspotCustomCompaniesExtract": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotCustomObjectCompaniesExtract", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--Url.$": "$.Url", "--CutoffDate.$": "States.Format('{}',$.CutoffDate)", "--DefaultProperties.$": "States.Format('{}',$.DefaultProperties)", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (5)"}]}, "Lambda Invoke (5)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "ItemsPath": "$.Companies", "MaxConcurrency": 39}, "CustomObjectStage1": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Wait (1)", "States": {"Wait (1)": {"Type": "Wait", "Seconds": 30, "Next": "HubspotCustomObjectExtract"}, "HubspotCustomObjectExtract": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotCustomObjectCompaniesExtract", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--Url.$": "$.Url", "--CutoffDate.$": "States.Format('{}',$.CutoffDate)", "--DefaultProperties.$": "States.Format('{}',$.DefaultProperties)", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke"}]}, "Lambda Invoke": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "ItemsPath": "$.CustomObject", "MaxConcurrency": 39}, "ArchiveDealsmap": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Wait (2)", "States": {"Wait (2)": {"Type": "Wait", "Seconds": 35, "Next": "GlueArchiveDataExtractDeals"}, "GlueArchiveDataExtractDeals": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotArchiveDataextract", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--Url.$": "$.Url", "--CutoffDate.$": "States.Format('{}',$.CutoffDate)", "--DefaultProperties.$": "States.Format('{}',$.DefaultProperties)", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (6)"}]}, "Lambda Invoke (6)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "ItemsPath": "$.ArchiveDeals", "MaxConcurrency": 39}, "Archiveownersmap": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Wait (3)", "States": {"Wait (3)": {"Type": "Wait", "Seconds": 40, "Next": "GlueArchiveOwners"}, "GlueArchiveOwners": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotArchiveDataextract", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--Url.$": "$.Url", "--CutoffDate.$": "States.Format('{}',$.CutoffDate)", "--DefaultProperties.$": "States.Format('{}',$.DefaultProperties)", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (7)"}]}, "Lambda Invoke (7)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "ItemsPath": "$.ArchiveOwners", "MaxConcurrency": 39}, "ArchiveCompaniesStage1": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Wait (4)", "States": {"Wait (4)": {"Type": "Wait", "Seconds": 45, "Next": "ArchivecompaniesExtract"}, "ArchivecompaniesExtract": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotArchiveDataextract", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--Url.$": "$.Url", "--CutoffDate.$": "States.Format('{}',$.CutoffDate)", "--DefaultProperties.$": "States.Format('{}',$.DefaultProperties)", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (8)"}]}, "Lambda Invoke (8)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "ItemsPath": "$.ArchiveCompanies", "MaxConcurrency": 39, "End": true}, "AssociationContactsMap": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Wait (5)", "States": {"Wait (5)": {"Type": "Wait", "Seconds": 50, "Next": "GlueAssociationExtract"}, "GlueAssociationExtract": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotDataExtractProd", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--Url.$": "$.Url", "--CutoffDate.$": "States.Format('{}',$.CutoffDate)", "--DefaultProperties.$": "States.Format('{}',$.DefaultProperties)", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (9)"}]}, "Lambda Invoke (9)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "ItemsPath": "$.AssociationContacts", "MaxConcurrency": 39}, "AssociationCompaniesMap": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Wait (7)", "States": {"Wait (7)": {"Type": "Wait", "Seconds": 55, "Next": "AssociationCompaniesGlue"}, "AssociationCompaniesGlue": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotDataExtractProd", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--Url.$": "$.Url", "--CutoffDate.$": "States.Format('{}',$.CutoffDate)", "--DefaultProperties.$": "States.Format('{}',$.DefaultProperties)", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (10)"}]}, "Lambda Invoke (10)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "ItemsPath": "$.AssociationCompanies", "MaxConcurrency": 39}, "FranceAssociationmap": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Wait (8)", "States": {"Wait (8)": {"Type": "Wait", "Seconds": 60, "Next": "GlueFranceAssociation"}, "GlueFranceAssociation": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotDataExtractProd", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--Url.$": "$.Url", "--CutoffDate.$": "States.Format('{}',$.CutoffDate)", "--DefaultProperties.$": "States.Format('{}',$.DefaultProperties)", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (11)"}]}, "Lambda Invoke (11)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "End": true, "ItemsPath": "$.france_associations", "MaxConcurrency": 1}}}, "ItemsPath": "$.Substage1", "MaxConcurrency": 40, "Next": "Stage1SaveasTableInput"}, "Stage1SaveasTableInput": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:HubspotStage1DataextractValidation:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Stage1TableLoadMap"}, "Stage1TableLoadMap": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "HUbspotStage1Load", "States": {"HUbspotStage1Load": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotSaveTable", "Arguments": {"--InputType.$": "$.InputType", "--FilePath.$": "$.FilePath", "--TableName.$": "$.TableName", "--WritePath.$": "$.WritePath", "--WriteMode.$": "$.WriteMode"}}, "End": true}}}, "ItemsPath": "$.TableProperties", "MaxConcurrency": 20, "Next": "Stage1SaveTableValidation"}, "Stage1SaveTableValidation": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:HubspotStage1SaveTableValidation:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}, "Stage2map": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "HubspotScdExecute", "States": {"HubspotScdExecute": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotScdLoadExecute", "Arguments": {"--Function.$": "$.Function", "--Operation.$": "$.Operation", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--OrderProperties.$": "$.OrderProperties", "--RawTable.$": "$.RawTable", "--ScdTable.$": "$.ScdTable", "--TablePath.$": "$.TablePath", "--TempTable.$": "$.TempTable", "--TempTablePath.$": "$.TempTablePath", "--TempTableQuery.$": "$.TempTableQuery", "--DistinctProperties.$": "$.DistinctProperties", "--Condition.$": "$.Condition", "--PartitionProperties.$": "$.PartitionProperties"}}, "End": true}}}, "ItemsPath": "$.Stage2", "MaxConcurrency": 20, "Next": "HubspotScdLogsValidation"}, "HubspotScdLogsValidation": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:HubspotStage2validation:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}, "Stage3Map": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "HubspotDataenrichmentExecute", "States": {"HubspotDataenrichmentExecute": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotStage3Enrichment", "Arguments": {"--Object.$": "$.Object", "--Stage.$": "States.Format('{}',$.Stage)", "--Operation.$": "$.Operation", "--Table.$": "$.Table", "--TablePath.$": "$.TablePath", "--Status.$": "States.Format('{}',$.Status)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (12)"}]}, "Lambda Invoke (12)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "ItemsPath": "$.Stage3", "MaxConcurrency": 20, "Next": "HubspotStage3Validation"}, "HubspotStage3Validation": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:HubspotStage3Validation:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}, "Stage4Map": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Stage4S3toRedshift", "States": {"Stage4S3toRedshift": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotStage4S3toRedshift", "Arguments": {"--Object.$": "$.Object", "--Stage.$": "States.Format('{}',$.Stage)", "--Operation.$": "$.Operation", "--Table.$": "$.Table", "--Filepath.$": "$.Filepath", "--Status.$": "States.Format('{}',$.Status)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (13)"}]}, "Lambda Invoke (13)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "ItemsPath": "$.Stage4", "MaxConcurrency": 20, "Next": "HubspotStgae4Validation"}, "HubspotStgae4Validation": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:HubspotStage4Validation:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "ItemsPath": "$.ExecutionInfo", "MaxConcurrency": 1, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "LambdaFailCase"}], "Next": "PassCase"}, "PassCase": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_pass_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "HubspotExecutionStatusUpdate"}, "HubspotExecutionStatusUpdate": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:HubspotExecutionStatusUpdate:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "ExecutionStatusUpdate"}, "ExecutionStatusUpdate": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun", "Parameters": {"JobName": "ExecutionStatusUpdate", "Arguments": {"--input.$": "$.input"}}, "Next": "HubspotDataPipelineSuccessCheck"}, "HubspotDataPipelineSuccessCheck": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:HubspotDataFlowSuccess:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 1, "MaxAttempts": 3, "BackoffRate": 2}], "Next": "HubspotMyanmarVietnamPipelineTrigger"}, "HubspotMyanmarVietnamPipelineTrigger": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotMyanmarVietnamStepfunctionTrigger"}, "Next": "HubspotIdMapperProdwarehouse"}, "HubspotIdMapperProdwarehouse": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "hubspot_id_mapper"}, "Next": "HubspotSPStage5&6"}, "HubspotSPStage5&6": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotSPStage5andStage6"}, "Next": "ContactsReportSPTrigger"}, "ContactsReportSPTrigger": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "ContactsReportActualSPTriggerjob"}, "Next": "HubspotCountValidation"}, "HubspotCountValidation": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotCountValidationInput"}, "End": true}, "LambdaFailCase": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "HubspotExecutionStatusUpdate"}}}