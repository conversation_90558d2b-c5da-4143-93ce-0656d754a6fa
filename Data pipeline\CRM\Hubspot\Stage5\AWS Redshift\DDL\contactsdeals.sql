CREATE TABLE IF NOT EXISTS hubspot_crm.contactsdeals
(
	contactid BIGINT   ENCODE az64
	,contract_date DATE   ENCODE az64
	,lead_date DATE   ENCODE az64
	,mql_date DATE   ENCODE az64
	,booked_date DATE   ENCODE az64
	,showed_date DATE   ENCODE az64
	,center_name VA<PERSON>HA<PERSON>(64)   ENCODE lzo
	,hubspot_owner_id BIGINT   ENCODE az64
	,individual_corporate VARCHAR(18)   ENCODE lzo
	,source VARCHAR(64)   ENCODE lzo
	,sub_source_group VARCHAR(128)   ENCODE lzo
	,channel VARCHAR(256)   ENCODE lzo
	,channel_drill_down_1 VARCHAR(800)   ENCODE lzo
	,channel_drill_down_2 VARCHAR(400)   ENCODE lzo
	,landing_page VARCHAR(448)   ENCODE lzo
	,page VARCHAR(448)   ENCODE lzo
	,campaign VARCHAR(186)   ENCODE lzo
	,useful_contact_date DATE   ENCODE az64
	,tmk_owner VARCHAR(9)   ENCOD<PERSON> lzo
	,course_age_group VARCHAR(8)   ENCOD<PERSON> lzo
	,dealid BIGINT   ENCODE az64
	,territory_name VA<PERSON><PERSON><PERSON>(48)   ENCODE lzo
	,sales DOUBLE PRECISION   ENCODE raw
	,refund_date DATE
	,refunded_amount DOUBLE PRECISION
	,hubspotteamiddeal BIGINT   ENCODE az64
	,dealrows BIGINT   ENCODE az64
	,amount_gifts DOUBLE PRECISION   ENCODE RAW
	,amount_interest DOUBLE PRECISION   ENCODE RAW
	,core_course_amount DOUBLE PRECISION   ENCODE RAW
	,core_course_levels BIGINT   ENCODE az64
	,core_course_7_class_access_type VARCHAR(9)   ENCODE lzo
	,market_leader_1_amount DOUBLE PRECISION   ENCODE RAW
	,market_leader_2_hours DOUBLE PRECISION   ENCODE RAW
	,test_prep_group_1_amount DOUBLE PRECISION   ENCODE RAW
	,test_prep_group_2_quantity DOUBLE PRECISION   ENCODE RAW
	,business_partner_1_amount DOUBLE PRECISION   ENCODE RAW
	,certifications_1_amount DOUBLE PRECISION   ENCODE RAW
	,certifications_2_quantity BIGINT   ENCODE az64
	,core_course_fit_2_hours DOUBLE PRECISION   ENCODE RAW
	,core_course_fit_amount DOUBLE PRECISION   ENCODE RAW
	,core_course_online_1_amount DOUBLE PRECISION   ENCODE RAW
	,ilc_1_amount DOUBLE PRECISION   ENCODE RAW
	,test_prep_executive_1_amount DOUBLE PRECISION   ENCODE RAW
	,test_prep_executive_2_quantity BIGINT   ENCODE az64
	,business_partner_2_hours DOUBLE PRECISION   ENCODE RAW
	,ilc_2_hours DOUBLE PRECISION   ENCODE RAW
	,business_partner_5_start_level VARCHAR(64)   ENCODE lzo
	,business_partner_6_end_level VARCHAR(64)   ENCODE lzo
	,market_leader_5_start_level VARCHAR(36)   ENCODE lzo
	,market_leader_6_end_level VARCHAR(36)   ENCODE lzo
	,pe_end_level VARCHAR(36)   ENCODE lzo
	,pe_start_level VARCHAR(36)   ENCODE lzo
	,core_course_fit_5_start_level VARCHAR(64)   ENCODE lzo
	,core_course_fit_6_end_level VARCHAR(64)   ENCODE lzo
	,core_course_online_2_levels VARCHAR(64)   ENCODE lzo
	,core_course_online_5_start_level VARCHAR(36)   ENCODE lzo
	,core_course_online_6_end_level VARCHAR(36)   ENCODE lzo
	,test_prep_units BIGINT   ENCODE az64
	,fa_levels BIGINT   ENCODE az64
	,fa_amount DOUBLE PRECISION   ENCODE RAW
	,fa2_levels BIGINT   ENCODE az64
	,fa2_amount DOUBLE PRECISION   ENCODE RAW
	,fa3_levels BIGINT   ENCODE az64
	,fa3_amount DOUBLE PRECISION   ENCODE RAW
	,fa4_levels BIGINT   ENCODE az64
	,fa4_amount DOUBLE PRECISION   ENCODE RAW
	,fam_levels BIGINT   ENCODE az64
	,fam_amount DOUBLE PRECISION   ENCODE RAW
	,ic_levels BIGINT   ENCODE az64
	,ic_amount DOUBLE PRECISION   ENCODE RAW
	,ic2_levels BIGINT   ENCODE az64
	,ic2_amount DOUBLE PRECISION   ENCODE RAW
	,ic3_levels BIGINT   ENCODE az64
	,ic3_amount DOUBLE PRECISION   ENCODE RAW
	,ic4_levels BIGINT   ENCODE az64
	,ic4_amount DOUBLE PRECISION   ENCODE RAW
	,icm_levels BIGINT   ENCODE az64
	,icm_amount DOUBLE PRECISION   ENCODE RAW
	,source_key_fk VARCHAR(256)   ENCODE lzo
	,center_key_fk VARCHAR(256)   ENCODE lzo
)