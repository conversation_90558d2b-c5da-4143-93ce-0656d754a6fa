version: 2

sources:
  - name: stage_novu_service
    description: >
      Source data from the Novu notification service which manages templates, messages,
      subscribers, and notification jobs for the platform's communication system.
    database: awsdatacatalog
    schema: stg_novu_db
    tables:
      - name: notification_templates
        description: Templates for notifications that define structure, content, and variants for different communication channels.
        columns:
          - name: id
            description: Primary key for the notification template
          - name: name
            description: Name of the notification template
          - name: createdat
            description: ISO timestamp when the template was created
          - name: updatedat
            description: ISO timestamp when the template was last updated
          - name: steps
            description: JSON array containing steps with variants for the notification flow

      - name: messages
        description: Individual messages sent to subscribers through various communication channels.
        columns:
          - name: id
            description: Primary key for the message record
          - name: messagetemplateid
            description: ID of the message template variant used
          - name: templateid
            description: ID of the notification template
          - name: subscriberid
            description: ID of the subscriber receiving the message
          - name: channel
            description: Communication channel used for the message (email, SMS, push, etc.)
          - name: title
            description: Title or subject of the message
          - name: createdat
            description: ISO timestamp when the message was created
          - name: updatedat
            description: ISO timestamp when the message was last updated

      - name: subscribers
        description: Users subscribed to receive notifications through the system.
        columns:
          - name: id
            description: Primary key for the subscriber record
          - name: subscriberid
            description: External ID for the subscriber (usually maps to a user ID)
          - name: data
            description: JSON object containing subscriber metadata and preferences
          - name: createdat
            description: ISO timestamp when the subscriber was created
          - name: updatedat
            description: ISO timestamp when the subscriber was last updated

      - name: jobs
        description: Notification jobs that track the delivery status and processing of messages.
        columns:
          - name: id
            description: Primary key for the job record
          - name: messagetemplateid
            description: ID of the message template variant used
          - name: templateid
            description: ID of the notification template
          - name: subscriberid
            description: ID of the subscriber for the job
          - name: type
            description: Type of communication channel for the job
          - name: identifier
            description: Identifier or title for the job
          - name: createdat
            description: ISO timestamp when the job was created
          - name: updatedat
            description: ISO timestamp when the job was last updated