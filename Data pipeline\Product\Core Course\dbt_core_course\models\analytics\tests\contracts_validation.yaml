version: 2

models:
  - name: contracts
    columns:
      - name: contract_id
        tests:
          - not_null:
              severity: error
          - unique:
              severity: warn

      - name: student_id
        tests:
          - not_null:
              severity: error

      - name: student_reference_id
        tests:
          - not_null:
              severity: warn

      - name: center_id
        tests:
          - not_null:
              severity: error

      - name: consultant_id
        tests:
          - not_null:
              severity: error

      - name: contract_reference_id
        tests:
          - not_null:
              severity: error

      - name: center_reference_id
        tests:
          - not_null:
              severity: error

      - name: center_name
        tests:
          - not_null:
              severity: error

      - name: territory_name
        tests:
          - not_null:
              severity: error

      - name: start_date
        tests:
          - not_null:
              severity: error

      - name: end_date
        tests:
          - not_null:
              severity: error

      - name: created_date
        tests:
          - not_null:
              severity: error

      - name: local_created_date
        tests:
          - not_null:
              severity: error

      - name: last_updated_date
        tests:
          - not_null:
              severity: error

      - name: local_last_updated_date
        tests:
          - not_null:
              severity: error

      - name: location
        tests:
          - accepted_values:
              values: ['incenter', 'outcenter']
              severity: error

      - name: study_plan_type
        tests:
          - accepted_values:
              values: ['not_applicable', 'eightweeks_default', 'fourweeks', 'twelveweeks', 'eightweeks']
              severity: error

      - name: product_type
        tests:
          - accepted_values:
              values: ['other', 'core course', 'market leader', 'test prep', 'business partner', 'd2c']
              severity: error

      - name: service_type
        tests:
          - accepted_values:
              values: ['standard', 'vip']
              severity: error

      - name: contract_type
        tests:
          - accepted_values:
              values: ['private', 'b2b']
              severity: error

      - name: source_type
        tests:
          - accepted_values:
              values: ['nse', 'apim']
              severity: error

      - name: start_level
        tests:
          - not_null:
              severity: error

      - name: end_level
        tests:
          - not_null:
              severity: error

      - name: price_local_currency
        tests:
          - not_null:
              severity: error

      - name: state
        tests:
          - not_null:
              severity: error

      - name: status
        tests:
          - not_null:
              severity: error

      - name: current_validation_state
        tests:
          - not_null:
              severity: error

      - name: refunded_price_local_currency
        tests:
          - not_null:
              severity: error

      - name: is_renewed
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error

      - name: is_promotional
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error

      - name: is_transfer_in
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error

      - name: is_cross_center_booking
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error

      - name: is_membership
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error

      - name: is_teen
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
