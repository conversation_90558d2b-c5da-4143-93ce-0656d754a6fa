{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet',
) }}

WITH calendar as
    (
    select
    "date",
    last_month_date
    from reporting.dim_calendar
    where 
    -- Add FILTER TO THE CALENDAR
    "date" = last_month_date
    and last_month_date <= current_date
    and "date" > date '2021-01-01'
    )

,monthly_levels as (
SELECT
    date_add('day', -1, date_add('month',1,mt_lvl.report_start_date)) as "date"
    , tc.reference_center_id as center_reference_id
    , mt_lvl.consultant_id 
    , CASE
        WHEN lower(mt_lvl.class_access_type) in ('in-center and online','full access') THEN 'Full Access'
        WHEN lower(mt_lvl.class_access_type) in ('in-center only','in-center') THEN 'In-Center'
        WHEN lower(mt_lvl.class_access_type) in ('online only','online') THEN 'Online'
        ELSE 'No Access'
    END AS class_access_type
    , mt_lvl.location
    , mt_lvl.services
    , mt_lvl."group"
    , mt_lvl.is_promotional
    , mt_lvl.is_teen
    , CASE 
        WHEN mt_lvl.is_b2_b = true THEN 'B2B'
        ELSE 'Private'
    END AS contract_type
    , 
    CASE 
        WHEN mt_lvl.is_membership = 'Levels' THEN 'Level'
        ELSE coalesce(mt_lvl.is_membership,'No Membership')
    END AS is_membership
    , mt_lvl.course_level
    , CASE 
        WHEN mt_lvl.personal_tutor is null then 'No Personal Tutor'
        WHEN mt_lvl.personal_tutor = ' ' then 'No Personal Tutor'
        ELSE mt_lvl.personal_tutor
    END as personal_tutor
    , true as contract_inclusions
    , mt_lvl.contractendstud as contract_end_stud
    , mt_lvl.contractendstud_nonmembership as contract_end_stud_non_membership
    , mt_lvl.contractendstud_membership as contract_end_stud_membership
    , mt_lvl.lvlscmpltdbycontractendstud as lvls_cmpltd_by_contract_end_stud
    , mt_lvl.monthspurchasedbycontractendstud as months_purchased_by_contract_end_stud
    , mt_lvl.lvlsprchsdbycontractendstud as lvls_purchased_by_contract_end_stud
    , mt_lvl.lvlsprchsdnonmembership as lvls_purchased_non_membership
FROM 
    {{ ref('course_completed_metrics') }} as mt_lvl
INNER JOIN {{ref('dt_ls_center')}} as tc
    ON lower(mt_lvl.center_id) = tc.id
    AND mt_lvl.course in ('core course')
)

, inc_data as (
SELECT
cal.last_month_date as "date",
fdsp.group_id,
groups.name as group_name,
fdsp.consultant_id,
fdsp.personal_tutor,
fdsp.center_reference_id,
fdsp.contract_type,
fdsp.course_level,
fdsp.location,
fdsp.class_access_type,
fdsp.service_type,
fdsp.is_membership,
fdsp.is_teen,
fdsp.is_promotional,
fdsp.contract_inclusions,
fdsp."Current Valid Students",
fdsp."Total Students Serviced",
fdsp."Total Students Serviced 30days",
fdsp."Total Students Completing 21 days",
fdsp."Active Students Serviced",
fdsp."Active Students Serviced 30days",
fdsp."Active Booked 30days",
fdsp."Students done 1+",
fdsp."Students completed 21days",
fdsp."Levels started",
fdsp."Levels started first",
fdsp."Levels started later",
fdsp."DM revenue",
fdsp."DM revenue first",
fdsp."DM revenue later",
fdsp."Total CCs",
fdsp."Total CCs Onl",
fdsp."Total Encounters",
fdsp."Total Encounters Onl",
fdsp."Total SCs",
fdsp."Total SCs Onl",
fdsp."Total Advising Session",
fdsp."Total Eol Advising Session",
CAST(current_timestamp AS TIMESTAMP(6)) AS load_date
FROM calendar cal
left join {{ ref('fact_daily_student_progress') }} as fdsp 
    on cal.last_month_date = fdsp."date"
left join {{ref("dt_cs_groups")}} as groups 
    on groups.id = fdsp.group_id
)

SELECT
    TO_HEX(SHA256(cast(
        cast(coalesce(fdsp."date",mt_lvl."date") as varchar)
        || cast(coalesce(fdsp.group_id,mt_lvl."group") as varchar)
        || cast(coalesce(fdsp.consultant_id,mt_lvl.consultant_id) as varchar)
        || cast(coalesce(fdsp.center_reference_id,mt_lvl.center_reference_id) as varchar)
        || cast(coalesce(fdsp.contract_type,mt_lvl.contract_type) as varchar)
        || cast(coalesce(fdsp.course_level,mt_lvl.course_level) as varchar)
        || cast(coalesce(fdsp.location,mt_lvl.location) as varchar)
        || cast(coalesce(fdsp.class_access_type,mt_lvl.class_access_type) as varchar)
        || cast(coalesce(fdsp.service_type,mt_lvl."services") as varchar)
        || cast(coalesce(fdsp.is_membership,mt_lvl.is_membership) as varchar)
        || cast(coalesce(fdsp.is_teen,mt_lvl.is_teen) as varchar)
        || cast(coalesce(fdsp.is_promotional,mt_lvl.is_promotional) as varchar)
        || cast(coalesce(fdsp.contract_inclusions,mt_lvl.contract_inclusions) as varchar)
        || cast(coalesce(fdsp.personal_tutor,mt_lvl.personal_tutor) as varchar)
    as varbinary))) as dbt_unique_id,
    coalesce(fdsp."date",mt_lvl."date") as "date",
    coalesce(fdsp.group_id,mt_lvl."group") as group_id,
    coalesce(fdsp.group_name,fdsp.group_id) as group_name,
    coalesce(fdsp.consultant_id,mt_lvl.consultant_id) as consultant_id,
    coalesce(fdsp.personal_tutor,mt_lvl.personal_tutor) as personal_tutor,
    coalesce(fdsp.center_reference_id,mt_lvl.center_reference_id) as center_reference_id,
    coalesce(fdsp.contract_type,mt_lvl.contract_type) as contract_type,
    coalesce(fdsp.course_level,mt_lvl.course_level) as course_level,
    coalesce(fdsp.location,mt_lvl.location) as location,
    coalesce(fdsp.class_access_type,mt_lvl.class_access_type) as class_access_type,
    coalesce(fdsp.service_type,mt_lvl."services") as service_type,
    coalesce(fdsp.is_membership,mt_lvl.is_membership) as is_membership,
    coalesce(fdsp.is_teen,mt_lvl.is_teen) as is_teen,
    coalesce(fdsp.is_promotional,mt_lvl.is_promotional) as is_promotional,
    coalesce(fdsp.contract_inclusions,mt_lvl.contract_inclusions) as contract_inclusions,
    fdsp."Current Valid Students",
    fdsp."Total Students Serviced",
    fdsp."Total Students Serviced 30days",
    fdsp."Total Students Completing 21 days",
    fdsp."Active Students Serviced",
    fdsp."Active Students Serviced 30days",
    fdsp."Active Booked 30days",
    fdsp."Students done 1+",
    fdsp."Students completed 21days",
    fdsp."Levels started",
    fdsp."Levels started first",
    fdsp."Levels started later",
    fdsp."DM revenue",
    fdsp."DM revenue first",
    fdsp."DM revenue later",
    fdsp."Total CCs",
    fdsp."Total CCs Onl",
    fdsp."Total Encounters",
    fdsp."Total Encounters Onl",
    fdsp."Total SCs",
    fdsp."Total SCs Onl",
    fdsp."Total Advising Session",
    fdsp."Total Eol Advising Session",
    mt_lvl.contract_end_stud,
    mt_lvl.contract_end_stud_non_membership,
    mt_lvl.contract_end_stud_membership,
    mt_lvl.lvls_cmpltd_by_contract_end_stud,
    mt_lvl.months_purchased_by_contract_end_stud,
    mt_lvl.lvls_purchased_by_contract_end_stud,
    mt_lvl.lvls_purchased_non_membership,
    load_date
FROM
    inc_data as fdsp
FULL OUTER JOIN
    monthly_levels as mt_lvl
    ON mt_lvl."date" = fdsp."date"
    AND mt_lvl.center_reference_id = fdsp.center_reference_id
    AND mt_lvl.consultant_id = fdsp.consultant_id
    AND mt_lvl.class_access_type = fdsp.class_access_type
    AND mt_lvl.location = lower(fdsp.location)
    AND mt_lvl.services = fdsp.service_type
    AND mt_lvl."group" = fdsp.group_id
    AND mt_lvl.is_promotional = fdsp.is_promotional
    AND mt_lvl.is_membership = fdsp.is_membership
    AND mt_lvl.contract_type = fdsp.contract_type
    AND mt_lvl.course_level = fdsp.course_level
    AND mt_lvl.personal_tutor = fdsp.personal_tutor
    AND mt_lvl.contract_inclusions = fdsp.contract_inclusions