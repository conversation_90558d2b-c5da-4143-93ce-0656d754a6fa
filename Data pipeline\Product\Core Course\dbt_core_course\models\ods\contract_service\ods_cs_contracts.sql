{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    tags=['dt','contract_service'],
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        code,
        number,
        crmcontractnumber,
        producttypeid,
        servicetype,
        studentid,
        centerid,
        startdate,
        enddate,
        startlevelid,
        endlevelid,
        price,
        state,
        status,
        consultantid,
        labteacherid,
        refunded,
        isrenewed,
        ispromotional,
        istransferin,
        location,
        cast(createddate as timestamp(6)) as createddate,
        cast(lastupdateddate as timestamp(6)) as lastupdateddate,
        cast(contracttype as int) as contracttype,
        contractreferenceid,
        companyid,
        groupid,
        iscrosscenterbooking,
        cast(sourcetype as int) as sourcetype,
        saledate,
        canceldate,
        totalnumberofhours,
        mastercontractcourseid,
        currentvalidationstate,
        ismembership,
        isteen,
        maxnoofccandscclasses,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'contracts')}}
)

SELECT
    {{etl_load_date()}},
    id,
    code,
    number,
    crmcontractnumber as crm_contract_number,
    producttypeid as product_type_id,
    servicetype as service_type,
    studentid as student_id,
    centerid as center_id,
    startdate as start_date,
    enddate as end_date,
    startlevelid as start_level_id,
    endlevelid as end_level_id,
    price,
    state,
    status,
    consultantid as consultant_id,
    labteacherid as lab_teacher_id,
    refunded as refunded,
    isrenewed as is_renewed,
    ispromotional as is_promotional,
    istransferin as is_transfer_in,
    location,
    {{cast_to_timestamp('createddate')}} as created_date,
    {{cast_to_timestamp('lastupdateddate')}} as last_updated_date,
    contracttype as contract_type,
    contractreferenceid as contract_reference_id,
    companyid as company_id,
    groupid as group_id,
    iscrosscenterbooking as is_cross_center_booking,
    sourcetype as source_type,
    saledate as sale_date,
    canceldate as cancel_date,
    totalnumberofhours as total_number_of_hours,
    mastercontractcourseid as master_contract_course_id,
    currentvalidationstate as current_validation_state,
    ismembership as is_membership,
    isteen as is_teen,
    maxnoofccandscclasses as max_no_of_cc_and_sc_classes
FROM 
    RankedRecords
WHERE 
    rn = 1;