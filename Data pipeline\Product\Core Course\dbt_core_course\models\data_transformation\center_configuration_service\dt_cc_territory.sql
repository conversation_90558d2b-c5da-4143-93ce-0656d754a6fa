{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_cc_territory') }}
)
SELECT 
    {{etl_load_date()}},
    id,
    code,
    created_date,
    CASE
        When first_day_of_the_week = 0 Then 'sunday'
        When first_day_of_the_week = 1 Then 'monday'
        When first_day_of_the_week = 2 Then 'tuesday'
        When first_day_of_the_week = 3 Then 'wednesday'
        When first_day_of_the_week = 4 Then 'thursday'
        When first_day_of_the_week = 5 Then 'friday'
        When first_day_of_the_week = 6 Then 'saturday'
        Else cast(
            first_day_of_the_week as varchar
        )
    End As first_day_of_the_week,
    is_active,
    is_day_light_saving,
    is_twenty_four_hour_format,
    iso_code,
    last_modified_date,
    name,
    territory_reference_id,
    time_zone_id,
    navision_client,
    is_dual_class_access,
    CASE
        When dc_technology_type = 0 Then 'none'
        When dc_technology_type = 1 Then 'enablex'
        When dc_technology_type = 2 Then 'tokbox'
        When dc_technology_type = 3 Then 'zoom'
        Else cast(
            dc_technology_type as varchar
        )
    End As dc_technology_type
FROM
    ods_data as territory
