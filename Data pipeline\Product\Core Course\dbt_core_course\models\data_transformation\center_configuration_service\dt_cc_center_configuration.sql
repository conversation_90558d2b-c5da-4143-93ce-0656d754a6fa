{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_cc_center_configuration') }}
)
SELECT
    {{etl_load_date()}},
    beginning_of_didactic_year,
    book_minutes_up_to_class_start,
    cancel_minutes_up_to_class_start,
    carry_over_maximum,
    created_date,
    is_active,
    has_online_classes_visibility,
    has_scheduling_visibility,
    is_carry_over_enabled,
    is_stand_by_enabled,
    is_waiting_list_enabled,
    last_modified_date,
    no_of_booking_days_in_advance,
    no_of_class_rooms,
    no_of_other_spaces,
    no_of_students_per_encounter,
    no_of_students_per_complementary_class,
    reminder_notifications_before_class_start,
    social_class_or_complementary_class_per_level,
    maximum_students_in_standby,
    waiting_list_encounter_or_online_encounter,
    show_unused_encounters,
    staff_cancel_minutes_upto_class_start,
    is_stand_by_configurable,
    is_one_week_plan_available_in_study_plan,
    is_student_availability_enabled,
    id,
    center_id
FROM
    ods_data as centerconfiguration
