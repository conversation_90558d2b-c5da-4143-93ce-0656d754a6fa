import logging
import CloudOperations
import requests
import time

# Import Secret manager function from CLoudOperations module
credential = CloudOperations.SecretManager


class Oauth:

    @staticmethod

    def authenticate(territory_input):
        # Get authentication fields from secret manager
        auth_fields = credential.GetSecret('auth_hubspot_credentials', 'eu-west-1')

        def response(territory_input,auth_fields):
            # Construct token request URL
            token_request_url = auth_fields[territory_input]['url']

            # Send token request to obtain access token
            token_response = requests.post(token_request_url,
                                           data={'grant_type': auth_fields[territory_input]["grant_type"],
                                                 'client_id': auth_fields[territory_input]["client_id"],
                                                 'client_secret': auth_fields[territory_input]["client_secret"],
                                                 'refresh_token': auth_fields[territory_input]["refresh_token"]}
                                           )
            logging.warning("Generated access token ")
            return token_response

        # Retry authentication for a maximum of 3 attempts
        while_iterator = 0
        while while_iterator < 3:
            # Make token request and retrieve token response
            token_req_response = response(territory_input, auth_fields)
            token_response_content = token_req_response.json()

            # Log the token request response
            logging.warning(token_req_response)

            # Check the status code of the token request response
            access_token_status = token_req_response.status_code
            if access_token_status == 200:
                logging.warning("Generated access token initially")
                return token_response_content["access_token"]

            # Increment the while_iterator and retry after a delay of 3 seconds
            while_iterator += 1
            logging.warning("Error while generating access token")
            time.sleep(3)