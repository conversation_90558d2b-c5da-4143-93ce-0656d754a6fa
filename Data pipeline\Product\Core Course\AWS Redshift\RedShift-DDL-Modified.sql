
-- stage_analytics.scheduleandbookingservice_scheduledclassproperty definition

-- Drop table
___________________________________________________________________________________________________________________

-- stage_analytics.learningservice_bookedstudent definition

-- Drop table
___________________________________________________________________________________________________________________

-- stage_analytics.snb_scheduled_class_property definition

-- Drop table

-- DROP TABLE snb_scheduled_class_property;

--DROP TABLE stage_analytics.snb_scheduled_class_property;
CREATE TABLE IF NOT EXISTS stage_analytics.snb_scheduled_class_property
(
	etl_load_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_id VARCHAR(36)   ENCODE lzo
	,is_b2_b BOOLEAN   ENCODE RAW
	,visible_in_group BOOLEAN   ENCODE RAW
	,is_teen BOOLEAN   ENCODE RAW
	,service_type VARCHAR(100)   ENCODE lzo
)
DISTSTYLE AUTO
;
ALTER TABLE stage_analytics.snb_scheduled_class_property owner to dwhdevuser;

_________________________________________________________________________________________________________________

-- stage_analytics.placement_start_level definition

-- Drop table

-- DROP TABLE placement_start_level;

--DROP TABLE stage_analytics.placement_start_level;
CREATE TABLE IF NOT EXISTS stage_analytics.placement_start_level
(
	prospect_id VARCHAR(256)   ENCODE lzo
	,placement_test_entry_point INTEGER   ENCODE az64
	,start_level INTEGER   ENCODE az64
	,start_level_category VARCHAR(256)   ENCODE lzo
	,center_reference_id VARCHAR(256)   ENCODE lzo
)
DISTSTYLE AUTO
;
ALTER TABLE stage_analytics.placement_start_level owner to dwhdevuser;

________________________________________________________________________________________________________________

-- stage_analytics.union_classes definition

-- Drop table

-- DROP TABLE union_classes;

--DROP TABLE stage_analytics.union_classes;
CREATE TABLE IF NOT EXISTS stage_analytics.union_classes
(
	class_id VARCHAR(36)   ENCODE lzo
	,teacher_id VARCHAR(36)   ENCODE lzo
	,class_start_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_local_start_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_communication_account_type VARCHAR(30)   ENCODE lzo
	,class_description VARCHAR(400)   ENCODE lzo
	,class_number_of_seats INTEGER   ENCODE az64
	,class_number_of_seats_in_stand_by INTEGER   ENCODE az64
	,class_created_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_local_created_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_last_updated_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_local_last_updated_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_center_reference_id VARCHAR(20)   ENCODE lzo
	,ls_center_id VARCHAR(36)   ENCODE lzo
	,class_end_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_local_end_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_type VARCHAR(36)   ENCODE lzo
	,class_category_from_booking BOOLEAN   ENCODE RAW
	,class_cancelled_flag BOOLEAN   ENCODE RAW
	,class_source VARCHAR(12)   ENCODE lzo
	,class_created_by VARCHAR(36)   ENCODE lzo
	,class_created_by_role VARCHAR(50)   ENCODE lzo
	,class_last_updated_by VARCHAR(36)   ENCODE lzo
	,class_last_updated_by_role VARCHAR(50)   ENCODE lzo
	,categories_abbreviations VARCHAR(100)   ENCODE lzo
	,is_pro BOOLEAN   ENCODE RAW
	,duration INTEGER   ENCODE az64
	,is_complete BOOLEAN   ENCODE RAW
	,company_id VARCHAR(36)   ENCODE lzo
	,is_visible_in_group BOOLEAN   ENCODE RAW
	,is_b2_b BOOLEAN   ENCODE RAW
	,is_restricted_to_online_only BOOLEAN   ENCODE RAW
	,is_teen BOOLEAN   ENCODE RAW
	,ref_class_id VARCHAR(36)   ENCODE lzo
	,class_code VARCHAR(20)   ENCODE lzo
	,number_of_students INTEGER   ENCODE az64
	,is_closed BOOLEAN   ENCODE RAW
	,is_online BOOLEAN   ENCODE RAW
)
DISTSTYLE KEY
 DISTKEY (teacher_id)
;
ALTER TABLE stage_analytics.union_classes owner to dwhdevuser;

________________________________________________________________________________________________________________

-- stage_analytics.ls_booked_student definition

-- Drop table

-- DROP TABLE ls_booked_student;

--DROP TABLE stage_analytics.ls_booked_student;
CREATE TABLE IF NOT EXISTS stage_analytics.ls_booked_student
(
	id VARCHAR(36)   ENCODE lzo
	,class_id VARCHAR(36)   ENCODE lzo
	,ref_class_id VARCHAR(36)   ENCODE lzo
	,student_id VARCHAR(36)   ENCODE lzo
	,book_mode VARCHAR(10)   ENCODE lzo
	,book_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_book_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,"result" INTEGER   ENCODE az64
	,attended BOOLEAN   ENCODE RAW
	,created TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_created TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,last_updated TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_last_updated TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booked_by VARCHAR(36)   ENCODE lzo
	,booked_role_title VARCHAR(50)   ENCODE lzo
	,booked_person_type VARCHAR(10)   ENCODE lzo
	,cancelled_by VARCHAR(36)   ENCODE lzo
	,cancelled_role_title VARCHAR(50)   ENCODE lzo
	,cancelled_person_type VARCHAR(10)   ENCODE lzo
	,is_cancelled BOOLEAN   ENCODE RAW
	,book_mode_modified_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_book_mode_modified_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,standby_notification_type VARCHAR(20)   ENCODE lzo
	,is_accessed BOOLEAN   ENCODE RAW
	,registration_id VARCHAR(36)   ENCODE lzo
	,booking_order_desc BIGINT   ENCODE az64
	,standby_to_booked_flag INTEGER   ENCODE az64
)
DISTSTYLE KEY
 DISTKEY (student_id)
;
ALTER TABLE stage_analytics.ls_booked_student owner to dwhdevuser;

________________________________________________________________________________________________________________

-- analytics.student_progress definition

-- Drop table

-- DROP TABLE student_progress;

--DROP TABLE analytics.student_progress;
CREATE TABLE IF NOT EXISTS analytics.student_progress
(
	student_reference_id VARCHAR(40)   ENCODE lzo
	,digital_books_log_id VARCHAR(40)   ENCODE lzo
	,center_reference_id VARCHAR(40)   ENCODE lzo
	,registration_id VARCHAR(40)   ENCODE lzo
	,contract_reference_id VARCHAR(40)   ENCODE lzo
	,date_granted TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_date_granted TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,category VARCHAR(100)   ENCODE lzo
	,category_type VARCHAR(100)   ENCODE lzo
	,unlock_type VARCHAR(100)   ENCODE lzo
	,operation_type VARCHAR(100)   ENCODE lzo
	,workbook_type VARCHAR(100)   ENCODE lzo
	,is_teen BOOLEAN   ENCODE RAW
	,config_value VARCHAR(100)   ENCODE lzo
	,"sequence" INTEGER   ENCODE az64
	,is_restart BOOLEAN   ENCODE RAW
)
DISTSTYLE AUTO
;
ALTER TABLE analytics.student_progress owner to dwhdevuser;

________________________________________________________________________________________________________________

-- analytics.activity_multimedia definition

-- Drop table

-- DROP TABLE activity_multimedia;

--DROP TABLE analytics.activity_multimedia;
CREATE TABLE IF NOT EXISTS analytics.activity_multimedia
(
	activity_id VARCHAR(36)   ENCODE lzo
	,registration_id VARCHAR(36)   ENCODE lzo
	,student_id VARCHAR(36)   ENCODE lzo
	,teacher_id VARCHAR(36)   ENCODE lzo
	,ref_class_id VARCHAR(36)   ENCODE lzo
	,class_id VARCHAR(36)   ENCODE lzo
	,created_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_created_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,last_updated_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_last_updated_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,started_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_started_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,completed_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_completed_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,"level" VARCHAR(3)   ENCODE lzo
	,unit VARCHAR(3)   ENCODE lzo
	,lesson VARCHAR(3)   ENCODE lzo
	,mini_cycle VARCHAR(3)   ENCODE lzo
	,mini_cycle_stage VARCHAR(3)   ENCODE lzo
	,"comment" VARCHAR(65535)   ENCODE lzo
	,content_item VARCHAR(50)   ENCODE lzo
	,content_item_type VARCHAR(100)   ENCODE lzo
	,content_item_result_type VARCHAR(100)   ENCODE lzo
	,display_on_list BOOLEAN   ENCODE RAW
	,to_process_in_background BOOLEAN   ENCODE RAW
	,study_mode VARCHAR(36)   ENCODE lzo
	,activity_captured_type VARCHAR(256)   ENCODE lzo
	,total_questions INTEGER   ENCODE az64
	,total_question_answered INTEGER   ENCODE az64
	,duration INTEGER   ENCODE az64
	,score NUMERIC(18,2)   ENCODE az64
)
DISTSTYLE AUTO
 DISTKEY (student_id)
;
ALTER TABLE analytics.activity_multimedia owner to dwhdevuser;

________________________________________________________________________________________________________________

-- analytics.activity_workbook definition

-- Drop table

-- DROP TABLE activity_workbook;

--DROP TABLE analytics.activity_workbook;
CREATE TABLE IF NOT EXISTS analytics.activity_workbook
(
	activity_id VARCHAR(36)   ENCODE lzo
	,registration_id VARCHAR(36)   ENCODE lzo
	,activity_reference_id VARCHAR(36)   ENCODE lzo
	,student_id VARCHAR(36)   ENCODE lzo
	,created_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_created_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,last_updated_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_last_updated_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,"level" INTEGER   ENCODE az64
	,unit INTEGER   ENCODE az64
	,lesson INTEGER   ENCODE az64
	,activity INTEGER   ENCODE az64
	,activity_url VARCHAR(65535)   ENCODE lzo
	,is_active BOOLEAN   ENCODE RAW
	,is_completed BOOLEAN   ENCODE RAW
	,study_mode VARCHAR(36)   ENCODE lzo
	,category_type VARCHAR(36)   ENCODE lzo
	,no_of_attempts INTEGER   ENCODE az64
	,duration INTEGER   ENCODE az64
	,score NUMERIC(18,2)   ENCODE az64
)
DISTSTYLE AUTO
 DISTKEY (student_id)
;
ALTER TABLE analytics.activity_workbook owner to dwhdevuser;
________________________________________________________________________________________________________________

-- analytics.territory_centers definition

-- Drop table

-- DROP TABLE territory_centers;

--DROP TABLE analytics.territory_centers;
CREATE TABLE IF NOT EXISTS analytics.territory_centers
(
	territory_reference_id VARCHAR(256)   ENCODE lzo
	,center_reference_id VARCHAR(256)   ENCODE lzo
	,timezone VARCHAR(256)   ENCODE lzo
	,fin_id VARCHAR(256)   ENCODE lzo
	,center_id VARCHAR(256)   ENCODE lzo
	,ls_territory_id VARCHAR(256)   ENCODE lzo
	,ls_center_id VARCHAR(256)   ENCODE lzo
	,territory_name VARCHAR(256)   ENCODE lzo
	,center_name VARCHAR(256)   ENCODE lzo
	,"region" VARCHAR(256)   ENCODE lzo
	,group_name VARCHAR(256)   ENCODE lzo
	,goc_local VARCHAR(256)   ENCODE lzo
	,"matched" INTEGER   ENCODE az64
)
DISTSTYLE AUTO
;
ALTER TABLE analytics.territory_centers owner to dwhdevuser;

________________________________________________________________________________________________________________

-- analytics.b2b_contracts definition

-- Drop table

-- DROP TABLE b2b_contracts;

--DROP TABLE analytics.b2b_contracts;
CREATE TABLE IF NOT EXISTS analytics.b2b_contracts
(
	company_id VARCHAR(65535)   ENCODE lzo
	,company_name VARCHAR(65535)   ENCODE lzo
	,company_code VARCHAR(65535)   ENCODE lzo
	,center_owned VARCHAR(65535)   ENCODE lzo
	,license_number VARCHAR(65535)   ENCODE lzo
	,company_created_by VARCHAR(65535)   ENCODE lzo
	,company_modified_by VARCHAR(65535)   ENCODE lzo
	,company_created_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,company_local_created_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,company_last_updated_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,company_local_last_updated_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,master_contracts_id VARCHAR(65535)   ENCODE lzo
	,master_contract_number VARCHAR(65535)   ENCODE lzo
	,master_contracts_start_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,master_contracts_end_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,master_contracts_sale_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,master_contracts_state VARCHAR(65535)   ENCODE lzo
	,master_contracts_status VARCHAR(65535)   ENCODE lzo
	,master_contracts_cancel_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,master_contracts_resaon VARCHAR(65535)   ENCODE lzo
	,master_contracts_modified_by VARCHAR(65535)   ENCODE lzo
	,master_contracts_created_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,master_contracts_local_created_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,master_contracts_last_updated_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,master_contracts_local_last_updated_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,master_contract_course_id VARCHAR(65535)   ENCODE lzo
	,mcc_product_type VARCHAR(65535)   ENCODE lzo
	,mcc_study_type VARCHAR(65535)   ENCODE lzo
	,mcc_b2_b_contract_type VARCHAR(65535)   ENCODE lzo
	,mcc_price NUMERIC(19,5)   ENCODE az64
	,mcc_refund NUMERIC(19,5)   ENCODE az64
	,mcc_start_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,mcc_end_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,mcc_state VARCHAR(65535)   ENCODE lzo
	,mcc_status VARCHAR(65535)   ENCODE lzo
	,mcc_cancel_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,mcc_is_active BOOLEAN   ENCODE RAW
	,mcc_created_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,mcc_local_created_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,mcc_last_updated_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,mcc_local_last_updated_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,mcc_is_membership BOOLEAN   ENCODE RAW
	,mcc_is_non_membership BOOLEAN   ENCODE RAW
)
DISTSTYLE AUTO
;
ALTER TABLE analytics.b2b_contracts owner to dwhdevuser;

________________________________________________________________________________________________________________

-- analytics.users definition

-- Drop table

-- DROP TABLE users;

--DROP TABLE analytics.users;
CREATE TABLE IF NOT EXISTS analytics.users
(
	user_id VARCHAR(36)   ENCODE lzo
	,student_reference_id VARCHAR(36)   ENCODE lzo
	,center_id VARCHAR(36)   ENCODE lzo
	,territory_id VARCHAR(36)   ENCODE lzo
	,is_active BOOLEAN   ENCODE RAW
	,user_name VARCHAR(65535)   ENCODE lzo
	,first_name VARCHAR(65535)   ENCODE lzo
	,last_name VARCHAR(65535)   ENCODE lzo
	,birth_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,email VARCHAR(65535)   ENCODE lzo
	,created_at TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,updated_at TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,deleted_at TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,mobile_telephone VARCHAR(65535)   ENCODE lzo
	,"role" VARCHAR(100)   ENCODE lzo
	,role_type VARCHAR(100)   ENCODE lzo
	,center_reference_id VARCHAR(36)   ENCODE lzo
)
DISTSTYLE AUTO
 DISTKEY (user_id)
;
ALTER TABLE analytics.users owner to dwhdevuser;

________________________________________________________________________________________________________________

-- analytics.contracts definition

-- Drop table

-- DROP TABLE contracts;

--DROP TABLE analytics.contracts;
CREATE TABLE IF NOT EXISTS analytics.contracts
(
	contract_id VARCHAR(36)   ENCODE lzo
	,student_id VARCHAR(36)   ENCODE lzo
	,student_reference_id VARCHAR(36)   ENCODE lzo
	,center_id VARCHAR(36)   ENCODE lzo
	,consultant_id VARCHAR(36)   ENCODE lzo
	,lab_teacher_id VARCHAR(36)   ENCODE lzo
	,contract_reference_id VARCHAR(50)   ENCODE lzo
	,master_contract_course_id VARCHAR(36)   ENCODE lzo
	,group_id VARCHAR(36)   ENCODE lzo
	,center_reference_id VARCHAR(36)   ENCODE lzo
	,center_name VARCHAR(256)   ENCODE lzo
	,territory_name VARCHAR(256)   ENCODE lzo
	,start_date DATE   ENCODE az64
	,end_date DATE   ENCODE az64
	,created_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_created_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,last_updated_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_last_updated_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,sale_date DATE   ENCODE az64
	,cancel_date DATE   ENCODE az64
	,code VARCHAR(65535)   ENCODE lzo
	,number VARCHAR(100)   ENCODE lzo
	,crm_contract_number VARCHAR(100)   ENCODE lzo
	,"location" VARCHAR(100)   ENCODE lzo
	,product_type VARCHAR(100)   ENCODE lzo
	,service_type VARCHAR(100)   ENCODE lzo
	,contract_type VARCHAR(100)   ENCODE lzo
	,class_access_type VARCHAR(100)   ENCODE lzo
	,source_type VARCHAR(100)   ENCODE lzo
	,contract_product VARCHAR(100)   ENCODE lzo
	,start_level INTEGER   ENCODE az64
	,end_level INTEGER   ENCODE az64
	,price_local_currency NUMERIC(19,5)   ENCODE az64
	,state VARCHAR(50)   ENCODE lzo
	,status VARCHAR(50)   ENCODE lzo
	,current_validation_state VARCHAR(50)   ENCODE lzo
	,total_number_of_hours INTEGER   ENCODE az64
	,max_no_of_cc_and_sc_classes INTEGER   ENCODE az64
	,refunded_price NUMERIC(19,5)   ENCODE az64
	,is_renewed BOOLEAN   ENCODE RAW
	,is_promotional BOOLEAN   ENCODE RAW
	,is_transfer_in BOOLEAN   ENCODE RAW
	,is_cross_center_booking BOOLEAN   ENCODE RAW
	,is_membership BOOLEAN   ENCODE RAW
	,is_teen BOOLEAN   ENCODE RAW
)
DISTSTYLE AUTO
 DISTKEY (student_id)
;
ALTER TABLE analytics.contracts owner to dwhdevuser;

________________________________________________________________________________________________________________

-- analytics.placement_prospects definition

-- Drop table

-- DROP TABLE placement_prospects;

--DROP TABLE analytics.placement_prospects;
CREATE TABLE IF NOT EXISTS analytics.placement_prospects
(
	prospect_id VARCHAR(256)   ENCODE lzo
	,gradebook_id VARCHAR(256)   ENCODE lzo
	,ssds_reference_id VARCHAR(256)   ENCODE lzo
	,user_reference_id VARCHAR(256)   ENCODE lzo
	,center_id VARCHAR(256)   ENCODE lzo
	,center_reference_id VARCHAR(256)   ENCODE lzo
	,company_id VARCHAR(256)   ENCODE lzo
	,oral_test_id VARCHAR(256)   ENCODE lzo
	,prospect_registration_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,propspect_expiration_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,prospect_created TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,prospect_last_updated TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,gradebook_start_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,gradebook_date_completed TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,gradebook_assignment_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,pre_placement_test_created TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,pre_placement_test_last_updated TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,oral_test_created TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,oral_test_last_updated TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,latest_placement_test_score DOUBLE PRECISION   ENCODE RAW
	,source INTEGER   ENCODE az64
	,source_category VARCHAR(256)   ENCODE lzo
	,trial_enabled BOOLEAN   ENCODE RAW
	,show_placement_test_result BOOLEAN   ENCODE RAW
	,email VARCHAR(256)   ENCODE lzo
	,time_remaining INTEGER   ENCODE az64
	,placement_test_completed BOOLEAN   ENCODE RAW
	,start_level_score INTEGER   ENCODE az64
	,final_level_score INTEGER   ENCODE az64
	,oral_test_level INTEGER   ENCODE az64
	,start_level_category VARCHAR(256)   ENCODE lzo
	,final_level_category VARCHAR(256)   ENCODE lzo
	,oral_test_reason VARCHAR(65535)   ENCODE lzo
	,user_type VARCHAR(256)   ENCODE lzo
	,is_timeout BOOLEAN   ENCODE RAW
	,no_of_tests BIGINT   ENCODE az64
	,duration_with_timeout DOUBLE PRECISION   ENCODE RAW
	,duration_without_timeout DOUBLE PRECISION   ENCODE RAW
)
DISTSTYLE AUTO
;
ALTER TABLE analytics.placement_prospects owner to dwhdevuser;

________________________________________________________________________________________________________________

-- analytics.classes definition

-- Drop table

-- DROP TABLE classes;

--DROP TABLE analytics.classes;
CREATE TABLE IF NOT EXISTS analytics.classes
(
	class_id VARCHAR(65535)   ENCODE lzo
	,class_center_reference_id VARCHAR(65535)   ENCODE lzo
	,class_start_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_local_start_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_end_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_local_end_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_date_student TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_type VARCHAR(65535)   ENCODE lzo
	,class_number_of_seats INTEGER   ENCODE az64
	,class_number_of_seats_in_stand_by INTEGER   ENCODE az64
	,class_description VARCHAR(65535)   ENCODE lzo
	,class_teacher_user_reference_id VARCHAR(65535)   ENCODE lzo
	,class_category_from_booking BOOLEAN   ENCODE RAW
	,class_cancelled_flag BOOLEAN   ENCODE RAW
	,class_communication_account_type VARCHAR(65535)   ENCODE lzo
	,class_source VARCHAR(65535)   ENCODE lzo
	,class_created_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_local_created_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_last_updated_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_local_last_updated_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_created_by VARCHAR(65535)   ENCODE lzo
	,class_created_by_role VARCHAR(65535)   ENCODE lzo
	,class_last_updated_by VARCHAR(65535)   ENCODE lzo
	,class_last_updated_by_role VARCHAR(65535)   ENCODE lzo
	,class_b2b_flag BOOLEAN   ENCODE RAW
	,class_visible_in_group BOOLEAN   ENCODE RAW
	,class_teen_flag BOOLEAN   ENCODE RAW
	,class_code VARCHAR(65535)   ENCODE lzo
	,class_service_type VARCHAR(65535)   ENCODE lzo
	,class_category VARCHAR(65535)   ENCODE lzo
	,class_category_type VARCHAR(65535)   ENCODE lzo
	,class_type_billable BOOLEAN   ENCODE RAW
)
DISTSTYLE AUTO
;
ALTER TABLE analytics.classes owner to dwhdevuser;

________________________________________________________________________________________________________________

-- analytics.placement_test_score definition

-- Drop table

-- DROP TABLE placement_test_score;

--DROP TABLE analytics.placement_test_score;
CREATE TABLE IF NOT EXISTS analytics.placement_test_score
(
	prospect_id VARCHAR(256)   ENCODE lzo
	,created_at TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,updated_at TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,level_number INTEGER   ENCODE az64
	,activity_title VARCHAR(256)   ENCODE lzo
	,activity_duration INTEGER   ENCODE az64
	,activity_interaction_row_number INTEGER   ENCODE az64
	,interaction_type VARCHAR(256)   ENCODE lzo
	,test_row_number BIGINT   ENCODE az64
	,result_score DOUBLE PRECISION   ENCODE RAW
	,level_is_active BOOLEAN   ENCODE RAW
	,activity_is_active BOOLEAN   ENCODE RAW
	,activity_is_skippable BOOLEAN   ENCODE RAW
	,interaction_is_active BOOLEAN   ENCODE RAW
	,level_id VARCHAR(256)   ENCODE lzo
	,content_id VARCHAR(256)   ENCODE lzo
	,activity_id VARCHAR(256)   ENCODE lzo
	,interaction_id VARCHAR(256)   ENCODE lzo
	,result_id VARCHAR(256)   ENCODE lzo
	,interaction_result_id VARCHAR(256)   ENCODE lzo
)
DISTSTYLE AUTO
;
ALTER TABLE analytics.placement_test_score owner to dwhdevuser;

________________________________________________________________________________________________________________

-- analytics.bookings definition

-- Drop table

-- DROP TABLE bookings;

--DROP TABLE analytics.bookings;
CREATE TABLE IF NOT EXISTS analytics.bookings
(
	class_id VARCHAR(40)   ENCODE lzo
	,ref_class_id VARCHAR(40)   ENCODE lzo
	,booking_id VARCHAR(40)   ENCODE lzo
	,student_id VARCHAR(40)   ENCODE lzo
	,student_reference_id VARCHAR(40)   ENCODE lzo
	,center_reference_id VARCHAR(40)   ENCODE lzo
	,booking_territory_id VARCHAR(40)   ENCODE lzo
	,book_mode VARCHAR(10)   ENCODE lzo
	,booking_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_booking_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_created_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_local_created_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_last_updated_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_local_last_updated_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booked_by VARCHAR(40)   ENCODE lzo
	,booked_role_title VARCHAR(50)   ENCODE lzo
	,booking_person_type VARCHAR(50)   ENCODE lzo
	,booking_cancelled_by VARCHAR(40)   ENCODE lzo
	,booking_cancelled_role_title VARCHAR(50)   ENCODE lzo
	,booking_cancelled_person_type VARCHAR(50)   ENCODE lzo
	,booking_cancelled_flag BOOLEAN   ENCODE RAW
	,auto_cancel_type VARCHAR(20)   ENCODE lzo
	,booking_mode_modified_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_mode_modified_local_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_stand_by_notification_type VARCHAR(50)   ENCODE lzo
	,booking_accessed_flag BOOLEAN   ENCODE RAW
	,booked_student_contract_id VARCHAR(40)   ENCODE lzo
	,booked_student_contract_reference_id VARCHAR(40)   ENCODE lzo
	,booking_order_desc BIGINT   ENCODE az64
	,standby_to_booked_flag BOOLEAN   ENCODE RAW
	,standby_to_booked_24hrs INTEGER   ENCODE az64
	,cancellations_12hrs INTEGER   ENCODE az64
	,cancellations_24hrs INTEGER   ENCODE az64
	,cancellations_12hrs_not_sbtb_24hrs INTEGER   ENCODE az64
	,class_close_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_result_id VARCHAR(40)   ENCODE lzo
	,class_encounter_level VARCHAR(10)   ENCODE lzo
	,class_encounter_unit VARCHAR(10)   ENCODE lzo
	,class_result_class_type VARCHAR(10)   ENCODE lzo
	,class_result VARCHAR(50)   ENCODE lzo
	,class_comments VARCHAR(65535)   ENCODE lzo
	,class_result_created_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_access_type VARCHAR(50)   ENCODE lzo
    ,self_booking_access_flag BOOLEAN   ENCODE RAW
)
DISTSTYLE KEY
 DISTKEY (student_id)
;
ALTER TABLE analytics.bookings owner to dwhdevuser;

________________________________________________________________________________________________________________

-- analytics.class_bookings definition

-- Drop table

-- DROP TABLE class_bookings;

--DROP TABLE analytics.class_bookings;
CREATE TABLE IF NOT EXISTS analytics.class_bookings
(
	class_booking_id VARCHAR(100)   ENCODE lzo
	,class_id VARCHAR(40)   ENCODE lzo
	,class_center_reference_id VARCHAR(40)   ENCODE lzo
	,class_start_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_local_start_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_end_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_local_end_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_close_date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,student_local_start_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_type VARCHAR(50)   ENCODE lzo
	,class_code VARCHAR(50)   ENCODE lzo
	,class_number_of_seats INTEGER   ENCODE az64
	,class_number_of_seats_in_stand_by INTEGER   ENCODE az64
	,class_description VARCHAR(65535)   ENCODE lzo
	,class_teacher_user_reference_id VARCHAR(40)   ENCODE lzo
	,class_category_from_booking BOOLEAN   ENCODE RAW
	,class_cancelled_flag BOOLEAN   ENCODE RAW
	,class_communication_account_type VARCHAR(50)   ENCODE lzo
	,class_source VARCHAR(50)   ENCODE lzo
	,class_created_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_local_created_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_last_updated_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_local_last_updated_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_created_by VARCHAR(40)   ENCODE lzo
	,class_created_by_role VARCHAR(50)   ENCODE lzo
	,class_last_updated_by VARCHAR(40)   ENCODE lzo
	,class_last_updated_by_role VARCHAR(50)   ENCODE lzo
	,class_b2b_flag BOOLEAN   ENCODE RAW
	,class_visible_in_group BOOLEAN   ENCODE RAW
	,class_teen_flag BOOLEAN   ENCODE RAW
	,class_online_flag BOOLEAN   ENCODE RAW
	,class_service_type VARCHAR(50)   ENCODE lzo
	,class_category VARCHAR(65535)   ENCODE lzo
	,class_category_type VARCHAR(50)   ENCODE lzo
	,class_type_billable BOOLEAN   ENCODE RAW
	,booking_id VARCHAR(40)   ENCODE lzo
	,student_id VARCHAR(40)   ENCODE lzo
	,student_reference_id VARCHAR(40)   ENCODE lzo
	,booking_center_reference_id VARCHAR(40)   ENCODE lzo
	,booking_territory_id VARCHAR(40)   ENCODE lzo
	,book_mode VARCHAR(10)   ENCODE lzo
	,booking_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,local_booking_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_created_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_local_created_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_last_updated_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_local_last_updated_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_cancelled_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_local_cancelled_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booked_by_id VARCHAR(40)   ENCODE lzo
	,booked_by_role VARCHAR(50)   ENCODE lzo
	,booked_by VARCHAR(40)   ENCODE lzo
	,booking_cancelled_by_id VARCHAR(40)   ENCODE lzo
	,booking_cancelled_by_role VARCHAR(50)   ENCODE lzo
	,booking_cancelled_by VARCHAR(40)   ENCODE lzo
	,booking_cancelled_flag BOOLEAN   ENCODE RAW
	,booking_mode_modified_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_mode_modified_local_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,booking_stand_by_notification_type VARCHAR(50)   ENCODE lzo
	,booked_student_contract_id VARCHAR(40)   ENCODE lzo
	,booked_student_contract_reference_id VARCHAR(40)   ENCODE lzo
	,standby_to_booked_flag BOOLEAN   ENCODE RAW
	,standby_to_booked_24hrs INTEGER   ENCODE az64
	,cancellations_12hrs_flag BOOLEAN   ENCODE RAW
	,cancellations_24hrs_flag BOOLEAN   ENCODE RAW
	,cancellations_12hrs_not_sbtb_24hrs INTEGER   ENCODE az64
	,class_result_id VARCHAR(40)   ENCODE lzo
	,class_encounter_level VARCHAR(10)   ENCODE lzo
	,class_encounter_unit VARCHAR(10)   ENCODE lzo
	,class_result_class_type VARCHAR(10)   ENCODE lzo
	,class_result VARCHAR(50)   ENCODE lzo
	,class_comments VARCHAR(65535)   ENCODE lzo
	,class_price DOUBLE PRECISION   ENCODE RAW
	,attended_flag BOOLEAN   ENCODE RAW
	,technology_platform_staff_flag BOOLEAN   ENCODE RAW
	,technology_staff_flag BOOLEAN   ENCODE RAW
	,technology_student_flag BOOLEAN   ENCODE RAW
	,no_show_flag BOOLEAN   ENCODE RAW
	,late_cancellation_flag BOOLEAN   ENCODE RAW
	,billable_flag BOOLEAN   ENCODE RAW
	,class_result_created_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,class_access_type VARCHAR(50)   ENCODE lzo
    ,self_booking_access_flag BOOLEAN   ENCODE RAW
	,categories_abbreviations VARCHAR(328)   ENCODE lzo
	,ready_flag BOOLEAN   ENCODE RAW
	,lead_booked_datetime TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,rescheduled_flag BOOLEAN   ENCODE RAW
	,mm_ready_flag BOOLEAN   ENCODE RAW
	,wb_ready_flag BOOLEAN   ENCODE RAW
	,mm_ready TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,mm_complete_66 TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,mm_complete_33 TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,mm_complete_0 TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,wb_ready_80 TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,wb_complete_66 TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,wb_complete_33 TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,wb_complete_0 TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	
)
DISTSTYLE AUTO
DISTKEY (student_id)
;
ALTER TABLE analytics.class_bookings owner to dwhdevuser;
________________________________________________________________________________________________________________   

-- analytics.contracts_changes definition

-- Drop table

-- DROP TABLE contracts_changes;

--DROP TABLE analytics.contracts_changes;
CREATE TABLE IF NOT EXISTS analytics.contracts_changes
(
	contract_id VARCHAR(256)   ENCODE lzo
	,student_id VARCHAR(256)   ENCODE lzo
	,center_id VARCHAR(256)   ENCODE lzo
	,group_id VARCHAR(256)   ENCODE lzo
	,lab_teacher_id VARCHAR(256)   ENCODE lzo
	,consultant_id VARCHAR(256)   ENCODE lzo
	,valid_from DATE   ENCODE az64
	,valid_to DATE   ENCODE az64
	,status VARCHAR(256)   ENCODE lzo
	,"location" VARCHAR(256)   ENCODE lzo
	,class_access_type VARCHAR(256)   ENCODE lzo
	,service_type VARCHAR(256)   ENCODE lzo
	,is_membership BOOLEAN   ENCODE RAW
	
)
DISTSTYLE AUTO
 DISTKEY (lab_teacher_id)
;
ALTER TABLE analytics.contracts_changes owner to dwhdevuser;

________________________________________________________________________________________________________________

-- reporting.dim_time definition

-- Drop table

-- DROP TABLE dim_time;

--DROP TABLE reporting.dim_time;
CREATE TABLE IF NOT EXISTS reporting.dim_time
(
	hour_24 DOUBLE PRECISION   ENCODE RAW
	,hour_12 DOUBLE PRECISION   ENCODE RAW
	,"minute" BIGINT   ENCODE az64
	,am_pm VARCHAR(2)   ENCODE lzo
	,full_time VARCHAR(20)   ENCODE lzo
)
DISTSTYLE AUTO
;
ALTER TABLE reporting.dim_time owner to dwhdevuser;

________________________________________________________________________________________________________________

-- reporting.dim_center definition

-- Drop table

-- DROP TABLE dim_center;

--DROP TABLE reporting.dim_center;
CREATE TABLE IF NOT EXISTS reporting.dim_center
(
	territory_reference_id VARCHAR(65535)   ENCODE lzo
	,territory_name VARCHAR(65535)   ENCODE lzo
	,center_reference_id VARCHAR(65535)   ENCODE lzo
	,center_name VARCHAR(65535)   ENCODE lzo
	,timezone VARCHAR(65535)   ENCODE lzo
	,"region" VARCHAR(65535)   ENCODE lzo
	,group_name VARCHAR(65535)   ENCODE lzo
	,"matched" VARCHAR(65535)   ENCODE lzo
	,fin_id VARCHAR(65535)   ENCODE lzo
	,center_id VARCHAR(65535)   ENCODE lzo
	,goc_local VARCHAR(65535)   ENCODE lzo
)
DISTSTYLE AUTO
 DISTKEY (fin_id)
;
ALTER TABLE reporting.dim_center owner to dwhdevuser;

________________________________________________________________________________________________________________

-- reporting.fact_contracts_by_territory definition

-- Drop table

-- DROP TABLE fact_contracts_by_territory;

--DROP TABLE reporting.fact_contracts_by_territory;
CREATE TABLE IF NOT EXISTS reporting.fact_contracts_by_territory
(
	year_month_date DATE   ENCODE az64
	,territory_name VARCHAR(256)   ENCODE lzo
	,students BIGINT   ENCODE az64
	,offline_only BIGINT   ENCODE az64
	,full_access BIGINT   ENCODE az64
	,online_only BIGINT   ENCODE az64
	,online_access BIGINT   ENCODE az64
)
DISTSTYLE AUTO
;
ALTER TABLE reporting.fact_contracts_by_territory owner to dwhdevuser;

________________________________________________________________________________________________________________

-- reporting.dim_calendar definition

-- Drop table

-- DROP TABLE dim_calendar;

--DROP TABLE reporting.dim_calendar;
CREATE TABLE IF NOT EXISTS reporting.dim_calendar
(
	date_key INTEGER   ENCODE az64
	,date DATE   ENCODE az64
	,"year" INTEGER   ENCODE az64
	,quarter INTEGER   ENCODE az64
	,"month" INTEGER   ENCODE az64
	,year_week VARCHAR(10)   ENCODE lzo
	,year_week_key VARCHAR(8)   ENCODE lzo
	,year_month VARCHAR(7)   ENCODE lzo
	,year_month_key VARCHAR(6)   ENCODE lzo
	,first_month_date VARCHAR(10)   ENCODE lzo
	,last_month_date VARCHAR(10)   ENCODE lzo
	,first_week_date VARCHAR(10)   ENCODE lzo
	,last_week_date VARCHAR(10)   ENCODE lzo
	,month_name VARCHAR(13)   ENCODE lzo
	,week DOUBLE PRECISION   ENCODE RAW
	,day_of_week INTEGER   ENCODE az64
	,day_of_week_name VARCHAR(11)   ENCODE lzo
	,day_of_month INTEGER   ENCODE az64
	,day_of_year INTEGER   ENCODE az64
	,is_weekday BOOLEAN   ENCODE RAW
	,is_weekend BOOLEAN   ENCODE RAW
)
DISTSTYLE AUTO
;
ALTER TABLE reporting.dim_calendar owner to dwhdevuser;

________________________________________________________________________________________________________________

-- reporting.fact_goc_by_territory definition

-- Drop table

-- DROP TABLE fact_goc_by_territory;

--DROP TABLE reporting.fact_goc_by_territory;
CREATE TABLE IF NOT EXISTS reporting.fact_goc_by_territory
(
	class_month VARCHAR(10)   ENCODE lzo
	,territory_name VARCHAR(256)   ENCODE lzo
	,goc_bookings BIGINT   ENCODE az64
	,goc_students BIGINT   ENCODE az64
	,goc_users BIGINT   ENCODE az64
	,goc_users_bookings BIGINT   ENCODE az64
	,ec_revenue DOUBLE PRECISION   ENCODE RAW
	,sc_revenue DOUBLE PRECISION   ENCODE RAW
	,cc_revenue DOUBLE PRECISION   ENCODE RAW
	,goc_revenue DOUBLE PRECISION   ENCODE RAW
	,encounters_bookings BIGINT   ENCODE az64
	,sc_bookings BIGINT   ENCODE az64
	,cc_bookings BIGINT   ENCODE az64
	,frequency_encounters DOUBLE PRECISION   ENCODE RAW
	,frequency_sc DOUBLE PRECISION   ENCODE RAW
	,frequency_cc DOUBLE PRECISION   ENCODE RAW
	,attendance BIGINT   ENCODE az64
	,no_shows BIGINT   ENCODE az64
	,late_cancellation BIGINT   ENCODE az64
	,tech_repeat BIGINT   ENCODE az64
)
DISTSTYLE AUTO
;
ALTER TABLE reporting.fact_goc_by_territory owner to dwhdevuser;

________________________________________________________________________________________________________________

-- reporting.fact_goc definition

-- Drop table

-- DROP TABLE fact_goc;

--DROP TABLE reporting.fact_goc;
CREATE TABLE IF NOT EXISTS reporting.fact_goc
(
	class_month VARCHAR(10)   ENCODE lzo
	,goc_classes BIGINT   ENCODE az64
	,goc_bookings BIGINT   ENCODE az64
	,goc_students BIGINT   ENCODE az64
	,goc_users BIGINT   ENCODE az64
	,goc_users_bookings BIGINT   ENCODE az64
	,ec_revenue DOUBLE PRECISION   ENCODE RAW
	,sc_revenue DOUBLE PRECISION   ENCODE RAW
	,cc_revenue DOUBLE PRECISION   ENCODE RAW
	,goc_revenue DOUBLE PRECISION   ENCODE RAW
	,encounters BIGINT   ENCODE az64
	,scs BIGINT   ENCODE az64
	,ccs BIGINT   ENCODE az64
	,encounters_bookings BIGINT   ENCODE az64
	,sc_bookings BIGINT   ENCODE az64
	,cc_bookings BIGINT   ENCODE az64
	,frequency_encounters DOUBLE PRECISION   ENCODE RAW
	,frequency_sc DOUBLE PRECISION   ENCODE RAW
	,frequency_cc DOUBLE PRECISION   ENCODE RAW
	,attendance BIGINT   ENCODE az64
	,no_shows BIGINT   ENCODE az64
	,late_cancellation BIGINT   ENCODE az64
	,tech_repeat BIGINT   ENCODE az64
)
DISTSTYLE AUTO
;
ALTER TABLE reporting.fact_goc owner to dwhdevuser;

________________________________________________________________________________________________________________

-- reporting.fact_student_progress definition

-- Drop table

-- DROP TABLE fact_student_progress;

--DROP TABLE reporting.fact_student_progress;
CREATE TABLE IF NOT EXISTS reporting.fact_student_progress
(
	territory_name VARCHAR(100)   ENCODE lzo
	,center_name VARCHAR(100)   ENCODE lzo
	,date TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,contract_type VARCHAR(100)   ENCODE lzo
	,product_type VARCHAR(100)   ENCODE lzo
	,"location" VARCHAR(100)   ENCODE lzo
	,service_type VARCHAR(100)   ENCODE lzo
	,billing_code VARCHAR(100)   ENCODE lzo
	,unlock_type VARCHAR(100)   ENCODE lzo
	,workbook_type VARCHAR(100)   ENCODE lzo
	,operation_type VARCHAR(100)   ENCODE lzo
	,is_restart BOOLEAN   ENCODE RAW
	,first_later VARCHAR(20)   ENCODE lzo
	,levels_started BIGINT   ENCODE az64
	,levels_started_teens BIGINT   ENCODE az64
	,levels_started_not_teens BIGINT   ENCODE az64
)
DISTSTYLE AUTO
;
ALTER TABLE reporting.fact_student_progress owner to dwhdevuser;

________________________________________________________________________________________________________________

-- external_sources.goc_budget definition

-- Drop table

-- DROP TABLE goc_budget;

--DROP TABLE external_sources.goc_budget;
CREATE TABLE IF NOT EXISTS external_sources.goc_budget
(
	year_month VARCHAR(8)   ENCODE lzo
	,territory_name VARCHAR(256)   ENCODE lzo
	,budget BIGINT   ENCODE az64
)
DISTSTYLE AUTO
;
ALTER TABLE external_sources.goc_budget owner to dwhdevuser;

________________________________________________________________________________________________________________

-- external_sources.center_group definition

-- Drop table

-- DROP TABLE center_group;

--DROP TABLE external_sources.center_group;
CREATE TABLE IF NOT EXISTS external_sources.center_group
(
	territoryname VARCHAR(40)   ENCODE lzo
	,centername VARCHAR(256)   ENCODE lzo
	,centerid VARCHAR(40)   ENCODE lzo
	,finid VARCHAR(40)   ENCODE lzo
	,referencecenterid VARCHAR(40)   ENCODE lzo
	,"matched" INTEGER   ENCODE az64
	,groupname VARCHAR(40)   ENCODE lzo
)
DISTSTYLE AUTO
;
ALTER TABLE external_sources.center_group owner to dwhdevuser;

________________________________________________________________________________________________________________

-- external_sources.class_pricing_center definition

-- Drop table

-- DROP TABLE class_pricing_center;

--DROP TABLE external_sources.class_pricing_center;
CREATE TABLE IF NOT EXISTS external_sources.class_pricing_center
(
	territory VARCHAR(256)   ENCODE lzo
	,territoryid VARCHAR(256)   ENCODE lzo
	,centername VARCHAR(256)   ENCODE lzo
	,centerid VARCHAR(256)   ENCODE lzo
	,referencecenterid VARCHAR(256)   ENCODE lzo
	,classtype VARCHAR(256)   ENCODE lzo
	,classcode VARCHAR(256)   ENCODE lzo
	,price DOUBLE PRECISION   ENCODE RAW
	,validfrom TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,validto TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
)
DISTSTYLE AUTO
;
ALTER TABLE external_sources.class_pricing_center owner to dwhdevuser;

________________________________________________________________________________________________________________

-- external_sources.class_pricing definition

-- Drop table

-- DROP TABLE class_pricing;

--DROP TABLE external_sources.class_pricing;
CREATE TABLE IF NOT EXISTS external_sources.class_pricing
(
	territory VARCHAR(256)   ENCODE lzo
	,territoryid VARCHAR(256)   ENCODE lzo
	,classtype VARCHAR(256)   ENCODE lzo
	,classcode VARCHAR(256)   ENCODE lzo
	,price DOUBLE PRECISION   ENCODE RAW
	,validfrom TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
	,validto TIMESTAMP WITHOUT TIME ZONE   ENCODE az64
)
DISTSTYLE AUTO
;
ALTER TABLE external_sources.class_pricing owner to dwhdevuser;