{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_contract_validations'
        ) }}

    {% if is_incremental() %}
        where last_updated_date > ((select max(last_updated_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    contvalidation.id  as id,
    contvalidation.contract_id as contract_id,
    contvalidation.master_contract_id as master_contract_id,
    contvalstate.name as state,
    contvalidation.signature_id as signature_id,
    contvalidation.document_id as document_id,
    contdoctype.name as document_type,
    contvalidation.created_date as created_date,
    {{convert_to_local_timestamp('contvalidation.created_date','time_zone_id')}} as local_created_date,
    contvalidation.last_updated_date as last_updated_date,
    {{convert_to_local_timestamp('contvalidation.last_updated_date','time_zone_id')}} as local_last_updated_date,
    contvalidation.reason
from ods_data as contvalidation
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_contract_validation_state' ) }}
    ) as contvalstate on contvalidation.state = contvalstate.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_contract_document_types' ) }}
    ) as contdoctype on contvalidation.document_type = contdoctype.id
    left join (
                select id,
                    center_id
                from {{ ref( 'ods_cs_contracts' ) }}
            ) as cont on contvalidation.contract_id = cont.id
    left join (
        select id,
            center_reference_id 
        from {{ ref( 'ods_cs_centers' ) }}
    ) as center on center.id = cont.center_id
    left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on center.center_reference_id = tz.center_reference_id