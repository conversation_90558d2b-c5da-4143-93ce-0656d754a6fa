version: 2

models:
  - name: dt_cc_center
    description: >
      Transformed center data from the Center Configuration Service. Contains information about
      education centers including reference IDs, names, and configuration settings.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: id
        description: Primary key for the center record
      - name: center_reference_id
        description: External reference ID for the center
      - name: name
        description: Name of the center
      - name: territory_id
        description: Foreign key reference to the territory
      - name: first_day_of_the_week
        description: Numeric representation of the first day of the week for this center (0=Sunday, 1=Monday, etc.)
      - name: has_deluxe_service
        description: Boolean flag indicating if the center offers deluxe service
      - name: has_vip_service
        description: Boolean flag indicating if the center offers VIP service
      - name: is_active
        description: Boolean flag indicating if the center is currently active
      - name: is_daylight_saving
        description: Boolean flag indicating if the center observes daylight saving time
      - name: is_online_center
        description: Boolean flag indicating if the center is an online-only center
      - name: is_twenty_four_hour_format
        description: Boolean flag indicating if the center uses 24-hour time format
      - name: time_zone_id
        description: Foreign key reference to the timezone

  - name: dt_cc_timezone
    description: >
      Transformed timezone data from the Center Configuration Service. Contains timezone information
      used by centers and territories for scheduling and time-based operations.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: id
        description: Primary key for the timezone record
      - name: description
        description: Description or name of the timezone

  - name: dt_cs_centers
    description: >
      Transformed center data from the Contract Service. Contains information about centers
      registered in the contract system.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: id
        description: Primary key for the center record
      - name: center_reference_id
        description: External reference ID for the center
      - name: name
        description: Name of the center
      - name: territory_id
        description: Foreign key reference to the territory
      - name: has_pilot
        description: Boolean flag indicating if the center is a pilot center
      - name: time_zone_id
        description: Foreign key reference to the timezone
      - name: has_digital_validation
        description: Boolean flag indicating if the center uses digital validation

  - name: dt_cs_contracts
    description: >
      Transformed contract data from the Contract Service. Contains information about individual
      student contracts with details about services, pricing, and status.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: id
        description: Primary key for the contract record
      - name: code
        description: Code identifier for the contract
      - name: number
        description: Contract number
      - name: crm_contract_number
        description: Contract number in the CRM system
      - name: producttype
        description: Type of product associated with the contract
      - name: service_type
        description: Type of service associated with the contract
      - name: student_id
        description: Foreign key reference to the student
      - name: center_id
        description: Foreign key reference to the center
      - name: start_date
        description: Start date for the contract
      - name: end_date
        description: End date for the contract
      - name: start_level
        description: Starting level for the student
      - name: end_level
        description: Target ending level for the student
      - name: price
        description: Price of the contract
      - name: state
        description: Current state of the contract
      - name: status
        description: Current status of the contract
      - name: contract_type
        description: Type of contract
      - name: local_created_date
        description: Local timestamp when the contract was created
      - name: local_last_updated_date
        description: Local timestamp when the contract was last updated

  - name: dt_ls_center
    description: >
      Transformed center data from the Learning Service. Contains information about education
      centers in the learning system.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: id
        description: Primary key for the center record
      - name: reference_center_id
        description: External reference ID for the center
      - name: name
        description: Name of the center
      - name: territory_id
        description: Foreign key reference to the territory
      - name: zone
        description: Zone where the center is located
      - name: center_number
        description: Center number identifier
      - name: created
        description: Timestamp when the center record was created
      - name: local_created
        description: Local timestamp when the center record was created
      - name: timezone
        description: Name of the timezone for the center
      - name: address1
        description: First line of the center's address
      - name: address2
        description: Second line of the center's address
      - name: city
        description: City where the center is located
      - name: postal_code
        description: Postal code for the center's address
      - name: primary_phone
        description: Primary phone number for the center
      - name: email
        description: Email address for the center
      - name: state
        description: State or province where the center is located
      - name: country
        description: Country where the center is located
      - name: maximum_students_in_standby
        description: Maximum number of students allowed in standby
      - name: is_standby_enabled
        description: Boolean flag indicating if standby is enabled
      - name: last_updated
        description: Timestamp when the center record was last updated
      - name: local_last_updated
        description: Local timestamp when the center record was last updated

  - name: dt_ls_student
    description: >
      Transformed student data from the Learning Service. Contains information about students
      in the learning system.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: id
        description: Primary key for the student record
      - name: user_id
        description: Foreign key reference to the user
      - name: student_reference_id
        description: External reference ID for the student
      - name: center_id
        description: Foreign key reference to the center
      - name: created
        description: Timestamp when the student record was created
      - name: local_created
        description: Local timestamp when the student record was created
      - name: last_updated
        description: Timestamp when the student record was last updated
      - name: local_last_updated
        description: Local timestamp when the student record was last updated

  - name: dt_ls_user
    description: >
      Transformed user data from the Learning Service. Contains information about users
      in the learning system.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: user_id
        description: Primary key for the user record
      - name: user_name
        description: Username for system login
      - name: password
        description: Hashed password for authentication
      - name: first_name
        description: User's first name
      - name: last_name
        description: User's last name
      - name: role
        description: User's role in the system
      - name: ssds_id
        description: SSDS identifier for the user
      - name: email
        description: User's email address
      - name: gender
        description: User's gender
      - name: birth_date
        description: User's date of birth
      - name: created
        description: Timestamp when the user record was created
      - name: local_created
        description: Local timestamp when the user record was created
      - name: last_updated
        description: Timestamp when the user record was last updated
      - name: local_last_updated
        description: Local timestamp when the user record was last updated
      - name: is_email_verified
        description: Boolean flag indicating if the user's email is verified
      - name: center_id
        description: Foreign key reference to the center
      - name: territory_id
        description: Foreign key reference to the territory

  - name: dt_snb_scheduled_class
    description: >
      Transformed scheduled class data from the Schedule and Booking Service. Contains information
      about scheduled classes with their timing, location, and configuration details.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: id
        description: Primary key for the scheduled class record
      - name: start_time
        description: Start time of the scheduled class
      - name: end_time
        description: End time of the scheduled class
      - name: number_of_seats
        description: Number of available seats in the class
      - name: category_from_booking
        description: Category associated with the booking
      - name: is_cancelled
        description: Boolean flag indicating if the class is cancelled
      - name: communication_account_type
        description: Type of communication account used (e.g., webex, zoom)
      - name: created
        description: Timestamp when the scheduled class was created
      - name: last_updated
        description: Timestamp when the scheduled class was last updated
      - name: no_of_seats_in_standby
        description: Number of seats available for standby
      - name: center_reference_id
        description: Reference ID for the center where the class is scheduled
      - name: class_type
        description: Type of class being scheduled
      - name: description
        description: Description of the scheduled class
      - name: teacher_id
        description: ID of the teacher assigned to the class
      - name: source
        description: Source of the scheduled class record
      - name: created_by
        description: ID of the user who created the class
      - name: last_updated_by
        description: ID of the user who last updated the class

  - name: dt_cai_conversation
    description: >
      Transformed conversation data from the Conversation AI Service. Contains information about
      AI-powered conversations between users and the system.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: chat_id
        description: Unique identifier for the chat session
      - name: content_id
        description: Identifier for the content being discussed
      - name: gpt_4o_mini_cost
        description: Cost of using the GPT-4o mini model for this conversation
      - name: hasEnded
        description: Boolean flag indicating if the conversation has ended
      - name: start_date
        description: ISO timestamp when the conversation started
      - name: end_date
        description: ISO timestamp when the conversation ended
      - name: local_end_date
        description: Local timestamp when the conversation ended
      - name: local_start_date
        description: Local timestamp when the conversation started
      - name: total_input_tokens
        description: Total number of input tokens used in the conversation
      - name: total_output_tokens
        description: Total number of output tokens generated in the conversation
      - name: user_id
        description: ID of the user participating in the conversation
      - name: contract_id
        description: ID of the contract associated with the user
      - name: message_index
        description: Index of the message in the conversation
      - name: message_interaction_id
        description: ID of the message interaction
      - name: message_role
        description: Role of the message sender (user or assistant)
      - name: message_outputtokens
        description: Number of output tokens for this message
      - name: message_inputtokens
        description: Number of input tokens for this message
      - name: message_content
        description: Content of the message
      - name: message_audio_duration
        description: Duration of audio in the message, if applicable

  - name: dt_novu_messages
    description: >
      Transformed message data from the Novu notification service. Contains information about
      individual messages sent to subscribers through various communication channels.
    columns:
      - name: message_id
        description: Primary key for the message record
      - name: student_reference_id
        description: Reference ID for the student receiving the message
      - name: channel
        description: Communication channel used for the message (email, SMS, push, etc.)
      - name: title
        description: Title or subject of the message
      - name: notification_id
        description: ID of the notification template
      - name: notification_type
        description: Type of notification
      - name: variant_id
        description: ID of the message template variant used
      - name: variant
        description: Name of the variant used
      - name: created_at
        description: Timestamp when the message was created
      - name: updated_at
        description: Timestamp when the message was last updated

  - name: dt_novu_jobs
    description: >
      Transformed job data from the Novu notification service. Contains information about
      notification jobs that track the delivery status and processing of messages.
    columns:
      - name: message_id
        description: Primary key for the job record
      - name: student_reference_id
        description: Reference ID for the student receiving the notification
      - name: channel
        description: Communication channel used for the job
      - name: title
        description: Title or subject of the notification
      - name: notification_id
        description: ID of the notification template
      - name: notification_type
        description: Type of notification
      - name: variant_id
        description: ID of the message template variant used
      - name: variant
        description: Name of the variant used
      - name: created_at
        description: Timestamp when the job was created
      - name: updated_at
        description: Timestamp when the job was last updated

  - name: dt_sai_feedback
    description: >
      Transformed feedback data from the Speaking AI Beta Service. Contains user feedback
      data collected after speaking practice sessions.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: chat_id
        description: Unique identifier for the chat session
      - name: user_id
        description: ID of the user who provided the feedback
      - name: user_type
        description: Type of user providing the feedback
      - name: feedback
        description: Feedback content provided by the user
      - name: completed_date
        description: ISO timestamp when the feedback was completed
      - name: local_completed_date
        description: Local timestamp when the feedback was completed

  - name: dt_sai_conversation
    description: >
      Transformed conversation data from the Speaking AI Beta Service. Contains raw conversation
      data from speaking practice sessions with the AI system.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: chat_id
        description: Unique identifier for the chat session
      - name: user_id
        description: ID of the user participating in the session
      - name: user_type
        description: Type of user (e.g., student, prospect)
      - name: duration
        description: Duration of the chat session in seconds
      - name: gpt4o_mini_cost
        description: Cost of using the GPT-4o mini model for this session
      - name: has_ended
        description: Boolean flag indicating if the chat has ended
      - name: level
        description: Language proficiency level for the session
      - name: start_date
        description: ISO timestamp when the session started
      - name: local_start_date
        description: Local timestamp when the session started
      - name: end_date
        description: ISO timestamp when the session ended
      - name: local_end_date
        description: Local timestamp when the session ended
      - name: total_input_tokens
        description: Total number of input tokens used in the session
      - name: total_output_tokens
        description: Total number of output tokens generated in the session

  - name: dt_dsw_activity_progress
    description: >
      Transformed activity progress data from the Digital Student Workbook Service. Contains
      progress records for student activities in digital workbooks.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: id
        description: Primary key for the activity progress record
      - name: student_id
        description: Foreign key reference to the student
      - name: course_content_id
        description: Foreign key reference to the course content
      - name: level
        description: Level associated with the activity
      - name: unit
        description: Unit associated with the activity
      - name: lesson
        description: Lesson associated with the activity
      - name: progress
        description: Progress percentage or status
      - name: score
        description: Score achieved on the activity
      - name: created_date
        description: Timestamp when the progress record was created
      - name: local_created_date
        description: Local timestamp when the progress record was created
      - name: last_updated_date
        description: Timestamp when the progress record was last updated
      - name: local_last_updated_date
        description: Local timestamp when the progress record was last updated
      - name: study_mode
        description: Mode of study (e.g., incenter, mobile)

  - name: dt_idam_user_basic_info
    description: >
      Transformed user basic information from the Identity and Access Management (IDAM) Service.
      Contains core user information including authentication details and basic profile data.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: user_id
        description: Primary key for the user record
      - name: gender
        description: User's gender
      - name: birth_date
        description: User's date of birth
      - name: created
        description: Timestamp when the user record was created
      - name: last_updated
        description: Timestamp when the user record was last updated
      - name: is_active
        description: Boolean flag indicating if the user is active
      - name: enable_password_reset
        description: Boolean flag indicating if password reset is enabled
      - name: is_prospect
        description: Boolean flag indicating if the user is a prospect
      - name: username
        description: Username for system login
      - name: first_name
        description: User's first name
      - name: last_name
        description: User's last name
      - name: email
        description: User's email address
      - name: ssds_id
        description: SSDS identifier for the user
      - name: center_id
        description: Foreign key reference to the user's center
      - name: territory_id
        description: Foreign key reference to the user's territory
      - name: user_external_id
        description: External identifier for the user

  - name: dt_idam_user_additional_info
    description: >
      Transformed user additional information from the Identity and Access Management (IDAM) Service.
      Contains additional information and preferences for users in the system.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: id
        description: Primary key for the user additional info record
      - name: is_email_verified
        description: Boolean flag indicating if the user's email is verified
      - name: created
        description: Timestamp when the record was created
      - name: last_updated
        description: Timestamp when the record was last updated
      - name: is_active
        description: Boolean flag indicating if the record is active
      - name: send_mail_preference
        description: User's preference for receiving emails
      - name: preferred_contact_method
        description: User's preferred method of contact
      - name: user_basic_info_id
        description: Foreign key reference to the user basic info
      - name: social_network_id1
        description: ID of the first social network
      - name: social_network_address1
        description: Address/handle for the first social network
      - name: mobile_telephone
        description: Mobile telephone number
      - name: home_telephone
        description: Home telephone number
      - name: work_telephone
        description: Work telephone number
      - name: address1
        description: First line of address
      - name: address2
        description: Second line of address
      - name: city
        description: City
      - name: state
        description: State or province
      - name: postal_code
        description: Postal or ZIP code

  - name: dt_p_prospect
    description: >
      Transformed prospect data from the Prospect Service. Contains information about
      prospective students including contact details and test status.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: id
        description: Primary key for the prospect record
      - name: registered_on
        description: Timestamp when the prospect registered
      - name: show_test_result
        description: Boolean flag indicating if test results should be shown to the prospect
      - name: has_accepted_privacy_policy
        description: Boolean flag indicating if the prospect has accepted the privacy policy
      - name: settled_level
        description: Settled language level for the prospect
      - name: is_timeout
        description: Boolean flag indicating if the test timed out
      - name: test_completed_on
        description: Timestamp when the test was completed
      - name: created
        description: Timestamp when the record was created
      - name: last_updated
        description: Timestamp when the record was last updated
      - name: placement_test_entry_point
        description: Entry point for the placement test
      - name: first_name
        description: First name of the prospect
      - name: last_name
        description: Last name of the prospect
      - name: email
        description: Email address of the prospect
      - name: phone_number
        description: Phone number of the prospect
      - name: center_reference_id
        description: Reference ID for the center
      - name: source
        description: Source of the prospect record

  - name: dt_p_placement_test_result
    description: >
      Transformed placement test results from the Prospect Service. Contains results from
      placement tests that determine final level placement.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded into the data warehouse
      - name: id
        description: Primary key for the placement test result record
      - name: prospect_id
        description: Foreign key reference to the prospect
      - name: interaction_id
        description: ID of the interaction during the test
      - name: score
        description: Score achieved on the placement test
      - name: result
        description: Result of the placement test
      - name: created
        description: Timestamp when the record was created
      - name: created_by
        description: ID of the user who created the record
      - name: last_updated
        description: Timestamp when the record was last updated
      - name: last_updated_by
        description: ID of the user who last updated the record
