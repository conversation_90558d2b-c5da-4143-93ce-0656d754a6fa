import boto3
import json

Bucket = "facebook-ads-production"
Boto3Resource = boto3.resource("s3")

# Creating a bucket object for the specified bucket
S3Bucket = Boto3Resource.Bucket(Bucket)


def lambda_handler(event, context):
    # Creating an object reference for the file 'Config/Stage1.json' in the S3 bucket
    Stage1FilePath = Boto3Resource.Object(Bucket, 'Config/Stage1.json')

    # Retrieving the content of the file as bytes, decoding it to UTF-8 and storing it in a string variable
    Stage1FileContent = Stage1FilePath.get()['Body'].read().decode('utf-8')

    # Parsing the JSON string and converting it into a Python dictionary
    Stage1 = json.loads(Stage1FileContent)

    # Creating an object reference for the file 'ExecutionCheck.json' in the S3 bucket
    ExecutionCheckFilePath = Boto3Resource.Object(Bucket, 'ExecutionCheck.json')

    # Retrieving the content of the file as bytes, decoding it to UTF-8 and storing it in a string variable
    ExecutionCheckFileContent = ExecutionCheckFilePath.get()['Body'].read().decode('utf-8')

    # Parsing the JSON string and converting it into a Python dictionary
    ExecutionCheck = json.loads(ExecutionCheckFileContent)
    print(ExecutionCheck)

    # Creating a folder path using the value of 'CycleId' from the ExecutionCheck dictionary
    Folder = f"Logs/{ExecutionCheck['CycleId']}/Stage1"

    # Listing objects in the S3 bucket with a specific prefix and extracting the file names
    FilesInS3 = [f.key.split(Folder + "/")[1] for f in S3Bucket.objects.filter(Prefix=Folder).all()]
    print(FilesInS3)
    print(len(FilesInS3))
    print(len(Stage1['Stage1']))

    if len(FilesInS3) == len(Stage1['Stage1']):
        print("Stage 1 completed")
    else:
        print("Stage 1 not completed")

        # Raising an exception to indicate an error condition
        raise Exception

    # Returning a message to indicate the completion of Stage 1
    return "Stage 1 Completed"
