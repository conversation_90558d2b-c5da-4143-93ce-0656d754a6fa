{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet',
    unique_key = 'dbt_unique_id'
) }}

with misentrydate as
    (select contract_id
          , min(created_date) as misentry_date
     from {{ ref('dt_cs_contracts_audit_info') }}
     where modified_field = 'state'
       and present_value = 'misentry'
     group by contract_id
    )
,user as
    (
        select
            id,
            first_name || ' ' || last_name  as personal_tutor
        from {{ ref('dt_cs_users') }}
    )
,contracts_changes as
    (
    select
    contract_id
    ,student_id
    ,center_id
    ,group_id
    ,lab_teacher_id
    ,consultant_id
    ,start_date
    ,end_date
    ,date(valid_from) as valid_from
    ,date(valid_to) as valid_to
    ,status
    ,location
    ,class_access_type
    ,service_type
    ,is_membership
    from {{ ref('contracts_changes') }}
    )
,contracts as
    (
    select
    *
    ,lag(is_promotional) over (partition by student_id order by created_date) as lag_is_promotional
    ,lag(state) over (partition by student_id order by created_date) as lag_state
    ,case when lag(is_promotional) over (partition by student_id order by created_date) = false
            and lag(state) over (partition by student_id order by created_date) not in ('removed','rpending','misentry') then false
            else true
            end as first_contract
    from {{ ref('contracts') }}
    )
,calendar as
    (
    select
    "date"
    ,first_week_date
    ,last_week_date
    ,date_add('day',-6,last_week_date) as rolling_7days_start
    ,date_add('day',-13,last_week_date) as rolling_14days_start
    ,date_add('day',-20,last_week_date) as rolling_21days_start
    ,date_add('day',-27,last_week_date) as rolling_28days_start
    ,date_add('day',4,last_week_date) as next_month_4th
    ,date_add('day',-29,"date") as rolling30_start_date
    ,first_month_date
    ,least(date_add('day',-29,"date"),first_month_date) as earliest_date
    ,last_month_date
    ,year_month_key
    ,case when current_date < last_month_date then "date"
          when current_date between last_month_date and date_add('day',3,last_month_date) then last_month_date
          when current_date >= date_add('day',4,last_month_date) then date_add('day',3,last_month_date) end as misentry_date
    from reporting.dim_calendar
    where "date" <= current_date
    {% set start_date = var('start_date', none) %}
    {% set end_date = var('end_date', none) %}
    {% if start_date is not none and end_date is not none %}
        and "date" between date('{{ start_date }}') and date('{{ end_date }}' )
    {% elif is_incremental() %}
        {% set current_date = modules.datetime.date.today() %}
        {% set current_day = current_date.day %}
        {% set current_hour = modules.datetime.datetime.now().hour %}
        {% set first_day_current_month = current_date.replace(day=1) %}
        {% set last_day_previous_month = first_day_current_month - modules.datetime.timedelta(days=1) %}
        {% set first_day_previous_month = last_day_previous_month.replace(day=1) %}
 
        {% if current_day > 4 or (current_day == 4 and current_hour >= 12) %}
            {% set cutoff_date = first_day_current_month %}
        {% else %}
            {% set cutoff_date = first_day_previous_month %}
        {% endif %}
        and "date" >= date '{{ cutoff_date.strftime('%Y-%m-%d') }}'
    {% else %}
        and 1=2 --This is to avoid the initial refresh without input parameters.
    {% endif %}
    )
,contracts_dims as
    (
    select cal.date
         , cal.first_week_date
         , cal.last_month_date
         , cal.year_month_key
         , c.student_reference_id
         , c.contract_reference_id
         , cc.group_id
         , cc.lab_teacher_id
         , u.personal_tutor
         , cc.consultant_id
         , tc.center_reference_id
         , c.product_type
         , c.study_plan_type
         , cc.status
         , case
           when cc.location = 'incenter' then 'InCenter'
           when cc.location = 'outcenter' then 'OutCenter'
           else cc.location
           end as location
         , case
           when cc.class_access_type = 'full access' then 'Full Access'
           when cc.class_access_type = 'in-center class access' then 'In-Center'
           when cc.class_access_type = 'online class access' then 'Online'
           when cc.class_access_type = 'no_class_access_type' then 'No Access'
           else cc.class_access_type
           end as class_access_type
         , case
           when c.is_teen = FALSE and cc.service_type = 'standard' then 'Standard'
           when c.is_teen = FALSE AND cc.service_type = 'vip' then 'VIP'
           when c.is_teen = FALSE AND cc.service_type = 'combined' then 'Standard'
           when c.is_teen = TRUE AND cc.service_type = 'standard' then 'Teen Standard'
           when c.is_teen = TRUE AND cc.service_type = 'combined' then 'Teen Standard'
           when c.is_teen = TRUE AND cc.service_type = 'vip' then 'Teen VIP'
           else cc.service_type
           end as service_type
         , concat(c.student_reference_id, '_', c.contract_reference_id, '_', cast(cal.last_month_date as varchar)) as compound_key
         , case
           when c.contract_type = 'private' then 'Private'
           when c.contract_type = 'b2b' then 'B2B'
           else c.contract_type
           end as contract_type
         , case
           when cc.is_membership = FALSE then 'Level'
           when cc.is_membership = TRUE then 'Membership'
           end as is_membership
         , c.is_teen
         , c.is_promotional
         ,c.first_contract
         , case
           when c.contract_product like('%self-booking%')
           then true
           else false end as self_booking_access_flag
         , cc.start_date
         , cc.end_date
         , date_add('day',20,cc.start_date) as first_21d_end_date
         , case when date_add('day',20,cc.start_Date) between cal.first_month_date and cal."date"
                         and cc.end_date >= date_add('day',20,cc.start_date)
                         and c.first_contract = true
                         and cc.valid_from <= cal."date" and cc.valid_to >= cal.first_month_date then c.student_reference_id else null end as valid_completed21days
         , case when cc.valid_from <= cal."date" and cc.valid_to >= cal.first_month_date then c.student_reference_id else null end as valid_month_to_date
         , case when cc.valid_from <= cal."date" and cc.valid_to >= cal.rolling30_start_date then c.student_reference_id else null end as valid_rolling30
         , case when cc.valid_from <= cal."date" and cc.valid_to >= cal."date" then c.student_reference_id else null end as valid_current_date
         , dbmk.bookmark_mm_level
         , dbmk.bookmark_mm_unit
         , dbmk.bookmark_wb_level
         , dbmk.bookmark_wb_unit
         , dbmk.bookmark_enc_level
         , dbmk.bookmark_enc_unit
         , case
           when dbmk.course_level = 'first level' then '1. First Level'
           when dbmk.course_level = 'second level' then '2. Second Level'
           else  '3. Other Level'
           end as course_level
         , case when CC.valid_from <= CAL."date" and CC.valid_to >= CAL.rolling_7days_start then C.student_reference_id else null end as valid_rolling_7days
         , case when CC.valid_from <= CAL."date" and CC.valid_to >= CAL.rolling_14days_start then C.student_reference_id else null end as valid_rolling_14days
         , case when CC.valid_from <= CAL."date" and CC.valid_to >= CAL.rolling_21days_start then C.student_reference_id else null end as valid_rolling_21days
         , case when CC.valid_from <= CAL."date" and CC.valid_to >= CAL.rolling_28days_start then C.student_reference_id else null end as valid_rolling_28days
         , case when c.state not in ('removed','rpending') and (me.misentry_date is null or me.misentry_date > cal.misentry_date) then TRUE else FALSE  end as contract_inclusions
         , row_number() over (partition by c.student_reference_id, c.product_type, cal."date" order by valid_to desc) as rn
     from calendar cal
              left join contracts_changes cc on cc.valid_from <= cal."date"
                                                      and cc.valid_to >= cal.earliest_date
              left join contracts c on c.contract_reference_id = cc.contract_id
              left join {{ ref('territory_centers') }} tc on c.center_reference_id = tc.center_reference_id
              left join misentrydate me on c.contract_id = me.contract_id
              left join {{ref('dim_daily_bookmark')}} dbmk on c.contract_reference_id = dbmk.contract_reference_id
                                                        and cal."date" = dbmk."date"
              left join user u on cc.lab_teacher_id = u.id
     where cc.status = 'valid'
       and c.product_type in ('core course','d2c')
    )
select
    cd."date"
    , cd.first_week_date
    , cd.last_month_date
    , cd.year_month_key
    , cd.student_reference_id
    , cd.contract_reference_id
    , case
      when
          cd.group_id = 'no_group' THEN 'Individual'
        WHEN
            cd."date" > date '2024-07-31' THEN cd.group_id --initial load till July 2024
        ELSE
            'Group'
    END as group_id
    , cd.lab_teacher_id
    , cd.personal_tutor
    , CASE
        WHEN cd."date" < date '2024-08-01' THEN 'No Consultant Id'
        ELSE cd.consultant_id
      END as consultant_id
    , cd.center_reference_id
    , cd.product_type
    , cd.study_plan_type
    , cd.contract_type
    , cd.course_level
    , cd.status
    , cd.location
    , cd.class_access_type
    , case
           when cd.class_access_type = 'Full Access'
                and (cp.contract_products like '%11%' or sp.valid_current_date is not null) then 'Full Access+'
           when cd.class_access_type = 'Full Access' then 'Full Access'
           when cd.class_access_type = 'In-Center' 
                and (cp.contract_products like '%11%' or sp.valid_current_date is not null) then 'In-Center+'
           when cd.class_access_type = 'In-Center' then 'In-Center'
           when cd.class_access_type = 'Online' 
                and (cp.contract_products like '%11%' or sp.valid_current_date is not null) then 'Online+'
           when cd.class_access_type = 'Online' then 'Online'
           else cd.class_access_type
           end as class_access_plus_type
    , cd.service_type
    , cd.is_membership
    , cd.is_teen
    , cd.is_promotional
    , case when cp.contract_products like '%4%' then true else false end as self_booking_access_flag
    , case when (cp.contract_products like '%11%' or sp.valid_current_date is not null) then true else false end as speak_plus_flag
    , cd.start_date
    , cd.end_date
    , cd.first_21d_end_date
    , cd.first_contract
    , cd.valid_completed21days
    , cd.valid_month_to_date
    , cd.valid_rolling30
    , cd.valid_current_date
    , cd.valid_rolling_7days
    , cd.valid_rolling_14days
    , cd.valid_rolling_21days
    , cd.valid_rolling_28days
    , cd.bookmark_mm_level
    , cd.bookmark_mm_unit
    , cd.bookmark_wb_level
    , cd.bookmark_wb_unit
    , cd.bookmark_enc_level
    , cd.bookmark_enc_unit
    , cd.contract_inclusions
    , cd.rn
    ,(cast(cd."date" as varchar) || cast(cd.student_reference_id as varchar) || cast(cd.product_type as varchar)) as dbt_unique_id
    , cd.compound_key
    ,CAST(current_timestamp AS TIMESTAMP(6)) AS load_date
from contracts_dims cd
    left join {{ref('contracts_product_changes')}} cp on cd.contract_reference_id = cp.contract_id
                                                and cp.valid_from <= cd."date"
                                                and cp.valid_to >= cd."date"
    left join (select distinct "date", valid_current_date from contracts_dims where product_type = 'd2c') as sp 
          on cd.valid_current_date = sp.valid_current_date
          and cd."date" = sp."date"
          and cd.product_type = 'core course' -- this is to get the students has contract with both product type 
where cd.rn = 1
and cd.student_reference_id is not null