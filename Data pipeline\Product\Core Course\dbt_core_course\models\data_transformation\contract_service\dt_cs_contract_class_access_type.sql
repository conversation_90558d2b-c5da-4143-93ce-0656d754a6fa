{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_contract_class_access_type'
        ) }}

    {% if is_incremental() %}
        where last_updated_date > ((select max(last_updated_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    contclsacstype.id  as id,
    contract_id,
    cont.contract_reference_id as contract_reference_id,
    clsacstypes.name as class_type_description,
    max_no_of_cc_and_sc_classes,
    is_active,
    modified_by_id,
    created_date,
    {{convert_to_local_timestamp('created_date','time_zone_id')}} as local_created_date,
    last_updated_date,
    {{convert_to_local_timestamp('last_updated_date','time_zone_id')}} as local_last_updated_date,
    is_access_period_permanent
from ods_data as contclsacstype
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_class_access_types' ) }}
    ) as clsacstypes on contclsacstype.class_access_type_id = clsacstypes.id
    left join (
        select id,
            center_id, contract_reference_id
        from {{ ref( 'ods_cs_contracts' ) }}
    ) as cont on contclsacstype.contract_id = cont.id
    left join (
        select id,
            center_reference_id 
        from {{ ref( 'ods_cs_centers' ) }}
    ) as center on center.id = cont.center_id
    left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on center.center_reference_id = tz.center_reference_id