{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_encounter_result_aggregate') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT 
    {{etl_load_date()}},
    encresagg.id as id,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(category.path, '.', 1), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(category.path, '.', 1), '[^0-9]', '')
    END AS level,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(category.path, '.', 2), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(category.path, '.', 2), '[^0-9]', '')
    END AS unit,
    category_type.name as category_type,
    registration_id,
    class_start_date,
    {{ convert_to_local_timestamp(
        'class_start_date',
        'tz.time_zone_id'
    ) }} as local_class_start_date,
    teacher_id,
    contitemrestype.name as content_item_result_type,
    reference_class_id,
    student_id,
    class_id,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated,
    show_banner_flag
from
    ods_data as encresagg
    Left Join (
        select
            id,
            path,
            category_type_id
        from
            {{ ref('ods_ls_category') }}
    ) as category
    ON encresagg.unit_id = category.id
    Left Join (
        select
            id,
            name
        from
            {{ ref('ods_ls_category_type') }}
    ) as category_type
    ON category.category_type_id = category_type.id
    Left Join (
        select
            id,
            name
        from
            {{ ref('ods_ls_content_item_result_type') }}
    ) as contitemrestype
    ON encresagg.content_item_result_type_id = contitemrestype.id
    Left Join (
        select
            id,
            center_id
        from
            {{ ref('ods_ls_class') }}
    ) as class
    ON encresagg.class_id = class.id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = class.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
