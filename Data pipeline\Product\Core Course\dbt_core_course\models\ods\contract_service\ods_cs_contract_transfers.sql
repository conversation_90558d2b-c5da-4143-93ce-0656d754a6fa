{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        contractid,
        studentid,
        sourcecenterid,
        destinationcenterid,
        {{cast_to_int('contracttype')}},
        ispromotional,
        transferstatus,
        reason,
        groupid,
        companyid,
        createdbyid,
        modifiedbyid,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
        sourceterritoryid,
        destinationterritoryid,
        grouptransferrequestref,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'contracttransfers')}}
)

SELECT 
    {{etl_load_date()}},
    id,
    contractid as contract_id,
    studentid as student_id,
    sourcecenterid as source_center_id,
    destinationcenterid as destination_center_id,
    contracttype as contract_type,
    ispromotional as is_promotional,
    transferstatus as transfer_status,
    reason,
    groupid as group_id,
    companyid as company_id,
    createdbyid as created_by_id,
    modifiedbyid as modified_by_id,
    createddate as created_date,
    lastupdateddate as last_updated_date,
    sourceterritoryid as source_territory_id,
    destinationterritoryid as destination_territory_id,
    grouptransferrequestref as group_transfer_request_ref
FROM 
    RankedRecords
WHERE 
    rn = 1;