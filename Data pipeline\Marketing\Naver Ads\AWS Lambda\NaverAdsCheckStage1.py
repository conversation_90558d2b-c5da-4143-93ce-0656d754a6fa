import boto3
import json

# Define the S3 bucket name
Bucket = "naver-ads-production"

# Create an S3 resource object
Boto3Resource = boto3.resource("s3")

# Get the S3 bucket object
S3Bucket = Boto3Resource.Bucket(Bucket)


def lambda_handler(event, context):
    # Define the file path for the execution check JSON file
    ExecutionCheckFilePath = Boto3Resource.Object(Bucket, 'ExecutionCheck.json')

    # Read the content of the execution check JSON file
    ExecutionCheckFileContent = ExecutionCheckFilePath.get()['Body'].read().decode('utf-8')

    # Parse the JSON content into a Python dictionary
    ExecutionCheck = json.loads(ExecutionCheckFileContent)
    print(ExecutionCheck)

    # Define the folder path based on the CycleId from the execution check
    Folder = f"Logs/{ExecutionCheck['CycleId']}/Stage1"

    # Get the list of files in the S3 bucket under the specified folder
    FilesInS3 = [f.key.split(Folder + "/")[1] for f in S3Bucket.objects.filter(Prefix=Folder).all()]
    print(FilesInS3)
    print(len(FilesInS3))

    # Check if the number of logs files is 2 on the current CycleId
    if len(FilesInS3) == 2:
        print("Stage 1 completed")
    else:
        print("Stage 1 not completed")
        raise Exception
    return "Stage 1 Completed"
