{% macro backup_table(schema_name, table_name) %}
    {% set backup_table_name = table_name ~ '_backup' %}
    {% set backup_location = 's3://corecourse-wse-backup/iceberg/dbt_backup/' ~ backup_table_name ~ '/' %}

    {% set drop_sql %}
        DROP TABLE IF EXISTS dbt_backup.{{ backup_table_name }}
    {% endset %}
    {% do run_query(drop_sql) %}
    {{ log("Dropped existing backup table if existed: " ~ backup_table_name, info=True) }}

    -- Fetch column info
    {% set column_query %}
        SELECT column_name, data_type
        FROM information_schema.columns 
        WHERE table_schema = '{{ schema_name }}'
          AND table_name = '{{ table_name }}'
        ORDER BY ordinal_position
    {% endset %}
    {% set results = run_query(column_query) %}
    
    {% set select_exprs = [] %}
    {% for row in results %}
        {% if 'timestamp' in row[1] | lower %}
            {% do select_exprs.append("CAST(" ~ row[0] ~ " AS timestamp(6)) AS " ~ row[0]) %}
        {% else %}
            {% set col_name = '"' ~ row[0] ~ '"' %}
            {% do select_exprs.append(col_name) %}
        {% endif %}
    {% endfor %}
    {% set select_clause = select_exprs | join(', ') %}

    -- Now use the interpolated backup_location in the CREATE TABLE
    {% set create_sql %}
        CREATE TABLE dbt_backup.{{ backup_table_name }}
        WITH (
            format = 'PARQUET',
            table_type = 'ICEBERG',
            is_external = false,
            location = '{{ backup_location }}'
        ) AS
        SELECT {{ select_clause }}
        FROM {{ schema_name }}.{{ table_name }}
    {% endset %}

    {% do run_query(create_sql) %}
    {{ log("Iceberg backup created: " ~ backup_table_name ~ " at location: " ~ backup_location, info=True) }}
{% endmacro %}
