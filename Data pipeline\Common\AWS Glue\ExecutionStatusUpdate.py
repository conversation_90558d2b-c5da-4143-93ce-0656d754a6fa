import sys
import CloudOperations
from CloudOperations import logging, pd, json, boto3, io
import ast
from awsglue.utils import getResolvedOptions

Args = getResolvedOptions(sys.argv, ['input'])
ExecutionInput = Args['input']
S3 = CloudOperations.S3

# Read SourceSystemConfigUpdate.json and extract relevant configuration values
SourceSystemConfigUpdate = S3.ReadJsonFile('gluejob-dependencies-production', 'EtlDependencies/SourceSystemConfigUpdate.json')
Bucket = SourceSystemConfigUpdate[ExecutionInput]["Bucket"]
S3ResourceBucket = S3.Connect('resource').Bucket(SourceSystemConfigUpdate[ExecutionInput]["Bucket"])
Validate = SourceSystemConfigUpdate[ExecutionInput]["Validate"]
ExecutionCheck = S3.ReadJsonFile(Bucket, "ExecutionCheck.json")
CycleId = ExecutionCheck['CycleId']
ExecutionSummary = {}

# Iterate through each stage of the ETL process and update configuration values based on logs
for Stages in Validate:
    Path = Stages['file_path']
    ConfigContent = S3.ReadJsonFile(Bucket, Path)
    Stage = Stages['Stage']
    UpdatedValues = []

    # Retrieve all JSON log files for the current stage and append to UpdatedValues list
    for Obj in S3ResourceBucket.objects.filter(Prefix=f"Logs/{CycleId}/{Stage}/"):
        if Obj.key.endswith('.json'):
            JsonData = S3.ReadJsonFile(S3ResourceBucket.name, Obj.key)
            UpdatedValues.append(JsonData)

    # Match configuration values with relevant logs and update as needed
    LogsResponse = {Stage: UpdatedValues}
    for Config in ConfigContent[Stage]:
        if Config['Status'] != 300:
            Config['Status'] = 500
    for Config in ConfigContent[Stage]:
        for Log in LogsResponse[Stage]:
            if ExecutionInput == "GoogleTrends":
                if Config['Territory'] == Log['Territory'] and Config['ServiceType'] == Log['ServiceType']:
                    if Config['Stage'] == 1:
                        Config['CutoffDate'] = Log['CutoffDate']
                        Config['LoadType'] = "Incremental"
                    Config['Status'] = 404
            if ExecutionInput == "GoogleAds" or ExecutionInput == "FacebookAds":
                if Config['Territory'] == Log['Territory'] and Config['AccountId'] == Log['AccountId']:
                    if Config['Stage'] == 1:
                        Config['CutoffDate'] = Log['CutoffDate']
                        Config['LoadType'] = "Incremental"
                    Config['Status'] = 404
            if ExecutionInput == "CoreCourse":
                if Config['Stage'] == 1:
                    if Config['SecretManager'] == Log['SecretManager'] and Config['Object'] == Log['Object']:
                        Config['CutoffDate'] = Log['CutoffDate']
                        Config['Status'] = 404
                if Config['Stage'] == 2:
                    if Config['RawTable'] == Log['RawTable'] and Config['ScdTable'] == Log['ScdTable']:
                        Config['Status'] = 404
                if Config['Stage'] == 3:
                    if Config['Module'] == Log['Module'] and Config['Query'] == Log['Query']:
                        Config['Status'] = 404
                if Config['Stage'] == 4:
                    if Config['Table'] == Log['Table'] and Config['Filepath'] == Log['Filepath']:
                        Config['Status'] = 404
            if ExecutionInput == "Hubspot":
                print("Entering Hubspot")
                if Config['Stage'] == 1:
                    if Config['Territory'] == Log['Territory'] and Config['Object'] == Log['Object'] and Config['Operation'] == Log['Operation']:
                        Config['CutoffDate'] = Log['CutoffDate']
                        Config['Status'] = 404
                if Config['Stage'] == 2:
                    if Config['ScdTable'] == Log['ScdTable'] and Config['Operation'] == Log['Operation']:
                        Config['Status'] = 404
                if Config['Stage'] == 3:
                    if Config['Object'] == Log['Object'] and Config['Operation'] == Log['Operation']:
                        Config['Status'] = 404
                if Config['Stage'] == 4:
                    if Config['Object'] == Log['Object'] and Config['Operation'] == Log['Operation']:
                        Config['Status'] = 404
                        print(Config['Object'],  Log['Object'])
            if ExecutionInput == 'HubspotMyanmarVietnam':
                if Config['Stage'] == 1:
                    if Config['Territory'] == Log['Territory'] and Config['Object'] == Log['Object']:
                        Config['Status'] = 404
                if Config['Stage'] == 3:
                    if Config['Object'] == Log['Object'] and Config['Operation'] == Log['Operation']:
                        Config['Status'] = 404
                if Config['Stage'] == 4:
                    if Config['Object'] == Log['Object'] and Config['Operation'] == Log['Operation']:
                        Config['Status'] = 404
            if ExecutionInput == "NaverAds":
                if Config['AccountName'] == Log['AccountName'] and Config['AccountId'] == Log['AccountId']:
                    if Config['Stage'] == 1:
                        Config['CutoffDate'] = Log['CutoffDate']
                        Config['LoadType'] = "Incremental"
                    Config['Status'] = 404
    logging.warning("ConfigContent: %s", format(ConfigContent))

    # Write updated configuration values back to S3
    S3.WriteJsonFile(Bucket, Path, ConfigContent)
    FailureCount = 0

    # Count the number of configuration values with a Status of 500 (indicating a failure)
    for Config in ConfigContent[Stage]:
        if Config['Status'] == 500:
            FailureCount = FailureCount + 1
    ExecutionSummary.update({Stage: FailureCount})


# Determine overall ETL execution status based on number of failed stages
logging.warning("ExecutionSummary: %s", format(ExecutionSummary))
ExecutionCount = 0
for StageKey, StageValue in ExecutionSummary.items():
    if StageValue != 0:
        ExecutionCount = ExecutionCount + 1

if ExecutionCount != 0:
    logging.warning("Fail")
    ExecutionCheck['Status'] = "Fail"
else:
    logging.warning("Pass")
    ExecutionCheck['Status'] = "Pass"

# Write the updated execution check status to a JSON file in the specified S3 bucket
S3.WriteJsonFile(Bucket, "ExecutionCheck.json", ExecutionCheck)
