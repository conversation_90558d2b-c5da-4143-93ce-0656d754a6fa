import sys
import CloudOperations
from CloudOperations import logging, pd, json, boto3, io
import ast
from awsglue.utils import getResolvedOptions

Args = getResolvedOptions(sys.argv, ['input'])
ExecutionInput = Args['input']
S3 = CloudOperations.S3
S3Resource = S3.Connect('resource')
SourceSystemConfigUpdate = S3.ReadJsonFile('etl-glue-job-dependencies', 'EtlDependencies/SourceSystemConfigUpdate.json')
Bucket = SourceSystemConfigUpdate[ExecutionInput]["Bucket"]
S3Bucket = S3.Connect('resource').Bucket(SourceSystemConfigUpdate[ExecutionInput]["Bucket"])
Validate = SourceSystemConfigUpdate[ExecutionInput]["Validate"]
ExecutionCheck = S3.ReadJsonFile(Bucket, "ExecutionCheck.json")
CycleId = ExecutionCheck['CycleId']
ExecutionSummary = {}
for Stages in Validate:
    Path = Stages['file_path']
    ConfigContent = S3.ReadJsonFile(Bucket, Path)
    Stage = Stages['Stage']
    UpdatedValues = []
    for Obj in S3Bucket.objects.filter(Prefix=f"Logs/{CycleId}/{Stage}/"):
        if Obj.key.endswith('.json'):
            File = S3Resource.Object(S3Bucket.name, Obj.key)
            FileContent = File.get()['Body'].read().decode('utf-8')
            JsonData = json.loads(FileContent)
            UpdatedValues.append(JsonData)
    LogsResponse = {Stage: UpdatedValues}
    for Config in ConfigContent[Stage]:
        if Config['Status'] != 300:
            Config['Status'] = 500
    for Config in ConfigContent[Stage]:
        for Log in LogsResponse[Stage]:
            if ExecutionInput == "GoogleTrends":
                if Config['Territory'] == Log['Territory'] and Config['ServiceType'] == Log['ServiceType']:
                    if Config['Stage'] == 1:
                        Config['CutoffDate'] = Log['CutoffDate']
                        Config['LoadType'] = "Incremental"
                    Config['Status'] = 404
            if ExecutionInput == "GoogleAds" or ExecutionInput == "FacebookAds":
                if Config['Territory'] == Log['Territory'] and Config['AccountId'] == Log['AccountId']:
                    if Config['Stage'] == 1:
                        Config['CutoffDate'] = Log['CutoffDate']
                        Config['LoadType'] = "Incremental"
                    Config['Status'] = 404
            if ExecutionInput == "SalesPlanningToolRead":
                if Config['Territory'] == Log['Territory'] and Config['Center'] == Log['Center']:
                    Config['Status'] = 404
    logging.warning("ConfigContent: %s", format(ConfigContent))
    S3.WriteJsonFile(Bucket, Path, ConfigContent)
    FailureCount = 0
    for Config in ConfigContent[Stage]:
        if Config['Status'] == 500:
            FailureCount = FailureCount + 1
    ExecutionSummary.update({Stage: FailureCount})

logging.warning("ExecutionSummary: %s", format(ExecutionSummary))
ExecutionCount = 0
for StageKey, StageValue in ExecutionSummary.items():
    if StageValue != 0:
        ExecutionCount = ExecutionCount + 1

if ExecutionCount != 0:
    logging.warning("Fail")
    ExecutionCheck['Status'] = "Fail"
else:
    logging.warning("Pass")
    ExecutionCheck['Status'] = "Pass"
S3.WriteJsonFile(Bucket, "ExecutionCheck.json", ExecutionCheck)
