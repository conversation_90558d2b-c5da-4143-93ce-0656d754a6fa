{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        territoryid,
        {{cast_to_int('contracttypeid')}},
        classaccesstypeid,
        isactive,
        {{cast_to_timestamp('created')}} as created,
        {{cast_to_timestamp('lastupdated')}} as lastupdated,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdated DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'territoryclassaccess')}}
)

SELECT
    {{etl_load_date()}},
    id,
    territoryid as territory_id,
    contracttypeid as contract_type_id,
    classaccesstypeid as class_access_type_id,
    isactive as is_active,
    created,
    lastupdated as last_updated
FROM 
    RankedRecords
WHERE 
    rn = 1;