{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_products'
        ) }}
    
    {% if is_incremental() %}
        where Last_Updated_Date > ((select max(Last_Updated_Date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    Id,
    Name,
    Is_Active,
    Created_Date,
    Last_Updated_Date
FROM ods_data