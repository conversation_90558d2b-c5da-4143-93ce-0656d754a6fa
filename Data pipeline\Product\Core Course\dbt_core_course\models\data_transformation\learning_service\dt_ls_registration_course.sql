{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_registration_course') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT
    {{etl_load_date()}},
    regcourse.id as id,
    registration_id,
    category.path as course,
    sequence,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated,
    regcourse.center_id as center_id,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(firstcategory.path, '.', 1), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(firstcategory.path, '.', 1), '[^0-9]', '')
    END AS first_category_level,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(firstcategory.path, '.', 2), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(firstcategory.path, '.', 2), '[^0-9]', '')
    END AS first_category_unit,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(lastcategory.path, '.', 1), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(lastcategory.path, '.', 1), '[^0-9]', '')
    END AS last_category_level,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(lastcategory.path, '.', 2), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(lastcategory.path, '.', 2), '[^0-9]', '')
    END AS last_category_unit
from
    ods_data as regcourse
    Left Join (
        select
            id,
            category_id
        from
            {{ ref('ods_ls_course') }}
    ) as course
    ON regcourse.course_id = course.id
    Left Join (
        select
            id,
            path
        from
            {{ ref('ods_ls_category') }}
    ) as category
    ON course.category_id = category.id
    Left Join (
        select
            id,
            path
        from
            {{ ref('ods_ls_category') }}
    ) as firstcategory
    ON regcourse.first_category_id = firstcategory.id
    Left Join (
        select
            id,
            path
        from
            {{ ref('ods_ls_category') }}
    ) as lastcategory
    ON regcourse.last_category_id = lastcategory.id
    Left Join (
        select
            id,
            center_id
        from
            {{ ref('ods_ls_registration') }}
    ) as registration
    ON regcourse.registration_id = registration.id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = registration.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
