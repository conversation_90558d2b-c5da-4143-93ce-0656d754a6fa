import json
import boto3

Boto3Resource = boto3.resource('s3')
SourceSystemBucketInfoPath = Boto3Resource.Object\
    ('etl-glue-job-dependencies', 'LambdaDependencies/SourceSystemBucketInfo.json')
SourceSystemBucketInfoContent = SourceSystemBucketInfoPath.get()['Body'].read().decode('utf-8')
SourceSystemBucketInfo = json.loads(SourceSystemBucketInfoContent)


def lambda_handler(event, context):
    Bucket = SourceSystemBucketInfo[event['input']]['Bucket']
    FilePath = Boto3Resource.Object(Bucket, 'Execution.json')
    FileContent = FilePath.get()['Body'].read().decode('utf-8')
    Execution = json.loads(FileContent)
    print(Execution)
    return Execution
