{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        bookmode,
        {{ cast_to_timestamp('bookdate') }} as bookdate,
        RESULT,
        attended,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        iscancelled,
        {{ cast_to_timestamp('bookmodemodifieddate') }} as bookmodemodifieddate,
        standbynotificationtype,
        isaccessed,
        id,
        classid,
        studentid,
        bookedby,
        cancelledby,
        registrationid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'bookedstudent'
        ) }}
),
ods_ls_bookedstudent as (SELECT
    bookmode,
    bookdate,
    RESULT,
    attended,
    created,
    lastupdated,
    iscancelled,
    bookmodemodifieddate,
    standbynotificationtype,
    isaccessed,
    id,
    classid,
    studentid,
    bookedby,
    cancelledby,
    registrationid
FROM
    rankedrecords
WHERE
    rn = 1)
, stage1 as (SELECT 
    bookstu.bookmode ,
    bookstu.bookdate ,
    bookstu.result ,
    bookstu.attended ,
    bookstu.created ,
    bookstu.lastupdated ,
    bookstu.iscancelled ,
    bookstu.bookmodemodifieddate ,
    bookstu.standbynotificationtype ,
    bookstu.isaccessed ,
    bookstu.id ,
    bookstu.classid ,
    bookstu.studentid ,
    bookstu.bookedby ,
    bookstu.cancelledby ,
    CASE 
        WHEN bookstu.registrationid  is null then encrsltagg.registration_id
        ELSE bookstu.registrationid 
    END as registrationid
FROM ods_ls_bookedstudent AS bookstu
        LEFT JOIN {{ref('ods_ls_encounter_result_aggregate')}} AS encrsltagg
            ON bookstu.studentid = encrsltagg.student_id
                AND bookstu.classid = encrsltagg.class_id
WHERE bookstu.registrationid IS NULL)
, stage2 as 
(SELECT 
    bookstu.bookmode ,
    bookstu.bookdate ,
    bookstu.result ,
    bookstu.attended ,
    bookstu.created ,
    bookstu.lastupdated ,
    bookstu.iscancelled ,
    bookstu.bookmodemodifieddate ,
    bookstu.standbynotificationtype ,
    bookstu.isaccessed ,
    bookstu.id ,
    bookstu.classid ,
    bookstu.studentid ,
    bookstu.bookedby ,
    bookstu.cancelledby ,
    CASE 
        WHEN bookstu.registrationid  is null then clsrslt.registration_id
        ELSE bookstu.registrationid 
    END as registrationid
FROM stage1 AS bookstu
        LEFT JOIN {{ref('ods_ls_class_result')}}  AS clsrslt
            ON bookstu.studentid = clsrslt.student_id
                AND bookstu.classid = clsrslt.class_id
WHERE bookstu.registrationid IS NULL),
stage3 as
(SELECT 
    bookstu.bookmode ,
    bookstu.bookdate ,
    bookstu.result ,
    bookstu.attended ,
    bookstu.created ,
    bookstu.lastupdated ,
    bookstu.iscancelled ,
    bookstu.bookmodemodifieddate ,
    bookstu.standbynotificationtype ,
    bookstu.isaccessed ,
    bookstu.id ,
    bookstu.classid ,
    bookstu.studentid ,
    bookstu.bookedby ,
    bookstu.cancelledby ,
    CASE 
        WHEN bookstu.registrationid  is null then cir.registration_id
        ELSE bookstu.registrationid 
    END as registrationid
FROM stage2 AS bookstu
        LEFT JOIN {{ref('ods_ls_content_item_result')}} AS cir
            ON bookstu.studentid = cir.student_id
                AND bookstu.classid = cir.class_id
WHERE bookstu.registrationid IS NULL)
, PerContract as (
SELECT user_id, id AS registrationid
FROM {{ref('ods_ls_registration')}} reg
        JOIN (SELECT bookedstud.studentid
                    , COUNT(Distinct reg_lk.id) AS cnt
            FROM stage3 as bookedstud
                        JOIN {{ref('ods_ls_registration')}} as reg_lk
                            ON bookedstud.studentid = reg_lk.user_id
            WHERE bookedstud.registrationid IS NULL
            GROUP BY bookedstud.studentid
            HAVING COUNT(Distinct reg_lk.id) = 1) AS SingleContract
            ON reg.user_id = SingleContract.studentid)
, stage4 as
(SELECT 
    bookstu.bookmode ,
    bookstu.bookdate ,
    bookstu.result ,
    bookstu.attended ,
    bookstu.created ,
    bookstu.lastupdated ,
    bookstu.iscancelled ,
    bookstu.bookmodemodifieddate ,
    bookstu.standbynotificationtype ,
    bookstu.isaccessed ,
    bookstu.id ,
    bookstu.classid ,
    bookstu.studentid ,
    bookstu.bookedby ,
    bookstu.cancelledby ,
    CASE 
        WHEN bookstu.registrationid  is null then PC.registrationid
        ELSE bookstu.registrationid 
    END as registrationid
FROM stage3 AS bookstu
        LEFT JOIN PerContract PC ON bookstu.studentid = PC.user_id
WHERE bookstu.registrationid IS NULL)
, bookedstudent_backdating  AS
(SELECT bst.id AS Bookingid
    , bst.studentid
    , reg.id      AS registrationid
    , reg.created AS contractcreated
    , reg.start_date
    , reg.end_date
    , reg.offer_type
    , bst.bookdate
FROM stage4 as bst
        JOIN {{ref('ods_ls_registration')}} reg
            ON bst.studentid = reg.user_id
WHERE bst.registrationid IS NULL)
, Mapped_Values AS (SELECT b1.bookingid,
                            b1.bookdate,
                            MAX(b2.contractcreated) AS mapped_contract_date
                    FROM bookedstudent_backdating b1
                                JOIN bookedstudent_backdating b2
                                    ON b1.studentid = b2.studentid --AND  b2.contractcreated > b1.bookdate
                    GROUP BY b1.bookingid, b1.bookdate)
, bookedstudent_tmp AS (
SELECT b.bookingid,
    b.studentid,
    b.bookdate,
    mv.mapped_contract_date,
    b2.registrationid AS Mapped_RegistrationId
FROM bookedstudent_backdating b
        JOIN Mapped_Values mv ON b.bookingid = mv.bookingid AND b.bookdate = mv.bookdate
        JOIN bookedstudent_backdating b2 ON b.studentid = b2.studentid AND b2.contractcreated = mv.mapped_contract_date
GROUP BY b.bookingid,
        b.studentid,
        b.bookdate,
        mv.mapped_contract_date,
        b2.registrationid)
, stage5 as
(SELECT 
    bookstu.bookmode ,
    bookstu.bookdate ,
    bookstu.result ,
    bookstu.attended ,
    bookstu.created ,
    bookstu.lastupdated ,
    bookstu.iscancelled ,
    bookstu.bookmodemodifieddate ,
    bookstu.standbynotificationtype ,
    bookstu.isaccessed ,
    bookstu.id ,
    bookstu.classid ,
    bookstu.studentid ,
    bookstu.bookedby ,
    bookstu.cancelledby ,
    CASE 
        WHEN bookstu.registrationid  is null then bookedstudent_tmp.mapped_registrationid
        ELSE bookstu.registrationid 
    END as registrationid
FROM stage4 AS bookstu
        LEFT JOIN (
            select studentid, bookdate, max(mapped_registrationid) as mapped_registrationid from bookedstudent_tmp 
            group by studentid, bookdate
            ) as bookedstudent_tmp
            ON bookstu.studentid = bookedstudent_tmp.studentid
                AND bookstu.bookdate = bookedstudent_tmp.bookdate
WHERE bookstu.registrationid IS NULL)

Select 
    {{etl_load_date()}},
    bookmode as book_mode ,
    bookdate as book_date ,
    result ,
    attended ,
    created ,
    lastupdated as last_updated ,
    iscancelled as is_cancelled,
    bookmodemodifieddate as book_mode_modified_date,
    standbynotificationtype as standby_notification_type,
    isaccessed as is_accessed,
    id ,
    classid as class_id,
    studentid as student_id,
    bookedby as booked_by,
    cancelledby as cancelled_by ,
    registrationid as registration_id
From stage5
UNION 
(Select {{etl_load_date()}},* from stage1 where registrationid IS NOT NULL)
UNION 
(Select {{etl_load_date()}},* from stage2 where registrationid IS NOT NULL)
UNION 
(Select {{etl_load_date()}},* from stage3 where registrationid IS NOT NULL)
UNION 
(Select {{etl_load_date()}},* from stage4 where registrationid IS NOT NULL)
UNION
(Select {{etl_load_date()}},* from ods_ls_bookedstudent where registrationid IS NOT NULL)