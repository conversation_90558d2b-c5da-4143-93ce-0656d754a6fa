{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        contentitemtypeid,
        sequence,
        isskippable,
        duration,
        ismilestone,
        contentoptimizationlevelid,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        isscorable,
        id,
        description,
        url,
        courseid,
        ancestorcategoryid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'contentitem'
        ) }}
)
SELECT
    {{etl_load_date()}},
    contentitemtypeid as content_item_type_id,
    sequence,
    isskippable as is_skippable,
    duration,
    ismilestone as is_milestone,
    contentoptimizationlevelid as content_optimization_level_id,
    created,
    lastupdated as last_updated,
    isscorable as is_scorable,
    id,
    description,
    url,
    courseid as course_id,
    ancestorcategoryid as ancestor_category_id
FROM
    rankedrecords
WHERE
    rn = 1;
