{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH product_changes AS (
    SELECT
        cai.*
    FROM
        dt_contract_service.dt_cs_contracts_audit_info cai
    WHERE
        cai.modified_field = 'products'
),

first_change_row AS (
    SELECT
        c.id AS contract_id,
        cai.previous_value AS contract_products,
        c.created_date AS valid_from,
        cai.created_date AS valid_to,
        ROW_NUMBER() OVER (PARTITION BY c.id ORDER BY cai.created_date) AS rn
    FROM
        dt_contract_service.dt_cs_contracts c
    LEFT JOIN
        product_changes cai ON c.id = cai.contract_id
    WHERE
        cai.contract_id IS NOT NULL
),

contract_products AS (
    SELECT
        contract_id,
        ARRAY_JOIN(ARRAY_AGG(product_id), ',') AS contract_products
    FROM (
        SELECT
            *
        FROM
            dt_contract_service.dt_cs_contract_products
        ORDER BY
            contract_id, product_id
    ) cp
    GROUP BY
        contract_id
)

SELECT
    contract_id,
    contract_products,
    valid_from,
    CASE
        WHEN valid_to IS NULL THEN TIMESTAMP '3000-01-01 00:00:00.000'
        ELSE valid_to
    END AS valid_to,
    union_source
FROM (
    -- Without changes
    SELECT
        c.id AS contract_id,
        cp.contract_products,
        c.created_date AS valid_from,
        TIMESTAMP '3000-01-01 00:00:00.000' AS valid_to,
        'without changes' AS union_source
    FROM
        dt_contract_service.dt_cs_contracts c
    LEFT JOIN
        contract_products cp ON c.id = cp.contract_id
    LEFT JOIN
        product_changes cai ON c.id = cai.contract_id
    WHERE
        cai.contract_id IS NULL

    UNION ALL

    -- First change
    SELECT
        contract_id,
        contract_products,
        valid_from,
        valid_to,
        'first change' AS union_source
    FROM
        first_change_row
    WHERE
        rn = 1

    UNION ALL

    -- Other changes
    SELECT
        c.id AS contract_id,
        cai.present_value AS contract_products,
        cai.created_date AS valid_from,
        LEAD(cai.created_date) OVER (PARTITION BY cai.contract_id ORDER BY cai.created_date) AS valid_to,
        'other change' AS union_source
    FROM
        dt_contract_service.dt_cs_contracts c
    LEFT JOIN
        product_changes cai ON c.id = cai.contract_id
    WHERE
        cai.contract_id IS NOT NULL
)
ORDER BY
    contract_id, valid_from
