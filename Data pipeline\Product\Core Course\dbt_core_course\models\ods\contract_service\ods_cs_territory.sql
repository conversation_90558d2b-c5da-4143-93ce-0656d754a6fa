{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        territoryreferenceid,
        name,
        code,
        isocode,
        isactive,
        isdualclassaccess,
        currency
    FROM 
        {{source('stage_contract_service', 'territory')}}
)

SELECT
    {{etl_load_date()}},
    id,
    territoryreferenceid as territory_reference_id,
    name as name,
    code as code,
    isocode as iso_code,
    isactive as is_active,
    isdualclassaccess as is_dual_class_access,
    currency as currency
FROM 
    RankedRecords