{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH combined_data AS (
    SELECT 
        contract_id,
        contract_products AS product,
        valid_from,
        valid_to
    FROM {{ref('contracts_product_type_history')}}
    
    UNION ALL
    
    SELECT 
        contract_id,
        product,
        dbt_valid_from AS valid_from,
        dbt_valid_to AS valid_to
    FROM {{ref('contracts_product_type_changes')}}
),
min_valid_from AS (
    SELECT 
        contract_id,
        MIN(dbt_valid_from) AS min_valid_from
    FROM {{ref('contracts_product_type_changes')}}
    GROUP BY contract_id
),
adjusted_data AS (
    SELECT 
        cnt.contract_reference_id as contract_id,
        c.product,
    {{ convert_to_local_timestamp ('c.valid_from', 'student_center.timezone') }} as valid_from,
    CASE 
        WHEN c.valid_to = timestamp'3000-01-01 00:00:00.000' 
        THEN {{ convert_to_local_timestamp ('mvf.min_valid_from', 'student_center.timezone') }}
        WHEN c.valid_to IS NOT NULL THEN {{ convert_to_local_timestamp ('c.valid_to', 'student_center.timezone') }}
        ELSE cast('3000-01-01' AS timestamp(6))
    END AS valid_to
    FROM combined_data c
    LEFT JOIN min_valid_from mvf
    ON c.contract_id = mvf.contract_id
    LEFT JOIN {{ref("dt_cs_contracts")}} cnt -- converting contract id
    ON c.contract_id = cnt.id
    LEFT JOIN {{ref('territory_centers')}} as student_center
    ON cnt.contract_reference_id = student_center.center_reference_id
)
SELECT 
contract_id,
product as contract_products,
valid_from,
valid_to
FROM adjusted_data