-- campaign information table
create table if not exists naver.ads
(
    ncccampaignid   varchar(27),
    customerid      varchar(10),
    name            varchar(32),
    userlock        varchar(5),
    campaigntp      varchar(14),
    deliverymethod  varchar(11),
    tracking<PERSON>l     varchar(51),
    trackingmode    varchar(18),
    useperiod       varchar(5),
    dailybudget     varchar(7),
    usedailybudget  varchar(5),
    totalchargecost varchar(15),
    status          varchar(8),
    statusreason    varchar(48),
    expectcost      varchar(5),
    migtype         varchar(5),
    delflag         varchar(5),
    regtm           timestamp encode az64,
    edittm          timestamp encode az64,
    accountname     varchar(15)
);

-- campaign stat table
create table if not exists naver.ads_stat
(
    ctr           varchar(10),
    pcnxavgrnk    varchar(10),
    mblnxavgrnk   varchar(10),
    convamt       varchar(10),
    crto          varchar(10),
    ccnt          varchar(10),
    avgrnk        varchar(10),
    dateend       date encode az64,
    salesamt      varchar(10),
    clkcnt        varchar(10),
    ror           varchar(10),
    datestart     date encode az64,
    viewcnt       varchar(10),
    cpc           varchar(10),
    cpconv        varchar(10),
    impcnt        varchar(10),
    ncccampaignid varchar(32),
    customerid    varchar(10),
    accountname   varchar(15)
);