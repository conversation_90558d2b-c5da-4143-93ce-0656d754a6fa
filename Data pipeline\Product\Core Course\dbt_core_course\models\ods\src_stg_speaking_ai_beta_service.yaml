version: 2

sources:
  - name: stage_speaking_ai_beta_service
    description: >
      Source data from the Speaking AI Beta Service which provides AI-powered speaking practice
      and feedback for language learners.
    database: awsdatacatalog
    schema: stg_speaking_ai_beta_service
    tables:
      - name: raw_data_prospect_speak_beta
        description: Raw conversation data from speaking practice sessions with the AI system.
        columns:
          - name: chat_id
            description: Unique identifier for the chat session
          - name: duration
            description: Duration of the chat session in seconds
          - name: gpt4o_mini_cost
            description: Cost of using the GPT-4o mini model for this session
          - name: has_ended
            description: Boolean flag indicating if the chat has ended
          - name: messages
            description: JSON array containing all messages in the conversation
          - name: level
            description: Language proficiency level for the session
          - name: start_date
            description: ISO timestamp when the session started
          - name: end_date
            description: ISO timestamp when the session ended
          - name: total_input_tokens
            description: Total number of input tokens used in the session
          - name: total_output_tokens
            description: Total number of output tokens generated in the session
          - name: user_id
            description: ID of the user participating in the session
          - name: user_type
            description: Type of user (e.g., student, prospect)

      - name: raw_data_feedback
        description: User feedback data collected after speaking practice sessions.
        columns:
          - name: chat_id
            description: Unique identifier for the chat session
          - name: user_id
            description: ID of the user who provided the feedback
          - name: user_type
            description: Type of user providing the feedback
          - name: feedback
            description: Feedback content provided by the user
          - name: completed_date
            description: ISO timestamp when the feedback was completed