{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

With contracts as 
(
    Select 
        * ,
        ROW_NUMBER() OVER(partition by contract_reference_id order by study_plan_type DESC) as rn 
    from 
        {{ ref('contracts') }}
)


SELECT 
        c.contract_reference_id AS contract_id,
        c.center_reference_id AS center_id,
        c.created_date AS contract_created_date,
        b2b.company_created_date,
        c.start_date AS contract_start_date,
        c.end_date AS contract_end_date,
        c.location,
        c.product_type,
        c.class_access_type,
        c.start_level,
        c.end_level,
        b2b.company_name AS b2b_company_name,
        b2b.mcc_product_type,
        b2b.mcc_b2_b_contract_type AS mcc_b2b_contract_type,
        CASE WHEN service_type = 'vip' THEN TRUE ELSE FALSE END AS vip_flag,
        CASE
            WHEN contract_type = 'b2b' THEN TRUE
            WHEN c.master_contract_course_id IS NOT NULL THEN TRUE
            ELSE FALSE 
        END AS b2b_flag,
        c.is_promotional AS promotional_flag,
        c.is_teen AS teen_flag,
        c.is_membership AS membership_flag,
        CASE
            WHEN c.contract_product like('%self-booking%') THEN true
            ELSE false 
        END AS self_booking_access_flag
        , CAST(current_timestamp AS TIMESTAMP(6)) AS load_date 
    FROM 
        contracts c
    LEFT JOIN {{ ref('b2b_contracts') }} b2b
        ON b2b.master_contract_course_id = c.master_contract_course_id
    WHERE 
        rn = 1