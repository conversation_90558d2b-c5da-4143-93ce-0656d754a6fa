{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_company_additional_info'
        ) }}
    
    {% if is_incremental() %}
        where last_updated_date > ((select max(last_updated_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    cmpadlinfo.id as id,
    new_version_company_id,
    center_id,
    address_line1,
    address_line2,
    city,
    state,
    country,
    postal_code,
    primary_phone_number,
    secondary_phone_number,
    fax_number,
    email,
    call_between_from,
    call_between_to,
    contact_person_first_name,
    contact_person_last_name,
    contact_person_email,
    contact_person_designation,
    contact_person_department,
    contact_person_mobile,
    company_ref_id,
    created_by_id,
    modified_by_id,
    created_date,
    {{convert_to_local_timestamp('created_date','time_zone_id')}} as local_created_date,
    last_updated_date,
    {{convert_to_local_timestamp('last_updated_date','time_zone_id')}} as local_last_updated_date
from ods_data as cmpadlinfo
    left join (
        select id,
            center_reference_id 
        from {{ ref( 'ods_cs_centers' ) }}
    ) as center on center.id = cmpadlinfo.center_id
    left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on center.center_reference_id = tz.center_reference_id