{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        score,
        {{ cast_to_timestamp('created') }} as created,
        createdby,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        lastupdatedby,
        id,
        prospectid,
        interactionid,
        result,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_prospect_service',
            'placementtestresult'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    score,
    created,
    createdby as created_by,
    lastupdated as last_updated,
    lastupdatedby as last_updated_by,
    id,
    prospectid as prospect_id,
    interactionid as interaction_id,
    result
FROM
    rankedrecords
WHERE
    rn = 1;
