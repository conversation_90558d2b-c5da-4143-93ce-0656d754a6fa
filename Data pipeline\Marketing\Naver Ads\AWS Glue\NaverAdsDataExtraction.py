import sys
import NaverAdsFramework
import CloudOperations
from CloudOperations import logging, pd, json, boto3, io
from awsglue.utils import getResolvedOptions

args = getResolvedOptions(sys.argv,
                          ['Status', 'Stage', 'Operation', 'AccountId', 'AccountName',
                           'CycleId', 'CutoffDate', 'LoadType'])

# Extract input arguments into separate variables
status = args['Status']
stage = args['Stage']
operation = args['Operation']
account_id = args['AccountId']
account_name = args['AccountName']
cycle_id = args['CycleId']
cutoff_date = args['CutoffDate']
load_type = args['LoadType']
secret_name = 'NaverAds'
region = 'eu-north-1'
bucket = 'naver-ads-production'
s3 = CloudOperations.S3

logging.warning("LoadType:'%s'", format(load_type))
logging.warning("CutoffDate:'%s'", format(cutoff_date))

campaign_id_extraction = NaverAdsFramework.NaverAds.get_campaign_ids(cycle_id=cycle_id,
                                                                     account_id=account_id,
                                                                     account_name=account_name,
                                                                     secret_name=secret_name,
                                                                     region=region,
                                                                     bucket=bucket)
logging.warning("campaign data response in: '%s'", format(campaign_id_extraction))

campaign_stat_extraction = NaverAdsFramework.NaverAds.get_campaign_information(cycle_id=cycle_id,
                                                                               account_id=account_id,
                                                                               account_name=account_name,
                                                                               secret_name=secret_name,
                                                                               region=region,
                                                                               bucket=bucket,
                                                                               cutoff_date=cutoff_date,
                                                                               load_type=load_type,
                                                                               campaign_id_file_path=campaign_id_extraction
                                                                               )
logging.warning("campaign stat data response in: '%s'", format(campaign_stat_extraction))

# Create a JSON object to store data extraction logs
Logs = {
    "Status": 200,
    "Stage": int(stage),
    "CutoffDate": campaign_stat_extraction['cutoff_date'],
    "LoadType": "Incremental",
    "Operation": operation,
    "AccountId": account_id,
    "AccountName": account_name,
    "Summary": str(campaign_stat_extraction),
    "CycleId": cycle_id
}
logging.warning("Logs:'%s'", format(Logs))

# Write the data extraction logs to Amazon S3
s3.WriteJsonFile(bucket, f"Logs/{cycle_id}/Stage{stage}/{account_name}{account_id}.json", Logs)
