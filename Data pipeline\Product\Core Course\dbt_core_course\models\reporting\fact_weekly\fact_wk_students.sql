{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet',
    unique_key = 'dbt_unique_id'
) }}

-- getting last 5 weeks based on current date
{% set end_date = var('end_date', modules.datetime.date.today()) %}
{% set start_date = var('start_date', (end_date - modules.datetime.timedelta(weeks=6))) %}



with calendar as
    (
    select
        "date"
        ,first_week_date
        ,last_week_date
    from reporting.dim_calendar
    where
        "date" = last_week_date
        AND "date" <= CURRENT_DATE
        AND "date" >= DATE '{{ start_date }}'
        AND "date" <= DATE '{{ end_date }}'
    order by "date"
    )

select 
    fds."date"
    , fds.first_week_date
    , fds.last_month_date
    , fds.year_month_key
    , fds.student_reference_id
    , fds.contract_reference_id
    , fds.group_id
    , fds.lab_teacher_id
    , fds.personal_tutor
    , fds.consultant_id
    , fds.center_reference_id
    , fds.product_type
    , fds.study_plan_type
    , fds.contract_type
    , fds.course_level
    , fds.status
    , fds.location
    , fds.class_access_type
    , fds.service_type
    , fds.is_membership
    , fds.is_teen
    , fds.is_promotional
    , fds.self_booking_access_flag
    , fds.start_date
    , fds.end_date
    , fds.first_21d_end_date
    , fds.first_contract
    , fds.valid_completed21days
    , fds.valid_month_to_date
    , fds.valid_rolling30
    , fds.valid_current_date
    , fds.valid_rolling_7days
    , fds.valid_rolling_14days
    , fds.valid_rolling_21days 
    , fds.valid_rolling_28days
    , fds.bookmark_mm_level
    , fds.bookmark_mm_unit
    , fds.bookmark_wb_level
    , fds.bookmark_wb_unit
    , fds.bookmark_enc_level
    , fds.bookmark_enc_unit
    , fds.contract_inclusions
    ,(cast(fds."date" as varchar) || cast(fds.student_reference_id as varchar) || cast(fds.product_type as varchar)) as dbt_unique_id
    ,CAST(current_timestamp AS TIMESTAMP(6)) AS load_date
from calendar cal
left join {{ ref('fact_daily_students') }} as fds 
    on cal.last_week_date = fds."date"