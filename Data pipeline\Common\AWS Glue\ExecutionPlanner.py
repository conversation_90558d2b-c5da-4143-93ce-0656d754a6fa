import sys
import CloudOperations
from CloudOperations import logging, pd, json, boto3, io
import ast
from datetime import date
from jsonref import JsonRef
from awsglue.utils import getResolvedOptions

# Retrieving command-line arguments and resolving options
Args = getResolvedOptions(sys.argv, ['input'])
ExecutionInput = Args['input']

# Importing necessary components from CloudOperations module
S3 = CloudOperations.S3
SourceSystemConfigInfo = S3.ReadJsonFile('gluejob-dependencies-production', 'EtlDependencies/SourceSystemConfigInfo.json')
Bucket = SourceSystemConfigInfo[ExecutionInput]["Bucket"]
ExecutionCheck = S3.ReadJsonFile(Bucket, "ExecutionCheck.json")
logging.warning("ExecutionCheck: %s", format(ExecutionCheck))
FilePath = SourceSystemConfigInfo[ExecutionInput]["FilePath"]
Stages = SourceSystemConfigInfo[ExecutionInput]["Stages"]
CycleId = ""
Data = {}
TotalResponse = []
Dictionary = {"ExecutionInfo": TotalResponse}

# Reading necessary configuration files from S3
for File in FilePath:
    ReadContent = S3.ReadJsonFile(Bucket, File)
    ReadResponse = JsonRef.replace_refs(ReadContent)
    ReadResponseString = str(ReadResponse)
    ReadResponseDictionary = ast.literal_eval(ReadResponseString)
    Data.update(ReadResponseDictionary)
PreviousCycleId = str(ExecutionCheck['CycleId'])

# Checking the status and updating the cycle ID accordingly
if ExecutionCheck['Status'] == 'Pass':
    CurrentDay = date.today().strftime("%Y%m%d")
    logging.warning("CurrentDay: %s", format(CurrentDay))
    IncrementCycleId = int(PreviousCycleId[8:])
    logging.warning("IncrementCycleId: %s", format(IncrementCycleId))

    # Updating the cycle ID if the current day matches
    if CurrentDay == PreviousCycleId[0:8]:
        logging.warning("Cycle id date matches IncrementedCycleId by one")
        IncrementedCycleId = IncrementCycleId + 1
        logging.warning("IncrementedCycleId: %s", format(IncrementedCycleId))
        CycleId = int(str(PreviousCycleId[0:8]) + str(IncrementedCycleId))
        ExecutionCheck['CycleId'] = CycleId
        ExecutionCheck['Status'] = "Running"
        logging.warning("The New CycleId Is: %s", format(CycleId))

    # Updating the cycle ID if the current day does not match
    if CurrentDay != PreviousCycleId[0:8]:
        logging.warning("Cycle Id Does Not Match As It Is Yesterday's Run")
        CycleId = int(str(CurrentDay) + str(1))
        ExecutionCheck['CycleId'] = CycleId
        ExecutionCheck['Status'] = "Running"
        logging.warning("The New CycleId Is: %s", format(CycleId))
    for Stage in Stages:
        UpdatedValues = []
        for Log in Data[Stage]:
            if Log['Status'] == 404:
                NewObj = {"CycleId": CycleId}
                Log.update(NewObj)
                UpdatedValues.append(Log)
        DictionaryFinal = {Stage: UpdatedValues}
        TotalResponse.append(DictionaryFinal)
else:
    for Stage in Stages:
        UpdatedValues = []
        for Log in Data[Stage]:
            if Log['Status'] == 500:
                NewObj = {"CycleId": PreviousCycleId}
                Log.update(NewObj)
                UpdatedValues.append(Log)
        DictionaryFinal = {Stage: UpdatedValues}
        TotalResponse.append(DictionaryFinal)
    ExecutionCheck['Status'] = "Running"
logging.warning("Dictionary: %s", format(Dictionary))
logging.warning("ExecutionCheck: %s", format(ExecutionCheck))
S3.WriteJsonFile(Bucket, "Execution.json", Dictionary)
S3.WriteJsonFile(Bucket, "ExecutionCheck.json", ExecutionCheck)
