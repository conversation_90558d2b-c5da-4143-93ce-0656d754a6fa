{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet',
    unique_key = 'dbt_unique_id'
) }}


with Master as
    (
    SELECT
        'Master' AS private_master,
        C.company_id AS company_student_id,
        C.id AS contract_id,
        C."state" AS c_state,
        CE.center_reference_id,
        T."name" AS territory_name,
        C.created_date,
        C.end_date,
        CF.id AS future_contract,
        CF."state" AS f_state,
        CF.created_date AS future_created_date,
        CASE WHEN CF.sale_date < date '2020-10-01' THEN CF.created_date ELSE CF.sale_date END AS future_sales_date,
        ROW_NUMBER() OVER(PARTITION BY C.id ORDER BY CF.created_date) AS rn,
        U.id AS consultant_id
    FROM ods_contract_service.ods_cs_master_contracts C
    LEFT JOIN (
        SELECT * FROM ods_contract_service.ods_cs_master_contracts
                ) CF
        ON C.company_id = CF.company_id
        AND CF.created_date > C.created_date
    INNER JOIN ods_contract_service.ods_cs_centers CE
        ON C.center_id = CE.id
    INNER JOIN ods_contract_service.ods_cs_territory T
        ON CE.territory_id = T.id
    LEFT JOIN ods_contract_service.ods_cs_users U
        ON U.id = C.consultant_id
    WHERE C."state" NOT IN (9, 12, 13)
    )

,private as
(
    SELECT
        'Private' AS private_master,
        C.student_id AS company_student_id,
        C.id AS contract_id,
        C."state" AS c_state,
        CE.center_reference_id,
        T."name" AS territory_name,
        C.created_date,
        CASE WHEN C."state" = 8 THEN ObjR.created_date ELSE C.end_date END AS end_date,
        CF.id AS future_contract,
        CF."state" AS f_state,
        CF.created_date AS future_created_date,
        CASE WHEN CF.sale_date < date '2020-10-01' THEN CF.created_date ELSE CF.sale_date END AS future_sales_date,
        ROW_NUMBER() OVER(PARTITION BY C.id ORDER BY CF.created_date) AS rn,
        U.id AS consultant_id
    FROM (
        SELECT * FROM ods_contract_service.ods_cs_contracts WHERE contract_type = 1
    ) C
    LEFT JOIN (
        SELECT * FROM ods_contract_service.ods_cs_contracts
        WHERE is_promotional = false AND is_transfer_in = false AND contract_type = 1
    ) CF
        ON C.student_id = CF.student_id
        AND CF.created_date > C.created_date
    LEFT JOIN (
        SELECT 
            CAI.contract_id,
            CAI.created_date,
            ROW_NUMBER() OVER(PARTITION BY CAI.contract_id ORDER BY CAI.created_date) AS rn2
        FROM ods_contract_service.ods_cs_contracts_audit_info CAI
        WHERE CAI.change_type = 4
    ) ObjR
        ON ObjR.contract_id = C.id
        AND ObjR.rn2 = 1
    INNER JOIN ods_contract_service.ods_cs_centers CE
        ON C.center_id = CE.id
    INNER JOIN ods_contract_service.ods_cs_territory T
        ON CE.territory_id = T.id
    LEFT JOIN ods_contract_service.ods_cs_users U
        ON U.id = C.consultant_id
    WHERE C.is_promotional = false
    AND C."state" NOT IN (9, 12, 13)
)

,newcontracts as
(
    SELECT *, 
    CASE
        WHEN day(current_date) < 4 THEN date_trunc('month',date_add('month',-1,current_date))
        WHEN day(current_date) = 4 and hour(current_timestamp) < 12 THEN date_trunc('month',date_add('month',-1,current_date))
        ELSE date_trunc('month',current_date)
    END AS calc_date
    FROM (
        SELECT * FROM master where rn = 1
        UNION ALL
        SELECT * FROM private where rn = 1
    )
)

,renewals_output as
    (
    SELECT
    NC.center_reference_id AS centerid,
    NC.private_master as privatemaster,
    NC.consultant_id as consultantid,
    calc_date as "month",
    COUNT(DISTINCT CASE WHEN NC.c_state = 6 THEN NULL WHEN NC."end_date" >= calc_date AND NC."end_date" < date_add('month',1,calc_date) THEN NC.contract_id ELSE NULL END) AS end1month,
    COUNT(DISTINCT CASE WHEN NC.c_state = 6 THEN NULL WHEN NC."end_date" >= calc_date AND NC."end_date" < date_add('month',3,calc_date) THEN NC.contract_id ELSE NULL END) AS end3month,
    COUNT(DISTINCT CASE WHEN NC.c_state = 6 THEN NULL WHEN NC."end_date" >= date_add('month',3,calc_date) THEN NC.contract_id ELSE NULL END) AS end3monthplus,
    COUNT(DISTINCT CASE WHEN NC.c_state = 6 THEN NULL WHEN NC."end_date" < calc_date AND NC."end_date" >= date_add('month',-3,calc_date) THEN NC.contract_id ELSE NULL END) AS end3monthago,
    COUNT(DISTINCT CASE WHEN NC.c_state = 6 THEN NULL WHEN NC."end_date" < date_add('month',-3,calc_date) THEN NC.contract_id ELSE NULL END) AS end3monthagoplus,
    COUNT(DISTINCT CASE WHEN NC."end_date" >= calc_date AND NC."end_date" < date_add('month',1,calc_date) AND NC.future_contract IS NOT NULL AND NC.future_sales_date < DATE_ADD('DAY', 90, NC.end_date) THEN NC.contract_id ELSE NULL END) AS end1monthrenewed,
    COUNT(DISTINCT CASE WHEN NC."end_date" >= calc_date AND NC."end_date" < date_add('month',1,calc_date) AND NC.future_contract IS NULL AND NC.future_sales_date < DATE_ADD('DAY', 90, NC.end_date) THEN NC.contract_id ELSE NULL END) AS end1monthnotrenewed,
    COUNT(DISTINCT CASE WHEN NC.future_sales_date IS NOT NULL AND NC.future_sales_date >= calc_date AND NC.future_sales_date < date_add('month',1,calc_date) THEN NC.contract_id ELSE NULL END) AS renewsold,
    COUNT(DISTINCT CASE WHEN NC.future_sales_date IS NOT NULL AND NC.future_sales_date >= calc_date AND NC.future_sales_date < date_add('month',1,calc_date) AND NC."end_date" >= calc_date AND NC."end_date" < date_add('month',3,calc_date) THEN NC.contract_id ELSE NULL END) AS renewend3month,
    COUNT(DISTINCT CASE WHEN NC.future_sales_date IS NOT NULL AND NC.future_sales_date >= calc_date AND NC.future_sales_date < date_add('month',1,calc_date) AND NC."end_date" >= date_add('month',3,calc_date) THEN NC.contract_id ELSE NULL END) AS renewend3monthplus,
    COUNT(DISTINCT CASE WHEN NC.future_sales_date IS NOT NULL AND NC.future_sales_date >= calc_date AND NC.future_sales_date < date_add('month',1,calc_date) AND NC."end_date" < calc_date AND NC."end_date" >= date_add('month',-3,calc_date) THEN NC.contract_id ELSE NULL END) AS renewend3monthago,
    COUNT(DISTINCT CASE WHEN NC.future_sales_date IS NOT NULL AND NC.future_sales_date >= calc_date AND NC.future_sales_date < date_add('month',1,calc_date) AND NC."end_date" < date_add('month',-3,calc_date) THEN NC.contract_id ELSE NULL END) AS renewend3monthagoplus
    from newcontracts NC
    GROUP BY
        NC.center_reference_id,
        NC.private_master,
        NC.consultant_id,
        calc_date
    )


SELECT
    centerid || privatemaster || consultantid || CAST("month" as varchar) as dbt_unique_id,
    centerid,
    privatemaster,
    "month",
    end1month,
    end3month,
    end3monthplus,
    end3monthago,
    end3monthagoplus,
    end1monthrenewed,
    end1monthnotrenewed,
    renewsold,
    renewend3month,
    renewend3monthplus,
    renewend3monthago,
    renewend3monthagoplus,
    consultantid
FROM 
    {% if is_incremental() %}
        renewals_output
    {% else %}
        {{ source('initial_load', 'renewals_initial') }}
    where "month" < date('2024-09-01')
    {% endif %}