{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_class') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT {{etl_load_date()}},
    class.id as id,
    ref_class_id,
    class_code,
    clstype.title as class_type,
    start_date,
    {{ convert_to_local_timestamp(
        'start_date',
        'tz.time_zone_id'
    ) }} as local_start_date,
    number_of_seats,
    number_of_students,
    center_id,
    number_of_waiting_students,
    is_complete,
    is_closed,
    is_online,
    class_description,
    teacher_id,
    categories_abbreviations,
    Duration,
    is_pro,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated,
    company_id,
    is_visible_in_group,
    is_b2_b,
    CASE
        When technology = -1 then 'none'
        When technology = 0 then 'webex'
        When technology = 1 then 'tokbox'
        When technology = 2 then 'zoom'
        When technology = 3 then 'dc'
        When technology = 4 then 'tencent'
        Else CAST(
            technology AS Varchar
        )
    end as technology,
    is_restricted_to_online_only,
    is_teen
from
    ods_data as class
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = class.center_id
    Left Join (
        select
            code,
            title
        from
            {{ ref('ods_ls_class_type') }}
    ) as clstype
    ON class.class_type = clstype.code
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
