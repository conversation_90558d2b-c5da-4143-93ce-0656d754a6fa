{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        sequence,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        registrationid,
        courseid,
        centerid,
        firstcategoryid,
        lastcategoryid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'registrationcourse'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    sequence,
    created,
    lastupdated as last_updated,
    id,
    registrationid as registration_id,
    courseid as course_id,
    centerid as center_id,
    firstcategoryid as first_category_id,
    lastcategoryid as last_category_id
FROM
    rankedrecords
WHERE
    rn = 1;
