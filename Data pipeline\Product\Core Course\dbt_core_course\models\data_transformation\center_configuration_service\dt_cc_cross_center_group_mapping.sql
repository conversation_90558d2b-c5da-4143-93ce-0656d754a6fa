{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_cc_cross_center_group_mapping') }}
)
SELECT 
    {{etl_load_date()}},
    is_active,
    id,
    center_id,
    cross_center_group_id
FROM
    ods_data as crosscentergroupmapping