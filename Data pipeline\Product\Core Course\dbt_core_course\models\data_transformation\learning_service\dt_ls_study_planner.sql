{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_study_planner') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT 
    {{etl_load_date()}},
    studyplanner.id as id,
    studyplanner.user_id as user_id,
    category_id,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(path, '.', 1), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(path, '.', 1), '[^0-9]', '')
    END AS category_level,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(path, '.', 2), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(path, '.', 2), '[^0-9]', '')
    END AS category_unit,
    cattype.name as category_type,
    start_date,
    {{ convert_to_local_timestamp(
        'start_date',
        'tz.time_zone_id'
    ) }} as local_start_date,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated,
    CASE
        When study_plan_type = 4 then 'fourweeks'
        When study_plan_type = 8 then 'eightweeks'
        When study_plan_type = 12 then 'twelveweeks'
        Else CAST(
            study_plan_type AS Varchar
        )
    End as study_plan_type,
    show_current_unit_banner_flag,
    show_current_level_popup_flag,
    is_first_lesson_popup_shown
from
    ods_data as studyplanner
    Left Join (
        select
            id,
            path,
            category_type_id
        from
            {{ ref('ods_ls_category') }}
    ) as category
    ON studyplanner.category_id = category.id
    Left Join (
        select
            id,
            Name
        from
            {{ ref('ods_ls_category_type') }}
    ) cattype
    ON category.category_type_id = cattype.id
    Left Join (
        select
            user_id,
            center_id
        from
            {{ ref('ods_ls_user') }}
    ) User
    ON studyplanner.user_id = User.user_id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = User.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
