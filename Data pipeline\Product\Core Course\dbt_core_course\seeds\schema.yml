version: 2

seeds:
  - name: current_exchange_rate
    description: >
      Contains exchange rate data for different territories, including historical rates and forecasts.
      Used for financial calculations and reporting across different currencies.
    columns:
      - name: Territory
        description: Name of the territory or country
      - name: Month
        description: Month of the exchange rate data
      - name: Date
        description: Date of the exchange rate data
      - name: MonthYear
        description: Month and year in YYYY/MMM format
      - name: Number_Of_Days
        description: Number of days in the month
      - name: Sales_Self_Report
        description: Self-reported sales figures in local currency
      - name: Prior_Year
        description: Prior year sales in local currency
      - name: Prior_Year_USD
        description: Prior year sales converted to USD
      - name: Y2019_Local
        description: 2019 sales in local currency
      - name: Y2019_USD
        description: 2019 sales converted to USD
      - name: Y2018_Local
        description: 2018 sales in local currency
      - name: Y2018_USD
        description: 2018 sales converted to USD
      - name: Y2017_Local
        description: 2017 sales in local currency
      - name: Y2017_USD
        description: 2017 sales converted to USD
      - name: Target
        description: Target sales in local currency
      - name: Budget_USD
        description: Budget in USD
      - name: Forecast
        description: Forecasted sales in local currency
      - name: Territory_Forecast
        description: Territory-specific sales forecast
      - name: Stretch_Goal
        description: Stretch goal for sales in local currency
      - name: Exchange_Rate
        description: Exchange rate to USD
      - name: Current_Exchange_Rate
        description: Current exchange rate to USD
        config:
          column_types:
            Territory_Forecast: double

  - name: fact_dynamic_targets
    description: >
      Contains dynamic targets for different territories and activities.
      Used for performance tracking and goal setting.
    columns:
      - name: Territory
        description: Name of the territory or country
      - name: Activity
        description: Activity rate adjustment factor
      - name: TotalDone1+
        description: Adjustment factor for total activities completed
      - name: MPR
        description: Adjustment factor for Monthly Participation Rate
      - name: TerritoryYear
        description: Territory and year combined (e.g., Argentina2022)

  - name: fx_rates_eur_to_usd_current
    description: >
      Contains historical and current exchange rates from EUR to USD.
      Used for currency conversion in financial reporting.
    columns:
      - name: Year
        description: Year of the exchange rate
      - name: Month
        description: Month of the exchange rate
      - name: fx_rates_eur_to_usd_current
        description: Exchange rate from EUR to USD
      - name: Date
        description: Date in YYYY MMM format

  - name: pricing
    description: >
      Contains pricing data for different territories over time.
      Used for financial analysis and reporting.
    columns:
      - name: Territory_Name
        description: Name of the territory or country
      - name: Year
        description: Year of the pricing data
      - name: Month
        description: Month of the pricing data
      - name: Value
        description: Price value
      - name: Date
        description: Date in YYYY MMM format

  - name: Fixed_Charges
    description: >
      Contains fixed charges data for different territories.
      Used for financial planning and reporting.
    columns:
      - name: Territory_Name
        description: Name of the territory or country
      - name: Month
        description: Month in MMM-YY format
      - name: First_Later
        description: Indicates if this is a first or later charge
      - name: DM_Fixed_Revenue
        description: Fixed revenue amount for Digital Marketing

  - name: dm_budget_territory
    description: >
      Contains budget data for territories including sales, level starts, and student metrics.
      Used for financial planning and performance tracking.
    columns:
      - name: Territory
        description: Name of the territory or country
      - name: Date
        description: Date in MM-DD-YYYY format
      - name: DM_Sales
        description: Digital Marketing sales
      - name: Fixed_fee
        description: Fixed fee amount
      - name: Level_start_sales
        description: Sales from level starts
      - name: Average_book_price
        description: Average price of books
      - name: Level_starts
        description: Number of level starts
      - name: Level_starts__Non_core
        description: Number of non-core level starts
      - name: Level_starts__Standard_First_level
        description: Number of standard first level starts
      - name: Level_starts__Standard_Later_level
        description: Number of standard later level starts
      - name: Total_Students_Serviced
        description: Total number of students serviced
      - name: Active_Students
        description: Number of active students
      - name: Encounters_Attended
        description: Number of encounters attended

  - name: center_group
    description: >
      Contains mapping between centers, territories, and groups.
      Used for organizational hierarchy and reporting.
    columns:
      - name: TerritoryName
        description: Name of the territory
      - name: CenterName
        description: Name of the center
      - name: CenterId
        description: Unique identifier for the center
      - name: FinId
        description: Financial identifier for the center
      - name: ReferenceCenterId
        description: Reference ID for the center
      - name: Matched
        description: Flag indicating if the center is matched (1) or not (0)
      - name: GroupName
        description: Name of the group the center belongs to

  - name: novu_reference
    description: >
      Contains reference data for Novu notification campaigns and messages.
      Used for tracking and analyzing notification effectiveness.
    columns:
      - name: Campaign
        description: Name of the notification campaign
      - name: Channel
        description: Communication channel used (Push, Email, etc.)
      - name: Message
        description: Full message content or description
      - name: Message_abbreviated
        description: Abbreviated version of the message
      - name: Treatment
        description: Treatment type (A/B testing)
      - name: Treatment_group
        description: Group for the treatment
      - name: start_date
        description: Start date for the campaign
      - name: end_date
        description: End date for the campaign
      - name: Wokflow_name
        description: Name of the workflow
      - name: Workflow_id
        description: ID of the workflow
      - name: Variant_id
        description: ID of the message variant
      - name: message_type
        description: Type of message (sent, clicked, etc.)
