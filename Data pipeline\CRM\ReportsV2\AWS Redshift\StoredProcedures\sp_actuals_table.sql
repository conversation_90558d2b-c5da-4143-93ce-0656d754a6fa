create or replace procedure hubspot_crm.sp_actuals_table()
    language plpgsql
as
$$

BEGIN
    DROP TABLE IF EXISTS hubspot_crm.actual_table;
    DROP TABLE IF EXISTS #date_table;
    DROP TABLE IF EXISTS #hs_temp;


    CREATE TABLE #date_table
    (
        date_id              int PRIMARY KEY,
        date_value           date,
        --TerritoryId   nvarchar(40),
        territoryname        varchar(128),
        centerid             varchar(40),
        centername           varchar(40),
        source               varchar(40),
        hubspot_owner_id     varchar(128),
        individual_corporate varchar(128)
    );

    -----------------------------------------------------------------
-- Using 2.0 script to build the base of lead to contact dates --
-----------------------------------------------------------------
SELECT hs_object_id
     , lead_date
     , mql_date
     , useful_contact_date
     , booked_date
     , show_date
     , contract_date
     , lost_date
     , first_contract_amount
     , call_campaign
     , CASE
           WHEN (center_name IS NULL OR center_name = '') AND
                (first_contract_center IS NULL OR first_contract_center = '')
               THEN 'No Center'
           WHEN (center_name IS NULL OR center_name = '') AND
                (first_contract_center IS NOT NULL AND first_contract_center <> '')
               THEN first_contract_center
           WHEN center_name = '' THEN 'No Center'
           ELSE center_name
    END                     AS center_name

     , first_contract_center
     , CASE
           WHEN hubspot_owner_id IS NULL
               THEN 'NoOwner'

           ELSE hubspot_owner_id
    END                     AS hubspot_owner_id
     , first_contract_owner
     , createdate
     , email
     , contactname
     , how_did_you_hear_about_us
     , individual_corporate
     , channel
     , channel_drill_down_1
     , channel_drill_down_2
     , CASE
           WHEN re_engaged_lead = 'Manually r' THEN 'Old Contacts'
           WHEN [source] IS NULL
               THEN 'No Source'
           WHEN [source] = ''
               THEN 'No Source'
           WHEN [source] = 'NULL'
               THEN 'No Source'

           ELSE [source]
    END                     AS source
     , sub_source
     , tmk_owner
     , recent_conversion_date
     , recent_conversion_event_name
     , course_age_group
     , latestrecord
     , territory_code
     , re_engaged_lead
     , territoryname


  INTO #hs_temp -- Creating temporary table with 2.0 script
  FROM (SELECT cr.hs_object_id
             , cr.lead_date
             , cr.mql_date
             , cr.useful_contact_date
             , cr.booked_date
             , cr.show_date
             , cr.contract_date
             , cr.lost_date
             , cr.first_contract_amount
             , cr.call_campaign
             , cr.center_name
             , cr.first_contract_center
             , cr.hubspot_owner_id
             , cr.first_contract_owner
             , cr.createdate
             , cr.email
             , CONCAT(CONCAT(firstname, ' '), lastname) AS contactname
             , cr.how_did_you_hear_about_us
             , cr.individual_corporate
             , cr.channel
             , cr.channel_drill_down_1
             , cr.channel_drill_down_2
             , cr.source
             , cr.sub_source
             , cr.tmk_owner
             , cr.recent_conversion_date
             , cr.recent_conversion_event_name
             , cr.course_age_group
             , cr.latestrecord
             , cr.etl_account                           AS territory_code
             , cr.re_engaged_lead
             , CASE
                   WHEN cr.etl_account = 'CL'
                       THEN 'Chile'
                   WHEN cr.etl_account = 'CO'
                       THEN 'Colombia'
                   WHEN cr.etl_account = 'DO' AND cr.center_name = 'Santo Domingo: Novocentro'
                       THEN 'Dominican Republic'
                   WHEN cr.etl_account = 'DO'
                       THEN 'Venezuela'
                   WHEN cr.etl_account = 'EC'
                       THEN 'Ecuador'
                   WHEN cr.etl_account = 'PE'
                       THEN 'Peru'
                   WHEN cr.etl_account = 'TN'
                       THEN 'Tunisia'
                   WHEN cr.etl_account = 'ES'
                       THEN 'Spain'
                   WHEN cr.etl_account = 'KZ'
                       THEN 'Kazakhstan'
                   WHEN cr.etl_account = 'MN'
                       THEN 'Mongolia'
                   WHEN cr.etl_account = 'SA'
                       THEN 'Saudi Arabia'
                   WHEN cr.etl_account = 'AR'
                       THEN 'Argentina'
                   WHEN cr.etl_account = 'UY'
                       THEN 'Uruguay'
                   WHEN cr.etl_account = 'US'
                       THEN 'USA'
                   WHEN cr.etl_account = 'MX'
                       THEN 'Mexico'
                   WHEN cr.etl_account = 'DE'
                       THEN 'Germany'
                   WHEN cr.etl_account = 'DZ'
                       THEN 'Algeria'
                   WHEN cr.etl_account = 'FR'
                       THEN 'France'
                   WHEN cr.etl_account = 'CH' AND cr.center_name = 'WSE Praha'
                       THEN 'Czech Republic'
                   WHEN cr.etl_account = 'CH'
                       THEN 'Switzerland'
                   WHEN cr.etl_account = 'KR'
                       THEN 'Korea'
                   ELSE NULL
          END                                           AS territoryname


          FROM devdwh.hubspot_crm.contactsreport cr
                   LEFT JOIN devdwh.hubspot_crm.contactsenriched dc
                             ON cr.hs_object_id = dc.hs_object_id
                                 AND cr.etl_account = dc.territory_code


         WHERE (cr.source NOT IN ('Renewal', 'Renewals', 'Alumni', 'Existing Customer', 'Credit Note', 'Upgrade'))

           --AND cr.etl_latest = 1
           AND CR.etl_account IN ('KZ', 'MN', 'ES')
           AND DC.hs_object_id IS NOT NULL
           --and cr.individual_corporate <> ''

         UNION ALL

        SELECT d.hs_object_id             AS hs_object_id

             , CAST(d.createdate AS date) AS lead_date
             , CAST(d.createdate AS date) AS mql_date
             , CAST(d.createdate AS date) AS useful_contact_date
             , CAST(d.createdate AS date) AS booked_date

             , CAST(d.createdate AS date) AS show_date
             , CASE
                   WHEN dealstage = 'closedwon'
                       THEN date(closedate)
            END                           AS contract_date
             , CASE
                   WHEN dealstage = 'closedlost'
                       THEN date(closedate)
            END                           AS lost_date
             , CASE
                   WHEN dealstage = 'closedwon'
                       THEN amount
                 else 0
            END                           AS first_contract_amount

             , NULL                         AS call_campaign
             , center_name
             , NULL                         AS first_contract_center
             , CAST(d.hubspot_owner_id AS varchar) AS hubspot_owner_id
             , NULL                         AS first_contract_owner
             , date(hs_createdate)        AS createdate
             , NULL                         AS email
             , dealname                   AS contactname
             , NULL                         AS how_did_you_hear_about_us
             , individual_corporate
             , NULL                         AS channel
             , NULL                         AS channel_drill_down_1
             , NULL                         AS channel_drill_down_2
             , [source]                   AS source
             , sub_source
             , NULL                         AS tmk_owner
             , NULL                         AS recent_conversion_event_name
             , NULL                         AS recent_conversion_event_name
             , course_age_group
             , 1                          AS latestrecord
             , d.territory_code
             , NULL                       AS re_engaged_lead
             , CASE
                   WHEN d.territory_code = 'CL'
                       THEN 'Chile'
                   WHEN d.territory_code = 'CO'
                       THEN 'Colombia'
                   WHEN d.territory_code = 'DO' AND d.center_name = 'Santo Domingo: Novocentro'
                       THEN 'Dominican Republic'
                   WHEN d.territory_code = 'DO'
                       THEN 'Venezuela'
                   WHEN d.territory_code = 'EC'
                       THEN 'Ecuador'
                   WHEN d.territory_code = 'PE'
                       THEN 'Peru'
                   WHEN d.territory_code = 'TN'
                       THEN 'Tunisia'
                   WHEN d.territory_code = 'ES'
                       THEN 'Spain'
                   WHEN d.territory_code = 'KZ'
                       THEN 'Kazakhstan'
                   WHEN d.territory_code = 'MN'
                       THEN 'Mongolia'
                   WHEN d.territory_code = 'SA'
                       THEN 'Saudi Arabia'
                   WHEN d.territory_code = 'AR'
                       THEN 'Argentina'
                   WHEN d.territory_code = 'UY'
                       THEN 'Uruguay'
                   WHEN d.territory_code = 'US'
                       THEN 'USA'
                   WHEN d.territory_code = 'MX'
                       THEN 'Mexico'
                   WHEN d.territory_code = 'DE'
                       THEN 'Germany'
                   WHEN d.territory_code = 'DZ'
                       THEN 'Algeria'
                   WHEN d.territory_code = 'FR'
                       THEN 'France'
                   WHEN d.territory_code = 'CH' AND d.center_name = 'WSE Praha'
                       THEN 'Czech Republic'
                   WHEN d.territory_code = 'CH'
                       THEN 'Switzerland'
                   WHEN d.territory_code = 'KR'
                       THEN 'Korea'
                   ELSE NULL
            END                           AS territoryname

          FROM devdwh.hubspot_crm.dealsenriched d
                   LEFT JOIN hubspot_crm.associationdealstocontacts dtc
                             ON d.hs_object_id = dtc.hs_object_id
                                 AND d.territory_code = dtc.territory_code

         WHERE (d.source IN ('Renewal', 'Renewals', 'Alumni', 'Existing Customer', 'Credit Note', 'Upgrade')
             OR dtc.hs_object_id IS NULL)

           AND D.territory_code IN ('MN', 'ES', 'KZ')

           AND d.deleteflag = 'N') A;

    ---------------------------------------------------------------------------------------------------------------------------------------
-- Creating #date_table with all the possible combinations of: Territory - Center - Source - Hubspot_owner_id, and then adding date --
--------------------------------------------------------------------------------------------------------------------------------------

    INSERT
      INTO #date_table ( date_id, date_value, territoryname, centerid, centername, source, hubspot_owner_id
                       , individual_corporate)

      WITH
          RECURSIVE daterange(date_value) AS (SELECT date '2021-01-01'
                                               UNION ALL
                                              SELECT TRUNC(DATEADD(DAY, 1, date_value))
                                                FROM daterange
                                               WHERE TRUNC(DATEADD(DAY, 1, date_value)) <= LAST_DAY(CURRENT_DATE))


    SELECT ROW_NUMBER() OVER (ORDER BY dt.date_value) AS date_id
         , CAST(dt.date_value AS timestamp)           AS date_value
         , hs.territoryname
         , centerid                                   AS centerid
         , hs.center_name                             AS centername
         , hs.source                                  AS source
         , hs.hubspot_owner_id
         , hs.individual_corporate
      FROM daterange dt
         , (SELECT DISTINCT territoryname
                          , center_name
                          , source
                          , hubspot_owner_id
                          , individual_corporate
              FROM #hs_temp) hs

               LEFT JOIN devdwh.warehouse.dim_geolocation ce
                         ON hs.center_name = ce.centername;

      WITH lead_volume AS
          (SELECT #date_table.date_value    AS lead_date
                , #date_table.centername    AS center_name
                , #date_table.source
                , #date_table.hubspot_owner_id
                , #date_table.territoryname AS territory_name
                , #date_table.individual_corporate
                , COUNT(lead_date)          AS leads
             FROM #date_table
                      LEFT JOIN #hs_temp
                                ON #date_table.date_value = lead_date
                                    AND #date_table.territoryname = #hs_temp.territoryname
                                    AND center_name = centername
                                    AND #hs_temp.source = #date_table.source
                                    AND #date_table.hubspot_owner_id = #hs_temp.hubspot_owner_id
                                    AND #date_table.individual_corporate = #hs_temp.individual_corporate

            WHERE (#hs_temp.lead_date < TRUNC(GETDATE())
                OR #hs_temp.lead_date IS NULL)

            GROUP BY #date_table.date_value, #date_table.centername, #date_table.source, #date_table.hubspot_owner_id
                   , #date_table.territoryname, #date_table.individual_corporate)
         , mql_volume AS
          (SELECT #date_table.date_value    AS mql_date
                , #date_table.centername    AS center_name
                , #date_table.source
                , #date_table.hubspot_owner_id
                , #date_table.territoryname AS territory_name
                , #date_table.individual_corporate
                , COUNT(mql_date)           AS mql
             FROM #date_table
                      LEFT JOIN #hs_temp
                                ON #date_table.date_value = mql_date
                                    AND #date_table.territoryname = #hs_temp.territoryname
                                    AND center_name = centername
                                    AND #hs_temp.source = #date_table.source
                                    AND #date_table.hubspot_owner_id = #hs_temp.hubspot_owner_id
                                    AND #date_table.individual_corporate = #hs_temp.individual_corporate

            WHERE (#hs_temp.mql_date < TRUNC(GETDATE())
                OR #hs_temp.mql_date IS NULL)

            GROUP BY #date_table.date_value, #date_table.centername, #date_table.source, #date_table.hubspot_owner_id
                   , #date_table.territoryname, #date_table.individual_corporate)
         , useful_contact_volume AS
          (SELECT #date_table.date_value     AS useful_contact_date
                , #date_table.centername     AS center_name
                , #date_table.source
                , #date_table.hubspot_owner_id
                , #date_table.territoryname  AS territory_name
                , #date_table.individual_corporate
                , COUNT(useful_contact_date) AS useful_contact
             FROM #date_table
                      LEFT JOIN #hs_temp
                                ON #date_table.date_value = useful_contact_date
                                    AND #date_table.territoryname = #hs_temp.territoryname
                                    AND center_name = centername
                                    AND #hs_temp.source = #date_table.source
                                    AND #date_table.hubspot_owner_id = #hs_temp.hubspot_owner_id
                                    AND #date_table.individual_corporate = #hs_temp.individual_corporate

            WHERE (#hs_temp.useful_contact_date < TRUNC(GETDATE())
                OR #hs_temp.useful_contact_date IS NULL)

            GROUP BY #date_table.date_value, #date_table.centername, #date_table.source, #date_table.hubspot_owner_id
                   , #date_table.territoryname, #date_table.individual_corporate)
         , booked_volume AS
          (SELECT #date_table.date_value    AS booked_date
                , #date_table.centername    AS center_name
                , #date_table.source
                , #date_table.hubspot_owner_id
                , #date_table.territoryname AS territory_name
                , #date_table.individual_corporate
                , COUNT(booked_date)        AS booked
             FROM #date_table
                      LEFT JOIN #hs_temp
                                ON #date_table.date_value = booked_date
                                    AND #date_table.territoryname = #hs_temp.territoryname
                                    AND center_name = centername
                                    AND #hs_temp.source = #date_table.source
                                    AND #date_table.hubspot_owner_id = #hs_temp.hubspot_owner_id
                                    AND #date_table.individual_corporate = #hs_temp.individual_corporate

            WHERE (#hs_temp.booked_date < TRUNC(GETDATE())
                OR #hs_temp.booked_date IS NULL)

            GROUP BY #date_table.date_value, #date_table.centername, #date_table.source, #date_table.hubspot_owner_id
                   , #date_table.territoryname, #date_table.individual_corporate)
         , show_volume AS
          (SELECT #date_table.date_value    AS show_date
                , #date_table.centername    AS center_name
                , #date_table.source
                , #date_table.hubspot_owner_id
                , #date_table.territoryname AS territory_name
                , #date_table.individual_corporate
                , COUNT(show_date)          AS shows
             FROM #date_table
                      LEFT JOIN #hs_temp
                                ON #date_table.date_value = show_date
                                    AND #date_table.territoryname = #hs_temp.territoryname
                                    AND center_name = centername
                                    AND #hs_temp.source = #date_table.source
                                    AND #date_table.hubspot_owner_id = #hs_temp.hubspot_owner_id
                                    AND #date_table.individual_corporate = #hs_temp.individual_corporate

            WHERE (#hs_temp.show_date < TRUNC(GETDATE())
                OR #hs_temp.show_date IS NULL)

            GROUP BY #date_table.date_value, #date_table.centername, #date_table.source, #date_table.hubspot_owner_id
                   , #date_table.territoryname, #date_table.individual_corporate)
         , contract_volume AS
          (SELECT #date_table.date_value     AS contract_date
                , #date_table.centername     AS center_name
                , #date_table.source
                , #date_table.hubspot_owner_id
                , #date_table.territoryname  AS territory_name
                , #date_table.individual_corporate
                , COUNT(contract_date)       AS contracts
                , SUM(first_contract_amount) AS total_sales_amount

             FROM #date_table
                      LEFT JOIN #hs_temp
                                ON #date_table.date_value = contract_date
                                    AND #date_table.territoryname = #hs_temp.territoryname
                                    AND center_name = centername
                                    AND #hs_temp.source = #date_table.source
                                    AND #date_table.hubspot_owner_id = #hs_temp.hubspot_owner_id
                                    AND #date_table.individual_corporate = #hs_temp.individual_corporate

            WHERE (#hs_temp.contract_date < TRUNC(GETDATE())
                OR #hs_temp.contract_date IS NULL)

            GROUP BY #date_table.date_value, #date_table.centername, #date_table.source, #date_table.hubspot_owner_id
                   , #date_table.territoryname, #date_table.individual_corporate)
         , full_funnel_volume AS
          (SELECT #date_table.*
                , CAST(#date_table.date_value AS date)             AS weekday
                , CONCAT(CONCAT(DATE_PART(YEAR, #date_table.date_value), ''),
                         DATE_PART(MONTH, #date_table.date_value)) AS yearmonth
                , lead_volume.leads
                , mql_volume.mql
                , useful_contact_volume.useful_contact
                , booked_volume.booked
                , show_volume.shows
                , contract_volume.contracts
                , contract_volume.total_sales_amount

             FROM #date_table

                      LEFT JOIN lead_volume
                                ON #date_table.territoryname = lead_volume.territory_name
                                    AND #date_table.centername = lead_volume.center_name
                                    AND #date_table.source = lead_volume.source
                                    AND #date_table.hubspot_owner_id = lead_volume.hubspot_owner_id
                                    AND #date_table.individual_corporate = lead_volume.individual_corporate
                                    AND #date_table.date_value = lead_volume.lead_date

                      LEFT JOIN mql_volume
                                ON #date_table.territoryname = mql_volume.territory_name
                                    AND #date_table.centername = mql_volume.center_name
                                    AND #date_table.source = mql_volume.source
                                    AND #date_table.hubspot_owner_id = mql_volume.hubspot_owner_id
                                    AND #date_table.individual_corporate = mql_volume.individual_corporate
                                    AND #date_table.date_value = mql_volume.mql_date

                      LEFT JOIN useful_contact_volume
                                ON #date_table.territoryname = useful_contact_volume.territory_name
                                    AND #date_table.centername = useful_contact_volume.center_name
                                    AND #date_table.source = useful_contact_volume.source
                                    AND #date_table.hubspot_owner_id = useful_contact_volume.hubspot_owner_id
                                    AND #date_table.individual_corporate = useful_contact_volume.individual_corporate
                                    AND #date_table.date_value = useful_contact_volume.useful_contact_date

                      LEFT JOIN booked_volume
                                ON #date_table.territoryname = booked_volume.territory_name
                                    AND #date_table.centername = booked_volume.center_name
                                    AND #date_table.source = booked_volume.source
                                    AND #date_table.hubspot_owner_id = booked_volume.hubspot_owner_id
                                    AND #date_table.individual_corporate = booked_volume.individual_corporate
                                    AND #date_table.date_value = booked_volume.booked_date

                      LEFT JOIN show_volume
                                ON #date_table.territoryname = show_volume.territory_name
                                    AND #date_table.centername = show_volume.center_name
                                    AND #date_table.source = show_volume.source
                                    AND #date_table.hubspot_owner_id = show_volume.hubspot_owner_id
                                    AND #date_table.individual_corporate = show_volume.individual_corporate
                                    AND #date_table.date_value = show_volume.show_date

                      LEFT JOIN contract_volume
                                ON #date_table.territoryname = contract_volume.territory_name
                                    AND #date_table.centername = contract_volume.center_name
                                    AND #date_table.source = contract_volume.source
                                    AND #date_table.hubspot_owner_id = contract_volume.hubspot_owner_id
                                    AND #date_table.individual_corporate = contract_volume.individual_corporate
                                    AND #date_table.date_value = contract_volume.contract_date)--,


    SELECT ff.date_value
         , ff.weekday
         , ff.yearmonth
         , ff.territoryname
         , ff.centername
         , ff.source
         , ff.hubspot_owner_id
         , ff.individual_corporate
         , ff.leads
         , ff.mql
         , ff.useful_contact
         , ff.booked
         , ff.shows
         , ff.contracts
         , ff.total_sales_amount
         , SUM(ff2.leads)              AS leads_cum
         , SUM(ff2.mql)                AS mql_cum
         , SUM(ff2.useful_contact)     AS useful_contract_cum
         , SUM(ff2.booked)             AS booked_cum
         , SUM(ff2.shows)              AS shows_cum
         , SUM(ff2.contracts)          AS contract_cum
         , SUM(ff2.total_sales_amount) AS total_sales_amount_cum

      INTO devdwh.hubspot_crm.actual_table

      FROM full_funnel_volume ff

               LEFT JOIN full_funnel_volume ff2
                         ON ff.date_value >= ff2.date_value
                             AND ff.yearmonth = ff2.yearmonth
                             AND ff.territoryname = ff2.territoryname
                             AND ff.centername = ff2.centername
                             AND ff.source = ff2.source
                             AND ff.hubspot_owner_id = ff2.hubspot_owner_id
                             AND ff.individual_corporate = ff2.individual_corporate

     GROUP BY ff.date_value, ff.weekday, ff.yearmonth, ff.territoryname, ff.centername, ff.source, ff.hubspot_owner_id
            , ff.individual_corporate, ff.leads, ff.mql, ff.useful_contact, ff.booked, ff.shows, ff.contracts
            , ff.total_sales_amount;


    COMMIT;


END;

$$;

