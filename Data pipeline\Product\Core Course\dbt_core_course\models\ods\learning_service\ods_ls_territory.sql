{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        referenceterritoryid,
        name,
        preferredlanguageid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'territory'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    created,
    lastupdated as last_updated,
    id,
    referenceterritoryid as reference_territory_id,
    name,
    preferredlanguageid as preferred_language_id
FROM
    rankedrecords
WHERE
    rn = 1;
