{"Stage2": [{"ScdTable": "hubspot.contacts_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "contacts_updated", "TablePath": "s3://etl-hubspot-prod/scdtables/contacts_scd", "TempTableQuery": "select * from hubspot.contacts_scd_temp", "TempTable": "hubspot.contacts_scd_temp", "TempTablePath": "s3://etl-hubspot-prod/temptables/contacts_scd_temp", "RawTable": "hubspot.contacts", "Properties": ["Actual_Status", "address", "agreement_type", "booked_date", "brand_name", "call_campaign", "call_count", "campana_mql", "center_name", "centro_de_estudio", "channel", "channel_drill_down_1", "channel_drill_down_2", "city", "como_nos_conociste", "contract_date", "Core_Course_IDAM_UserId", "core_course_student_id", "course_age_group", "course_type", "createdate", "decision_maker_date", "email", "excludefromstats__c", "first_contract_amount", "first_contract_center", "first_contract_owner", "first_conversion_date", "first_conversion_event_name", "first_source", "first_sub_source", "firstname", "fuente", "fuente_dos", "gender", "how_did_you_hear_about_us", "hs_analytics_first_url", "hs_analytics_source", "hs_analytics_source_data_1", "hs_analytics_source_data_2", "hs_createdate", "hs_lastmodifieddate", "hs_lifecyclestage_customer_date", "hs_lifecyclestage_evangelist_date", "hs_lifecyclestage_lead_date", "hs_lifecyclestage_marketingqualifiedlead_date", "hs_lifecyclestage_opportunity_date", "hs_lifecyclestage_other_date", "hs_lifecyclestage_salesqualifiedlead_date", "hs_merged_object_ids", "hs_object_id", "hubspot_owner_id", "hubspotscore", "individual_corporate", "last_touch_utm_campaign", "last_touch_utm_medium", "last_touch_utm_referral", "last_touch_utm_source", "lastmodifieddate", "lastname", "latest_source", "latest_source_drill_down_1", "latest_source_drill_down_2", "lead_date", "lead_source", "lifecyclestage", "lost_date", "medio_landing_mql", "mql_date", "not_interested_suitable_reason", "notes_last_updated", "notes_next_activity_date", "otras_fuentes", "phone", "provedor_mql", "province", "qu_te_motiva_a_aprender_ingl_s_", "qualifying_stage", "razones_de_no_interes", "raz_n_para_aprender_ingl_s", "reason_for_learning_english", "recent_conversion_date", "recent_conversion_event_name", "show_date", "source", "sub_source", "tmk_owner", "useful_contact_date", "where_did_you_hear_about_us", "zip", "id", "createdat", "updatedat", "territory_code", "STRING(archived)", "cycleid"], "OrderProperties": "cycleid", "DistinctProperties": "id", "Condition": "dw.territory_code = raw.territory_code", "PartitionProperties": "id,territory_code"}, {"ScdTable": "hubspot.deals_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "contactsscd", "TablePath": "s3://etl-hubspot-prod/scdtables/deals_scd", "TempTableQuery": "select * from hubspot.deals_scd_temp", "TempTable": "hubspot.deals_scd_temp", "TempTablePath": "s3://etl-hubspot-prod/temptables/deals_scd_temp", "RawTable": "hubspot.deals", "Properties": ["dealid", "isdeleted", "associatedcompanyids", "associateddealids", "<PERSON><PERSON><PERSON>", "amount", "amount_gifts", "amount_interest", "amount_in_home_currency", "booked_date", "business_partner_1_amount", "business_partner_2_hours", "business_partner_3_start_date", "business_partner_4_end_date", "business_partner_5_start_level", "business_partner_6_end_level", "business_partner_7_tags", "center_name", "channel_drill_down_1", "channel_drill_down_2", "closedate", "closed_lost_reason", "closed_won_reason", "coach", "contacted_date", "conversion", "conversion_date", "contract_number", "core_course_7_class_access_type", "core_course_amount", "core_course_end_date", "core_course_levels", "core_course_start_date", "core_course_contract_id", "core_course_student_id", "createdate", "days_to_close", "dealname", "deal_source", "dealstage", "dealtype", "description", "dm_known", "interest_date", "lead_date", "likelihood_to_close", "lost_reason", "lost_stage", "hs_all_accessible_team_ids", "hs_all_owner_ids", "hs_all_team_ids", "hs_analytics_source", "hs_analytics_source_data_1", "hs_analytics_source_data_2", "hs_campaign", "hs_createdate", "hs_deal_amount_calculation_preference", "hs_lastmodifieddate", "hs_object_id", "hs_sales_email_last_replied", "hubspot_owner_assigneddate", "hubspot_owner_id", "hubspot_team_id", "individual_corporate", "market_leader_1_amount", "market_leader_2_hours", "market_leader_3_start_date", "market_leader_4_end_date", "market_leader_5_start_level", "market_leader_6_end_level", "membership", "mql_date", "notes_last_contacted", "notes_last_updated", "notes_next_activity_date", "num_associated_contacts", "num_contacted_notes", "num_notes", "pe_end_level", "pe_start_level", "pipeline", "prospect_date", "showed_date", "source", "stage", "sub_source", "test_prep_group_1_amount", "test_prep_group_2_quantity", "test_prep_group_3_start_date", "test_prep_group_4_end_date", "test_prep_types_1", "extraurlparameters", "channel", "show_location", "core_course_idam_userid", "offer_date", "offer_amount", "course_age_group", "core_course_7_product_type", "amount_net", "call_campaign", "center_name_deal", "centro_de_estudio", "certifications_1_amount", "certifications_2_quantity", "certifications_3_type", "certifications_4_start_date", "certifications_5_end_date", "convenzioni", "core_course_7_cross_center_booking", "core_course_7_type", "core_course_fit_0_group_name", "core_course_fit_2_hours", "core_course_fit_3_start_date", "core_course_fit_4_end_date", "core_course_fit_5_start_level", "core_course_fit_6_end_level", "core_course_fit_7_type", "core_course_fit_amount", "core_course_online_1_amount", "core_course_online_2_levels", "core_course_online_3_start_date", "core_course_online_4_end_date", "core_course_online_5_start_level", "core_course_online_6_end_level", "core_course_online_7_type", "fuente_wse", "hs_merged_object_ids", "ilc_1_amount", "ilc_2_hours", "ilc_3_start_date", "ilc_4_end_date", "ilc_6_tags", "ilc_language", "market_leader_7_tags", "otras_fuentes", "sales_center_name", "subsource", "test_prep_executive_1_amount", "test_prep_executive_2_quantity", "test_prep_executive_4_start_date", "test_prep_executive_5_end_date", "test_prep_executive_6_tags", "test_prep_executive_types", "test_prep_group_0_group_name", "test_prep_group_2_tags", "voucher_welfare", "id", "createdat", "updatedat", "territory_code", "STRING(archived)", "cycleid"], "OrderProperties": "hs_lastmodifieddate", "DistinctProperties": "id", "Condition": "dw.territory_code = raw.territory_code", "PartitionProperties": "id,territory_code,cycleid"}, {"ScdTable": "hubspot.owners_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "contactsscd", "TablePath": "s3://etl-hubspot-prod/scdtables/owners_scd", "TempTableQuery": "select * from hubspot.owners_scd_temp", "TempTable": "hubspot.owners_scd_temp", "TempTablePath": "s3://etl-hubspot-prod/temptables/owners_scd_temp", "RawTable": "hubspot.owners", "Properties": ["id", "email", "firstname", "lastname", "userid", "createdat", "updatedat", "STRING(archived)", "territory_code", "cycleid"], "OrderProperties": "updatedat", "DistinctProperties": "id", "Condition": "dw.territory_code = raw.territory_code", "PartitionProperties": "id,territory_code,cycleid"}, {"ScdTable": "hubspot.teams_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "teams_updated", "TablePath": "s3://etl-hubspot-prod/scdtables/teams_scd", "TempTableQuery": "select * from hubspot.teams_scd_temp", "TempTable": "hubspot.teams_scd_temp", "TempTablePath": "s3://etl-hubspot-prod/temptables/teams_scd_temp", "RawTable": "hubspot.teams", "Properties": ["id", "name", "primary", "ownerid", "territory_code", "cycleid"], "OrderProperties": "id", "DistinctProperties": "id", "Condition": "dw.territory_code = raw.territory_code", "PartitionProperties": "id,territory_code,ownerid,cycleid"}, {"ScdTable": "hubspot.customobject_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "contactsscd", "TablePath": "s3://etl-hubspot-prod/scdtables/customobject_scd", "TempTableQuery": "select * from hubspot.customobject_scd_temp", "TempTable": "hubspot.customobject_scd_temp", "TempTablePath": "s3://etl-hubspot-prod/temptables/customobject_scd_temp", "RawTable": "hubspot.customobject", "Properties": ["amount_for_sales___contract", "contract_category", "hs_createdate", "hs_lastmodifieddate", "hs_object_id", "name", "sign_date", "status_reason", "id", "createdat", "updatedat", "STRING(archived)", "territory_code", "cycleid", "contactid", "contacttype"], "OrderProperties": "hs_lastmodifieddate", "DistinctProperties": "id", "Condition": "dw.territory_code = raw.territory_code", "PartitionProperties": "id,territory_code,cycleid"}, {"ScdTable": "hubspot.companies_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "contactsscd", "TablePath": "s3://etl-hubspot-prod/scdtables/companies_scd", "TempTableQuery": "select * from hubspot.companies_scd_temp", "TempTable": "hubspot.companies_scd_temp", "TempTablePath": "s3://etl-hubspot-prod/temptables/companies_scd_temp", "RawTable": "hubspot.companies", "Properties": ["annualrevenue", "booked_date", "domain", "hs_object_id", "name", "hubspot_owner_id", "company_source", "contacted_date", "contract_date", "createdate", "create_deal", "hs_date_entered_customer", "hs_date_entered_evangelist", "hs_date_entered_lead", "hs_date_entered_marketingqualifiedlead", "hs_date_entered_opportunity", "hs_date_entered_other", "hs_date_entered_salesqualifiedlead", "hs_date_entered_subscriber", "decision_maker_date", "hubspot_team_id", "industry", "lead_date", "lifecyclestage", "lost_date", "mql_date", "hs_createdate", "hs_analytics_source_data_1", "hs_analytics_source_data_2", "hs_analytics_source", "potential", "qualifying_stage_company", "segment", "show_date", "state", "value_per_employee", "hs_lastmodifieddate", "id", "createdat", "updatedat", "territory_code", "STRING(archived)", "cycleid"], "OrderProperties": "hs_lastmodifieddate", "DistinctProperties": "id", "Condition": "dw.territory_code = raw.territory_code", "PartitionProperties": "id,territory_code,cycleid"}, {"ScdTable": "hubspot.archivecontacts_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "contactsscd", "TablePath": "s3://etl-hubspot-prod/scdtables/archivecontacts_scd", "TempTableQuery": "select * from hubspot.archivecontacts_scd_temp", "TempTable": "hubspot.archivecontacts_scd_temp", "TempTablePath": "s3://etl-hubspot-prod/temptables/archivecontacts_scd_temp", "RawTable": "hubspot.archivecontacts", "Properties": ["createdate", "email", "firstname", "hs_object_id", "lastmodifieddate", "lastname", "id", "createdat", "updatedat", "territory_code", "STRING(archived)", "archivedat", "cycleid"], "OrderProperties": "lastmodifieddate", "DistinctProperties": "id", "Condition": "dw.territory_code = raw.territory_code", "PartitionProperties": "id,territory_code,cycleid"}, {"ScdTable": "hubspot.archivedeals_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "contactsscd", "TablePath": "s3://etl-hubspot-prod/scdtables/archivedeals_scd", "TempTableQuery": "select * from hubspot.archivedeals_scd_temp", "TempTable": "hubspot.archivedeals_scd_temp", "TempTablePath": "s3://etl-hubspot-prod/temptables/archivedeals_scd_temp", "RawTable": "hubspot.archivedeals", "Properties": ["createdate", "email", "firstname", "hs_object_id", "lastmodifieddate", "lastname", "id", "createdat", "updatedat", "territory_code", "STRING(archived)", "archivedat", "cycleid"], "OrderProperties": "lastmodifieddate", "DistinctProperties": "id", "Condition": "dw.territory_code = raw.territory_code", "PartitionProperties": "id,territory_code,cycleid"}, {"ScdTable": "hubspot.archiveowners_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "contactsscd", "TablePath": "s3://etl-hubspot-prod/scdtables/archiveowners_scd", "TempTableQuery": "select * from hubspot.archiveowners_scd_temp", "TempTable": "hubspot.archiveowners_scd_temp", "TempTablePath": "s3://etl-hubspot-prod/temptables/archiveowners_scd_temp", "RawTable": "hubspot.archiveowners", "Properties": ["createdate", "email", "firstname", "hs_object_id", "lastmodifieddate", "lastname", "id", "createdat", "updatedat", "territory_code", "STRING(archived)", "archivedat", "cycleid"], "OrderProperties": "lastmodifieddate", "DistinctProperties": "id", "Condition": "dw.territory_code = raw.territory_code", "PartitionProperties": "id,territory_code,cycleid"}, {"ScdTable": "hubspot.archivecompanies_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "contactsscd", "TablePath": "s3://etl-hubspot-prod/scdtables/archivecompanies_scd", "TempTableQuery": "select * from hubspot.archivecompanies_scd_temp", "TempTable": "hubspot.archivecompanies_scd_temp", "TempTablePath": "s3://etl-hubspot-prod/temptables/archivecompanies_scd_temp", "RawTable": "hubspot.archivecompanies", "Properties": ["createdate", "domain", "hs_lastmodifieddate", "hs_object_id", "name", "id", "createdat", "updatedat", "territory_code", "STRING(archived)", "archivedat", "cycleid"], "OrderProperties": "hs_lastmodifieddate", "DistinctProperties": "id", "Condition": "dw.territory_code = raw.territory_code", "PartitionProperties": "id,territory_code,cycleid"}, {"ScdTable": "hubspot.webhooks_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "webhooks_scd", "TablePath": "s3://etl-hubspot-prod/scdtables/webhooks_scd", "TempTableQuery": "select * from hubspot.webhooks_scd_temp", "TempTable": "hubspot.webhooks_scd_temp", "TempTablePath": "s3://etl-hubspot-prod/temptables/webhooks_scd_temp", "RawTable": "hubspot.webhooks", "Properties": ["eventId", "subscriptionId", "portalId", "appId", "occurredat", "subscriptionType", "attemptNumber", "objectId", "changeFlag", "changeSource", "sourceid"], "OrderProperties": "occurredat", "DistinctProperties": "objectid", "Condition": "raw.objectid = dw.objectid and dw.portalId = raw.portalId", "PartitionProperties": "objectid,portalId"}, {"ScdTable": "hubspot.contacttodeals_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "associationsupdated", "TablePath": "s3://etl-hubspot-prod/scdtables/contacttodeals_scd", "TempTableQuery": "None", "TempTable": "None", "TempTablePath": "None", "RawTable": "hubspot.contactstodeals", "Properties": ["createdate", "hs_object_id", "lastmodifieddate", "id", "createdat", "updatedat", "territory_code", "cycleid", "dealid", "dealtype"], "OrderProperties": "None", "DistinctProperties": "None", "Condition": "None", "PartitionProperties": "None"}, {"ScdTable": "hubspot.contactstocompanies_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "associationsupdated", "TablePath": "s3://etl-hubspot-prod/scdtables/contactstocompanies_scd", "TempTableQuery": "None", "TempTable": "None", "TempTablePath": "None", "RawTable": "hubspot.contactstocompanies", "Properties": ["createdate", "hs_object_id", "lastmodifieddate", "id", "createdat", "updatedat", "territory_code", "cycleid", "companiesid", "companiestype"], "OrderProperties": "None", "DistinctProperties": "None", "Condition": "None", "PartitionProperties": "None"}, {"ScdTable": "hubspot.companiestocontacts_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "associationsupdated", "TablePath": "s3://etl-hubspot-prod/scdtables/companiestocontacts_scd", "TempTableQuery": "None", "TempTable": "None", "TempTablePath": "None", "RawTable": "hubspot.companiestocontacts", "Properties": ["contract_date", "createdate", "hs_lastmodifieddate", "hs_object_id", "hubspot_owner_id", "hubspot_team_id", "name", "id", "createdat", "updatedat", "territory_code", "cycleid", "contactid", "contacttype"], "OrderProperties": "None", "DistinctProperties": "None", "Condition": "None", "PartitionProperties": "None"}, {"ScdTable": "hubspot.companiestodeals_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "associationsupdated", "TablePath": "s3://etl-hubspot-prod/scdtables/companiestodeals_scd", "TempTableQuery": "None", "TempTable": "None", "TempTablePath": "None", "RawTable": "hubspot.companiestodeals", "Properties": ["contract_date", "createdate", "hs_lastmodifieddate", "hs_object_id", "hubspot_owner_id", "hubspot_team_id", "name", "id", "createdat", "updatedat", "territory_code", "cycleid", "dealid", "dealtype"], "OrderProperties": "None", "DistinctProperties": "None", "Condition": "None", "PartitionProperties": "None"}, {"ScdTable": "hubspot.dealstocontacts_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "associationsupdated", "TablePath": "s3://etl-hubspot-prod/scdtables/dealstocontacts_scd", "TempTableQuery": "None", "TempTable": "None", "TempTablePath": "None", "RawTable": "hubspot.dealstocontacts", "Properties": ["createdate", "hs_lastmodifieddate", "hs_object_id", "id", "createdat", "updatedat", "territory_code", "cycleid", "contactid", "contacttype"], "OrderProperties": "None", "DistinctProperties": "None", "Condition": "None", "PartitionProperties": "None"}, {"ScdTable": "hubspot.dealstocompanies_scd", "Status": 404, "Stage": 2, "Operation": "SCD", "Function": "associationsupdated", "TablePath": "s3://etl-hubspot-prod/scdtables/dealstocompanies_scd", "TempTableQuery": "None", "TempTable": "None", "TempTablePath": "None", "RawTable": "hubspot.dealstocompanies", "Properties": ["createdate", "hs_lastmodifieddate", "hs_object_id", "id", "createdat", "updatedat", "territory_code", "cycleid", "companiesid", "companiestype"], "OrderProperties": "None", "DistinctProperties": "None", "Condition": "None", "PartitionProperties": "None"}]}