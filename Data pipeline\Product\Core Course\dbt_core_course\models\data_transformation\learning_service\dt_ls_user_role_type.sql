{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_user_role_type') }}

{% if is_incremental() %}
where
    updated_date > (
        (
            select
                max(updated_date)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT 
    {{etl_load_date()}},
    userroletype.id as id,
    name,
    created_date,
    updated_date,
    is_active
from
    ods_data as userroletype
