{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}
 
 
with activities_sample as
    (
        select 
            student_id
            ,activity_type
            ,level
            ,unit
            ,lesson
            ,mini_cycle_stage
            ,mini_cycles
            ,content_item_id
            ,completed_date
        from {{ref('activities')}}
        where completed_date >= date('2021-01-01')
        and unit is not null
    )
,activities_per_unit as
    (
        SELECT
            activity_type,
            level,
            unit,
            date_format(completed_date, '%Y-%m') as year_month,
            COUNT(DISTINCT content_item_id) AS total_activities
        FROM
        activities_sample
        GROUP BY
            activity_type,
            level,
            unit,
            date_format(completed_date, '%Y-%m')
    )
,cumulative_activities as
    (
        select
            student_id
            ,activity_type
            ,level
            ,unit
            ,lesson
            ,mini_cycle_stage
            ,mini_cycles
            ,completed_date
            ,row_number() over (partition by student_id, activity_type, level, unit order by completed_date) as cumm_activities
        from activities_sample
    )
,perc_complete as
    (
    select
        act.*
        ,apu.total_activities
        ,act.cumm_activities * 100 / apu.total_activities as perc_activities
    from cumulative_activities act
    left join activities_per_unit apu on act.activity_type = apu.activity_type
                                    and act.level = apu.level
                                    and act.unit = apu.unit
                                    and date_format(act.completed_date, '%Y-%m') = apu.year_month
    order by student_id, activity_type, completed_date
    )
select
    student_id
    ,level
    ,unit
    ,min(case when activity_type = 'multimedia' and perc_activities >= 97 then completed_date else null end) as mm_ready
    ,min(case when activity_type = 'multimedia' and perc_activities >= 66 then completed_date else null end) as mm_complete_66
    ,min(case when activity_type = 'multimedia' and perc_activities >= 33 then completed_date else null end) as mm_complete_33
    ,min(case when activity_type = 'multimedia' and perc_activities >= 0 then completed_date else null end) as mm_complete_0
    ,min(case when activity_type = 'digital_workbook' and perc_activities >= 80 then completed_date else null end) as wb_ready_80
    ,min(case when activity_type = 'digital_workbook' and perc_activities >= 66 then completed_date else null end) as wb_complete_66 
    ,min(case when activity_type = 'digital_workbook' and perc_activities >= 33 then completed_date else null end) as wb_complete_33
    ,min(case when activity_type = 'digital_workbook' and perc_activities >= 0 then completed_date else null end) as wb_complete_0
    from perc_complete
where completed_date >= date '2021-01-01'
group by
    student_id
    ,level
    ,unit