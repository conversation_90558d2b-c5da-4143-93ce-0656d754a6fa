# Importing necessary libraries
from shareplum import Site
from shareplum import Office365
from shareplum.site import Version
import boto3
import io
import ast
import pandas as pd
import numpy as np
import logging
from openpyxl import load_workbook


def get_secret(secret_name, region_name):
    """
    Retrieves the secret from AWS Secret Manager.

    Args:
        secret_name (str): The name of the secret.
        region_name (str): The AWS region where the secret is stored.

    Returns:
        str: The secret value as a string.
    """
    return boto3.client('secretsmanager', region_name=region_name).get_secret_value(SecretId=secret_name)[
        'SecretString']


def read_excel(secret_name, region_name, sharepoint_folder,
               sharepoint_filename, sharepoint_file_sheet_name, dbt_folder, dbt_filename,
               seed_file_index, seed_file_header):
    folder = sharepoint_folder
    filename = sharepoint_filename

    try:
        # Attempt to retrieve and parse the secret
        secret_value = ast.literal_eval(get_secret(secret_name, region_name))
    except Exception as e:
        # Log a warning if an error occurs and raise an exception
        logging.warning("Error retrieving the secret:", e)
        raise Exception

    # SharePoint credential variables
    url = secret_value['url']
    username = secret_value['username']
    password = secret_value['password']
    site_url = secret_value['site_url']

    # Authenticate to SharePoint
    authcookie = Office365(url, username=username, password=password).GetCookies()
    site = Site(site_url, version=Version.v2016, authcookie=authcookie)

    # Get the file from SharePoint
    folder = site.Folder(folder)
    response = folder.get_file(filename)

    # Log the SharePoint response
    logging.warning(response)

    # Get the sheet name from the Excel file
    workbook = load_workbook(io.BytesIO(response), data_only=True)
    get_sheet_names = workbook.sheetnames

    # Log sheet names and set the active sheet
    logging.warning(get_sheet_names)
    sheet = workbook[sharepoint_file_sheet_name]

    # Log available sheet names and the active sheet
    logging.warning("Sheet Names Available " + str(get_sheet_names))
    logging.warning("Sheet Active " + str(sheet))

    # Convert the sheet into a DataFrame
    data_df = pd.DataFrame(sheet.values)
    # The below code is to replace empty space as null in cell values
    data_df = data_df.replace(r'^\s*$', np.nan, regex=True)
    # The below code is to remove unwanted spacing in cell values
    data_df = data_df.apply(lambda x: x.map(lambda y: y.lstrip() if isinstance(y, str) else y))
    data_df = data_df.apply(lambda x: x.map(lambda y: y.rstrip() if isinstance(y, str) else y))

    # Log the DataFrame
    logging.warning(data_df)

    # Specify the folder path and filename for saving the CSV
    folder_path = dbt_folder
    file_name = dbt_filename

    # Combine the folder path and filename
    file_path = f'{folder_path}/{file_name}'

    # Save the DataFrame to CSV
    data_df.to_csv(file_path, index=seed_file_index, header=seed_file_header)

    # Log the saved file path
    logging.warning(f'DataFrame has been saved to: {file_path}')
