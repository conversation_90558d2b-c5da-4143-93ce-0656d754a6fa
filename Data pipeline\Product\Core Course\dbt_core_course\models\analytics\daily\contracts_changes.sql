{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

-- its one-off load history fact valid data
WITH history_fact_valid as (
    select 
        lower(contractid) as contract_id
        ,lower(studentid) as student_id
        ,lower(centerid) as center_id
        ,validfrom as valid_from
        ,validto as valid_to
    from 
    {{ source(
            'hist_valid_cnt',
            'contracts_status_history'
        ) }} 
),
-- fact valid gaps fill
-- Step 1: Identify gaps and generate invalid periods
date_ranges AS (
    SELECT 
        contract_id,
        student_id, 
        center_id, 
        cast(valid_from as timestamp(6)) as valid_from, 
        CAST(CONCAT((CAST(CAST(valid_to AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) AS valid_to,
        'valid' AS status,
        LAG(CAST(CONCAT((CAST(CAST(valid_to AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6))) OVER (PARTITION BY contract_id, student_id, center_id ORDER BY valid_from) AS prev_valid_to
    from history_fact_valid
),
invalid_periods AS (
    SELECT
        contract_id,
        student_id,
        center_id,
        prev_valid_to AS valid_from,
        valid_from AS valid_to,
        'invalid' AS status
    FROM date_ranges
    WHERE prev_valid_to IS NOT NULL
      AND 
      prev_valid_to < valid_from
),
-- Step 2: Combine original data with generated invalid periods
between_invalid_period as (
    SELECT 
        contract_id,
        student_id,
        center_id,
        cast(valid_from as timestamp(6)) as valid_from, 
        CAST(CONCAT((CAST(CAST(valid_to AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6)) AS valid_to,
        'valid' AS status
from history_fact_valid
UNION ALL
SELECT 
    contract_id,
    student_id,
    center_id,
    valid_from,
    valid_to,
    status
FROM invalid_periods
ORDER BY contract_id, valid_from
),
end_invalid_period AS (
    SELECT
        contract_id,
        student_id,
        center_id,
        MAX(CAST(CONCAT((CAST(CAST(valid_to AS Date) as varchar)), ' 23:59:59.997') AS TIMESTAMP(6))) AS last_valid_to
    FROM between_invalid_period
    GROUP BY contract_id, student_id, center_id
),
final_invalid_period AS (
    SELECT
        e.contract_id,
        e.student_id,
        e.center_id,
        e.last_valid_to AS valid_from,
        TIMESTAMP '3000-01-01 23:59:59.997' AS valid_to,
        case when e.last_valid_to != TIMESTAMP '2024-06-20 23:59:59.997' then 'invalid' else 'valid' end AS status
    FROM end_invalid_period e
    WHERE NOT EXISTS (
        SELECT 1
        FROM between_invalid_period g
        WHERE g.contract_id = e.contract_id
          AND g.student_id = e.student_id
          AND g.center_id = e.center_id
          AND g.valid_from > e.last_valid_to
          
    )
),
contracts_filled as (
    SELECT 
        contract_id,
        student_id,
        center_id,
        valid_from,
        valid_to,
        status 
    FROM between_invalid_period
    UNION ALL
    SELECT 
        contract_id,
        student_id,
        center_id,
        valid_from,
        valid_to,
        status  
    FROM final_invalid_period
    ORDER BY contract_id, valid_from
),

valid_contracts as (
    select
        cnt.id as contract_id
        ,cnt_fact.student_id
        ,cnt_fact.center_id
        ,cnt_fact.status
        ,cnt_fact.valid_from
        ,cnt_fact.valid_to
    from contracts_filled as cnt_fact
    left join {{ref("dt_cs_contracts")}} cnt -- converting contract refrenence id
    on cnt_fact.contract_id = cnt.contract_reference_id
    where cnt.id is not null --18 history contracts not have information in contracts table

), 

unified_timeline as ( 
    select 
        contract_id, 
        start_date as valid_from 
    from {{source('hist_valid_cnt','contracts_location_history')}}
    union
    select 
        contract_id, 
        start_date as valid_from 
    from {{source('hist_valid_cnt','contracts_class_access_type_history')}}
    union
    select 
        contract_id, 
        start_date as valid_from 
    from {{source('hist_valid_cnt','contracts_service_type_history')}}
    union
    select 
        contract_id, 
        start_date as valid_from 
    from {{source('hist_valid_cnt','contracts_group_history')}}
    union
    select 
        contract_id, 
        start_date as valid_from 
    from {{source('hist_valid_cnt','contracts_lab_teacher_history')}}
    union
    select 
        contract_id, 
        start_date as valid_from 
    from {{source('hist_valid_cnt','contracts_consultant_history')}}
    union
    select 
        contract_id, 
        start_date as valid_from 
    from {{source('hist_valid_cnt','contracts_membership_history')}}
    union
    select 
        contract_id, 
        valid_from 
    from valid_contracts
    union
    select 
        contract_id, 
        start_date as valid_from 
    from {{source('hist_valid_cnt','contracts_start_date_history')}}
    union
    select 
        contract_id, 
        start_date as valid_from 
    from {{source('hist_valid_cnt','contracts_end_date_history')}}
),

unified_timeline_recalculate_valid_to as (
        select
            contract_id,
            valid_from,
            COALESCE(LEAD(valid_from) OVER(PARTITION BY contract_id ORDER BY valid_from), CAST('3000-01-01' AS timestamp(6))) AS valid_to,
            CASE WHEN COALESCE(LEAD(valid_from) OVER(PARTITION BY contract_id ORDER BY valid_from), CAST('3000-01-01' AS timestamp(6))) = CAST('3000-01-01' AS timestamp(6)) THEN 1 ELSE 0 END AS is_current
        from unified_timeline
    ),
joined as (
        select
            timeline.contract_id,
            scd2_table1.location,
            scd2_table2.class_access_type,
            scd2_table3.service_type,
            scd2_table4.group_id,
            scd2_table5.lab_teacher_id,
            scd2_table6.consultant_id,
            scd2_table7.is_membership,
            scd2_table8.student_id,
            scd2_table8.center_id,
            scd2_table8.status,
            scd2_table9.contract_start_date,
            scd2_table10.contract_end_date,
            coalesce(timeline.valid_from, CAST('1900-01-01' AS timestamp(6))) as valid_from,
            coalesce(timeline.valid_to, CAST('3000-01-01' AS timestamp(6))) as valid_to,
            row_number() over (partition by timeline.contract_id,timeline.valid_from,timeline.valid_to order by timeline.valid_from asc) as rn
        from unified_timeline_recalculate_valid_to as timeline
        left join {{source('hist_valid_cnt','contracts_location_history')}} as scd2_table1
            on timeline.contract_id = scd2_table1.contract_id 
            and scd2_table1.start_date <= timeline.valid_from 
            and scd2_table1.end_date >= timeline.valid_to
        left join {{source('hist_valid_cnt','contracts_class_access_type_history')}} as scd2_table2
            on timeline.contract_id = scd2_table2.contract_id 
            and scd2_table2.start_date <= timeline.valid_from 
            and scd2_table2.end_date >= timeline.valid_to
        left join {{source('hist_valid_cnt','contracts_service_type_history')}} as scd2_table3
            on timeline.contract_id = scd2_table3.contract_id 
            and scd2_table3.start_date <= timeline.valid_from 
            and scd2_table3.end_date >= timeline.valid_to
        left join {{source('hist_valid_cnt','contracts_group_history')}} as scd2_table4
            on timeline.contract_id = scd2_table4.contract_id 
            and scd2_table4.start_date <= timeline.valid_from 
            and scd2_table4.end_date >= timeline.valid_to   
        left join {{source('hist_valid_cnt','contracts_lab_teacher_history')}} as scd2_table5
            on timeline.contract_id = scd2_table5.contract_id 
            and scd2_table5.start_date <= timeline.valid_from 
            and scd2_table5.end_date >= timeline.valid_to  
        left join {{source('hist_valid_cnt','contracts_consultant_history')}} as scd2_table6
            on timeline.contract_id = scd2_table6.contract_id 
            and scd2_table6.start_date <= timeline.valid_from 
            and scd2_table6.end_date >= timeline.valid_to
        left join {{source('hist_valid_cnt','contracts_membership_history')}} as scd2_table7
            on timeline.contract_id = scd2_table7.contract_id 
            and scd2_table7.start_date <= timeline.valid_from 
            and scd2_table7.end_date >= timeline.valid_to 
        left join valid_contracts as scd2_table8
            on timeline.contract_id = scd2_table8.contract_id 
            and scd2_table8.valid_from <= timeline.valid_from 
            and scd2_table8.valid_to >= timeline.valid_to
        left join {{source('hist_valid_cnt','contracts_start_date_history')}} as scd2_table9
            on timeline.contract_id = scd2_table9.contract_id 
            and scd2_table9.start_date <= timeline.valid_from 
            and scd2_table9.end_date >= timeline.valid_to
        left join {{source('hist_valid_cnt','contracts_end_date_history')}} as scd2_table10
            on timeline.contract_id = scd2_table10.contract_id 
            and scd2_table10.start_date <= timeline.valid_from 
            and scd2_table10.end_date >= timeline.valid_to 
    ),

-- this is to handle duplication for some records in the joined cte
remove_duplication  AS (
SELECT 
    contract_id,
    location,
    class_access_type,
    service_type,
    group_id,
    lab_teacher_id,
    consultant_id,
    is_membership,
    student_id,
    center_id,
    status,
    contract_start_date,
    contract_end_date,
    valid_from,
    valid_to
FROM joined
WHERE rn =1
),

-- fix snapshot valid records which is greater than end_date
fix_snapshot as (
    select 
    contract_id
    ,student_id
    ,center_id
    ,status
    ,location
    ,class_access_type
    ,service_type
    ,group_id
    ,lab_teacher_id
    ,consultant_id
    ,is_membership
    ,start_date
    ,end_date
    ,case when
      lag(case when dbt_valid_to > end_date and status = 'valid'
      then date_add('millisecond',-3,date_add('day',1,cast(end_date as timestamp))) -- convert to datetime at the end of the day
      else dbt_valid_to end) over (partition by contract_id order by dbt_updated_at) is null then dbt_valid_from
      else lag(case when dbt_valid_to > end_date and status = 'valid'
      then date_add('millisecond',-3,date_add('day',1,cast(end_date as timestamp)))  -- convert to datetime at the end of the day
      else dbt_valid_to end) over (partition by contract_id order by dbt_updated_at)
      end
      as valid_from
    ,case when dbt_valid_to > end_date and status = 'valid'
        then date_add('millisecond',-3,date_add('day',1,cast(end_date as timestamp)))  -- convert to datetime at the end of the day
        else dbt_valid_to end as valid_to
    ,case when dbt_valid_to > end_date and status = 'valid'
        then 1
        else 0 end as valid_to_in_local
    ,case when lag(dbt_valid_to) over (partition by contract_id order by dbt_updated_at) > lag(end_date) over (partition by contract_id order by dbt_updated_at) 
        and lag(status) over (partition by contract_id order by dbt_updated_at) = 'valid'
        then 1
        else 0 end as valid_from_in_local
    from {{ref("contracts_field_changes")}}
),

union_tables as (
select 
     cnt.contract_reference_id as contract_id
    ,cnt_hist.student_id
    ,cnt_hist.center_id
    ,cnt_hist.status
    ,cnt_hist.location
    ,cnt_hist.class_access_type
    ,cnt_hist.service_type
    ,cnt_hist.group_id
    ,cnt_hist.lab_teacher_id
    ,cnt_hist.consultant_id
    ,cnt_hist.is_membership
    ,cnt_hist.contract_start_date as start_date
    ,cnt_hist.contract_end_date as end_date
    ,cnt_hist.valid_from
    ,CASE
		    WHEN cnt_hist.valid_to = TIMESTAMP '3000-01-01 00:00:00.000000'
		    THEN TIMESTAMP '2024-06-20 00:00:00.000000'    -- previous day start of snapshot
            WHEN cnt_hist.valid_to = TIMESTAMP '3000-01-01 23:59:59.997'
		    THEN TIMESTAMP '2024-06-20 23:59:59.997'    -- previous day end of snapshot
		    ELSE cnt_hist.valid_to
		END AS valid_to
    ,row_number() over (partition by cnt_hist.contract_id order by cnt_hist.valid_from desc) as hist_row
    ,row_number() over (partition by cnt_hist.contract_id order by cnt_hist.valid_from asc) as hist_row_asc
from remove_duplication as cnt_hist
left join {{ref("dt_cs_contracts")}} cnt -- converting contract id
on cnt_hist.contract_id = cnt.id
union all
select 
    contracts_snapshot.contract_id
    ,contracts_snapshot.student_id
    ,contracts_snapshot.center_id
    ,contracts_snapshot.status
    ,contracts_snapshot.location
    ,contracts_snapshot.class_access_type
    ,contracts_snapshot.service_type
    ,contracts_snapshot.group_id
    ,contracts_snapshot.lab_teacher_id
    ,contracts_snapshot.consultant_id
    ,contracts_snapshot.is_membership
    ,contracts_snapshot.start_date
    ,contracts_snapshot.end_date
    ,case 
        when contracts_snapshot.valid_from_in_local = 1 then contracts_snapshot.valid_from
        when contracts_snapshot.valid_from_in_local = 0 then {{ convert_to_local_timestamp ('contracts_snapshot.valid_from', 'student_center.timezone') }}  
        end as valid_from
    ,CASE
            WHEN contracts_snapshot.valid_to IS NOT NULL and contracts_snapshot.valid_to_in_local = 1 THEN contracts_snapshot.valid_to
            WHEN contracts_snapshot.valid_to IS NOT NULL and contracts_snapshot.valid_to_in_local = 0 THEN {{ convert_to_local_timestamp ('contracts_snapshot.valid_to', 'student_center.timezone') }}
            ELSE cast('3000-01-01' AS timestamp(6))
        END AS valid_to
    ,null as hist_row
    ,null as hist_row_asc
from fix_snapshot as contracts_snapshot
LEFT JOIN {{ref('territory_centers')}} as student_center
    ON contracts_snapshot.center_id = student_center.center_reference_id


),
filling_1st_record_fields as(  -- this is a fix to fill the 1st history record field values
select 
    contract_id
    ,student_id
    ,center_id
    ,case when hist_row_asc = 1 then lead(group_id) IGNORE NULLS OVER (partition by contract_id order by valid_from ) else group_id end as group_id
    ,case when hist_row_asc = 1 then lead(lab_teacher_id) IGNORE NULLS OVER (partition by contract_id order by valid_from ) else lab_teacher_id end as lab_teacher_id
    ,case when hist_row_asc = 1 then lead(consultant_id) IGNORE NULLS OVER (partition by contract_id order by valid_from ) else consultant_id end as consultant_id
    ,case when hist_row_asc = 1 then lead(start_date) IGNORE NULLS OVER (partition by contract_id order by valid_from ) else start_date end as start_date
    ,case when hist_row_asc = 1 then lead(end_date) IGNORE NULLS OVER (partition by contract_id order by valid_from ) else end_date end as end_date
    ,valid_from
    ,valid_to
    ,status
    ,case when hist_row_asc = 1 then lead(location) IGNORE NULLS OVER (partition by contract_id order by valid_from) else location end as location
    ,case when hist_row_asc = 1 then lead(class_access_type) IGNORE NULLS OVER (partition by contract_id order by valid_from) else class_access_type end as class_access_type
    ,case when hist_row_asc = 1 then lead(service_type) IGNORE NULLS OVER (partition by contract_id order by valid_from) else service_type end as service_type
    ,case when hist_row_asc = 1 then lead(is_membership) IGNORE NULLS OVER (partition by contract_id order by valid_from) else is_membership end as is_membership
    ,hist_row
    ,hist_row_asc
from union_tables
),
applying_default_values as(
select 
    contract_id
    ,CASE 
        when location is null then 'no_location'
        ELSE location
    END as location
    ,CASE 
        when student_id is null then 'no_student_id'
        ELSE student_id
    END as student_id
    ,CASE 
        when center_id is null then 'no_center_id'
        ELSE center_id
    END as center_id
    ,CASE 
        when status is null then 'no_status'
        ELSE status
    END as status
    ,CASE 
        when class_access_type is null then 'no_class_access_type'
        when class_access_type = '' then 'no_class_access_type'
        ELSE class_access_type
    END as class_access_type
    ,CASE 
        when service_type is null then 'no_service_type'
        ELSE service_type
    END as service_type
    ,CASE 
        when group_id is null then 'no_group'
        ELSE group_id
    END as group_id
    ,CASE 
        when lab_teacher_id is null then 'no_lab_teacher'
        when lab_teacher_id ='' then 'no_lab_teacher'
        ELSE lab_teacher_id
    END as lab_teacher_id
    ,CASE 
        when consultant_id is null then 'no_consultant'
        when consultant_id ='' then 'no_consultant'
        ELSE consultant_id
    END as consultant_id
    ,CASE 
        when is_membership is null then false
        ELSE is_membership
    END as is_membership
    ,CASE 
        when start_date is null then lead(start_date) IGNORE NULLS OVER (partition by contract_id order by valid_from )
        ELSE start_date
    END as start_date
    ,CASE 
        when end_date is null then lead(end_date) IGNORE NULLS OVER (partition by contract_id order by valid_from )
        ELSE end_date
    END as end_date
    ,valid_from
    ,valid_to
    ,hist_row
    ,hist_row_asc
from filling_1st_record_fields
),
correct_valid_to as(
    select 
    contract_id
    ,student_id
    ,center_id
    ,group_id
    ,lab_teacher_id
    ,consultant_id
    ,start_date
    ,end_date
    ,valid_from
    ,case when hist_row = 1 then lead(valid_from) over (partition by contract_id order by valid_from) else valid_to end as valid_to --completes timeline when unioning tables
    ,status
    ,location
    ,class_access_type
    ,service_type
    ,is_membership
from applying_default_values
)

select 
    contract_id
    ,student_id
    ,center_id
    ,group_id
    ,lab_teacher_id
    ,consultant_id
    ,start_date
    ,end_date
    ,valid_from
    ,valid_to
    ,status
    ,location
    ,class_access_type
    ,service_type
    ,is_membership
from correct_valid_to
order by 
    contract_id, 
    valid_from, 
    valid_to, 
    class_access_type,
    location, 
    service_type,
    group_id,
    lab_teacher_id,
    consultant_id,
    is_membership,
    student_id,
    center_id,
    status,
    start_date,
    end_date