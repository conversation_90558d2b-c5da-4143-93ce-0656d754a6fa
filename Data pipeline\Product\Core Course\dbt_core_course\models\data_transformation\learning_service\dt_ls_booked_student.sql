{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_booked_student') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT {{etl_load_date()}},
    bookstudent.id as id,
    class_id,
    student_id,
    book_mode,
    book_date,
    {{ convert_to_local_timestamp(
        'book_date',
        'tz.time_zone_id'
    ) }} as local_book_date,
    result,
    attended,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated,
    booked_by,
    roleJoin1.description as booked_role_title,
    Case
        When roleJoin1.description <> 'student' Then 'staff'
        When roleJoin1.description = 'student' Then 'student'
        Else roleJoin1.description
    End as booked_person_type,
    cancelled_by,
    roleJoin2.description as cancelled_role_title,
    Case
        When roleJoin2.description <> 'student' Then 'staff'
        When roleJoin2.description = 'student' Then 'student'
        Else roleJoin2.description
    End as cancelled_person_type,
    is_cancelled,
    book_mode_modified_date,
    {{ convert_to_local_timestamp(
        'book_mode_modified_date',
        'tz.time_zone_id'
    ) }} as local_book_mode_modified_date,
    CASE
        When standby_notification_type = 1 then 'stillinstandby'
        When standby_notification_type = 2 then 'standbysuccess'
        else CAST(
            standby_notification_type AS varchar
        )
    end as standby_notification_type,
    is_accessed,
    registration_id
from
    ods_data as bookstudent
    Left Join (
        select
            user_id,
            role_id
        from
            {{ ref('ods_ls_user') }}
    ) as userjoin1
    ON bookstudent.booked_by = userjoin1.user_id
    Left Join (
        select
            id,
            description
        from
            {{ ref('ods_ls_role') }}
    ) as roleJoin1
    ON userjoin1.role_id = roleJoin1.id
    Left Join (
        select
            user_id,
            role_id
        from
            {{ ref('ods_ls_user') }}
    ) as userjoin2
    ON bookstudent.cancelled_by = userjoin2.user_id
    Left Join (
        select
            id,
            description
        from
            {{ ref('ods_ls_role') }}
    ) as roleJoin2
    ON userjoin2.role_id = roleJoin2.id
    Left Join (
        select
            user_id,
            center_id
        from
            {{ ref('ods_ls_user') }}
    ) as user
    on bookstudent.student_id = user.user_id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = user.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
