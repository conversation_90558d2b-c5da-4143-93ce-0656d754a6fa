{% macro backfill_date(start="2023-02-01",days=3) %}
   BETWEEN "{{start}}" AND DATE_SUB(CURRENT_DATE("Europe/Paris"), INTERVAL {{days}} DAY) 
{% endmacro %}


{% macro daily_run(days=3) %}
   = DATE_SUB(CURRENT_DATE("Europe/Paris"), INTERVAL {{days}} DAY) 
{% endmacro %}

{% macro ga_date() %}
   PARSE_DATE("%y%m%d",_TABLE_SUFFIX)
{% endmacro %}


{%macro ga_param(key)%}
(SELECT ep.value.string_value FROM UNNEST(event_params) ep WHERE ep.key="{{key}}" )
{% endmacro %}

{%macro ga_select(lang)%}
SELECT parse_date("%Y%m%d",event_date) event_date
,event_name,event_timestamp,event_params,user_pseudo_id,"{{lang}}" as lang
FROM 
{% endmacro %}

{%macro ga_where()%}
 where 
 {% if is_incremental() %}
  -- this filter will only be applied on an incremental run
  {{ga_date()}} {{daily_run()}}
{% else %}
  {{ga_date()}} {{backfill_date("2023-03-01",3)}}
{% endif %}

{% endmacro %}

{# GSC HISTORY #}

{%macro gsch_where(date_end="2023-04-01") %}
    where
 {% if is_incremental() %}
  -- this filter will only be applied on an incremental run
  date_converted {{daily_run()}}
{% else %}
  date_converted < "{{date_end}}" 
{% endif %}   
{%endmacro%}

{%macro gsch_select(lang,country="xxx")%}
select distinct * EXCEPT (page,date,country),
SPLIT(page,"?")[SAFE_OFFSET(0)] as page,
parse_date("%Y-%m-%d",date) event_date,
"{{lang}}" lang,
"{{country}}" country,
country as gsc_country
 FROM
{%endmacro%}

{%macro gsch_site_where(date_end="2023-04-01") %}
    where
 {% if is_incremental() %}
  -- this filter will only be applied on an incremental run
  date_converted {{daily_run()}}
{% else %}
  date_converted < "{{date_end}}" 
{% endif %}   
{%endmacro%}

{%macro gsch_site_select(lang,country="xxx")%}
select distinct * EXCEPT (page,date,country),
parse_date("%Y-%m-%d",date) event_date,
"{{lang}}" lang,
"{{country}}" country,
country as gsc_country
 FROM
{%endmacro%}


{# GSC NATIVE CONNECTOR #}

{%macro gsc_select(lang,country="xxx") %}
select data_date as event_date,clicks,impressions,url,query,sum_position,"{{lang}}" lang
,"{{country}}" country, country as gsc_country FROM
{%endmacro%}

{%macro gsc_site_select(lang,country="xxx") %}
select data_date as event_date,clicks,impressions,query,sum_top_position,"{{lang}}" lang
,"{{country}}" country, country as gsc_country
 FROM
{%endmacro%}

{%macro gsc_where(date_start="2023-04-01") %}

    where url is not null and 
 {% if is_incremental() %}
  -- this filter will only be applied on an incremental run
  data_date {{daily_run()}} 
  
{% else %}
  data_date >= "{{date_start}}" 
  
{% endif %}   
{%endmacro%}

{%macro gsc_site_where(date_start="2023-04-01") %}

    where  
 {% if is_incremental() %}
  -- this filter will only be applied on an incremental run
  data_date {{daily_run()}} 
  
{% else %}
  data_date >= "{{date_start}}" 
  
{% endif %}   
{%endmacro%}

{# WHERE for INCREMENTAL #}

{%macro increment() %}
     
 {% if is_incremental() %}
  -- this filter will only be applied on an incremental run
  event_date {{daily_run()}} 
  
{% else %}

true  
{% endif %}   
{%endmacro%}