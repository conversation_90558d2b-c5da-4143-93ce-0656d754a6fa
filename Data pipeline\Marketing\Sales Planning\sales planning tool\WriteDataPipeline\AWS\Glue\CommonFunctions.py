import openpyxl
from openpyxl import load_workbook
from openpyxl.utils import range_boundaries
import io
import pandas as pd



class Common:
    @staticmethod
    def read_workbook(file_path, data_only=False):
        workbook = load_workbook(io.BytesIO(file_path), data_only=data_only)
        return workbook

    @staticmethod
    def sheet_data_mode(file_path):
        # virtual storage
        virtual_workbook = io.BytesIO()
        workbook_read = Common.read_workbook(file_path=file_path, data_only=True)
        workbook_write = Common.read_workbook(file_path=file_path, data_only=False)
        sheet_names_list = ['Planning Tool', 'Guidance']
        for sheet_name in sheet_names_list:
            sheet_read = workbook_read[sheet_name]
            response_list = []
            for row in sheet_read.iter_rows():
                for cell in row:
                    response_list.append(cell.value)
            sheet_write = workbook_write[sheet_name]
            count = 0
            for row in sheet_write.iter_rows():
                for cell in row:
                    cell.value = response_list[count]
                    count += 1
        workbook_write.save(virtual_workbook)
        workbook_bytes = virtual_workbook.getvalue()
        workbook = Common.read_workbook(file_path=workbook_bytes, data_only=False)
        return workbook

    @staticmethod
    def read_table(workbook, sheet_name, table_name):
        sheet = workbook[sheet_name]
        table_range = sheet.tables[table_name].ref
        print(table_range)
        table_data = []
        for row in sheet[table_range]:
            table_data.append([cell.value for cell in row])
        print(table_data)
        table_df = pd.DataFrame(table_data)
        new_header = table_df.iloc[0]
        table_df = table_df[1:]
        table_df.columns = new_header
        print(table_df)
        return table_df

    @staticmethod
    def write_workbook(workbook, file_path,folder):
        # Convert the workbook to bytes
        buffer = io.BytesIO()
        workbook.save(buffer)
        workbook_bytes = buffer.getvalue()
        folder.upload_file(workbook_bytes,file_path )
        print("upload completed")
        return "file_written"

    @staticmethod
    def write_rows(workbook, sheet_name, table_name, write_data_list):
        sheet = workbook[sheet_name]
        table = sheet.tables[table_name]
        start_row = table.ref.split(':')[0].replace('$', '')
        print(start_row)
        exist_end_row = table.ref.split(':')[1].replace('$', '')
        print(exist_end_row)
        start_row_number = int(start_row[1:]) + 1
        table_range = sheet.tables[table_name].ref
        table_start, table_end = table_range.split(':')[0], table_range.split(':')[1]
        new_end_row = ''
        for i, row in enumerate(write_data_list):
            sheet.insert_rows(start_row_number + i)
            print("its check", exist_end_row[2:])
            if sheet_name =="Consultant Targets":
                new_end_row = int(exist_end_row[1:]) + i
            else:
                new_end_row = int(exist_end_row[2:]) + 1 + i
            print("new_end_row", new_end_row)
            for j, value in enumerate(row):
                sheet.cell(row=start_row_number + i, column=j + 2, value=value)
        print("this is", table_end[1])
        if sheet_name =="Consultant Targets":
            new_range = f'{table_start}:{table_end[0]}{new_end_row}'  # Set the new range of the table
            print(new_range)
        else:
            new_range = f'{table_start}:{table_end[:2]}{new_end_row}'  # Set the new range of the table
            print(new_range)
        table.ref = new_range
        return workbook

    @staticmethod
    def number_format(workbook, sheet_name, table_name):
        sheet = workbook[sheet_name]
        table = sheet.tables[table_name]
        start_row = table.ref.split(':')[0].replace('$', '')
        start_row_number = int(start_row[1:]) + 1
        end_row = table.ref.split(':')[1].replace('$', '')
        end_row_number = int(end_row[2:])
        number_format_list = [{"min_row": start_row_number, "column_index": 2, "number_format": "mmm/yyyy;@"},
                              {"min_row": start_row_number, "column_index": 4, "number_format": "#"},
                              {"min_row": start_row_number, "column_index": 5, "number_format": "0%"},
                              {"min_row": start_row_number, "column_index": 7, "number_format": "0%"},
                              {"min_row": start_row_number, "column_index": 8, "number_format": "###"},
                              {"min_row": start_row_number, "column_index": 9, "number_format": "0%"},
                              {"min_row": start_row_number, "column_index": 10, "number_format": "###"},
                              {"min_row": start_row_number, "column_index": 11, "number_format": "0%"},
                              {"min_row": start_row_number, "column_index": 12, "number_format": "###"},
                              {"min_row": start_row_number, "column_index": 13, "number_format": "0%"},
                              {"min_row": start_row_number, "column_index": 14, "number_format": "#.#"},
                              {"min_row": start_row_number, "column_index": 15, "number_format": "##"},
                              {"min_row": start_row_number, "column_index": 16, "number_format": "0%"},
                              {"min_row": start_row_number, "column_index": 17, "number_format": "##.#"},
                              {"min_row": start_row_number, "column_index": 20, "number_format": "#,##"},
                              {"min_row": start_row_number, "column_index": 18, "number_format": "#,##"},
                              {"min_row": start_row_number, "column_index": 19, "number_format": "#,##"},
                              {"min_row": start_row_number, "column_index": 21, "number_format": "0%"},
                              {"min_row": start_row_number, "column_index": 22, "number_format": "##,#"},
                              {"min_row": start_row_number, "column_index": 23, "number_format": "##.#"},
                              {"min_row": start_row_number, "column_index": 24, "number_format": "#"},
                              {"min_row": start_row_number, "column_index": 25, "number_format": "#"},
                              {"min_row": start_row_number, "column_index": 26, "number_format": "##.#"},
                              {"min_row": start_row_number, "column_index": 27, "number_format": "#,##.#"}]
        for number_format in number_format_list:
            for row in sheet.iter_rows(min_row=number_format['min_row'], max_row=end_row_number,
                                       min_col=number_format['column_index'], max_col=number_format['column_index']):
                for cell in row:
                    cell.number_format = number_format['number_format']
        return workbook

    @staticmethod
    def truncate_table(workbook, sheet_name, table_name):
        sheet = workbook[sheet_name]
        table = sheet.tables[table_name]  # Define the table range as a string
        table_range = table.ref
        print(table_range)
        table_start, table_end = table_range.split(':')[0], table_range.split(':')[1]
        # Get the minimum and maximum row numbers from the table range
        min_row, min_col, max_row, max_col = range_boundaries(table_range)
        print(min_row, min_col, max_row, max_col)
        # Iterate through the rows in the table range and get their row numbers
        for row in range(min_col, max_col + 1):
            if row != min_col:
                print(row)
                sheet.delete_rows(min_col + 1)
        new_range = f'{table_start}:{table_end[:1]}{min_col + 1}'  # Set the new range of the table
        print(new_range)
        table.ref = new_range
        return workbook
