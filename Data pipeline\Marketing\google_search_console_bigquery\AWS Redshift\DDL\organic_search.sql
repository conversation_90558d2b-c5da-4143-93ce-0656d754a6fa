CREATE TABLE IF NOT EXISTS google.organic_search
(
	data_date DATE   ENCODE az64
	,country_geo_loc VARCHAR(8)   ENCODE lzo
	,device VARCHAR(12)   ENCODE lzo
	,page_url VARCHAR(688)   ENCODE lzo
	,page_path VARCHAR(448)   ENCODE lzo
	,host_name VARCHAR(124)   ENCODE lzo
	,page_type VARCHAR(64)   ENCODE lzo
	,"position" DOUBLE PRECISION   ENCODE RAW
	,query VARCHAR(2448)   ENCODE lzo
	,sum_position BIGINT   ENCODE az64
	,impressions BIGINT   ENCODE az64
	,clicks BIGINT   ENCODE az64
	,territory_code VARCHAR(12)   ENCODE lzo
	,territory_name VARCHAR(28)   ENCODE lzo
	,table_source VARCHAR(12)   ENCODE lzo
)