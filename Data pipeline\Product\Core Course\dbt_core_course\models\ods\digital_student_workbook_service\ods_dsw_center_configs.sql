{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH RankedRecords AS (
    SELECT 
        id
        ,referencecenterid
        ,configsetting
        ,configvalue
        ,{{cast_to_timestamp('created')}} as created
        ,{{cast_to_timestamp('lastupdated')}} as lastupdated
        ,ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdated DESC) AS rn
    FROM 
        {{source('stage_digital_student_workbook_service', 'centerconfigs')}}
)

SELECT
    {{etl_load_date()}}
    ,id
    ,referencecenterid as reference_center_id
    ,configvalue as config_value
    ,created
    ,lastupdated as last_updated
FROM 
    RankedRecords
WHERE 
    rn = 1;