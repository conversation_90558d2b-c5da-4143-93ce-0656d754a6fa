{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH booked_student AS (
    SELECT *
    FROM {{ref('ls_booked_student')}}
), ls_user AS (
    SELECT *
    FROM {{ref('dt_ls_user')}}
), registration AS (
    SELECT *
    FROM {{ref('dt_ls_registration')}}
), ls_center AS (
    SELECT *
    FROM {{ref('dt_ls_center')}}
), cc_center AS (
    SELECT *
    FROM {{ref('dt_cc_center')}}
), 
class_result AS (
    SELECT * 
    FROM {{ref('dt_ls_class_result')}}
),
encounterresultaggregate AS (
    SELECT *  
    FROM {{ref('dt_ls_encounter_result_aggregate')}}
),
classes as (
    select distinct class_id, class_type in ('encounter', 'ghost encounter', 'online encounter') as enc_flag  
    from {{ ref('union_classes') }}
),
stg_classes as 
    (
    select class_id, class_start_datetime 
    from {{ ref('union_classes') }}
)

, bookings as (
    SELECT 
        booked_student.class_id,
        booked_student.ref_class_id,
        booked_student.id                                                                       AS booking_id,
        booked_student.student_id,
        student.ssds_id                                                                         AS student_reference_id,
        ls_center.reference_center_id                                                           AS center_reference_id,
        ls_center.territory_id                                                                  AS booking_territory_id,
        booked_student.book_mode,
        booked_student.book_date                                                                AS booking_datetime,
        booked_student.local_book_date                                                          AS local_booking_datetime,
        booked_student.created                                                                  AS booking_created_datetime,
        booked_student.local_created                                                            AS booking_local_created_datetime,
        booked_student.last_updated                                                             AS booking_last_updated_datetime,
        booked_student.local_last_updated                                                       AS booking_local_last_updated_datetime,
        booked_student.booked_by                                                                AS booked_by,
        booked_student.booked_role_title                                                        AS booked_role_title,
        booked_student.booked_person_type                                                       AS booking_person_type,
        booked_student.cancelled_by                                                             AS booking_cancelled_by,
        CASE
            WHEN
                booked_student.cancelled_role_title = 'service manager' 
                and rtc.center_reference_id LIKE 'v%goc' THEN 'goc service manager'
            ELSE
                booked_student.cancelled_role_title
        END                                                                                     AS booking_cancelled_role_title,
        booked_student.cancelled_person_type                                                    AS booking_cancelled_person_type,
        booked_student.is_cancelled                                                             AS booking_cancelled_flag,
        booked_student.book_mode_modified_date                                                  AS booking_mode_modified_datetime,
        booked_student.local_book_mode_modified_date                                            AS booking_mode_modified_local_datetime,
        booked_student.standby_notification_type                                                AS booking_stand_by_notification_type,
        booked_student.is_accessed                                                              AS booking_accessed_flag,
        booked_student.registration_id                                                          AS booked_student_contract_id,
        registration.contract_id                                                                AS booked_student_contract_reference_id,
        booking_order_desc,
        CASE 
                WHEN booked_student.standby_to_booked_flag = 1 THEN true
                ELSE false
            END                                                                                 AS standby_to_booked_flag,
        CASE 
                WHEN booked_student.standby_to_booked_flag = 1 AND 
                    date_diff('hour', date_trunc('hour',booked_student.book_mode_modified_date), 
                    date_trunc('hour',stg_classes.class_start_datetime)) < 24 THEN 1
                ELSE 0
            END                                                                                 AS standby_to_booked_24hrs,
        CASE
                WHEN 
                    date_diff('hour',date_trunc('hour', booked_student.last_updated) ,
                    date_trunc('hour', stg_classes.class_start_datetime))  < 12 THEN 1
                ELSE 0
        END                                                                                     AS cancellations_12hrs,
        --CASE
        --        WHEN booked_student.is_cancelled = true AND
        --            date_diff('hour', booked_student.last_updated ,
        --            stg_classes.class_start_datetime) < 12 THEN 1
        --        ELSE 0
        --    END                                                                                 AS cancellations_12hrs,
        CASE
                WHEN 
                    date_diff('hour',date_trunc('hour', booked_student.last_updated) ,
                    date_trunc('hour', stg_classes.class_start_datetime))  < 24 THEN 1
                ELSE 0
        END                                                                                     AS cancellations_24hrs,
        --CASE
        --        WHEN booked_student.is_cancelled = true AND
        --            date_diff('hour', booked_student.last_updated ,
        --            stg_classes.class_start_datetime) < 24 THEN 1
        --        ELSE 0
        --    END                                                                                 AS cancellations_24hrs,
        CASE
                WHEN booked_student.is_cancelled = true AND
                    date_diff('hour', booked_student.last_updated,
                    stg_classes.class_start_datetime) < 12
                    AND (CASE
                            WHEN booked_student.standby_to_booked_flag = 1 AND 
                                date_diff('hour', booked_student.book_mode_modified_date,
                                            stg_classes.class_start_datetime ) < 24 THEN 1
                            ELSE 0
                        END) = 0
                    THEN 1
                ELSE 0
            END                                                                                 AS cancellations_12hrs_not_sbtb_24hrs
        FROM booked_student
        LEFT JOIN ls_user AS student ON booked_student.student_id = student.user_id
        LEFT JOIN registration ON booked_student.registration_id = registration.id
        LEFT JOIN ls_center ON registration.center_id = ls_center.id
        LEFT JOIN cc_center ON ls_center.reference_center_id = cc_center.center_reference_id
        LEFT JOIN stg_classes ON stg_classes.class_id = booked_student.ref_class_id
        LEFT JOIN 
            {{ref('dt_ls_user')}} as user
            ON user.user_id = booked_student.cancelled_by
        LEFT JOIN 
            {{ref('territory_centers')}} as rtc
            ON rtc.ls_center_id = user.center_id
),

-- 1st value for booking class access type in contracts changes
booking_class_access_minimum AS (
    SELECT
        contract_id,
        case
           when class_access_type = 'full access' then 'Full Access'
           when class_access_type = 'in-center class access' then 'In-Center'
           when class_access_type = 'online class access' then 'Online'
           when class_access_type = 'no_class_access_type' then 'No Access'
           else class_access_type
           end as class_access_type,
        ROW_NUMBER() OVER (PARTITION BY contract_id ORDER BY valid_from ASC) AS rn
    from {{ref("contracts_changes")}}
),

booking_class_access_type as (
    select
        bookings.booking_id,
        bookings.student_reference_id,
        bookings.booked_student_contract_reference_id,
        COALESCE (case
           when cc.class_access_type = 'full access' then 'Full Access'
           when cc.class_access_type = 'in-center class access' then 'In-Center'
           when cc.class_access_type = 'online class access' then 'Online'
           when cc.class_access_type = 'no_class_access_type' then 'No Access'
           else cc.class_access_type
           end,
           bkg_cls_acs_type_min.class_access_type ) as class_access_type,
        case
           when c.contract_product like('%self-booking%')
           then true
           else false end as self_booking_access_flag,
        ROW_NUMBER() OVER(partition by cc.contract_id order by cc.valid_from DESC) as rn 
    from bookings
    left join {{ref("contracts_changes")}} as cc 
        ON bookings.booked_student_contract_reference_id = cc.contract_id 
        AND bookings.local_booking_datetime > cc.valid_from 
        AND bookings.local_booking_datetime <= cc.valid_to 
    left join booking_class_access_minimum as bkg_cls_acs_type_min
        ON bookings.booked_student_contract_reference_id = bkg_cls_acs_type_min.contract_id 
        AND bkg_cls_acs_type_min.rn =1
    left join {{ref("contracts")}} as c
        ON c.contract_reference_id = cc.contract_id
),


final as (
    SELECT 
        bookings.class_id,
        bookings.ref_class_id,
        bookings.booking_id,
        bookings.student_id,
        bookings.student_reference_id,
        bookings.center_reference_id,
        bookings.booking_territory_id,
        bookings.book_mode,
        bookings.booking_datetime,
        bookings.local_booking_datetime,
        bookings.booking_created_datetime,
        bookings.booking_local_created_datetime,
        bookings.booking_last_updated_datetime,
        bookings.booking_local_last_updated_datetime,
        bookings.booked_by,
        bookings.booked_role_title,
        bookings.booking_person_type,
        bookings.booking_cancelled_by,
        bookings.booking_cancelled_role_title,
        bookings.booking_cancelled_person_type,
        bookings.booking_cancelled_flag,
        CASE 
            WHEN bookings.booking_cancelled_by = '45e963a0-8d29-4882-86b0-76a6ef79c9e3' 
                AND date_diff('hour', bookings.booking_last_updated_datetime, stg_classes.class_start_datetime ) > 14
            THEN '24' 
            WHEN  bookings.booking_cancelled_by = '45e963a0-8d29-4882-86b0-76a6ef79c9e3' 
                AND date_diff('hour', bookings.booking_last_updated_datetime, stg_classes.class_start_datetime ) < 14
            THEN  '12'
            ELSE 'not auto cancel'
        END     AS auto_cancel_type,
        bookings.booking_mode_modified_datetime,
        bookings.booking_mode_modified_local_datetime,
        bookings.booking_stand_by_notification_type,
        bookings.booking_accessed_flag,
        bookings.booked_student_contract_id,
        bookings.booked_student_contract_reference_id,
        bookings.booking_order_desc,
        bookings.standby_to_booked_flag,
        bookings.standby_to_booked_24hrs,
        bookings.cancellations_12hrs,
        bookings.cancellations_24hrs,
        bookings.cancellations_12hrs_not_sbtb_24hrs,
        CASE
            WHEN classes.enc_flag = true THEN enc_rslt.last_updated
            ELSE cls_rslt.last_updated END AS class_close_date,
        CASE
            WHEN classes.enc_flag = true THEN enc_rslt.id -- encounter_result_id
            ELSE cls_rslt.id -- class_result_id
        END AS class_result_id,
        CASE
            WHEN classes.enc_flag = true THEN enc_rslt.level
        END AS class_encounter_level,
        CASE
            WHEN classes.enc_flag = true THEN enc_rslt.unit
        END AS class_encounter_unit,
        CASE
            WHEN classes.enc_flag = true THEN cls_rslt.class_type
        END AS class_result_class_type,
        CASE
            WHEN classes.enc_flag = true THEN enc_rslt.content_item_result_type
            ELSE cls_rslt.content_item_result_type
        END AS class_result,
        CASE
            WHEN classes.enc_flag = false THEN cls_rslt.result
        END AS class_comments,
        CASE
            WHEN classes.enc_flag = true THEN enc_rslt.local_created 
            ELSE cls_rslt.local_created
        END AS class_result_created_datetime,
        bkg_cls_acs_type.class_access_type,
        bkg_cls_acs_type.self_booking_access_flag

    FROM bookings
    left join classes 
        ON bookings.ref_class_id = classes.class_id 
    left join stg_classes
        ON bookings.class_id = stg_classes.class_id
    left join class_result as cls_rslt --use the GOC flag to choose the join 
        ON cls_rslt.class_id = bookings.class_id
        AND cls_rslt.student_id = bookings.student_id
        AND bookings.book_mode = 'book'
        AND booking_cancelled_flag = false
        AND classes.enc_flag = false -- Query optimization
    left join encounterresultaggregate as enc_rslt
        ON enc_rslt.class_id = bookings.class_id
        AND enc_rslt.student_id = bookings.student_id
        AND bookings.book_mode = 'book'
        AND booking_cancelled_flag = false
        and classes.enc_flag = true -- Query optimization
    left join booking_class_access_type as bkg_cls_acs_type 
        ON bookings.booking_id = bkg_cls_acs_type.booking_id
    -- to avoid duplication in booking id's when joining with contracts changes for 2 records in 2017
    WHERE bookings.booking_id NOT IN 
    ('b820c484-f643-49b1-8f77-31e8b906aed1','c1cebd9d-6130-40ea-991f-8f36e420ce8f')

)

select *
from final