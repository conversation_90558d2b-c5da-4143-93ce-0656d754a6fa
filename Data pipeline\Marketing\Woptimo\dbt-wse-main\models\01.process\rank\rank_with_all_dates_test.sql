{{
    config(
        tags=["incremental","rank","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}


with a as(
select generate_date_array((select min(event_date) from {{ ref('rank_global_query_test') }}), (select max(event_date) from {{ ref('rank_global_query_test') }})) dates
), tab_dates as (
select dates as event_date FROM a, unnest(dates) dates
), tab_dates_with_query as (
select 
event_date,query FROM tab_dates
CROSS JOIN (SELECT DISTINCT query FROM {{ ref('rank_global_query_test') }}  )
),
tab_all_dates as (
select
tab_dates_with_query.event_date,
tab_dates_with_query.query,
* EXCEPT (event_date,query)
from tab_dates_with_query
LEFT JOIN {{ ref('rank_global_query_test') }}  using (event_date,query)
)


select event_date,query,
LAST_VALUE(rank_organic IGNORE NULLS) 
OVER 
(PARTITION BY query ORDER BY event_date desc 
RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW ) AS rank_organic,

LAST_VALUE(page IGNORE NULLS) 
OVER 
(PARTITION BY query ORDER BY event_date desc 
RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW ) AS page,


LAST_VALUE(is_featured_snippet IGNORE NULLS) 
OVER 
(PARTITION BY query ORDER BY event_date desc 
RANGE BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW ) AS is_featured_snippet,

from tab_all_dates

