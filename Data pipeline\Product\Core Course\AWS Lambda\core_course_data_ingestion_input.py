import json
import boto3

boto3_resource = boto3.resource('s3')


def lambda_handler(event, context):
    source_system_bucket_info_path = boto3_resource.Object\
    ('prod-corecourse', 'config/initial_load_config.json')
    source_system_bucket_info_content = source_system_bucket_info_path.get()['Body'].read().decode('utf-8')
    source_system_bucket_info = json.loads(source_system_bucket_info_content)
    print(source_system_bucket_info)
    return source_system_bucket_info