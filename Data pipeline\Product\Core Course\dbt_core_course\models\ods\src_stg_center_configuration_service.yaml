version: 2

sources:

  - name: stage_center_configuration_service
    description: >
      Source data from the Center Configuration Service which manages center-related configurations,
      territories, timezones, and cross-center groupings.
    database: awsdatacatalog
    schema: stg_center_configuration_service
    tables:
      - name: center
        description: Contains information about education centers including reference IDs, names, contact details, and configuration settings.
        columns:
          - name: id
            description: Primary key for the center record
          - name: createddate
            description: Timestamp when the center record was created
          - name: firstdayoftheweek
            description: Numeric representation of the first day of the week for this center (0=Sunday, 1=Monday, etc.)
          - name: hasdeluxeservice
            description: Boolean flag indicating if the center offers deluxe service
          - name: hasvipservice
            description: Boolean flag indicating if the center offers VIP service
          - name: isactive
            description: Boolean flag indicating if the center is currently active
          - name: isdaylightsaving
            description: Boolean flag indicating if the center observes daylight saving time
          - name: isonlinecenter
            description: Boolean flag indicating if the center is an online-only center
          - name: istwentyfourhourformat
            description: Boolean flag indicating if the center uses 24-hour time format
          - name: notallowpnbonholidays
            description: Boolean flag related to holiday booking restrictions
          - name: haspilot
            description: Boolean flag indicating if the center is a pilot center
          - name: hasdigitalvalidation
            description: Boolean flag indicating if the center uses digital validation
          - name: isnewdigitalworkbook
            description: Boolean flag indicating if the center uses the new digital workbook
          - name: issurveyenabled
            description: Boolean flag indicating if surveys are enabled for this center
          - name: isteensenabled
            description: Boolean flag indicating if teen programs are enabled for this center
          - name: hasd2cproduct
            description: Boolean flag indicating if the center has direct-to-consumer products
          - name: addressid
            description: Foreign key reference to the address record
          - name: centerreferenceid
            description: External reference ID for the center
          - name: centerdirectorname
            description: Name of the center director
          - name: email
            description: Email address for the center
          - name: mobile
            description: Mobile phone number for the center
          - name: name
            description: Name of the center
          - name: phone
            description: Main phone number for the center
          - name: territoryid
            description: Foreign key reference to the territory
          - name: timezoneid
            description: Foreign key reference to the timezone
          - name: navisionclient
            description: Navision client identifier

      - name: centerconfiguration
        description: Configuration settings for centers including scheduling, booking, and operational parameters.
        columns:
          - name: id
            description: Primary key for the center configuration record
          - name: beginningofdidacticyear
            description: Date marking the beginning of the didactic year
          - name: bookminutesuptoclassstart
            description: Number of minutes before class start when booking is allowed
          - name: cancelminutesuptoclassstart
            description: Number of minutes before class start when cancellation is allowed
          - name: carryovermaximum
            description: Maximum number of classes that can be carried over
          - name: centerid
            description: Foreign key reference to the center
          - name: createddate
            description: Timestamp when the configuration was created
          - name: isactive
            description: Boolean flag indicating if the configuration is active
          - name: hasonlineclassesvisibility
            description: Boolean flag for online classes visibility
          - name: hasschedulingvisibility
            description: Boolean flag for scheduling visibility
          - name: iscarryoverenabled
            description: Boolean flag indicating if carry-over is enabled
          - name: isstandbyenabled
            description: Boolean flag indicating if standby is enabled
          - name: iswaitinglistenabled
            description: Boolean flag indicating if waiting list is enabled
          - name: lastmodifieddate
            description: Timestamp when the configuration was last modified
          - name: noofbookingdaysinadvance
            description: Number of days in advance that booking is allowed
          - name: noofclassrooms
            description: Number of classrooms in the center
          - name: noofotherspaces
            description: Number of other spaces in the center
          - name: noofstudentsperencounter
            description: Maximum number of students per encounter
          - name: noofstudentspercomplementaryclass
            description: Maximum number of students per complementary class
          - name: remindernotificationsbeforeclassstart
            description: Minutes before class start when reminder notifications are sent
          - name: socialclassorcomplementaryclasssperlevel
            description: Number of social or complementary classes per level
          - name: maximumstudentsinstandby
            description: Maximum number of students allowed in standby
          - name: waitinglistencounteronlineencounter
            description: Configuration for waiting list in encounters
          - name: showunusedencounters
            description: Boolean flag to show unused encounters
          - name: staffcancelminutesuptoclassstart
            description: Minutes before class start when staff can cancel
          - name: isstandbyconfigurable
            description: Boolean flag indicating if standby is configurable
          - name: isoneweekplanavailableinstudyplan
            description: Boolean flag for one-week plan availability

      - name: crosscentergroup
        description: Groups that span multiple centers for shared resources or management.
        columns:
          - name: id
            description: Primary key for the cross-center group
          - name: name
            description: Name of the cross-center group
          - name: description
            description: Description of the cross-center group's purpose

      - name: crosscentergroupmapping
        description: Mapping table that associates centers with cross-center groups.
        columns:
          - name: id
            description: Primary key for the mapping record
          - name: centerid
            description: Foreign key reference to the center
          - name: crosscentergroupid
            description: Foreign key reference to the cross-center group

      - name: territory
        description: Geographic territories where centers operate, with regional settings and configurations.
        columns:
          - name: id
            description: Primary key for the territory record
          - name: createddate
            description: Timestamp when the territory record was created
          - name: firstdayoftheweek
            description: Numeric representation of the first day of the week for this territory
          - name: isactive
            description: Boolean flag indicating if the territory is active
          - name: isdaylightsaving
            description: Boolean flag indicating if the territory observes daylight saving time
          - name: istwentyfourhourformat
            description: Boolean flag indicating if the territory uses 24-hour time format
          - name: lastmodifieddate
            description: Timestamp when the territory was last modified
          - name: isdualclassaccess
            description: Boolean flag for dual class access
          - name: dctechnologytype
            description: Type of digital classroom technology used
          - name: code
            description: Code identifier for the territory
          - name: isocode
            description: ISO country code for the territory
          - name: name
            description: Name of the territory
          - name: territoryreferenceid
            description: External reference ID for the territory
          - name: timezoneid
            description: Foreign key reference to the timezone
          - name: navisionclient
            description: Navision client identifier

      - name: timezone
        description: Timezone information used by centers and territories for scheduling and time-based operations.
        columns:
          - name: id
            description: Primary key for the timezone record
          - name: description
            description: Description or name of the timezone