{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_dsw_course_contents'
        ) }}

    {% if is_incremental() %}
        where last_updated > ((select max(last_updated) from {{ this }}))
    {% endif %}
)

SELECT
    {{etl_load_date()}}
    ,id
    ,level
    ,unit
    ,lesson
    ,url
    ,version
    ,activity_count
    ,is_active
    ,created
    ,last_updated
    ,reference_id
FROM 
    ods_data