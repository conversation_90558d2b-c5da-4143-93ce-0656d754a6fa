{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'user_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        gender,
        {{ cast_to_timestamp('birthdate') }} as birthdate,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        isactive,
        enablepasswordreset,
        isprospect,
        userid,
        username,
        password,
        firstname,
        lastname,
        email,
        ssdsid,
        centerid,
        territoryid,
        userexternalid,
        ROW_NUMBER() over (
            PARTITION BY userid
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_idam_service',
            'userbasicinfo'
        ) }}
)
SELECT
    {{etl_load_date()}},
    gender,
    birthdate as birth_date,
    created as created,
    lastupdated as last_updated,
    isactive as is_active,
    enablepasswordreset as enable_password_reset,
    isprospect as is_prospect,
    userid as user_id,
    username as user_name,
    password,
    firstname as first_name,
    lastname as last_name,
    email,
    ssdsid as ssds_id,
    centerid as center_id,
    territoryid as territory_id,
    userexternalid as user_external_id
FROM
    rankedrecords
WHERE
    rn = 1;
