version: 2

models:
  - name: contracts_changes
    columns:
      - name: contract_id
        tests:
          - not_null:
              severity: error
      - name: student_id
        tests:
          - not_null:
              severity: error
      - name: center_id
        tests:
          - not_null:
              severity: error
      - name: group_id
        tests:
          - not_null:
              severity: warn
      - name: lab_teacher_id
        tests:
          - not_null:
              severity: warn
      - name: consultant_id
        tests:
          - not_null:
              severity: warn
      - name: valid_from
        tests:
          - not_null:
              severity: error
      - name: valid_to
        tests:
          - not_null:
              severity: error
      - name: status
        tests:
          - accepted_values:
              values: ['invalid', 'no_status', 'valid', 'future']
              severity: warn
      - name: location
        tests:
          - accepted_values:
              values: ['outcenter', 'no_location', 'incenter']
              severity: warn
      - name: class_access_type
        tests:
          - accepted_values:
              values: ['full access', 'in-center class access', 'no_class_access_type', 'online class access']
              severity: warn
      - name: service_type
        tests:
          - accepted_values:
              values: ['no_service_type', 'standard', 'vip']
              severity: warn
      - name: is_membership
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: warn
