create or replace procedure hubspot_crm.sp_dim_corporateagegroup()
    language plpgsql
as
$$

BEGIN

--------- dim course_age_group----------

drop table hubspot_crm.dim_course_age_group;

create table hubspot_crm.dim_course_age_group as
select DISTINCT course_age_group,md5(course_age_group) as age_key from hubspot_crm.contactsdeals
where course_age_group <> 'NULL' and course_age_group<>'' and course_age_group is NOT NULL;

---------dim_individual_corporate-------

drop table hubspot_crm.dim_individual_corporate;

create table hubspot_crm.dim_individual_corporate as
select DISTINCT individual_corporate,md5(individual_corporate) as corporate_key from hubspot_crm.contactsdeals
where individual_corporate <> 'NULL' and individual_corporate <>'' and individual_corporate is NOT NULL;




    COMMIT;


END;

$$;

