{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

SELECT
    event_date,
    event_timestamp,
    event_name,
    user_id, 
    user_identifier,
    screen,
    role,
    gb_variant,
    variant,
    experiment,
    etl_load_date
FROM
    {{ ref('page_navigation_events') }}
{% if is_incremental() %}
WHERE
    event_date >= (SELECT max(event_date) from {{this}})
{% endif %}
