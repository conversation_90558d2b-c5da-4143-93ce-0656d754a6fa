{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        groupid,
        classaccesstypeid,
        maxnoofccandscclasses,
        isactive,
        modifiedbyid,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
        isaccessperiodpermanent,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'groupclassaccesstype')}}
)

SELECT
    {{etl_load_date()}},
    id,
    groupid as group_id,
    classaccesstypeid as class_access_type_id,
    maxnoofccandscclasses as max_no_of_cc_and_sc_classes,
    isactive as is_active,
    modifiedbyid as modified_by_id,
    {{cast_to_timestamp('createddate')}} as created_date,
    {{cast_to_timestamp('lastupdateddate')}} as last_updated_date,
    isaccessperiodpermanent as is_access_period_permanent
FROM 
    RankedRecords
WHERE 
    rn = 1;