{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='dbt_unique_id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        (producttypeid || cast(productid as varchar)) as dbt_unique_id,
        producttypeid,
        productid,
        row_number() over(partition by producttypeid, productid order by producttypeid, productid) as rn
    FROM 
        {{source('stage_contract_service', 'producttypeproducts')}}
)

SELECT 
    {{etl_load_date()}},
    dbt_unique_id,
    producttypeid as product_type_id,
    productid as product_id
FROM
    RankedRecords
WHERE rn = 1