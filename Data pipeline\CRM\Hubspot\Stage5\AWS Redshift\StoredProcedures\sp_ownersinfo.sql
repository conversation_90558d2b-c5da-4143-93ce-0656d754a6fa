create or replace procedure hubspot_crm.sp_ownersinfo()
    language plpgsql
as
$$

BEGIN

INSERT INTO hubspot_crm.ownersenriched
select
    cast(id as BIGINT),
    email,
    firstname,
    lastname,
    cast(userid as bigint),
    cast(createdat as timestamp),
    cast(updatedat as timestamp),
    archived,
    territory_code,
    cast(cycleid as bigint),
    territory_name,
    deleteflag,
    cast(archiveddate as timestamp),
    cast(restoreddate as timestamp)
    from hubspot_crm.history_ownersenriched;

 INSERT INTO hubspot_crm.teamsenriched
    SELECT
        cast(id as bigint),
        name,
        "primary",
        cast(ownerid as BIGINT),
        territory_code,
        cast(cycleid as bigint),
        territory_name
        from hubspot_crm.history_teamsenriched;


truncate table hubspot_crm.ownersinfo;

INSERT INTO hubspot_crm.ownersinfo
SELECT AB.id,
       AB.email,
       AB.firstname,
       AB.lastname,
       AB.userid,
       AB.createdat,
       AB.updatedat,
       AB.archived,
       AB.territory_code,
       AB.cycleid,
       AB.territory_name,
       AB.deleteflag,
       AB.teamid,
       AB.teamname,
       AB.wse_consultant_id,
       AB.ownerkey
FROM (SELECT oe.id,
             oe.email,
             oe.firstname,
             oe.lastname,
             oe.userid,
             oe.createdat,
             oe.updatedat,
             oe.archived,
             oe.territory_code,
             oe.cycleid,
             oe.territory_name,
             oe.deleteflag,
             te.id                                                                    AS teamid,
             te.name                                                                  AS teamname,
             idw.wse_consultant_id,
             MD5(oe.id || oe.territory_code)                                          AS ownerkey,
             ROW_NUMBER() OVER (PARTITION BY oe.id,territory_name,wse_consultant_id ORDER BY cycleid DESC) AS owners_count
      FROM hubspot_crm.ownersenriched oe
               LEFT JOIN
           (SELECT id, name, ownerid, territory_code
            FROM (SELECT *, ROW_NUMBER() OVER (PARTITION BY ownerid,territory_code ORDER BY cycleid DESC) AS row_count
                  FROM hubspot_crm.teamsenriched
                  WHERE "primary" = 'True') A
            WHERE row_count = 1) te
           ON te.territory_code = oe.territory_code
               AND te.ownerid = oe.id
               LEFT JOIN
           (SELECT wse_consultant_id, email
            FROM (SELECT wse_consultant_id,
                         email,
                         ROW_NUMBER() OVER (PARTITION BY email ORDER BY created_at DESC) AS lastest_num
                  FROM hubspot_crm.id_mapper_dw) B
--             WHERE lastest_num = 1
            ) idw
           ON TRIM(idw.email) = TRIM(oe.email)) AB
WHERE owners_count = 1;

    COMMIT;


END

$$;

