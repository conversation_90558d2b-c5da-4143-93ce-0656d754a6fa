{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_contracts_audit_info'
        ) }}

    {% if is_incremental() %}
        where created_date > ((select max(created_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    contadtinfo.id  as id,
    contract_id,
    contadtfields.field_name as modified_field,
    case
        when contadtfields.field_name in ('end_level_id','start_level_id') then cast(plevelprev."order" as varchar) else contadtinfo.previous_value
    end as previous_value,
    case
        when contadtfields.field_name in ('end_level_id','start_level_id') then cast(plevelpres."order" as varchar) else contadtinfo.present_value 
    end as present_value,
    contcngtypes.name as change_type,
    reason,
    modified_by_id,
    created_date,
    {{convert_to_local_timestamp('created_date','time_zone_id')}} as local_created_date,
    effective_date,
    {{convert_to_local_timestamp('effective_date','time_zone_id')}} as local_effective_date
from ods_data as contadtinfo
    left join (
        select id,
            field_name
        from {{ ref( 'ods_cs_contract_audit_fields' ) }}
    ) as contadtfields on contadtinfo.modified_field_id = contadtfields.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_contract_change_types' ) }}
    ) as contcngtypes on contadtinfo.change_type = contcngtypes.id
    left join (
        select id,
            "order"
        from {{ ref( 'ods_cs_product_levels' ) }}
    ) as plevelprev on upper(contadtinfo.previous_value) = plevelprev.id
    left join (
        select id,
            "order"
        from {{ ref( 'ods_cs_product_levels' ) }}
    ) as plevelpres on upper(contadtinfo.present_value) = plevelpres.id
    left join (
                select id,
                    center_id
                from {{ ref( 'ods_cs_contracts' ) }}
            ) as cont on contadtinfo.contract_id = cont.id
    left join (
        select id,
            center_reference_id 
        from {{ ref( 'ods_cs_centers' ) }}
    ) as center on center.id = cont.center_id
    left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on center.center_reference_id = tz.center_reference_id