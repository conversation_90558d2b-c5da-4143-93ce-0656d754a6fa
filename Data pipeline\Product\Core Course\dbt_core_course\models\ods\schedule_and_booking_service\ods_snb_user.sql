{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH RankedRecords AS (
    SELECT 
        isactive,
        {{cast_to_timestamp("created")}} as created,
        {{cast_to_timestamp("lastupdated")}} as lastupdated,
        id,
        userreferenceid,
        firstname,
        lastname,
        username,
        email,
        photouri,
        roleid,
        centerreferenceid,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdated DESC) AS rn
    FROM 
        {{source('stage_schedule_and_booking_service', 'user')}}
)

SELECT
    {{etl_load_date()}},
    isactive as is_active,
    created,
    lastupdated as last_updated,
    id,
    userreferenceid as user_reference_id,
    firstname as first_name,
    lastname as last_name,
    username as user_name,
    email,
    photouri as photo_uri,
    roleid as role_id,
    centerreferenceid as center_reference_id
FROM
    RankedRecords
WHERE
    rn = 1