{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'user_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        studentid,
        tempisnewstudent,
        geid,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        preferredcontactmethod,
        privacypolicyapprove,
        isfirstminicyclecompleted,
        userid,
        nativelanguageid,
        secondarylanguageid,
        personalmotivationid,
        professionid,
        studentcode,
        preferredsocialnetworkid,
        prospectid,
        ROW_NUMBER() over (
            PARTITION BY userid
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'student'
        ) }}
)
SELECT
    {{etl_load_date()}},
    studentid as student_id,
    tempisnewstudent as tempis_new_student,
    geid as ge_id,
    created,
    lastupdated as last_updated,
    preferredcontactmethod as preferred_contact_method,
    privacypolicyapprove as privacy_policy_approve,
    isfirstminicyclecompleted as is_first_mini_cycle_completed,
    userid as user_id,
    nativelanguageid as native_language_id,
    secondarylanguageid as secondary_language_id,
    personalmotivationid as personal_motivation_id,
    professionid as profession_id,
    studentcode as student_code,
    preferredsocialnetworkid as preferred_social_network_id,
    prospectid as prospect_id
FROM
    rankedrecords
WHERE
    rn = 1;
