{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * 
    from {{ ref('ods_cs_contract_products') }}
)

SELECT {{etl_load_date()}},
    dbt_unique_id,
    contract_id,
    created_date,
    {{convert_to_local_timestamp('created_date','time_zone_id')}} as local_created_date,
    last_updated_date,
    {{convert_to_local_timestamp('last_updated_date','time_zone_id')}} as local_last_updated_date,
    product_id,
    prd.name as product
from ods_data as contprd
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_products' ) }}
    ) as prd on contprd.product_id = prd.id
    left join (
            select id,
                center_id
            from {{ ref( 'ods_cs_contracts' ) }}
        ) as cont on contprd.contract_id = cont.id
    left join (
        select id,
            center_reference_id 
        from {{ ref( 'ods_cs_centers' ) }}
    ) as center on center.id = cont.center_id
    left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on center.center_reference_id = tz.center_reference_id