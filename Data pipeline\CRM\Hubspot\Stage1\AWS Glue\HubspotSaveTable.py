import sys
import logging
from pyspark import SparkConf, SparkContext
from pyspark.sql import SparkSession
from awsglue.utils import getResolvedOptions
import json
import cloud_operations
from pyspark.sql import SparkSession
import CloudOperations
import LogFileGeneration
from pyspark.sql.functions import from_unixtime, to_timestamp, when, lit

s3_connect = CloudOperations.S3
Logs = LogFileGeneration.LogFile



Args = getResolvedOptions(sys.argv, ['JOB_NAME', 'InputType', 'FilePath', 'TableName', 'WritePath', 'WriteMode','Bucket'])

Bucket = Args['Bucket']
Executionjson = s3_connect.ReadJsonFile(Bucket, 'ExecutionCheck.json')
CycleId = Executionjson['CycleId']
GlueJobRunID = Args['JOB_RUN_ID']
logging.warning("this is the job run id: ", GlueJobRunID)
Conf = SparkConf()
Conf.setMaster("local").setAppName("My app")
Sc = SparkContext.getOrCreate(conf=Conf)
Spark = SparkSession(Sc)
spark = SparkSession.builder.appName("MultipleSQLStatements").getOrCreate()
InputType = Args['InputType']
FilePath = Args['FilePath']
TableName = "hubspot." + Args['TableName']
WritePath = Args['WritePath']
WriteMode = Args['WriteMode']
# df=0
if Args['TableName'] == 'Webhooks':
    logging.warning("It is webhooks table")
else:
    Query = """select * from {} where cycleid={}""".format(TableName, CycleId)
    df = spark.sql(Query)

if TableName == 'hubspot.Archivecompanies':
    companiesPath = 'Logs/' + str(CycleId) + '/Stage1/companies_data_extract_archive_DE.json'
    DataResponse = s3_connect.ReadJsonFile(Bucket, companiesPath)
    RecordsProcessed = DataResponse['RecordsProcessed']
if Args['TableName'] =='Archivecompanies' and RecordsProcessed == 0:
    logging.warning("There is no input file to process for Archive comapnies DE")
    LogFile = Logs.SaveTableLog(TableName=TableName, Status=200,
                                                  WritePath=WritePath,
                                                  CycleId=CycleId,
                                                    Bucket=Bucket)
else:
    def ReadFile(InputType, FilePath):
        if InputType == "json":
            return Spark.read.json(FilePath)
        if InputType == "parquet":
            return Spark.read.format("parquet").option("header", "true").load(FilePath)
    if Args['TableName'] == 'Webhooks':
        WriteResponse = ReadFile(InputType=InputType, FilePath=FilePath)
        logging.warning(WriteResponse)
        WriteResponse = WriteResponse.filter(WriteResponse.subscriptionType =='contact.privacyDeletion')
        WriteResponse = WriteResponse.withColumn("occurredAt", to_timestamp(from_unixtime(WriteResponse.occurredAt/1000)))
        WriteResponse = WriteResponse.withColumn("portalId", when((WriteResponse.portalId == '25174893') , 'FR').when((WriteResponse.portalId == '5016806') , 'EC').when((WriteResponse.portalId == '1982274') , 'IT').when((WriteResponse.portalId == '4147632') , 'CL').when((WriteResponse.portalId == '4072008') , 'CO').when((WriteResponse.portalId == '7540950') , 'PE').when((WriteResponse.portalId == '6266664') , 'DO').when((WriteResponse.portalId == '7690100') , 'TN').when((WriteResponse.portalId == '6043118') , 'ES').when((WriteResponse.portalId == '5307854') , 'MN').when((WriteResponse.portalId == '2550768') , 'SA').when((WriteResponse.portalId == '2389021') , 'AR').when((WriteResponse.portalId == '20643658') , 'UY').when((WriteResponse.portalId == '20790457') , 'US').when((WriteResponse.portalId == '20904016') , 'MX').when((WriteResponse.portalId == '25482297') , 'DE').when((WriteResponse.portalId == '25478488') , 'DZ').when((WriteResponse.portalId == '7377515') , 'CH').when((WriteResponse.portalId == '4137836') , 'KR').when((WriteResponse.portalId == '24000747') , 'KZ').otherwise(WriteResponse.portalId))
        WriteResponse.write.mode(WriteMode).saveAsTable(TableName, path=WritePath)
        LogFile = Logs.SaveTableLog(TableName=TableName, Status=200,
                                                   WritePath=WritePath,
                                                   CycleId=CycleId,
                                                    Bucket=Bucket)
    else:
        if df.count() == 0:
            WriteResponse = ReadFile(InputType=InputType, FilePath=FilePath)
            logging.warning("Printing write response before drop")
            logging.warning(WriteResponse)
            if Args['TableName'] == 'associationcompanies':
                WriteResponse= WriteResponse.withColumn('archived', when(WriteResponse['archived'] == lit('FALSE'), False).otherwise(False))
            if Args['TableName'] == 'Archivecontacts':
                columns_to_drop = ['brand_name','center_name']
                WriteResponse = WriteResponse.drop(*columns_to_drop)
            if Args['TableName'] == 'Archivedeals':
                columns_to_drop = "center_name"
                WriteResponse = WriteResponse.drop(columns_to_drop)
            WriteResponse.write.mode(WriteMode).saveAsTable(TableName, path=WritePath)
            LogFile = Logs.SaveTableLog(TableName=TableName, Status=200,
                                                       WritePath=WritePath,
                                                       CycleId=CycleId,
                                                        Bucket=Bucket)
            logging.warning("The data has been loaded in to table")
        else:
            logging.warning("The data for cycleid is already present")

