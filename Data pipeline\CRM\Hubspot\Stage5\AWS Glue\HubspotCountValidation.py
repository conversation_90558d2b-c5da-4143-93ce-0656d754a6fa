import sys
import requests
from awsglue.utils import getResolvedOptions
sys.path.insert ( 0 , '/glue/lib/installation' )
keys = [ k for k in sys.modules.keys () if 'boto' in k ]
for k in keys:
    if 'boto' in k:
        del sys.modules [ k ]
import DbOperations
import CloudOperations
import authentication
import credential_secret_manager
import logging

db_connect = DbOperations.Database
s3_connect = CloudOperations.S3
OauthConnect = authentication.Oauth

config_info = getResolvedOptions(sys.argv, ['Bucket'])

query = """select max(createdate),min(createdate),CAST( (DATE_PART(EPOCH , min(createdate)) * 1000 ) as varchar) as MIN_EPOCH_LASTMODIFIEDDATE, CAST((DATE_PART(EPOCH , max(createdate)) * 1000) as varchar) as MAX_EPOCH_LASTMODIFIEDDATE,territory_code,'CONTACTS' as OBJECT,'https://api.hubapi.com/crm/v3/objects/contacts/search' as URL, count(*) as TOTAL_COUNT from hubspot_crm.contactsenriched
where deleteflag in ('N','R')
GROUP BY territory_code
UNION
select max(createdate), min(createdate),CAST((DATE_PART(EPOCH , min(createdate)) * 1000 ) as varchar) as MIN_EPOCH_LASTMODIFIEDDATE, CAST((DATE_PART(EPOCH , max(createdate)) * 1000) as varchar) as MAX_EPOCH_LASTMODIFIEDDATE,territory_code,'DEALS' as OBJECT,'https://api.hubapi.com/crm/v3/objects/deals/search' as URL, count(*) as TOTAL_COUNT from hubspot_crm.dealsenriched
where deleteflag in ('N','R')
GROUP BY territory_code;"""

query_execute = db_connect.Execution('ReadTable', query ,'Hubspot count info from redshift')
logging.warning(query_execute)
logging.warning(query_execute)
bucket = config_info['Bucket']
execution_json = s3_connect.ReadJsonFile(bucket, 'ExecutionCheck.json')
# Extract the cycle ID from the execution check
cycle_id = execution_json['CycleId']
file_path = "CountFile/" + f"{cycle_id}" + f"/hubspotcount.json"
json_write = s3_connect.WriteJsonFile(bucket,file_path,query_execute)
logging.warning("json file has been written in the destination folder")
def FiterFetch(TerritoryCode, FilterProperty, min_date, max_date, object_info, filter_property_2):
    if TerritoryCode == 'CH':
        if object_info == 'CONTACTS':
            FilterGroup = [{"filters": [     {"propertyName": FilterProperty, "operator": "LTE",
                                             "value": max_date },
                                             {"propertyName": filter_property_2, "operator": "IN", "values": ["WSE"] },
                                            {"propertyName": "center_name", "operator": "IN",
                                             "values": ["WSE Bienne", "WSE Fribourg", "WSE Genève",
                                                        "WSE Juniors Fribourg",
                                                        "WSE Juniors Genève",
                                                        "WSE Juniors Lausanne",
                                                        "WSE Juniors Montreux",
                                                        "WSE La Chaux-de-Fonds",
                                                        "WSE Lausanne",
                                                        "WSE Lugano",
                                                        "WSE Montreux",
                                                        "WSE Neuchâtel",
                                                        "WSE Online",
                                                        "WSE Praha"]}
                                            ]}]
        else:
            FilterGroup = [{"filters": [{"propertyName": FilterProperty, "operator": "GTE",
                                             "value": min_date},
                                             {"propertyName": FilterProperty, "operator": "LTE",
                                             "value": max_date },
                                            {"propertyName": "center_name", "operator": "IN",
                                             "values": ["WSE Bienne", "WSE Fribourg", "WSE Genève",
                                                        "WSE Juniors Fribourg",
                                                        "WSE Juniors Genève",
                                                        "WSE Juniors Lausanne",
                                                        "WSE Juniors Montreux",
                                                        "WSE La Chaux-de-Fonds",
                                                        "WSE Lausanne",
                                                        "WSE Lugano",
                                                        "WSE Montreux",
                                                        "WSE Neuchâtel",
                                                        "WSE Online",
                                                        "WSE Praha"]}
                                            ]}]
            
        return FilterGroup
    else:
        FilterGroup = [{"filters": [{"propertyName": FilterProperty, "operator": "GTE",
                                         "value": min_date},
                                         {"propertyName": FilterProperty, "operator": "LTE",
                                         "value": max_date }
                                         ]}]
        return FilterGroup
count_data_list = []
for territory_info in query_execute:
    if territory_info['object'] =='CONTACTS':
        filter_property = 'createdate'
    if territory_info['object'] =='DEALS':
        filter_property = 'createdate'
    url = territory_info['url']
    filter_property_2 = 'brand_name'
    object_info = territory_info['object']
    min_date = territory_info['min_epoch_lastmodifieddate']
    max_date = territory_info['max_epoch_lastmodifieddate']
    table_count = territory_info['total_count']
    territory_code = territory_info['territory_code']
    logging.warning(territory_code)
    FilterGroups = FiterFetch(territory_code, filter_property, min_date,max_date, object_info, filter_property_2)
    access_token = OauthConnect.authenticate(territory_code)
    DefaultInputJson = {"after": "0", "limit":"0",
                            "filterGroups": FilterGroups
                            }
    Headers = {'accept': 'application/json', 'Authorization': 'Bearer ' + access_token} 
    try:
        DataResponse = requests.request("POST", url,json=DefaultInputJson, headers=Headers)
        if DataResponse.status_code == 200:
            JsonDataResponse = DataResponse.json()
            if 'total' in JsonDataResponse:
                portal_count = JsonDataResponse['total']
                difference = portal_count - table_count
                count_data = {
                        "territory_code": territory_code,
                        "object": territory_info['object'],
                        "min_epoch_timestamp":min_date,
                        "max_epoch_timestamp":max_date,
                        "min_modified_date":territory_info['min'],
                        "max_modified_date":territory_info['max'],
                        "table_count":table_count,
                        "portal_count":portal_count,
                        "difference":difference
                    }
                count_data_list.append(count_data)
                    
        else:
            logging.warning(DataResponse.status_code)
            JsonDataResponse = DataResponse.json()
            raise Exception
    except Exception as error:
        logging.warning(error)
        raise Exception
output_file_path = "HubspotOutputCountFile/" + f"{cycle_id}" + f"/hubspotcount.json"
out_json_write = s3_connect.WriteJsonFile(bucket,output_file_path,count_data_list)
logging.warning("Output comparison file has been generated")