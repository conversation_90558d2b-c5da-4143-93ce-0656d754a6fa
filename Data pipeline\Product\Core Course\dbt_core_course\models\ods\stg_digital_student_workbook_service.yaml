version: 2

sources:

  - name: stage_digital_student_workbook_service
    description: >
      Source data from the Digital Student Workbook Service which manages digital workbooks,
      student progress, and related activities for online learning.
    database: awsdatacatalog
    schema: stg_digital_student_workbook_service
    tables:
      - name: activityprogress
        description: Progress records for student activities in digital workbooks.
        columns:
          - name: id
            description: Primary key for the activity progress record
          - name: studentid
            description: Foreign key reference to the student
          - name: activityid
            description: Foreign key reference to the activity
          - name: progress
            description: Progress percentage or status
          - name: score
            description: Score achieved on the activity
          - name: created
            description: Timestamp when the progress record was created
          - name: lastupdated
            description: Timestamp when the progress record was last updated

      - name: centerconfigs
        description: Configuration settings for digital workbooks at the center level.
        columns:
          - name: id
            description: Primary key for the center configuration record
          - name: centerid
            description: Foreign key reference to the center
          - name: configkey
            description: Key name of the configuration setting
          - name: configvalue
            description: Value of the configuration setting
          - name: created
            description: Timestamp when the configuration was created
          - name: lastupdated
            description: Timestamp when the configuration was last updated

      - name: coursecontents
        description: Content items associated with courses in digital workbooks.
        columns:
          - name: id
            description: Primary key for the course content record
          - name: courseid
            description: Foreign key reference to the course
          - name: contentid
            description: Foreign key reference to the content item
          - name: sequence
            description: Sequence order of the content in the course
          - name: created
            description: Timestamp when the course content record was created
          - name: lastupdated
            description: Timestamp when the course content record was last updated

      - name: lessonprogress
        description: Progress records for student lessons in digital workbooks.
        columns:
          - name: id
            description: Primary key for the lesson progress record
          - name: studentid
            description: Foreign key reference to the student
          - name: lessonid
            description: Foreign key reference to the lesson
          - name: progress
            description: Progress percentage or status
          - name: score
            description: Score achieved on the lesson
          - name: created
            description: Timestamp when the progress record was created
          - name: lastupdated
            description: Timestamp when the progress record was last updated

      - name: reviewactivity
        description: Review activities for students to reinforce learning.
        columns:
          - name: id
            description: Primary key for the review activity record
          - name: studentid
            description: Foreign key reference to the student
          - name: activityid
            description: Foreign key reference to the activity
          - name: reviewdate
            description: Date when the review is scheduled
          - name: iscompleted
            description: Boolean flag indicating if the review is completed
          - name: created
            description: Timestamp when the review activity was created
          - name: lastupdated
            description: Timestamp when the review activity was last updated

      - name: studentaccessdetails
        description: Access details for students using digital workbooks.
        columns:
          - name: id
            description: Primary key for the student access details record
          - name: studentid
            description: Foreign key reference to the student
          - name: accesstype
            description: Type of access granted
          - name: startdate
            description: Start date for the access period
          - name: enddate
            description: End date for the access period
          - name: created
            description: Timestamp when the access details were created
          - name: lastupdated
            description: Timestamp when the access details were last updated