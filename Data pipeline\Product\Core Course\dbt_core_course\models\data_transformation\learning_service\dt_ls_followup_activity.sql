{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_followup_activity') }}

{% if is_incremental() %}
where
    date > (
        (
            select
                max(date)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT
    {{etl_load_date()}},
    folupactivity.id as id,
    date,
    {{ convert_to_local_timestamp(
        'date',
        'tz.time_zone_id'
    ) }} as local_date,
    student_id,
    substring(
        result,
        1,
        50000
    ) as result,
    is_deleted,
    reporter_id,
    folupacttype.name as follow_up_activity_type,
    is_private,
    has_reached_student
from
    ods_data as folupactivity
    Left Join (
        select
            id,
            name
        from
            {{ ref('ods_ls_followup_activity_type') }}
    ) as folupacttype
    ON folupactivity.follow_up_activity_type_id = folupacttype.id
    Left Join (
        select
            user_id,
            center_id
        from
            {{ ref('ods_ls_user') }}
    ) as User
    ON folupactivity.student_id = User.user_id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = User.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
