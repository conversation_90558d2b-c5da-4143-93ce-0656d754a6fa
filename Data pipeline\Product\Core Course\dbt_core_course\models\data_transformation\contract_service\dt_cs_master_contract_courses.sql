{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_master_contract_courses'
        ) }}
    {% if is_incremental() %}
        where last_updated_date > ((select max(last_updated_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    mccourses.id as id,
    master_contract_id,
    ptype.name as product_type,
    studytype.name as study_type,
    b2bcontract_types.name as b2_b_contract_type,
    price,
    refund,
    start_date,
    end_date,
    cstates.name as state,
    cstatus.name as status,
    cancel_date,
    is_active,
    created_date,
    {{convert_to_local_timestamp('created_date','time_zone_id')}} as local_created_date,
    last_updated_date,
    {{convert_to_local_timestamp('last_updated_date','time_zone_id')}} as local_last_updated_date,
    is_membership,
    is_non_membership
from ods_data as mccourses
    Left Join (
        select id,
            name
        from {{ ref( 'ods_cs_product_types' ) }}
    ) as ptype on mccourses.product_type_id = ptype.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_study_type' ) }}
    ) as studytype on mccourses.study_type_id = studytype.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_b2b_contract_types' ) }}
    ) as b2bcontract_types on mccourses.b2_b_contract_type = b2bcontract_types.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_contract_states' ) }}
    ) as cstates on mccourses.state = cstates.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_contract_statuses' ) }}
    ) as cstatus on mccourses.status = cstatus.id
    left join (
        select id,
            center_id
        from {{ ref( 'ods_cs_master_contracts' ) }}
    ) as mcontracts on mccourses.master_contract_id = mcontracts.id
    left join (
        select id,
            center_reference_id 
        from {{ ref( 'ods_cs_centers' ) }}
    ) as Center ON Center.id = MContracts.center_id
    Left Join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as TZ ON Center.center_reference_id = tz.center_reference_id