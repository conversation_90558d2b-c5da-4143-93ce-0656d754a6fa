# Import necessary modules
import boto3
import json
import logging
import os




def lambda_handler(event, context):
    # Define the name of the S3 bucket
    bucket = os.environ.get('bucket')

    # Create a Boto3 resource for S3
    boto3_resource = boto3.resource("s3")

    # Access the specific bucket using the Boto3 resource
    s3_bucket = boto3_resource.Bucket(bucket)

    # Creating an object reference for the file 'Config/Stage1.json' in the S3 bucket
    stage4_file_path = boto3_resource.Object(bucket, 'Config/Stage4.json')

    # Retrieving the content of the file as bytes, decoding it to UTF-8 and storing it in a string variable
    stage4_file_content = stage4_file_path.get()['Body'].read().decode('utf-8')

    # Parsing the JSON string and converting it into a Python dictionary
    stage4 = json.loads(stage4_file_content)

    # Define the path to the execution check file in S3
    execution_check_file_path = boto3_resource.Object(bucket, 'ExecutionCheck.json')

    # Retrieve the content of the execution check file
    execution_check_file_content = execution_check_file_path.get()['Body'].read().decode('utf-8')

    # Parse the JSON content of the execution check file
    execution_check = json.loads(execution_check_file_content)

    # Print the parsed execution check data
    logging.warning(execution_check)

    # Define the folder path for the logs based on the CycleId value from the execution check
    folder = f"Logs/{execution_check['CycleId']}/Stage4"

    # Get the list of files in the S3 bucket under the specified folder path
    files_in_s3 = [f.key.split(folder + "/")[1] for f in s3_bucket.objects.filter(Prefix=folder).all()]

    # Print the files in S3
    logging.warning(files_in_s3)

    # Print the count of files in S3
    logging.warning(len(files_in_s3))

    # Filtering the items marked as not run in particular run to match validation count in s3 folder
    filtered_list = [item for item in stage4['Stage4'] if item.get('Status') != 300]
    logging.warning(len(filtered_list))

    # Check if the number of files matches the expected count as config
    if len(files_in_s3) == len(filtered_list):
        logging.warning("The stage 4 is completed")
    else:
        raise Exception
