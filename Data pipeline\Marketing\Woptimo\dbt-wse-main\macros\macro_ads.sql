{%macro ads_select(lang)%}
SELECT 
search_term_view_search_term as query,
metrics_clicks as ad_clicks,
segments_date as event_date,
"{{lang}}" as lang
 FROM
{%endmacro%}

{%macro ads_where() %}
    where
 {% if is_incremental() %}
  -- this filter will only be applied on an incremental run
  segments_date {{daily_run()}}
{% else %}
  segments_date < DATE_ADD(CURRENT_DATE(), INTERVAL -3 DAY)  
{% endif %}   
{%endmacro%}