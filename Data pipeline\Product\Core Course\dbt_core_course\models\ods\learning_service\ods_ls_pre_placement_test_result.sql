{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        startlevel,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        prospectid,
        result,
        preplacementtestactivityid,
        preplacementtestinteractionid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'preplacementtestresult'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    startlevel as start_level,
    created,
    lastupdated as last_updated,
    id,
    prospectid as prospect_id,
    result,
    preplacementtestactivityid as pre_placement_test_activity_id,
    preplacementtestinteractionid as pre_placement_test_interaction_id
FROM
    rankedrecords
WHERE
    rn = 1;
