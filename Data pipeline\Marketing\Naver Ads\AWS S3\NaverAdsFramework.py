import requests
import time
import json
import hmac
import hashlib
import base64
import pandas as pd
import logging
import datetime
import CloudOperations
import DbOperations
import Utils

s3 = CloudOperations.S3
redshift = DbOperations.Database
iam_role = DbOperations.IamRole


class NaverAds:
    @staticmethod
    def get_credential(secret_name, region):
        return CloudOperations.SecretManager.GetSecret(secret_name, region)

    @staticmethod
    def generate_signature(timestamp, method, resource, secret_key):
        message = f'{timestamp}.{method}.{resource}'.encode('utf-8')
        hmac_sha256 = hmac.new(secret_key.encode('utf-8'), message, hashlib.sha256)
        signature = base64.b64encode(hmac_sha256.digest()).decode('utf-8')
        return signature

    @staticmethod
    def retry_operation(operation, max_retries=3):
        for _ in range(max_retries):
            try:
                response = operation
                return response
            except Exception as error:
                logging.warning(_)
                logging.warning(error)
                time.sleep(5)

        raise Exception("Max retries reached")

    @staticmethod
    def get_campaign_ids(cycle_id, account_id, account_name, secret_name, region, bucket):
        naver_search_ad_config = NaverAds.get_credential(secret_name, region)
        timestamp = str(int(time.time() * 1000))
        method = naver_search_ad_config['http_method']
        resource = naver_search_ad_config['campaign_request_uri']
        api_key = naver_search_ad_config['api_key']
        secret_key = naver_search_ad_config['api_secret']
        url = naver_search_ad_config['service_url'] + naver_search_ad_config['campaign_request_uri']
        customer_id = naver_search_ad_config[account_id]
        signature = NaverAds.generate_signature(timestamp, method, resource, secret_key)

        headers = {
            'accept': 'application/json',
            'X-Timestamp': timestamp,
            'X-API-KEY': api_key,
            'X-Customer': customer_id,
            'X-Signature': signature,
            'Cookie': 'nx_ssl=2'
        }
        response = NaverAds.retry_operation(requests.request(method, url, headers=headers))
        logging.warning("response status: '%s'", format(response.status_code))
        if response.status_code == 200:
            campaign_data = response.json()
            logging.warning("campaign response: '%s'", format(campaign_data))
            data_response = pd.DataFrame(campaign_data)
            data_response['AccountName'] = account_name
            column_names = ["nccCampaignId", "customerId", "name", "userLock", "campaignTp", "deliveryMethod",
                            "trackingUrl", "trackingMode", "usePeriod", "dailyBudget", "useDailyBudget",
                            "totalChargeCost", "status", "statusReason", "expectCost", "migType", "delFlag",
                            "regTm", "editTm", "AccountName"]
            reindex_data_response = data_response.reindex(columns=column_names)
            # Define the file key for the CSV file in S3
            campaign_response_file_key = \
                f'CampaignInformation/{account_name}{customer_id}/{cycle_id}/{account_name}CampaignResponse.csv'

            # Write the DataResponse as a CSV file in S3
            s3.WriteCsvFile(FilePath=campaign_response_file_key,
                            Bucket=bucket,
                            DataResponse=reindex_data_response)
            # Prepare query to delete existing campaign information for the particular account to be inserted
            delete_query = """delete from naver.ads WHERE customerid ='{}' 
                                and accountname ='{}';""".format(customer_id, account_name)
            logging.warning("delete_query:'%s'", format(delete_query))

            # execute delete query to delete the existing records present in table for the particular account
            execute_delete_query = redshift.Execution(ExecutionType="WriteTable",
                                                      Query=delete_query,
                                                      StatementName=f"DeleteNaver{account_name}Records")
            logging.warning("execute_delete_query:'%s'", format(execute_delete_query))

            # Prepare the COPY command query to move data from S3 to Redshift table
            copy_command_query = """COPY naver.ads FROM 's3://{}/{}' iam_role '{}' region '{}' IGNOREHEADER 1 CSV
                                                                        timeformat 'auto';""" \
                .format(bucket, campaign_response_file_key, iam_role, region)
            logging.warning("copy_command_query:'%s'", format(copy_command_query))

            # Execute the COPY command to write data to the Redshift table
            execute_copy_command = redshift.Execution(ExecutionType="WriteTable",
                                                      Query=copy_command_query,
                                                      StatementName=f"Naver{account_name}Execution")
            logging.warning("execute_copy_command:'%s'", format(execute_copy_command))
            return campaign_response_file_key

        else:
            logging.warning("Error retrieving campaign information. Status code: '%s'", format(response.status_code))

    @staticmethod
    def get_campaign_information(cycle_id, account_id, account_name, secret_name, region,
                                 bucket, cutoff_date, load_type, campaign_id_file_path):
        read_campaign_id = s3.ReadCsvFile(Bucket=bucket, FilePath=campaign_id_file_path)

        # Convert the column into a dictionary
        campaign_id_dict = read_campaign_id['nccCampaignId'].to_dict()
        naver_search_ad_config = NaverAds.get_credential(secret_name, region)
        method = naver_search_ad_config['http_method']
        resource = naver_search_ad_config['stat_request_uri']
        api_key = naver_search_ad_config['api_key']
        secret_key = naver_search_ad_config['api_secret']
        url = naver_search_ad_config['service_url'] + naver_search_ad_config['stat_request_uri']
        customer_id = naver_search_ad_config[account_id]

        """declaring empty List"""
        total_response_list = []
        """date conversion from date: previous date +1 and to_date: current date -1"""
        date_format = "%Y-%m-%d"

        # Convert cutoff_date to from_date by adding 1 day
        from_date_conversion = datetime.datetime.strptime(cutoff_date, date_format) + datetime.timedelta(days=1)
        from_date = from_date_conversion.strftime(date_format)
        logging.warning(from_date)

        # Get the current day's date and convert it to to_date by subtracting 1 day
        get_current_day_date = datetime.datetime.today().strftime(date_format)
        to_date_conversion = datetime.datetime.strptime(get_current_day_date, date_format) - datetime.timedelta(days=1)
        to_date = to_date_conversion.strftime(date_format)
        logging.warning(to_date)
        if load_type == "Initial":
            # Get the date range between from_date and to_date
            date_range = Utils.Tools.GetMonthRanges(from_date, to_date)

            # Iterate over the date range and extract campaign information for each date
            for date in date_range:
                logging.warning("date_range: '%s'", format(date))
                # Iterate through the dictionary values
                for campaign_id in campaign_id_dict.values():
                    timestamp = str(int(time.time() * 1000))
                    signature = NaverAds.generate_signature(timestamp, method, resource, secret_key)
                    headers = {
                        'accept': 'application/json',
                        'X-Timestamp': timestamp,
                        'X-API-KEY': api_key,
                        'X-Customer': customer_id,
                        'X-Signature': signature,
                        'Cookie': 'nx_ssl=2'
                    }
                    to_date_range = {
                        "since": date["FromDate"],
                        "until": date["ToDate"]
                    }
                    query_string = {
                        "id": campaign_id,
                        "fields": '["impCnt", "clkCnt", "salesAmt", "ctr", "cpc", "avgRnk", "ccnt", "pcNxAvgRnk",'
                                  '"mblNxAvgRnk", "crto", "convAmt", "ror", "cpConv", "viewCnt"]',
                        "timeRange": json.dumps(to_date_range, separators=(', ', ': '), ensure_ascii=False)
                    }

                    response = NaverAds.retry_operation(
                        requests.request(method, url, params=query_string, headers=headers))
                    logging.warning("response status: '%s'", format(response.status_code))
                    if response.status_code == 200:
                        campaign_data = response.json()
                        logging.warning("campaign response: '%s'", format(campaign_data))
                        if len(campaign_data) != 0:
                            for item in campaign_data['data']:
                                item['nccCampaignId'] = campaign_id
                                item['CustomerId'] = customer_id
                                item['AccountName'] = account_name
                            total_response_list.extend(campaign_data['data'])
                    else:
                        logging.warning("Error retrieving campaign information. Status code: '%s'",
                                        format(response.status_code))

        if load_type == "Incremental":
            # Iterate through the dictionary values
            for campaign_id in campaign_id_dict.values():
                timestamp = str(int(time.time() * 1000))
                signature = NaverAds.generate_signature(timestamp, method, resource, secret_key)
                headers = {
                    'accept': 'application/json',
                    'X-Timestamp': timestamp,
                    'X-API-KEY': api_key,
                    'X-Customer': customer_id,
                    'X-Signature': signature,
                    'Cookie': 'nx_ssl=2'
                }
                to_date_range = {
                    "since": from_date,
                    "until": to_date
                }
                logging.warning("date_range: '%s'", format(to_date_range))
                query_string = {
                    "id": campaign_id,
                    "fields": '["impCnt", "clkCnt", "salesAmt", "ctr","cpc","avgRnk","ccnt","pcNxAvgRnk",'
                              '"mblNxAvgRnk","crto","convAmt","ror","cpConv","viewCnt"]',
                    "timeRange": json.dumps(to_date_range, separators=(', ', ': '), ensure_ascii=False)

                }
                response = NaverAds.retry_operation(requests.request(method, url, params=query_string, headers=headers))
                logging.warning("response status: '%s'", format(response.status_code))
                if response.status_code == 200:
                    campaign_data = response.json()
                    logging.warning("campaign response: '%s'", format(campaign_data))
                    if len(campaign_data) != 0:
                        for item in campaign_data['data']:
                            item['nccCampaignId'] = campaign_id
                            item['CustomerId'] = customer_id
                            item['AccountName'] = account_name
                        total_response_list.extend(campaign_data['data'])
                else:
                    logging.warning("Error retrieving campaign information. Status code: '%s'",
                                    format(response.status_code))

        if len(total_response_list) == 0:
            logging.warning("no response to store")
            # Prepare summary when no response is found
            summary = {
                "cutoff_date": to_date,
                "records_processed": 0
            }
            return summary

        data_response = pd.DataFrame(total_response_list)
        column_names = ["ctr", "pcNxAvgRnk", "mblNxAvgRnk", "convAmt", "crto", "ccnt", "avgRnk", "dateEnd", "salesAmt",
                        "clkCnt", "ror", "dateStart", "viewCnt", "cpc", "cpConv", "impCnt", "nccCampaignId",
                        "CustomerId", "AccountName"]
        reindex_data_response = data_response.reindex(columns=column_names)
        # Find the minimum and maximum dates from the DataResponse
        response_from_date = min(reindex_data_response['dateStart'])
        response_to_date = max(reindex_data_response['dateEnd'])
        # Define the file key for the CSV file in S3
        campaign_stat_response_file_key = \
            (f'CampaignStat/{account_name}{customer_id}/{cycle_id}/'
             f'{response_from_date}-{response_to_date}/{account_name}CampaignStatResponse.csv')

        # Write the DataResponse as a CSV file in S3
        write_campaign_stat_response = s3.WriteCsvFile(FilePath=campaign_stat_response_file_key,
                                                       Bucket=bucket,
                                                       DataResponse=reindex_data_response)

        # Prepare the COPY command query to move data from S3 to Redshift table
        copy_command_query = """COPY naver.ads_stat FROM 's3://{}/{}' iam_role '{}' region '{}' IGNOREHEADER 1 CSV
                                                                                timeformat 'auto';""" \
            .format(bucket, campaign_stat_response_file_key, iam_role, region)
        logging.warning("copy_command_query:'%s'", format(copy_command_query))

        # Execute the COPY command to write data to the Redshift table
        execute_copy_command = redshift.Execution(ExecutionType="WriteTable",
                                                  Query=copy_command_query,
                                                  StatementName=f"Naver{account_name}Execution")
        logging.warning("execute_copy_command:'%s'", format(execute_copy_command))

        summary = {
            "cutoff_date": to_date,
            "records_processed": len(reindex_data_response),
            "s3_file_path": write_campaign_stat_response
        }
        return summary
