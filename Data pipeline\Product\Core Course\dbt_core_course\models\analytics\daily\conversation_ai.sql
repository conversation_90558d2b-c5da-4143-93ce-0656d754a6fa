{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

SELECT
    fm.chat_id,
    count(distinct(fm.message_interaction_id)) as total_no_of_interactions,
    fm.user_id,
    fm.contract_id,
    fm.content_id,
    fm.start_date,
    fm.end_date,
    fm.local_start_date AS chat_start_date,
    fm.local_end_date AS chat_end_date,
    fm.gpt_4o_mini_cost AS total_cost,
    BOOL_OR(fm.hasEnded) AS chat_ended,
    fm.total_input_tokens,
    fm.total_output_tokens,
    fm.total_input_tokens+fm.total_output_tokens as total_tokens,
    case when BOOL_OR(fm.hasEnded) = true then date_diff('second', fm.local_start_date, fm.local_end_date) end AS chat_duration_seconds,
    SUM(1) AS total_messages,
    SUM(CASE WHEN fm.message_role = 'user' THEN 1 ELSE 0 END) AS user_messages,
    SUM(CASE WHEN fm.message_role = 'assistant' THEN 1 ELSE 0 END) AS assistant_messages,
    SUM(CASE WHEN fm.message_role = 'user' THEN fm.message_audio_duration ELSE 0 END) AS user_speaking_time,
    SUM(CASE WHEN fm.message_role = 'assistant' THEN fm.message_audio_duration ELSE 0 END) AS assistant_speaking_time,
    SUM(fm.message_outputtokens) AS total_output_tokens_per_message,
    SUM(fm.message_inputtokens) AS total_input_tokens_per_message,
    SUM(fm.message_audio_duration) AS total_message_audio_duration,
    MAX(fm.message_outputtokens) AS max_output_tokens,
    MAX(fm.message_inputtokens) AS max_input_tokens,
    MIN(fm.message_outputtokens) AS min_output_tokens,
    MIN(fm.message_inputtokens) AS min_input_tokens,
    SUM(LENGTH(fm.message_content)) AS total_message_length,
    MAX(LENGTH(fm.message_content)) AS max_message_length,
    MIN(LENGTH(fm.message_content)) AS min_message_length,
    SUM(
    CASE 
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%i''m sorry, i don''t understand%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%could you please say that again%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%can you repeat%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%could you please say something%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%could you please repeat what you said%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%could you please repeat that%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%could you please say that%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%could you say that again%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%could you say something again%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%could you clarify%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%could you rephrase%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%could you please repeat%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%can you repeat the question please%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%can you rivet that%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%can you repeat that%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%i don''t understand%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%i still don''t understand%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%i still don''t quite understand%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%i don''t get it%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%i don''t hear%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%i went too wrong%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%i don''t understand everything%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%i don''t quite understand%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%i don''t understand that%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%i didn''t understand%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%i didn''t quite understand%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%please tell me%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%please could you%' THEN 1
        WHEN fm.message_role = 'assistant' AND LOWER(fm.message_content) LIKE '%what is the last question%' THEN 1
        ELSE 0
    END
) AS understanding_gap_count
FROM
    {{ref("dt_cai_conversation")}} fm
GROUP BY
    fm.chat_id,
    fm.user_id,
    fm.contract_id,
    fm.content_id,
    fm.start_date,
    fm.end_date,
    fm.local_start_date,
    fm.local_end_date,
    fm.gpt_4o_mini_cost,
    fm.hasEnded,
    fm.total_input_tokens,
    fm.total_output_tokens,
    fm.total_input_tokens,
    fm.total_output_tokens