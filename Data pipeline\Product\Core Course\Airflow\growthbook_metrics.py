import os
import logging
from datetime import datetime, timedelta
import boto3
import json
import psycopg2
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import BashOperator
from dependencies.slack_alerts import task_failure_callback, task_success_callback
from dependencies import glue_trigger

# Default arguments for the DAG
default_args = {
    'owner': 'wse_data_team',
    'start_date': datetime(2025, 2, 4)
}

# Function to fetch credentials from AWS Secrets Manager
def get_secret(secret_name, region_name='eu-west-1'):
    client = boto3.client('secretsmanager', region_name=region_name)
    response = client.get_secret_value(SecretId=secret_name)
    secret = json.loads(response['SecretString'])
    return secret

# Function to fetch configuration from S3
def fetch_query_from_s3(bucket_name, file_key, region_name='eu-west-1'):
    s3_client = boto3.client('s3', region_name=region_name)
    response = s3_client.get_object(Bucket=bucket_name, Key=file_key)
    query = response['Body'].read().decode('utf-8')
    return query

# Function to check DAG states in the database
def check_dag_states():
    dag_id = 'core_course_workflow'  # Replace with your DAG ID
    secret_name = "airflow_postgres"  # AWS Secrets Manager secret name
    region_name = "eu-west-1"  # AWS region where your secret is stored
    s3_bucket = "prod-corecourse"  # Replace with your S3 bucket name
    s3_file_key = "config/airflow_dag_state_check.sql"  # Replace with the path to your query file in S3

    try:
        # Fetch PostgreSQL credentials from Secrets Manager
        credentials = get_secret(secret_name, region_name)

        # Fetch the query from S3
        query = fetch_query_from_s3(s3_bucket, s3_file_key, region_name)

        # Connect to PostgreSQL
        conn = psycopg2.connect(
            host=credentials['host'],
            port=credentials['port'],
            database=credentials['database'],
            user=credentials['username'],
            password=credentials['password'],
        )
        cursor = conn.cursor()

        # Execute the query to fetch DAG runs for today
        cursor.execute(query, (dag_id,))
        results = cursor.fetchall()

        # Count the number of successful runs
        success_count = sum(1 for result in results if result[1] == 'success')

        if success_count >= 2:
            print(f"DAG {dag_id} has at least two successful runs today. Proceeding.")
        else:
            raise Exception(f"DAG {dag_id} does not have at least two successful runs today.")

    except Exception as e:
        print(f"Error: {e}")
        raise
    finally:
        if conn:
            conn.close()

# Define the DAG
with DAG(
    'growthbook_metrics',
    default_args=default_args,
    description='growthbook metrics refresh from athena to big query',
    schedule_interval="0 9 * * *",  # Runs daily at 9:00 AM UTC (2:30 PM IST)
    catchup=False,
    tags=['core-course']
) as dag:

    # Task to check the DAG state
    check_dag_states_task = PythonOperator(
        task_id='check_dag_state',
        python_callable=check_dag_states,
        on_failure_callback=task_failure_callback,
        dag=dag
    )

    # Proceed task if both DAGs succeeded

    # trigger glue job
    op_kwargs ={
        "glue_job_name": "Growthbook Metrics"
    }

    glue_job = PythonOperator(
            task_id='growthbook_metrics',
            python_callable=glue_trigger.run_glue_job,
            on_failure_callback=task_failure_callback,
            on_success_callback=task_success_callback,
            op_kwargs=op_kwargs,
            dag=dag
        )

    # Task dependencies
    check_dag_states_task >> glue_job
    