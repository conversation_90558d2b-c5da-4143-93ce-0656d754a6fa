version: 2

models:
  - name: fact_levels_started
    columns:
      - name: territory_name
        tests:
          - not_null:
              severity: error
      - name: center_name
        tests:
          - not_null:
              severity: error
      - name: date
        tests:
          - not_null:
              severity: error
      - name: billing_code
        tests:
          - accepted_values:
              values: ['DDM', 'DDMT']
              severity: error
      - name: unlock_type
        tests:
          - accepted_values:
              values: ['promo', 'standard']
              severity: error
      - name: workbook_type
        tests:
          - accepted_values:
              values: ['digital', 'printed']
              severity: error
      - name: operation_type
        tests:
          - accepted_values:
              values: ['refund', 'bill']
              severity: error
      - name: is_restart
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: levels_started
        tests:
          - accepted_values:
              values: [1, -1, 2, -2]
              quote: false
              severity: error
      - name: levels_started_teens
        tests:
          - accepted_values:
              values: [0, 1, -1, 2, -2]
              quote: false
              severity: error
      - name: levels_started_not_teens
        tests:
          - accepted_values:
              values: [0, 1, 2, -1, -2]
              quote: false
              severity: error
