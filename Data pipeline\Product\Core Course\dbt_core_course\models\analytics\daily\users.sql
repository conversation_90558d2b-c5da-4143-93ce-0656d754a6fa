{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH RankedRecords AS (
  SELECT
    user_basic_info.user_id,
    user_basic_info.ssds_id as student_reference_id,
    user_basic_info.center_id,
    user_basic_info.territory_id,
    user_basic_info.is_active,
    user_basic_info.user_name,
    user_basic_info.first_name,
    user_basic_info.last_name,
    user_basic_info.birth_date,
    user_basic_info.email,
    user_basic_info.created as created_at,
    user_basic_info.last_updated as updated_at,
    CASE 
      WHEN user_basic_info.is_active = false THEN user_basic_info.last_updated
      ELSE NULL
    END AS deleted_at,
    user_additional_info.mobile_telephone,
    user_roles.role,
    user_roles.role_type,
    center.center_reference_id,
    ROW_NUMBER() OVER (PARTITION BY user_basic_info.user_id ORDER BY user_basic_info.last_updated DESC) AS rn
  FROM {{ref('dt_idam_user_basic_info')}} as user_basic_info
  LEFT JOIN {{ref('dt_idam_user_additional_info')}} as user_additional_info
    ON user_basic_info.user_id = user_additional_info.user_basic_info_id
  LEFT JOIN {{ref('dt_idam_user_roles')}} as user_roles
    ON user_basic_info.user_id = user_roles.user_basic_info_id
  LEFT JOIN {{ref('dt_idam_center')}} as center
    ON user_basic_info.center_id = center.id
)

SELECT
  user_id,
  student_reference_id,
  center_id,
  territory_id,
  is_active,
  user_name,
  first_name,
  last_name,
  birth_date,
  email,
  created_at,
  updated_at,
  deleted_at,
  mobile_telephone,
  role,
  role_type,
  center_reference_id
FROM RankedRecords
WHERE rn = 1;
