{{ config(
    tags=["start_of_month"],
    materialized = 'incremental',
    incremental_strategy = 'merge',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet',
    unique_key = 'dbt_unique_id'
) }}
 
with speak_last_added_date as
(
    SELECT
        cai.contract_id,
        date(max(cai.local_created_date)) AS speak_last_added_date
    FROM {{ref("dt_cs_contracts_audit_info")}} cai
    WHERE cai.modified_field = 'products'
    and cai.present_value like '%11%' and cai.previous_value not like '%11%'
    group by
        cai.contract_id
)
select
c.center_reference_id
,c.contract_reference_id
,c.student_reference_id
,c.crm_contract_number
,c.student_code
,case when c.product_type = 'core course' then 'Core Course+' else 'Speak+' end as product_type
,c.contract_type
,array_join(transform(split(u.first_name, ' '),word -> concat(upper(substr(word, 1, 1)), lower(substr(word, 2)))),' ') as first_name
,array_join(transform(split(u.last_name, ' '),word -> concat(upper(substr(word, 1, 1)), lower(substr(word, 2)))),' ') as last_name
,c.is_promotional
,c.start_date
,ad.speak_last_added_date
,case when c.product_type = 'core course'
            and c.start_date < date(ad.speak_last_added_date)
            then ad.speak_last_added_date
                else null end as speak_last_added_date_post_start_month
,date_trunc('month',current_date) as billing_date
,case when c.product_type = 'core course'
        and c.start_date < date(ad.speak_last_added_date)
        then date_diff('month',ad.speak_last_added_date,date_trunc('month',current_date))+1
      else date_diff('month',c.start_date,date_trunc('month',current_date))+1 end as billing_month
,(cast(date_trunc('month',current_date) as varchar) || cast(c.student_reference_id as varchar)
|| cast(c.contract_reference_id as varchar)) as dbt_unique_id
,CAST(current_timestamp AS TIMESTAMP(6)) AS load_date
from {{ref("contracts")}} c
left join {{ref("users")}} u on c.student_reference_id = u.student_reference_id
left join speak_last_added_date ad on c.contract_id = ad.contract_id
where c.status = 'valid'
and (c.contract_product like '%speak+%'
or c.product_type = 'd2c')
and c.start_date < date_trunc('month',current_date)
-- this is to avoid null record due to etl refresh
and c.student_reference_id is not null