{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH RankedRecords AS (
    SELECT 
        id
        ,contractid
        ,studentid
        ,levelnumber
        ,hasdigitalworkbookaccess
        ,iseditable
        ,releaseworkbook
        ,operationtype
        ,{{cast_to_timestamp('created')}} as created
        ,{{cast_to_timestamp('lastupdated')}} as lastupdated
        ,isactive
        ,{{cast_to_timestamp('unlockeddate')}} as unlockeddate
        ,centerreferenceid
        ,reviewMode
        ,ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdated DESC) AS rn
    FROM 
        {{source('stage_digital_student_workbook_service', 'studentaccessdetails')}}
)

SELECT
    {{etl_load_date()}}
    ,id
    ,contractid as contract_id
    ,studentid  as student_id
    ,levelnumber as level_number
    ,hasdigitalworkbookaccess as has_digital_work_book_access
    ,iseditable as is_editable
    ,releaseworkbook as release_workbook
    ,operationtype as operation_type
    ,created
    ,lastupdated as last_updated
    ,isactive as is_active
    ,unlockeddate as unlocked_date
    ,centerreferenceid as center_reference_id
    ,reviewMode as review_Mode
FROM 
    RankedRecords
WHERE 
    rn = 1;