{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        scheduledclassid,
        hosturl,
        participanturl,
        vendorid,
        password,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_schedule_and_booking_service',
            'digitalmeetinginformation'
        ) }}
)
SELECT
    {{etl_load_date()}},
    {{ cast_to_timestamp('created') }} as created,
    {{ cast_to_timestamp('lastupdated') }} as last_updated,
    id,
    scheduledclassid as scheduled_class_id,
    hosturl as host_url,
    participanturl as participant_url,
    vendorid as vendor_id,
    password
FROM
    rankedrecords
WHERE
    rn = 1;
