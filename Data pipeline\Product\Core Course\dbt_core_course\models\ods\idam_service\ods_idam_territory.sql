{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        isactive,
        id,
        territoryreferenceid,
        name,
        code,
        isocode,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                id
        ) AS rn
    FROM
        {{ source(
            'stage_idam_service',
            'territory'
        ) }}
)
SELECT
    {{etl_load_date()}},
    isactive as is_active,
    id,
    territoryreferenceid as territory_reference_id,
    name,
    code,
    isocode as iso_code
FROM
    rankedrecords
WHERE
    rn = 1;
