import boto3
import logging

# Create an SNS client
sns = boto3.client('sns')


def lambda_handler(event, context):
    # Create the email message
    message = str(event)
    # Publish the email message
    response = sns.publish(
        TopicArn='arn:aws:sns:eu-west-1:262158335980:dktest',
        Message=message,
        Subject='Execution-Failure-Notification',
        MessageStructure='string'
    )
    # Print the response
    logging.warning(response)
    # TODO implement
    raise Exception
