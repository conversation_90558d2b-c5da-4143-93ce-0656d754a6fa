{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with union_class_id as (
    select id as class_id from {{ref('dt_snb_scheduled_class')}}
    union
    select ref_class_id as class_id from {{ref('dt_ls_class')}}
)
,
union_tables as (
    select
        class_id,
        coalesce(snb_class.teacher_id, ls_class.teacher_id)                                  as teacher_id,
        coalesce(snb_class.start_time, ls_class.start_date)                                  as start_date,
        coalesce(snb_class.local_start_time, ls_class.local_start_date)                      as local_start_date,
        coalesce(snb_class.communication_account_type, ls_class.technology)                  as technology,
        coalesce(snb_class.description, ls_class.class_description)                          as class_description,
        coalesce(snb_class.number_of_seats, ls_class.number_of_seats)                        as number_of_seats,
        coalesce(snb_class.no_of_seats_in_stand_by, ls_class.number_of_waiting_students)     as number_of_waiting_students,
        coalesce(snb_class.created, ls_class.created)                                        as created,
        coalesce(snb_class.local_created, ls_class.local_created)                            as local_created,
        coalesce(snb_class.last_updated, ls_class.last_updated)                              as last_updated,
        coalesce(snb_class.local_last_updated, ls_class.local_last_updated)                  as local_last_updated,
        snb_class.center_reference_id                                                        as snb_center_id,
        ls_class.center_id                                                                   as ls_center_id,
        snb_class.end_time,
        snb_class.local_end_time,
        coalesce(snb_class.class_type,ls_class.class_type)                                   as class_type,
        snb_class.category_from_booking,
        snb_class.is_cancelled,
        snb_class.source,
        snb_class.created_by,
        snb_class.created_by_role,
        snb_class.last_updated_by,
        snb_class.last_updated_by_role,
        ls_class.categories_abbreviations,
        ls_class.is_pro,
        ls_class.duration,
        ls_class.is_complete,
        ls_class.company_id,
        ls_class.is_visible_in_group,
        ls_class.is_b2_b,
        ls_class.is_restricted_to_online_only,
        ls_class.is_teen,
        ls_class.class_code,
        ls_class.number_of_students,
        ls_class.is_closed,
        ls_class.is_online,
        ROW_NUMBER() OVER (PARTITION BY  class_id order by ls_class.last_updated desc) as rank_last_class
    from
    union_class_id
    LEFT JOIN {{ref('dt_snb_scheduled_class')}} as snb_class ON class_id = snb_class.id
    LEFT JOIN {{ref('dt_ls_class')}} as ls_class ON class_id = ls_class.ref_class_id
)
, renaming as (
            select
                class_id,
                teacher_id,
                start_date as class_start_datetime,
                local_start_date as class_local_start_datetime,
                technology as class_communication_account_type,
                class_description,
                number_of_seats as class_number_of_seats,
                number_of_waiting_students as class_number_of_seats_in_stand_by,
                created as class_created_datetime,
                local_created as class_local_created_datetime,
                last_updated as class_last_updated_datetime,
                local_last_updated as class_local_last_updated_datetime,
                snb_center_id as class_center_reference_id,
                ls_center_id,
                end_time as class_end_datetime,
                local_end_time as class_local_end_datetime,
                class_type,
                category_from_booking as class_category_from_booking,
                is_cancelled as class_cancelled_flag,
                source as class_source,
                created_by as class_created_by,
                created_by_role as class_created_by_role,
                last_updated_by as class_last_updated_by,
                last_updated_by_role as class_last_updated_by_role,
                categories_abbreviations,
                is_pro,
                duration,
                is_complete,
                company_id,
                is_visible_in_group,
                is_b2_b,
                is_restricted_to_online_only,
                is_teen,
                class_code,
                number_of_students,
                is_closed,
                is_online
            from
                union_tables
            where rank_last_class = 1
            )
    , formatting as (
            select 
                class_id,
                teacher_id,
                class_start_datetime,
                class_local_start_datetime,
                class_communication_account_type,
                class_description,
                class_number_of_seats,
                class_number_of_seats_in_stand_by,
                class_created_datetime,
                class_local_created_datetime,
                class_last_updated_datetime,
                class_local_last_updated_datetime,
                class_center_reference_id,
                ls_center_id,
                class_end_datetime,
                class_local_end_datetime,
                class_type,
                class_category_from_booking,
                class_cancelled_flag,
                class_source,
                class_created_by,
                class_created_by_role,
                class_last_updated_by,
                class_last_updated_by_role,
                categories_abbreviations,
                is_pro,
                duration,
                is_complete,
                company_id,
                is_visible_in_group,
                is_b2_b,
                is_restricted_to_online_only,
                is_teen,
                class_code,
                number_of_students,
                is_closed,
                is_online
            from renaming
    )

select *
from formatting