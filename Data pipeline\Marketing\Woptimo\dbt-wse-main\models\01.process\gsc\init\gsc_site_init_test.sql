{{
    config(
        tags=["incremental","init","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}

    {{gsc_site_select("it","ita")}}
    {{source("gsc_it",'it_site')}}
    {{gsc_site_where("2023-04-01")}} 

    union all

    {{gsc_site_select("de","deu")}}
    {{source("gsc_de",'de_site')}}
    {{gsc_site_where("2023-04-01")}} 

    union all

    {{gsc_site_select("es","esp")}}
    {{source("gsc_es",'es_site')}}
    {{gsc_site_where("2023-04-01")}} 

    union all

    {{gsc_site_select("pe","per")}}
    {{source("gsc_pe",'pe_site')}}
    {{gsc_site_where("2023-04-01")}} 

    union all

    {{gsc_site_select("sa","sau")}}
    {{source("gsc_sa",'sa_site')}}
    {{gsc_site_where("2023-04-01")}} 


    union all

    {{gsc_site_select("mn","mng")}}
    {{source("gsc_mn",'mn_site')}}
    {{gsc_site_where("2023-04-01")}} 

    union all

    {{gsc_site_select("tn","tun")}}
    {{source("gsc_tn",'tn_site')}}
    {{gsc_site_where("2023-04-01")}} 

    union all

    {{gsc_site_select("dz","dza")}}
    {{source("gsc_dz",'dz_site')}}
    {{gsc_site_where("2023-04-01")}}

    union all

    {{gsc_site_select("com","")}}
    {{source("gsc_com",'com_site')}}
    {{gsc_site_where("2023-04-01")}}

    union all

    {{gsc_site_select("vn","vnm")}}
    {{source("gsc_vn",'vn_site')}}
    {{gsc_site_where("2023-04-25")}}   

    union all

    {{gsc_site_select("il","isr")}}
    {{source("gsc_il",'il_site')}}
    {{gsc_site_where("2023-07-25")}}   

    union all

    {{gsc_site_select("la","lao")}}
    {{source("gsc_la",'la_site')}}
    {{gsc_site_where("2023-12-22")}}    

    union all

    {{gsc_site_select("ma","mar")}}
    {{source("gsc_ma",'ma_site')}}
    {{gsc_site_where("2023-12-22")}}   
