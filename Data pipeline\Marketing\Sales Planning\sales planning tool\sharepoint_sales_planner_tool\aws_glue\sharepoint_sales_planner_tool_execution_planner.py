import packages

source = 'sales planner tool'
sql = "select process_id, source, object, operation, load_type, status, execution_type, territory,s3_path," \
      "cutoff_date from sharepoint.execution_planner where source =" + f"'{source}'" + \
      " and execution_type ='true' and status = '0';"
packages.logging.warning("sql:'%s'", format(sql))
planner_response = packages.execution_planner.Planner.execute(sql, source)
packages.logging.warning("planner_response:'%s'", format(planner_response))
