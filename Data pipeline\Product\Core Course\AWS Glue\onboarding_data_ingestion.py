import sys
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark.context import SparkContext
from awsglue.context import GlueContext
from awsglue.job import Job
from pyspark.sql.functions import col, to_timestamp, date_format, explode_outer, lit, when
from pyspark.sql.functions import col, explode_outer, posexplode_outer, monotonically_increasing_id, row_number
from pyspark.sql.window import Window
from pyspark.sql.types import StructType, StructField, StringType, DoubleType, BooleanType, IntegerType, ArrayType, TimestampType
from datetime import datetime, timezone
import boto3
import json
import pymongo
import CloudOperations


def execute_query(athena, query_string, database):
    response = athena.start_query_execution(
        QueryString=query_string,
        QueryExecutionContext={'Database': database},
        ResultConfiguration={'OutputLocation': 's3://prod-corecourse/athena-results'}
    )

    while True:
        query_status = athena.get_query_execution(QueryExecutionId=response['QueryExecutionId'])
        state = query_status['QueryExecution']['Status']['State']
        if state in ['SUCCEEDED', 'FAILED', 'CANCELLED']:
            break

    if state == 'SUCCEEDED':
        result = athena.get_query_results(QueryExecutionId=response['QueryExecutionId'])
        if result['ResultSet']['Rows']:
            # Check if there are any rows in the result
            columns = [col['VarCharValue'] for col in result['ResultSet']['Rows'][1]['Data']]
            return columns[0] if columns else None
        else:
            # For queries like DROP and CREATE TABLE that don't return rows
            return "Query executed successfully"
    else:
        raise Exception(f"Query execution failed with state: {state}")


def max_date(args, max_query):
    athena = boto3.client('athena')
    table_exists_query = f"""
    SELECT CASE WHEN EXISTS (SELECT * FROM information_schema.tables WHERE table_schema = '{args['OdsDatabase']}' 
    AND table_name = '{args['OdsObject']}') THEN 'yes' ELSE 'no' END AS table_exists"""

    print(table_exists_query)

    table_check_result = execute_query(athena, table_exists_query, args['OdsDatabase'])
    print(table_check_result)

    if table_check_result != 'yes':
        return 'full_load'

    if table_check_result == 'yes' and args['LoadType'] == 'Incremental':
        max_date_query_result = execute_query(athena, max_query, args['OdsDatabase'])
        print(max_date_query_result)
        return max_date_query_result

    else:
        raise Exception("Query execution failed")

# function to convert object id to string        
def convert_ids_to_str(doc):
    if isinstance(doc, dict):
        for key, value in doc.items():
            if key == '_id':  # Convert _id to string directly
                doc[key] = str(value)
            elif isinstance(value, (dict, list)):
                convert_ids_to_str(value)
    elif isinstance(doc, list):
        for item in doc:
            convert_ids_to_str(item)
    return doc


def data_processing(args, collection, batch_size=1000):
    if args['LoadType'] == 'Incremental':
        max_updated_date_query = f"""SELECT max({args['FilterColumn']}) as {args['FilterColumn']} 
        FROM {args['OdsDatabase']}.{args['OdsObject']}"""
        print("max query:", max_updated_date_query)

        cutoff_date_updated = max_date(args, max_updated_date_query)
        if cutoff_date_updated != 'full_load':
            last_export_date = datetime.strptime(cutoff_date_updated, "%Y-%m-%d %H:%M:%S.%f")
            query = {args['MongoFilterColumn']: {"$gt": last_export_date}}
        else:
            query = {}
    else:
        query = {}

    print("MongoDB query:", query)

    all_documents = []
    total_documents = 0

    # Use skip and limit for pagination
    skip = 0
    while True:
        cursor = collection.find(query).sort([(args['MongoFilterColumn'], 1), ("_id", 1)]).skip(skip).limit(batch_size)
        documents = list(cursor)

        if not documents:
            break

        # Process documents
        converted_documents = [convert_ids_to_str(doc) for doc in documents]
        all_documents.extend(converted_documents)
        total_documents += len(documents)
        print(total_documents)
        skip += len(documents)  # Increment skip by actual number of documents read

        print(f"Read batch of {len(documents)} documents, total so far: {total_documents}")

        if len(documents) < batch_size:
            # We've reached the end of the collection
            break

    print(f"Total documents read: {total_documents}")

    if total_documents > 0:
        print("going to write in s3")
        # Define schema explicitly
        schema = StructType([
        StructField("_id", StringType(), True),
        StructField("UserReferenceId", StringType(), True),
        StructField("FeatureType", StringType(), True),
        StructField("steps", ArrayType(StructType([  # Define steps as an array of structs
            StructField("IsCompleted", BooleanType(), True),
            StructField("IsSkipped", BooleanType(), True),
            StructField("StepType", StringType(), True),
            StructField("UpdatedAt", TimestampType(), True),
            StructField("_id", StringType(), True)
        ])), True),
        StructField("IsCompleted", BooleanType(), True),
        StructField("IsSkipped", BooleanType(), True),
        StructField("createdAt", TimestampType(), True),
        StructField("updatedAt", TimestampType(), True),
        StructField("__v", IntegerType(), True)
        ])
        # Create a Spark DataFrame from all documents with the defined schema
        spark_df = spark.createDataFrame(all_documents, schema)
        spark_df.show()
        print("data frame created")
        # Storing all data in S3
        s3_output_path = f"s3://conversation-ai/test_latest_read/mongo_db/onboarding/useronboards/"
        spark_df.repartition(1).write.mode("overwrite").json(s3_output_path)
        print(f"Written {total_documents} documents to S3")

        return all_documents[-1][args['MongoFilterColumn']]
    else:
        print("No documents to write")
        return None


# Initialize the Glue job
args = getResolvedOptions(sys.argv,
                          ["JOB_NAME", "Object", "SecretManager", "FilterColumn", "LoadType", "DatabaseConnection",
                           "AthenaDatabase", "OdsObject", "OdsDatabase", "HistDatabase","MongoFilterColumn"])
sc = SparkContext()
glueContext = GlueContext(sc)
spark = glueContext.spark_session
job = Job(glueContext)
job.init(args['JOB_NAME'], args)

# Fetch database connection values from AWS Secret Manager
fetch_database_connection = CloudOperations.SecretManager()
database_connection_values = fetch_database_connection.GetSecret(args["SecretManager"], "eu-west-1")

# MongoDB's connection details
mongo_uri = database_connection_values['mongo_uri']
mongo_database = database_connection_values['database']
mongo_collection = database_connection_values['collection']

# Default query parameters
BATCH_SIZE = 10000  # This controls how many documents are fetched from MongoDB in each iteration

# Connect to MongoDB
client = pymongo.MongoClient(mongo_uri)
db = client[mongo_database]
collection = db[mongo_collection]

# Run the incremental export
last_export_date = data_processing(args, collection, BATCH_SIZE)

print(f"Last export date: {last_export_date}")

# Close the MongoDB connection
client.close()
job.commit()