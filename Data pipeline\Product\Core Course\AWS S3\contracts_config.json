[{"execution_type": "yes", "SecretManager": "ContractService", "Object": "ContractClassAccesstype", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contract_class_access_type"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ContractTransfers", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contract_transfers"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ContractProducts", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contract_products"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ContractPrice", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contract_price"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "GroupProducts", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_group_products"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "GroupClassAccessType", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_group_class_access_type"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "Groups", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_groups"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ContractsAuditinfo", "FilterColumn": "CreatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contracts_audit_info"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ContractValidations", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contract_validations"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "MasterContractLimitMappings", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_master_contract_limit_mappings"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "MasterContractCourseAuditInfo", "FilterColumn": "CreatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_master_contract_course_audit_Info"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "MasterContractAuditInfo", "FilterColumn": "CreatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_master_contract_audit_Info"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "MasterContractProducts", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_master_contract_products"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "MasterContractCourses", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_master_contract_courses"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "MasterContracts", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_master_contracts"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "Contracts", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contracts"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "CompanyAdditionalInfo", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_company_additional_info"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "NewVersionCompanies", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_new_version_companies"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "Users", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_users"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "B2BContractTypes", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_b2b_contract_types"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ContractTypes", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contract_types"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "Territory", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_territory"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "UserType", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_user_type"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ProductTypes", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_product_types"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "CurrentValidationState", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_current_validation_state"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "Centers", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_centers"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ContractStatuses", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contract_statuses"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ClassAccessTypes", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_class_access_types"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "Roles", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_roles"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ProductTypeProducts", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_product_type_products"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "TimeZone", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_timezone"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "CenterProducts", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_center_products"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "StudyType", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_study_type"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ContractChangeTypes", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contract_change_types"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ProductLevels", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_product_levels"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "CenterClassAccess", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_center_class_access"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ContractValidationState", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contract_validation_state"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ContractStates", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contract_states"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ServiceTypes", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_service_types"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "MasterContractTypeOfLimit", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_master_contract_type_of_limit"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "Locations", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_locations"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "TerritoryClassAccess", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_territory_class_access"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "Products", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_products"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "TransferStatuses", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_transfer_statuses"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "Contract<PERSON><PERSON>t<PERSON><PERSON>s", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contract_audit_fields"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ContractDocumentTypes", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contract_document_types"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "MasterContractPriceInformation", "FilterColumn": "LastUpdatedDate", "LoadType": "Incremental", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_master_contract_price_information"}, {"execution_type": "yes", "SecretManager": "ContractService", "Object": "ContractSourceTypes", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ContractService", "AthenaDatabase": "stg_contract_service", "OdsDatabase": "ods_contract_service", "HistDatabase": "hist_contract_service", "OdsObject": "ods_cs_contract_source_types"}]