import openpyxl
from openpyxl import load_workbook
from openpyxl.styles import Border, Side, Protection, Font
import pandas as pd
import CommonFunctions
import CloudOperations
import DbOperations

Redshift = DbOperations.Database
IamRole = DbOperations.IamRole
Region = DbOperations.Region


class Type1:
    @staticmethod
    def write_formula(df):
        count = 5
        write_data_list = []
        for index, row in df.iterrows():
            """calculation cell values"""
            if row['Source'] == 'Call in + Walk in' or row['Source'] == 'Referral' \
                    or row['Source'] == 'Web':
                transform_table_data = [row['Month'],
                                        row['Source'],
                                        row['Leads'],
                                        row['Leads (MQL) / Leads'],
                                        f'=D{count}*E{count}',
                                        row['Contacted / Leads (MQL)'],
                                        f'=G{count}*F{count}',
                                        row['Booked / Contacted'],
                                        f'=I{count}*H{count}',
                                        row['Shows / Booked'],
                                        f'=K{count}*J{count}',
                                        row['Closing Rate'],
                                        f'=IFERROR(L{count}/O{count},"")',
                                        f'=M{count}*L{count}',
                                        f'=IFERROR(O{count}/D{count},"")',
                                        f'=IFERROR(D{count}/O{count},"")',
                                        row['Average  Price'],
                                        f'=R{count}*O{count}',
                                        None,
                                        None,
                                        None,
                                        None,
                                        None,
                                        None,
                                        None,
                                        None
                                        ]
                write_data_list.append(transform_table_data)
            if row['Source'] == 'Subtotal - First Contracts':
                transform_table_data = [row['Month'],
                                        row['Source'],
                                        f'=SUM(D{count - 3}:D{count - 1})',
                                        f'=F{count}/D{count}',
                                        f'=SUM(F{count - 3}:F{count - 1})',
                                        f'=H{count}/D{count}',
                                        f'=SUM(H{count - 3}:H{count - 1})',
                                        f'=J{count}/H{count}',
                                        f'=SUM(J{count - 3}:J{count - 1})',
                                        f'=L{count}/J{count}',
                                        f'=SUM(L{count - 3}:L{count - 1})',
                                        f'=O{count}/L{count}',
                                        f'=L{count}/O{count}',
                                        f'=SUM(O{count - 3}:O{count - 1})',
                                        f'=O{count}/D{count}',
                                        f'=D{count}/O{count}',
                                        f'=SUM(S{count - 1},S{count - 2},S{count - 3})/SUM(O{count - 3}:O{count - 1})',
                                        f'=R{count}*O{count}',
                                        None,
                                        None,
                                        None,
                                        None,
                                        None,
                                        None,
                                        None,
                                        None]
                write_data_list.append(transform_table_data)
            if row['Source'] == 'Renewal':
                transform_table_data = [row['Month'],
                                        row['Source'],
                                        row['Leads'],
                                        row['Leads (MQL) / Leads'],
                                        f'=D{count}*E{count}',
                                        row['Contacted / Leads (MQL)'],
                                        f'=G{count}*F{count}',
                                        row['Booked / Contacted'],
                                        f'=I{count}*H{count}',
                                        row['Shows / Booked'],
                                        f'=K{count}*J{count}',
                                        row['Closing Rate'],
                                        f'=L{count}/O{count}',
                                        f'=L{count}*M{count}',
                                        f'=O{count}/L{count}',
                                        f'=D{count}/O{count}',
                                        row['Average  Price'],
                                        f'=R{count}*O{count}',
                                        None,
                                        None,
                                        None,
                                        None,
                                        None,
                                        None,
                                        None,
                                        None]
                write_data_list.append(transform_table_data)
            if row['Source'] == 'TOTAL':
                transform_table_data = [row['Month'],
                                        row['Source'],
                                        f'=SUM(D{count - 1},D{count - 2})',
                                        f'=F{count}/D{count}',
                                        f'=SUM(F{count - 2}:F{count - 1})',
                                        f'=H{count}/D{count}',
                                        f'=SUM(H{count - 1},H{count - 2})',
                                        f'=J{count}/H{count}',
                                        f'=SUM(J{count - 1},J{count - 2})',
                                        f'=L{count}/J{count}',
                                        f'=SUM(L{count - 1},L{count - 2})',
                                        f'=O{count}/L{count}',
                                        f'=L{count}/O{count}',
                                        f'=SUM(O{count - 2}:O{count - 1})',
                                        f'=O{count}/D{count}',
                                        f'=D{count}/O{count}',
                                        f'=SUM(S{count - 5}:S{count - 3},S{count - 1})/O{count}',
                                        f'=R{count}*O{count}',
                                        row['Private Target'],
                                        f'=(S{count}-T{count})/S{count}',
                                        row['B2B Target'],
                                        f"=VLOOKUP(MONTH(B{count}),'Calendar'!$AH$5:$AI$27,2,0)",
                                        f'=J{count}/$W{count}',
                                        f'=L{count}/$W{count}',
                                        f'=O{count}/$W{count}',
                                        f'=S{count}/$W{count}'
                                        ]
                write_data_list.append(transform_table_data)
            count += 1
        print(write_data_list)
        return write_data_list

    @staticmethod
    def planning_tool_color_fill(workbook, sheet_name, table_name):
        sheet = workbook[sheet_name]
        table = sheet.tables[table_name]
        start_row = table.ref.split(':')[0].replace('$', '')
        end_row = table.ref.split(':')[1].replace('$', '')
        end_row_number = int(end_row[2:])
        start_row_number = int(start_row[1:]) + 1
        column_index = start_row_number
        white_fill = openpyxl.styles.PatternFill(start_color="FFFFFF", end_color="FFFFFF", patternType='solid')
        blue_fill = openpyxl.styles.PatternFill(start_color="a0c0e0", end_color="a0c0e0", patternType='solid')
        gray_fill = openpyxl.styles.PatternFill(start_color="E3E6E6", end_color="E3E6E6", patternType='solid')
        dark_gray_fill = openpyxl.styles.PatternFill(start_color="C0C0C0", end_color="C0C0C0", patternType='solid')
        loop_count = 0
        line = 1
        for row in sheet.iter_rows(min_row=start_row_number, max_row=end_row_number, min_col=2):
            print(row)
            for cell in row:
                print(cell)
                print("its line print", line)
                print(loop_count)
                print(column_index)
                if line > 6:
                    print("yes cell gray")
                    cell.fill = gray_fill
                    if loop_count == 26:
                        column_index += 1
                        loop_count = 0
                    else:
                        loop_count += 1
                if line == 1:
                    if f"B{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"C{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"D{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"E{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"F{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"G{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"H{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"I{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"J{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"K{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"L{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"M{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"N{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"O{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"P{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"Q{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"R{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"S{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"T{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"U{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"V{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"W{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"X{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Y{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Z{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"AA{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if loop_count == 26:
                        column_index += 1
                        loop_count = 0
                        if line < 6:
                            line += 1
                if line == 2:
                    if f"B{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"C{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"D{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"E{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"F{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"G{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"H{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"I{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"J{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"K{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"L{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"M{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"N{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"O{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"P{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"Q{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"R{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"S{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"T{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"U{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"V{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"W{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"X{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Y{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Z{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"AA{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if loop_count == 26:
                        column_index += 1
                        loop_count = 0
                        if line < 6:
                            line += 1
                if line == 3:
                    if f"B{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"C{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"D{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"E{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"F{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"G{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"H{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"I{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"J{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"K{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"L{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"M{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"N{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"O{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"P{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"Q{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"R{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"S{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"T{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"U{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"V{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"W{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"X{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Y{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Z{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"AA{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if loop_count == 26:
                        column_index += 1
                        loop_count = 0
                        if line < 6:
                            line += 1
                if line == 4:
                    if f"B{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"C{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"D{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"E{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"F{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"G{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"H{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"I{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"J{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"K{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"L{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"M{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"N{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"O{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"P{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"Q{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"R{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"S{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"T{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"U{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"V{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"W{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"X{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Y{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Z{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"AA{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if loop_count == 26:
                        column_index += 1
                        loop_count = 0
                        if line < 6:
                            line += 1
                if line == 5:
                    if f"B{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"C{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"D{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"E{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"F{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"G{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"H{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"I{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"J{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"K{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"L{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"M{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"N{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"O{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"P{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"Q{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"R{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"S{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"T{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"U{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"V{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"W{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"X{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Y{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Z{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"AA{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if loop_count == 26:
                        column_index += 1
                        loop_count = 0
                        if line < 6:
                            line += 1
                if line == 6:
                    if f"B{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"C{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"D{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"E{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"F{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"G{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"H{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"I{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"J{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"K{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"L{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"M{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"N{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"O{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"P{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"Q{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"R{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"S{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"T{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"U{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"V{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = blue_fill
                        loop_count += 1
                    if f"W{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"X{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Y{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Z{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"AA{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if loop_count == 26:
                        column_index += 1
                        loop_count = 0
                        line += 1
        # hide the previous month data
        for row in range(11, end_row_number + 1):
            sheet.row_dimensions[row].hidden = True
        return workbook

    @staticmethod
    def planning_tool_table_border(workbook, sheet_name, table_name):
        sheet = workbook[sheet_name]
        table_range = sheet.tables[table_name].ref
        print(table_range)
        thin_border = Border(left=Side(style='thin'),
                             right=Side(style='thin'))
        thick_border = Border(left=Side(style='thin'),
                              right=Side(style='thin'),
                              top=Side(style='thin'),
                              bottom=Side(style='thin'))
        Length_check = []
        for row in sheet[table_range]:
            for cell in row:
                cell.border = thin_border
            Length_check.append(row)
        Length_check.pop(0)
        for individual_row in range(5, len(Length_check), 6):
            print("this row dark", Length_check[individual_row])
            for cell in Length_check[individual_row]:
                cell.border = thick_border
                # Create a bold font object
                bold_font = Font(bold=True)
                # Apply the bold font to cell
                sheet[cell.coordinate].font = bold_font
        return workbook

    @staticmethod
    def guidance_color_fill(workbook, sheet_name, table_name):
        sheet = workbook[sheet_name]
        table = sheet.tables[table_name]
        start_row = table.ref.split(':')[0].replace('$', '')
        end_row = table.ref.split(':')[1].replace('$', '')
        end_row_number = int(end_row[2:])
        print("check", end_row_number)
        start_row_number = int(start_row[1:]) + 1
        column_index = start_row_number
        white_fill = openpyxl.styles.PatternFill(start_color="FFFFFF", end_color="FFFFFF", patternType='solid')
        gray_fill = openpyxl.styles.PatternFill(start_color="E3E6E6", end_color="E3E6E6", patternType='solid')
        dark_gray_fill = openpyxl.styles.PatternFill(start_color="C0C0C0", end_color="C0C0C0", patternType='solid')
        loop_count = 0
        line = 1
        for row in sheet.iter_rows(min_row=start_row_number, max_row=end_row_number, min_col=2):
            print(row)
            for cell in row:
                print(cell)
                print("its line print", line)
                print(loop_count)
                print(column_index)
                # if line > 6:  # confirm with team
                #     print("yes cell gray")
                #     cell.fill = gray_fill
                #     if loop_count == 26:
                #         column_index += 1
                #         loop_count = 0
                #     else:
                #         loop_count += 1
                if line == 1:
                    if f"B{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"C{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"D{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"E{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"F{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"G{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"H{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"I{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"J{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"K{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"L{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"M{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"N{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"O{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"P{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"Q{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"R{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"S{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"T{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"U{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"V{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"W{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"X{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Y{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Z{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"AA{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if loop_count == 26:
                        column_index += 1
                        loop_count = 0
                        if line < 6:
                            line += 1
                if line == 2:
                    if f"B{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"C{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"D{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"E{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"F{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"G{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"H{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"I{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"J{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"K{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"L{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"M{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"N{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"O{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"P{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"Q{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"R{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"S{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"T{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"U{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"V{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"W{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"X{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Y{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Z{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"AA{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if loop_count == 26:
                        column_index += 1
                        loop_count = 0
                        if line < 6:
                            line += 1
                if line == 3:
                    if f"B{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"C{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"D{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"E{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"F{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"G{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"H{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"I{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"J{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"K{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"L{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"M{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"N{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"O{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"P{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"Q{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"R{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"S{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"T{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"U{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"V{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"W{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"X{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Y{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Z{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"AA{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if loop_count == 26:
                        column_index += 1
                        loop_count = 0
                        if line < 6:
                            line += 1
                if line == 4:
                    if f"B{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"C{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"D{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"E{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"F{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"G{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"H{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"I{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"J{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"K{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"L{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"M{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"N{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"O{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"P{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"Q{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"R{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"S{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        # Create a bold font object
                        bold_font = Font(bold=True)
                        # Apply the bold font to cell
                        sheet[cell.coordinate].font = bold_font
                        loop_count += 1
                    if f"T{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"U{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"V{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"W{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"X{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Y{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Z{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"AA{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if loop_count == 26:
                        column_index += 1
                        loop_count = 0
                        if line < 6:
                            line += 1
                if line == 5:
                    if f"B{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"C{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"D{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"E{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"F{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"G{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"H{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"I{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"J{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"K{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"L{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"M{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"N{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"O{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"P{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"Q{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"R{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"S{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"T{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"U{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"V{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"W{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"X{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Y{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Z{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"AA{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if loop_count == 26:
                        column_index += 1
                        loop_count = 0
                        if line < 6:
                            line += 1
                if line == 6:
                    if f"B{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"C{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = white_fill
                        loop_count += 1
                    if f"D{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"E{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"F{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"G{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"H{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"I{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"J{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"K{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"L{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"M{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"N{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"O{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"P{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"Q{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"R{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"S{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"T{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"U{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"V{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = gray_fill
                        loop_count += 1
                    if f"W{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"X{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Y{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"Z{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if f"AA{column_index}" in cell.coordinate:
                        print(cell.coordinate)
                        cell.fill = dark_gray_fill
                        loop_count += 1
                    if loop_count == 26:
                        column_index += 1
                        loop_count = 0
                        line += 1
        return workbook

    @staticmethod
    def guidance_table_border(workbook, sheet_name, table_name):
        sheet = workbook[sheet_name]
        table_range = sheet.tables[table_name].ref
        print(table_range)
        thin_border = Border(left=Side(style='thin'),
                             right=Side(style='thin'))
        thick_border = Border(left=Side(style='thin'),
                              right=Side(style='thin'),
                              top=Side(style='thin'),
                              bottom=Side(style='thin'))
        Length_check = []
        for row in sheet[table_range]:
            for cell in row:
                cell.border = thin_border
            Length_check.append(row)
        Length_check.pop(0)
        for individual_row in range(5, len(Length_check), 6):
            print("this row dark", Length_check[individual_row])
            for cell in Length_check[individual_row]:
                cell.border = thick_border
                # Create a bold font object
                bold_font = Font(bold=True)
                # Apply the bold font to cell
                sheet[cell.coordinate].font = bold_font
        return workbook

    @staticmethod
    def cell_protection(workbook):
        consultant_sheet = workbook['Consultant Targets']
        table = consultant_sheet.tables['consultant_targets']
        end_row = table.ref.split(':')[1].replace('$', '')
        input_list = [{"sheet": "Planning Tool", "range": "D5:E7"},
                      {"sheet": "Planning Tool", "range": "D9:E9"},
                      {"sheet": "Planning Tool", "range": "G5:G7"},
                      {"sheet": "Planning Tool", "range": "G9"},
                      {"sheet": "Planning Tool", "range": "I5:I7"},
                      {"sheet": "Planning Tool", "range": "I9"},
                      {"sheet": "Planning Tool", "range": "K5:K7"},
                      {"sheet": "Planning Tool", "range": "K9"},
                      {"sheet": "Planning Tool", "range": "M5:M7"},
                      {"sheet": "Planning Tool", "range": "M9"},
                      {"sheet": "Planning Tool", "range": "R5:R7"},
                      {"sheet": "Planning Tool", "range": "R9"},
                      {"sheet": "Planning Tool", "range": "T10"},
                      {"sheet": "Planning Tool", "range": "V10"},
                      {"sheet": "Calendar", "range": "AL5"},
                      {"sheet": "Calendar", "range": "AL7"},
                      {"sheet": "Calendar", "range": "AL9"},
                      {"sheet": "Calendar", "range": "AL11"},
                      {"sheet": "Calendar", "range": "AL13"},
                      {"sheet": "Calendar", "range": "AL15"},
                      {"sheet": "Calendar", "range": "AL17"},
                      {"sheet": "Calendar", "range": "AO5:AP5"},
                      {"sheet": "Calendar", "range": "AO7:AP7"},
                      {"sheet": "Calendar", "range": "AO9:AP9"},
                      {"sheet": "Calendar", "range": "AO11:AP11"},
                      {"sheet": "Calendar", "range": "AO13:AP13"},
                      {"sheet": "Calendar", "range": "AO15:AP15"},
                      {"sheet": "Calendar", "range": "AO17:AP17"},
                      {"sheet": "Calendar", "range": "AO19:AP19"},
                      {"sheet": "Calendar", "range": "AO21:AP21"},
                      {"sheet": "Calendar", "range": "AO23:AP23"},
                      {"sheet": "Calendar", "range": "AO25:AP25"},
                      {"sheet": "Calendar", "range": "AO27:AP27"},
                      {"sheet": "Calendar", "range": "AO29:AP29"},
                      {"sheet": "Calendar", "range": "AO31:AP31"},
                      {"sheet": "Calendar", "range": "AO33:AP33"},
                      {"sheet": "Calendar", "range": "AO35:AP35"},
                      {"sheet": "Calendar", "range": "AO37:AP37"},
                      {"sheet": "Calendar", "range": "AO39:AP39"},
                      {"sheet": "Calendar", "range": "AO41:AP41"},
                      {"sheet": "Calendar", "range": "AO43:AP43"},
                      {"sheet": "Consultant Targets", "range": f"B5:{end_row}"}]
        for i in input_list:
            sheet = workbook[i['sheet']]
            # select the cell range to unlock
            print("range_length", len(i['range']))
            if len(i['range']) < 5:
                cell = i['range']
                print("single cell", cell)
                lock_style = Protection(locked=False, hidden=False)
                sheet[cell].protection = lock_style
            else:
                cell_range = sheet[i['range']]
                print("cell_range", cell_range)
                print("cell_range_length", len(cell_range))
                # unlock the cells in the selected range
                for row in cell_range:
                    print("row", row)
                    for cell in row:
                        print("cel", cell)
                        lock_style = Protection(locked=False, hidden=False)
                        cell.protection = lock_style
        return workbook

    @staticmethod
    def consultants_target_data(df):
        write_data_list = []
        for index, row in df.iterrows():
            """calculation cell values"""
            transform_table_data = list(row)
            write_data_list.append(transform_table_data)
        print(write_data_list)
        return write_data_list

    @staticmethod
    def consultants_target_color_fill(workbook, sheet_name, table_name):
        sheet = workbook[sheet_name]
        table = sheet.tables[table_name]
        start_row = table.ref.split(':')[0].replace('$', '')
        end_row = table.ref.split(':')[1].replace('$', '')
        end_row_number = int(end_row[1:])
        print("check", end_row_number)
        start_row_number = int(start_row[1:]) + 1
        print(start_row_number)
        column_index = start_row_number
        loop_count = 0
        blue_fill = openpyxl.styles.PatternFill(start_color="a0c0e0", end_color="a0c0e0", patternType='solid')
        for row in sheet.iter_rows(min_row=start_row_number, max_row=end_row_number, min_col=2):
            print("color check", row)
            for cell in row:
                print(cell)
                if f"B{column_index}" in cell.coordinate:
                    print(cell.coordinate)
                    cell.fill = blue_fill
                    loop_count += 1
                if f"C{column_index}" in cell.coordinate:
                    print(cell.coordinate)
                    cell.fill = blue_fill
                    loop_count += 1
                if f"D{column_index}" in cell.coordinate:
                    print(cell.coordinate)
                    cell.fill = blue_fill
                    loop_count += 1
                if loop_count == 3:
                    column_index += 1
                    loop_count = 0
        return workbook

    @staticmethod
    def write_process(file_path, test_write_path, folder, Territory, TerritoryReferenceId, ReferenceCenterId):
        """sheet 1"""
        # sample data instead of redshift
        sheet_name_1 = "Planning Tool"
        sheet_name_2 = "Guidance"
        sheet_name_3 = "Consultant Targets"
        table_name_1 = "planning_tool"
        table_name_2 = "guidance"
        table_name_3 = "consultant_targets"
        # wb = load_workbook(filename='WriteData/planning_tool_source_file.xlsx', data_only=True)
        # sheet_list = wb.sheetnames
        # name = sheet_list[0]
        # sheet_ranges = wb[name]
        # df = pd.DataFrame(sheet_ranges.values)
        # new_header = df.iloc[0]
        # df = df[1:]
        # df.columns = new_header
        Query1 = """select     Month	,
                Source	,
                Leads	,
                "Leads (MQL) / Leads"	,
                "Leads (MQL)",
                "Contacted / Leads (MQL)"	,
                Contacted	,
                "Booked / Contacted"	,
                "Booked"	,
                "Shows / Booked"	,
                "Shows"	,
                "Closing Rate",
                "Closing Ratio  Shows"	,
                Contracts	,
                "Contracts / Leads"	,
                "Closing Ratio  Leads"	,
                "Average  Price"	,
                Sales	,
                "Private Target"	,
                "% vs Plan"	,
                "B2B Target"	,
                "Working days"	,
                "Daily booked"	,
                "Daily Shows"	,
                "Daily contracts"	,
                "Daily Sales"
                from swrve.planningtool
                where territoryreferenceid = '{}'
                  and referencecenterid = '{}'
                order by rownumber;""".format(TerritoryReferenceId, ReferenceCenterId)
        ExecuteQuery1 = Redshift.Execution(ExecutionType="ReadTable",
                                           Query=Query1,
                                           StatementName=f"{Territory}")
        print(ExecuteQuery1)
        df = pd.DataFrame(ExecuteQuery1)
        df.columns = [
            'Month',
            'Source',
            'Leads',
            'Leads (MQL) / Leads',
            'Leads (MQL)',
            'Contacted / Leads (MQL)',
            'Contacted',
            'Booked / Contacted',
            'Booked',
            'Shows / Booked',
            'Shows',
            'Closing Rate',
            'Closing Ratio  Shows',
            'Contracts',
            'Contracts / Leads',
            'Closing Ratio  Leads',
            'Average  Price',
            'Sales',
            'Private Target',
            '% vs Plan',
            'B2B Target',
            'Working days',
            'Daily booked',
            'Daily Shows',
            'Daily contracts',
            'Daily Sales'
        ]
        # convert column 'Month' to date dtype
        df['Month'] = pd.to_datetime(df['Month'])
        # convert column 'Leads' to integer dtype
        df['Leads'] = df['Leads'].astype('int')
        # convert column 'Private Target' to int dtype for non-empty string values
        df['Private Target'] = df['Private Target'].apply(lambda x: int(x) if isinstance(x, str) and x != '' else x)
        # convert column 'Average  Price' to integer dtype
        df['Average  Price'] = df['Average  Price'].astype('float')
        # convert column 'Leads (MQL) / Leads' to float dtype
        df['Leads (MQL) / Leads'] = df['Leads (MQL) / Leads'].astype('float')
        # convert column 'Contacted / Leads (MQL)' to float dtype
        df['Contacted / Leads (MQL)'] = df['Contacted / Leads (MQL)'].astype('float')
        # convert column 'Booked / Contacted' to float dtype
        df['Booked / Contacted'] = df['Booked / Contacted'].astype('float')
        # convert column 'Shows / Booked' to float dtype
        df['Shows / Booked'] = df['Shows / Booked'].astype('float')
        # convert column 'Closing Rate' to float dtype
        df['Closing Rate'] = df['Closing Rate'].astype('float')
        print(df)
        planning_tool_calculations = Type1.write_formula(df)
        sheet_data_mode = CommonFunctions.Common.sheet_data_mode(file_path)
        planning_tool_write_workbook = CommonFunctions.Common.write_rows(sheet_data_mode, sheet_name_1, table_name_1,
                                                                         planning_tool_calculations)
        planning_tool_color_fill = Type1.planning_tool_color_fill(planning_tool_write_workbook, sheet_name_1,
                                                                  table_name_1)
        planning_tool_border = Type1.planning_tool_table_border(planning_tool_color_fill, sheet_name_1, table_name_1)
        planning_tool_number_format = CommonFunctions.Common.number_format(planning_tool_border, sheet_name_1,
                                                                           table_name_1)
        """sheet 2"""
        # sample data instead of redshift
        guidance_calculations = Type1.write_formula(df)
        guidance_write_workbook = CommonFunctions.Common.write_rows(planning_tool_number_format, sheet_name_2,
                                                                    table_name_2,
                                                                    guidance_calculations)
        guidance_color_fill = Type1.guidance_color_fill(guidance_write_workbook, sheet_name_2, table_name_2)
        guidance_border = Type1.guidance_table_border(guidance_color_fill, sheet_name_2, table_name_2)
        guidance_number_format = CommonFunctions.Common.number_format(guidance_border, sheet_name_2, table_name_2)
        """sheet 3"""
        # # sample data instead of redshift
        # sheet_ranges = wb[sheet_name_3]
        # df2 = pd.DataFrame(sheet_ranges.values)
        # new_header = df2.iloc[1]
        # df2 = df2[1:]
        # df2.columns = new_header
        # print(df2)
        """sheet 3 Consultant Targets"""
        Query2 = """select Name,
                            Target,
                            Notes
                        from swrve.consultanttargets
                        where territoryreferenceid = '{}'
                          and referencecenterid = '{}'
                        order by rownumber;""".format(TerritoryReferenceId, ReferenceCenterId)
        ExecuteQuery2 = Redshift.Execution(ExecutionType="ReadTable",
                                           Query=Query2,
                                           StatementName=f"{Territory}")
        print(ExecuteQuery2)
        df2 = pd.DataFrame(ExecuteQuery2)
        df2.columns = ['Name', 'Target', 'Notes']
        print(df2)
        truncate_table = CommonFunctions.Common.truncate_table(guidance_number_format, sheet_name_3, table_name_3)
        consultants_target_data = Type1.consultants_target_data(df2)
        consultants_target_write_workbook = CommonFunctions.Common.write_rows(truncate_table, sheet_name_3,
                                                                              table_name_3,
                                                                              consultants_target_data)
        consultants_target_color_fill = Type1.consultants_target_color_fill(consultants_target_write_workbook,
                                                                            sheet_name_3, table_name_3)
        cell_protection = Type1.cell_protection(consultants_target_color_fill)
        CommonFunctions.Common.write_workbook(cell_protection, test_write_path, folder)
        return "Type1 write operation completed"
