version: 2

sources:
  - name: stage_onboarding_service
    description: >
      Source data from the Onboarding Service which manages user onboarding processes
      and tracks completion of onboarding steps.
    database: awsdatacatalog
    schema: stg_onboarding_service
    tables:
      - name: user_onboards
        description: Records of user onboarding processes including steps, completion status, and timestamps.
        columns:
          - name: _id
            description: Primary key for the onboarding record
          - name: featuretype
            description: Type of feature being onboarded
          - name: iscompleted
            description: Boolean flag indicating if the onboarding is completed
          - name: isskipped
            description: Boolean flag indicating if the onboarding was skipped
          - name: userreferenceid
            description: Reference ID for the user being onboarded
          - name: __v
            description: Version number for the document
          - name: steps
            description: JSON array containing steps in the onboarding process
          - name: createdat
            description: ISO timestamp when the onboarding record was created
          - name: updatedat
            description: ISO timestamp when the onboarding record was last updated