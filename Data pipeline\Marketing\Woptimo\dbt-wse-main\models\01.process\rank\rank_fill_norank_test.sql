{{
    config(
        tags=["incremental","rank","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}
with 
tab_all as (
    select
    distinct query,event_date,language_name,location_name
    from {{ ref('rank_init_test') }}
    where {{increment()}}
)

-- set 100 if wse has no ranking

select 
query,event_date,language_name,location_name,

ifnull(rk.rank_organic,100) rank_organic,
rk.is_featured_snippet,
ifnull(rk.page,"(no ranking)") page,
ifnull(rk.snippet_title,"(no ranking)") snippet_title,
ifnull(rk.snippet_description,"(no ranking)") snippet_description,
ifnull(rk.snippet_breadcrumb,"(no ranking)") snippet_breadcrumb,
ifnull(rk.all_domains,"(no ranking)") all_domains,
ifnull(rk.all_pages,"(no ranking)") all_pages,

ifnull(comp.ef,100) compet_rank_ef,
ifnull(comp.italki,100) compet_rank_italki,
ifnull(comp.babbel,100) compet_rank_babbel,
ifnull(comp.amazingtalker,100) compet_rank_amazingtalker,
ifnull(comp.berlitz_corporation	,100) compet_rank_berlitz_corporation,
ifnull(comp.rosetta_stone	,100) compet_rank_rosetta_stone,
ifnull(comp.british_council	,100) compet_rank_british_council,



from tab_all ta
left join {{ ref('rank_wse_test') }} rk using (query,event_date,language_name,location_name)
left join {{ ref('rank_competition_pivot_test') }} comp using (query,event_date,language_name,location_name)