{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH ods_data AS (
    SELECT {{etl_load_date()}}, * FROM {{ ref(
            'ods_snb_scheduled_class'
        ) }}
)

SELECT {{etl_load_date()}},
    scheduledclass.id AS id,
    scheduledclass.center_reference_id AS center_reference_id,
    start_time,
    {{convert_to_local_timestamp("start_time","timezoneid")}} AS local_start_time,
    end_time,
    {{convert_to_local_timestamp("end_time","timezoneid")}} AS local_end_time,
    clstyp.title AS class_type,
    number_of_seats,
    no_of_seats_in_stand_by,
    scheduledclass.description AS "description",
    teacher_id,
    category_from_booking,
    is_cancelled,
    CASE
        WHEN communication_account_type = -1 THEN 'none'
        WHEN communication_account_type = 0 THEN 'webex'
        WHEN communication_account_type = 1 THEN 'tokbox'
        WHEN communication_account_type = 2 THEN 'zoom'
        WHEN communication_account_type = 3 THEN 'dc'
        WHEN communication_account_type = 4 THEN 'tencent' else cast(communication_account_type AS varchar)
    END AS communication_account_type,
    source,
    created,
    {{convert_to_local_timestamp("created","timezoneid")}} AS local_created,
    last_updated,
    {{convert_to_local_timestamp("last_updated","timezoneid")}} AS local_last_updated,
    created_by,
    rolecreated.description AS created_by_role,
    last_updated_by,
    rolelast_updated.description AS last_updated_by_role
FROM ods_data AS scheduledclass
    LEFT JOIN (
        SELECT id,
            title
        FROM {{ref("ods_snb_class_type")}}
    ) clstyp on scheduledclass.class_type_id = clstyp.id
    LEFT JOIN (
        SELECT  user.id AS id,
        role_id
        FROM {{ref("ods_snb_user")}} as user
    ) usrcreated on scheduledclass.created_by = usrcreated.id
    LEFT JOIN (
        SELECT  id,
            description
        FROM {{ref("ods_snb_role")}} 
    ) rolecreated on usrcreated.role_id = rolecreated.id
    LEFT JOIN (
        SELECT  user.id AS id,
            role_id
        FROM {{ref("ods_snb_user")}} as user
    ) usrlast_updated on scheduledclass.last_updated_by = usrlast_updated.id
    LEFT JOIN (
        SELECT  id,
            description
        FROM {{ref("ods_snb_role")}}
    ) rolelast_updated on usrlast_updated.role_id = rolelast_updated.id
    LEFT JOIN (
        SELECT  center_reference_id,
            time_zone_id as timezoneid
        FROM {{ref("ods_cc_center")}}
    ) tz on scheduledclass.center_reference_id = tz.center_reference_id