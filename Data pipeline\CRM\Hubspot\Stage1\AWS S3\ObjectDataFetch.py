import logging
import datetime
import TimeConversion
import Authentication
import DataFetchFormatting
import PostApiCall



today_date = datetime.date.today()
OauthConnect = Authentication.Oauth
DataFormat = DataFetchFormatting.DataFormating
APICall = PostApiCall


class ObjectFetch:
    @staticmethod
    def FiterFetch(TerritoryCode, FilterProperty, IncrementalLoadDate):
        # Define the FilterGroup based on TerritoryCode
        if TerritoryCode == 'CH':
            FilterGroup = [{"filters": [{"propertyName": FilterProperty, "operator": "GT",
                                         "value": IncrementalLoadDate},
                                        {"propertyName": "center_name", "operator": "IN",
                                         "values": ["WSE Bienne", "WSE Fribourg", "WSE Genève",
                                                    "WSE Juniors Fribourg",
                                                    "WSE Juniors Genève",
                                                    "WSE Juniors Lausanne",
                                                    "WSE Juniors Montreux",
                                                    "WSE La Chaux-de-Fonds",
                                                    "WSE Lausanne",
                                                    "WSE Lugano",
                                                    "WSE Montreux",
                                                    "WSE Neuchâtel",
                                                    "WSE Online",
                                                    "WSE Praha"]}
                                        ]}]
            return FilterGroup
        else:
            FilterGroup = [{"filters": [{"propertyName": FilterProperty, "operator": "GT",
                                         "value": IncrementalLoadDate}]}]
            return FilterGroup

    @staticmethod
    def object_incremental(ConfigInfo, Bucket, Properties, Defaultproperties, CycleId, Object):
        PropertiesStructure = {}
        DataExtract = []
        PropertiesVariable = Properties
        DefaultVariables = Defaultproperties
        Defaultproperties = PropertiesVariable + DefaultVariables
        logging.warning("The default properties are :'%s'", format(Defaultproperties))

        # Forming a default properties with value None initially
        for SingleProperty in Defaultproperties:
            PropertiesStructure[SingleProperty] = ''

        # Extracting the config info data to variables
        DataRequestUrl = ConfigInfo['Url']
        FilterProperty = ConfigInfo['Filter']
        logging.warning(FilterProperty)
        CutOffDate = ConfigInfo['CutoffDate']
        Operation = ConfigInfo['Operation']
        IncrementalLoadDate = TimeConversion.TimeFormat(CutOffDate)
        logging.warning("The incremental date is : '%s'", format(IncrementalLoadDate))

        # Extracting the Filter groups value from FilterFetch function
        FilterGroups = ObjectFetch.FiterFetch(ConfigInfo['Territory'], FilterProperty, IncrementalLoadDate)

        # Generating access token based on territories
        AccessToken = OauthConnect.authenticate(ConfigInfo['Territory'])
        DefaultInputJson = {"after": "0", "limit":"100", "properties": Properties,
                            "sorts": [{"propertyName": FilterProperty, "direction": "ASCENDING"}],
                            "filterGroups": FilterGroups
                            }
        Headers = {'accept': 'application/json', 'Authorization': 'Bearer ' + AccessToken}

        DataResponse = APICall.ApiCall(DataRequestUrl=DataRequestUrl, DefaultInputJson=DefaultInputJson,
                                       Headers=Headers, Object=Object, QueryString=None, Operation=Operation)
        logging.warning(DataResponse)
        # Check if DataResponse contains an error due to expired authentication
        if 'status' in DataResponse:
            logging.warning("Entering the 1st status")
            if DataResponse['status'] == 'error' and DataResponse['category'] == 'EXPIRED_AUTHENTICATION':
                AccessToken = OauthConnect.authenticate(ConfigInfo['Territory'])
                Headers = {'accept': 'application/json', 'Authorization': 'Bearer ' + AccessToken}
                DataResponse = DataFormat.AccessTokenRegenerate(Headers=Headers,
                                                                                DefaultInputJson=DefaultInputJson, APICall=APICall,
                                                                                DataRequestUrl=DataRequestUrl, DataExtract=DataExtract,
                                                                                DataFormat=DataFormat, FilterProperty=FilterProperty, Object=Object, Operation=Operation)
        if 'results' in DataResponse:
            TotalRecord = len(DataResponse['results'])

            if TotalRecord == 0:
                logging.warning("no incremental data")
            if TotalRecord != 0:

                if 'results' in DataResponse:
                    DataFetchFormat = DataFormat.DataFormation(DataResponse, ConfigInfo['Territory'],
                                                               Defaultproperties, PropertiesStructure, CycleId)
                    DataExtract.extend(DataFetchFormat)
                while 'paging' in DataResponse:
                    while 'paging' in DataResponse and int(DataResponse["paging"]["next"]["after"]) < int(10000):
                        DefaultInputJson["after"] = DataResponse["paging"]["next"]["after"]
                        DataResponse = APICall.ApiCall(DataRequestUrl=DataRequestUrl, DefaultInputJson=DefaultInputJson,
                                                       Headers=Headers, Object=Object, QueryString=None, Operation=Operation)

                        if 'status' in DataResponse:
                            if DataResponse['status'] == 'error' and DataResponse['category'] =='EXPIRED_AUTHENTICATION':
                                AccessToken = OauthConnect.authenticate(ConfigInfo['Territory'])
                                Headers = {'accept': 'application/json', 'Authorization': 'Bearer ' + AccessToken}
                                DataResponse = DataFormat.AccessTokenRegenerate(Headers=Headers,
                                                                                DefaultInputJson=DefaultInputJson, APICall=APICall,
                                                                                DataRequestUrl=DataRequestUrl, DataExtract=DataExtract,
                                                                                DataFormat=DataFormat, FilterProperty=FilterProperty, Object=Object, Operation=Operation)
                                logging.warning( "printing the Expiry Loop Data : '%s'", format(DataResponse))
                            else:
                                logging.warning("The response status is:'%s' ", format(DataResponse['status']))
                        if 'results' in DataResponse:
                            DataFetchFormat = DataFormat.DataFormation(DataResponse, ConfigInfo['Territory'],
                                                                       Defaultproperties,
                                                                       PropertiesStructure, CycleId)
                            DataExtract.extend(DataFetchFormat)
                    if 'paging' in DataResponse and int(DataResponse["paging"]["next"]["after"]) == int(10000):
                        DefaultInputJson["after"] = 0
                        IncrementalDefaultJson = DataFormat.IncrementalDateExtract(DataExtract, DefaultInputJson,
                                                                                   FilterProperty)
                        DataResponse = APICall.ApiCall(DataRequestUrl=DataRequestUrl,
                                                       DefaultInputJson=IncrementalDefaultJson, Headers=Headers,
                                                       Object=Object, QueryString=None, Operation=Operation)
                        logging.warning("Entering if paging")
                        if 'status' in DataResponse:
                            if DataResponse['status'] == 'error' and DataResponse['category'] == 'EXPIRED_AUTHENTICATION':
                                AccessToken = OauthConnect.authenticate(ConfigInfo['Territory'])
                                Headers = {'accept': 'application/json', 'Authorization': 'Bearer ' + AccessToken}
                                DataResponse = DataFormat.AccessTokenRegenerate(Headers=Headers,
                                                                                DefaultInputJson=DefaultInputJson, APICall=APICall,
                                                                                DataRequestUrl=DataRequestUrl, DataExtract=DataExtract,
                                                                                DataFormat=DataFormat, FilterProperty=FilterProperty, Object=Object, Operation=Operation)
                            else:
                                logging.warning("Dup ", DataResponse['status'])
                        if 'results' in DataResponse:
                            DataFetchFormat = DataFormat.DataFormation(DataResponse, ConfigInfo['Territory'],
                                                                       Defaultproperties,
                                                                       PropertiesStructure, CycleId)
                            DataExtract.extend(DataFetchFormat)
                    else:
                        logging.warning("There is no paging exit the loop")
                logging.warning("Exiting the data fetch operation")
        return DataExtract
