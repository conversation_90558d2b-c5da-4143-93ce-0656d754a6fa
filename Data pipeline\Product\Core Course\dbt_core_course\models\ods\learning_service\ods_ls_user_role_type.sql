{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('createddate') }} as createddate,
        {{ cast_to_timestamp('updateddate') }} as updateddate,
        isactive,
        id,
        name,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                updateddate DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'userroletype'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    createddate as created_date,
    updateddate as updated_date,
    isactive as is_active,
    id,
    name
FROM
    rankedrecords
WHERE
    rn = 1;
