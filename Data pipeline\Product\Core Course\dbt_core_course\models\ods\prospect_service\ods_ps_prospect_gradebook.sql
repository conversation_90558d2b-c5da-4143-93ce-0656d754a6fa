{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{cast_to_timestamp('testcompletedon')}} as testcompletedon,
        istimeout,
        {{cast_to_timestamp('teststartedon')}} as teststartedon,
        timeremaining,
        {{ cast_to_timestamp('created') }} as created,
        createdby,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        lastupdatedby,
        nextactivityid,
        id,
        status,
        prospectid,
        scoremode,
        settledlevel,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_prospect_service',
            'prospectgradebook'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    testcompletedon as test_completed_on,
    istimeout as is_time_out,
    teststartedon as test_started_on,
    timeremaining as time_remaining,
    created,
    createdby as created_by,
    lastupdated as last_updated,
    lastupdatedby as last_updated_by,
    nextactivityid as next_activity_id,
    id,
    status,
    prospectid as prospect_id,
    scoremode as score_mode,
    settledlevel as settled_level
FROM
    rankedrecords
WHERE
    rn = 1;
