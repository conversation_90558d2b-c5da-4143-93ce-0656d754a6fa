{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

{% if not var('start_date', none) %}
  {{ exceptions.raise_compiler_error("'start_date' variable is required but not provided.") }}
{% endif %}

{% if not var('end_date', none) %}
  {{ exceptions.raise_compiler_error("'end_date' variable is required but not provided.") }}
{% endif %}

{% if not var('event_names', none) %}
  {{ exceptions.raise_compiler_error("'event_names' variable is required but not provided.") }}
{% endif %}

{% set start_date = var('start_date') %}
{% set end_date = var('end_date') %}
{% set event_names = var('event_names') %}

SELECT 
  PARSE_DATE('%Y%m%d', _TABLE_SUFFIX) AS event_date,
  event_timestamp,
  event_name,
  user_id,
  event_params,
  user_properties
FROM `core-course-bigquery.analytics_358724658.events_intraday_*`
WHERE 
  PARSE_DATE('%Y%m%d', _TABLE_SUFFIX) BETWEEN DATE('{{ start_date }}') AND DATE('{{ end_date }}')
  AND event_name IN UNNEST({{ event_names }})
