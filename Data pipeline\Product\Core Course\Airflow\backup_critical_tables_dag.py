import logging
import os
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.bash import BashOperator
from airflow.operators.python import PythonOperator
from airflow.operators.dummy import DummyOperator
from dependencies import cloud_operations
from dependencies.slack_alerts import task_failure_callback, task_success_callback

default_args = {
    'owner': 'wse_data_team',
    'start_date': datetime(2025, 1, 1),
    'retries': 1,
    'retry_delay': timedelta(minutes=5)
}

dag = DAG(
    'backup_critical_tables',
    default_args=default_args,
    schedule_interval=None,
    catchup=False,
    max_active_tasks=15,  # Limit parallel execution to 15 tasks
    tags=['core-course', 'backup']
)

# Define backup configurations
s3 = cloud_operations.S3

backup_configs = s3.read_json_file(bucket="prod-corecourse", file_path="config/critical_tables_backup_config.json")

# Get dbt project path
HOME = os.environ["HOME"]
dbt_path = os.path.join(HOME, "dbt/dbt_core_course")

start = DummyOperator(task_id='start', dag=dag)

backup_tasks = []
for config in backup_configs:
    task_id = f"backup_{config['schema']}_{config['table']}"
    
    backup_task = BashOperator(
        task_id=task_id,
        bash_command=(
            "cd /home/<USER>/dbt"
            " && source dbt-venv/bin/activate"
            f" && cd {dbt_path}"
            f" && dbt run-operation backup_table --args '{{\"schema_name\": \"{config['schema']}\", \"table_name\": \"{config['table']}\"}}'"
        ),
        on_failure_callback=task_failure_callback,
        dag=dag
    )
    backup_tasks.append(backup_task)

# Define a checkpoint task to mark completion
checkpoint = PythonOperator(
    task_id='backup_completion_checkpoint',
    python_callable=lambda: logging.info("All backup tasks completed successfully"),
    on_failure_callback=task_failure_callback,
    on_success_callback=task_success_callback,
    dag=dag
)

# Set task dependencies
start >> backup_tasks >> checkpoint

