{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        contractid,
        mastercontractid,
        state,
        signatureid,
        documentid,
        documenttype,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
        reason,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'contractvalidations')}}
)

SELECT
    {{etl_load_date()}},
    id,
    contractid as contract_id,
    mastercontractid as master_contract_id,
    state,
    signatureid as signature_id,
    documentid as document_id,
    documenttype as document_type,
    {{cast_to_timestamp('createddate')}} as created_date,
    {{cast_to_timestamp('lastupdateddate')}} as last_updated_date,
    reason
FROM 
    RankedRecords
WHERE 
    rn = 1;