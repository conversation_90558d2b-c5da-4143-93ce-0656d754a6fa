{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_roles'
        ) }}
)

SELECT {{etl_load_date()}},
    id,
    lowered_role_name,
    description,
    role_code,
    user_type
from ods_data