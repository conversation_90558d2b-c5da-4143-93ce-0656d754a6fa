from dependencies.cloud_operations import Redshift
from dependencies.cloud_operations import SecretManager
from dependencies.cloud_operations import logging

redshift = Redshift
redshift_connect = redshift.connect()
"""reading the redshift credential from secret key management services and assigning to variables"""
secret_instance = SecretManager
db_details = secret_instance.get_secret('redshift_develop', 'eu-west-1')
cluster_Identifier, db_database, db_user, iam_role, region = \
    [db_details[key] for key in ('cluster_identifier', 'database', 'db_user', 'iam_role', 'region')]


class Database:
    @staticmethod
    def execution(execution_type, query, statement_name):
        try:

            """Step 1 Execute query"""

            execute_response = redshift_connect.execute_statement(
                ClusterIdentifier=cluster_Identifier,
                Database=db_database,
                DbUser=db_user,
                Sql=query,
                StatementName=statement_name,
                WithEvent=False)
            query_id = execute_response['Id']

            """Step 2 Check query execution Status"""

            describe_query_status = 'Started' """setting default value"""
            describe_response = '' """setting default value"""

            while (describe_query_status != 'FINISHED') & (describe_query_status != 'FAILED') & \
                    (describe_query_status != 'ABORTED'):
                describe_response = redshift_connect.describe_statement(Id=query_id)
                describe_query_status = str(describe_response['Status'])
            if describe_query_status == 'FAILED' or describe_query_status == 'ABORTED':
                logging.warning("raised exception in execution due to describe_query_status %s",
                                format(describe_query_status))
                raise Exception
            if execution_type == "WriteTable":
                return describe_response

            if execution_type == "ReadTable":

                """Step 3 Get query execution Result"""

                table_data = []
                table_header = []
                response_list = []

                table_response = redshift_connect.get_statement_result(Id=query_id)
                table_data.extend(table_response['Records'])
                response_header = table_response['ColumnMetadata']
                while 'NextToken' in table_response:
                    table_response = redshift_connect.get_statement_result(Id=query_id,
                                                                         NextToken=table_response['NextToken'])
                    table_data.extend(table_response['Records'])

                for column_header in response_header:
                    header = column_header['name']
                    table_header.append(header)

                for content in table_data:
                    list_length = len(content)
                    transformed_dict = {}
                    index_value = 0

                    for iterate in range(list_length):
                        logging.warning(iterate)
                        info = {table_header[index_value]: list(content[index_value].values())[0]}
                        transformed_dict.update(info)
                        index_value += 1
                    response_list.append(transformed_dict)
                return response_list

        except Exception as error_message:
            logging.warning("raised exception in execution due to %s", format(error_message))
            raise Exception
