CREATE EXTERNAL TABLE `analytics.page_navigation_events`(
  `event_date` date 
  `event_timestamp` bigint 
  `event_name` string 
  `user_id` string 
  `user_identifier` string 
  `screen` string 
  `role` string 
  `gb_variant` string 
  `variant` string 
  `experiment` string 
  `etl_load_date` string 
  )
ROW FORMAT SERDE 'org.openx.data.jsonserde.JsonSerDe'
STORED AS TEXTFILE
LOCATION 's3://dev-corecourse/data/bigquery_page_navigation_events';


CREATE EXTERNAL TABLE `analytics.cue_card_events`(
  `event_date` date  , 
  `event_timestamp` bigint , 
  `event_name` string , 
  `user_id` string, 
  `duration` DOUBLE, 
  `using_suggestion` string,
  `class_type` string, 
  `user_role` string, 
  `class_id` string)
ROW FORMAT SERDE 'org.openx.data.jsonserde.JsonSerDe'
STORED AS TEXTFILE
LOCATION 's3://dev-corecourse/data/bigquery_cue_card_events';

CREATE EXTERNAL TABLE analytics.camera_usage_events
(
  event_date Date,
  event_timestamp bigint,
  event_name STRING,
  user_id STRING, 
  platform STRING, 
  user_level STRING,
  user_origin_level STRING,
  user_center STRING,
  user_role STRING,
  class_id STRING,
  class_access STRING,
  camera_usage_time bigint
)
ROW FORMAT SERDE 'org.openx.data.jsonserde.JsonSerDe'
STORED AS TEXTFILE
LOCATION 's3://prod-corecourse/data/bigquery_camera_usage_events/';