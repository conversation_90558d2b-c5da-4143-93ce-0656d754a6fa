{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ps_prospect_gradebook') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT 
    {{etl_load_date()}},
    test_completed_on,
    is_time_out,
    test_started_on,
    time_remaining,
    created,
    created_by,
    last_updated,
    last_updated_by,
    next_activity_id,
    id,
    CASE 
          WHEN status = 0 THEN 'initialized'
          WHEN status = 1 THEN 'in_progress'
          WHEN status = 2 THEN 'final'
          ELSE NULL
        END as status,
    prospect_id,
    score_mode,
    settled_level
from
    ods_data as prospectgradebook
