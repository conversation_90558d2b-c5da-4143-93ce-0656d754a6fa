{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_snb_cls_typ_config_for_srvc_typ') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT {{etl_load_date()}},
    service_type,
    duration,
    duration_fixed,
    max_number_of_students,
    max_number_of_studentsfixed,
    created,
    last_updated,
    id,
    class_type_id
FROM
    ods_data as class_type_configuration_for_service_type
