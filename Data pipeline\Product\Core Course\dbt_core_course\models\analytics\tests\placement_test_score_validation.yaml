version: 2

models:
  - name: placement_test_score
    columns:
      - name: level_number
        tests:
          - accepted_values:
              values: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
              quote: false
              severity: warn
      - name: activity_title
        tests:
          - not_null:
              severity: error
      - name: activity_duration
        tests:
          - not_null:
              severity: error
      - name: test_row_number
        tests:
          - not_null:
              severity: warn
      - name: level_is_active
        tests:
          - accepted_values:
              values: ['false', 'true']
              quote: false
              severity: warn
      - name: activity_is_active
        tests:
          - accepted_values:
              values: ['false', 'true']
              quote: false
              severity: warn
      - name: level_id
        tests:
          - not_null:
              severity: error
      - name: content_id
        tests:
          - not_null:
              severity: error
      - name: activity_id
        tests:
          - not_null:
              severity: error
