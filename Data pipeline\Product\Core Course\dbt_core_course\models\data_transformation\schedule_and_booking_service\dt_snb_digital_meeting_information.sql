{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_snb_digital_meeting_information') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT {{etl_load_date()}},
    created,
    last_updated,
    id,
    scheduled_class_id,
    host_url,
    participant_url,
    vendor_id,
    password
FROM
    ods_data as digital_meeting_information
