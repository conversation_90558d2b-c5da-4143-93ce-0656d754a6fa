{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        id, 
        prospectid, 
        reason,
        level, 
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_prospect_service',
            'offlinetestresults'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    id, 
    prospectid as prospect_id, 
    reason,
    level, 
    created,
    lastupdated as last_updated
FROM
    rankedrecords
WHERE
    rn = 1;
