create table wse_seo_reporting.data_mart_page_test
(
    lang                              varchar(256),
    hostname                          varchar(256),
    page_path                         varchar(65535),
    page                              varchar(65535),
    event_date                        date encode az64,
    pageviews                         varchar(256),
    pageviews_organic                 varchar(256),
    pageviews_ggorganic               varchar(256),
    sessions                          varchar(256),
    sessions_organic                  varchar(256),
    sessions_ggorganic                varchar(256),
    conv_onlp_ggorganic_form_contact  varchar(256),
    conv_session_organic_form_contact varchar(256),
    conv_session_allsrc_form_contact  varchar(256),
    conv_onlp_allsrc_form_contact     varchar(256),
    conv_onlp_ggorganic_form_test     varchar(256),
    conv_session_organic_form_test    varchar(256),
    conv_session_allsrc_form_test     varchar(256),
    conv_onlp_allsrc_form_test        varchar(256),
    conv_onlp_ggorganic_click_tel     varchar(256),
    conv_session_organic_click_tel    varchar(256),
    conv_session_allsrc_click_tel     varchar(256),
    conv_onlp_allsrc_click_tel        varchar(256),
    conv_onlp_ggorganic_click_wa      varchar(256),
    conv_session_organic_click_wa     varchar(256),
    conv_session_allsrc_click_wa      varchar(256),
    conv_onlp_allsrc_click_wa         varchar(256),
    clicks                            bigint encode az64,
    impressions                       bigint encode az64,
    impressions_branded               bigint encode az64,
    clicks_branded                    bigint encode az64,
    impressions_local                 bigint encode az64,
    clicks_local                      bigint encode az64,
    page_title                        varchar(256),
    page_title_length_pixel           varchar(256),
    status_code                       varchar(256),
    meta_description                  varchar(1000),
    conversions_onlp_ggorganic        bigint encode az64,
    conversions_session_organic       bigint encode az64,
    conversions_session_allsrc        bigint encode az64,
    conversions_onlp_allsrc           bigint encode az64,
    page_type                         varchar(256)
);