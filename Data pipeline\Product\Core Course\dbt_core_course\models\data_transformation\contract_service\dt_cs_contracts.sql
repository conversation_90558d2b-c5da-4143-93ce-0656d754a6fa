{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_contracts'
        ) }}

    {% if is_incremental() %}
        where last_updated_date > ((select max(last_updated_date) from {{ this }}))
    {% endif %}
)


SELECT {{etl_load_date()}}, 
    contract.id as id,
    code,
    number,
    crm_contract_number,
    ptype.name as producttype,
    stype.name as service_type,
    student_id,
    center_id,
    start_date,
    end_date,
    plevelstart."order" as start_level,
    plevelend."order" as end_level,
    price,
    cstates.name as state,
    cstatus.name as status,
    consultant_id,
    lab_teacher_id,
    refunded,
    is_renewed,
    is_promotional,
    is_transfer_in,
    locations.name as location,
    created_date,
    {{convert_to_local_timestamp('created_date','time_zone_id')}} as local_created_date,
    last_updated_date,
    {{convert_to_local_timestamp('last_updated_date','time_zone_id')}}as local_last_updated_date,
    ctypes.name as contract_type,
    contract_reference_id,
    company_id,
    group_id,
    is_cross_center_booking,
    csource_type.name as source_type,
    sale_date,
    cancel_date,
    total_number_of_hours,
    master_contract_course_id,
    cvalidationstate.name as current_validation_state,
    is_membership,
    max_no_of_cc_and_sc_classes,
    is_teen
FROM ods_data as Contract
    left join (
        select id,
            name
        from {{ ref('ods_cs_product_types') }}
    ) as ptype on contract.product_type_id = ptype.id
    left join (
        select id,
            name
        from {{ ref('ods_cs_service_types') }}
    ) as stype on contract.service_type = stype.id
    left join (
        select id,
            name
        from {{ ref('ods_cs_contract_source_types') }}
    ) as csource_type on contract.source_type = csource_type.id
    left join (
        select id,
            "order"
        from {{ ref('ods_cs_product_levels') }}
    ) as plevelstart on contract.start_level_id = plevelstart.id
    left join (
        select id,
            "order"
        from {{ ref('ods_cs_product_levels') }}
    ) as plevelend on contract.end_level_id = plevelend.id
    left join (
        select id,
            name
        from {{ ref('ods_cs_contract_states') }}
    ) as cstates on contract.state = cstates.id
    left join (
        select id,
            name
        from {{ ref('ods_cs_contract_statuses') }}
    ) as cstatus on contract.status = cstatus.id
    left join (
        select id,
            name
        from {{ ref('ods_cs_locations') }}
    ) as locations on contract.location = locations.id
    left join (
        select id,
            name
        from {{ ref('ods_cs_contract_types') }}
    ) as ctypes on contract.contract_type = ctypes.id
    left join (
        select id,
            name
        from {{ ref('ods_cs_current_validation_state') }}
    ) as cvalidationstate on contract.current_validation_state = cvalidationstate.id
    left join (
        select id,
            center_reference_id 
        from {{ ref('ods_cs_centers') }}
    ) as center on center.id = contract.center_id
    left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on center.center_reference_id = tz.center_reference_id