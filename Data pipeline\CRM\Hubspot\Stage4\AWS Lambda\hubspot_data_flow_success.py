# Import necessary modules
import boto3
import json
import logging
import os




def lambda_handler(event, context):
    # Define the name of the S3 bucket
    bucket = os.environ.get('bucket')

    # Create a Boto3 resource for S3
    boto3_resource = boto3.resource("s3")

    # Access the specific bucket using the Boto3 resource
    s3_bucket = boto3_resource.Bucket(bucket)

    # Define the path to the execution check file in S3
    execution_check_file_path = boto3_resource.Object(bucket, 'ExecutionCheck.json')

    # Retrieve the content of the execution check file
    execution_check_file_content = execution_check_file_path.get()['Body'].read().decode('utf-8')

    # Parse the JSON content of the execution check file
    execution_check = json.loads(execution_check_file_content)

    # Print the parsed execution check data
    logging.warning(execution_check)
    
    execution_status = execution_check['Status']
    
    if execution_status == 'Pass':
        logging.warning("The Hubspot Execution has been completed proceeding for other pipelines")
    else:
        raise Exception