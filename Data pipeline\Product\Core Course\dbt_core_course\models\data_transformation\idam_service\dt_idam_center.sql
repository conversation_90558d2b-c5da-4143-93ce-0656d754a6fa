{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_idam_center') }}
)
SELECT {{etl_load_date()}},
    id,
    center_reference_id,
    Name,
    territory_id,
    center_code,
    student_code
from
    ods_data as center
