{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        name,
        "order",
        producttypeid,
        studytypeid
    FROM 
        {{source('stage_contract_service', 'productlevels')}}
)

SELECT 
    {{etl_load_date()}},
    id,
    name,
    "order",
    producttypeid as product_type_id,
    studytypeid as study_type_id
FROM 
    RankedRecords