{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'dbt_unique_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_bookmark') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT {{etl_load_date()}},
    dbt_unique_id,
    registration_id,
    content_item_type_id,
    content_item_id,
    contitemtype.name as content_item_type,
    contitem.description as content_item,
    center_id,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated
from
    ods_data as bookmark
    Left Join (
        select
            id,
            description
        from
            {{ ref('ods_ls_content_item') }}
    ) as contitem
    ON bookmark.content_item_id = contitem.id
    Left Join (
        select
            id,
            name
        from
            {{ ref('ods_ls_content_item_type') }}
    ) as contitemtype
    ON bookmark.content_item_type_id = contitemtype.id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = bookmark.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
