{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='dbt_unique_id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH ods_data AS (
    SELECT
        ods.dbt_unique_id,
        ods.etl_load_date as ods_etl_load_date,
        ods.event_date,
        ods.event_timestamp AS raw_event_timestamp,
        CAST(from_unixtime(ods.event_timestamp / 1000000) AS timestamp(6)) AS event_timestamp,
        ods.event_name,
        ods.user_id,
        ods.event_params,
        ods.user_properties
    FROM {{ ref('ods_ga4_events') }} ods
    {% if is_incremental() %}
    where
        etl_load_date > (
            (
                select
                    max(ods_etl_load_date)
                from
                    {{ this }}
            )
        )
    {% endif %}
)

SELECT
    {{ etl_load_date() }},
    dbt_unique_id,
    ods_etl_load_date,
    event_date,
    raw_event_timestamp,
    event_timestamp,
    CASE
        WHEN center.time_zone_id IS NOT NULL 
        THEN {{ convert_to_local_timestamp('event_timestamp', 'center.time_zone_id') }}
        ELSE event_timestamp
    END AS local_event_timestamp,
    event_name,
    user_id,
    event_params,
    user_properties
FROM ods_data
LEFT JOIN (
    SELECT ssds_id, center_id
    FROM {{ ref('ods_idam_user_basic_info') }}
) AS user ON ods_data.user_id = user.ssds_id
LEFT JOIN (
    SELECT id, time_zone_id
    FROM {{ ref('ods_cc_center') }}
) AS center ON user.center_id = center.id