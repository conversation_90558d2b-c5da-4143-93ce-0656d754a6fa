{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('classstartdate') }} as classstartdate,
        contentitemresulttypeid,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        showbannerflag,
        id,
        unitid,
        registrationid,
        teacherid,
        referenceclassid,
        studentid,
        classid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'encounterresultaggregate'
        ) }}
)
SELECT
    {{etl_load_date()}},
    classstartdate as class_start_date,
    contentitemresulttypeid as content_item_result_type_id,
    created,
    lastupdated as last_updated,
    showbannerflag as show_banner_flag,
    id,
    unitid as unit_id,
    registrationid as registration_id,
    teacherid as teacher_id,
    referenceclassid as reference_class_id,
    studentid as student_id,
    classid as class_id
FROM
    rankedrecords
WHERE
    rn = 1;
