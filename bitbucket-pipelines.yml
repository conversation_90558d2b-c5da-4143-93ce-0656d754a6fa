image: atlassian/default-image:3

definitions:
  scripts:
    - script: &pre-build-script
        |
        apt-get update
        apt-get install -y curl sshpass
    
  steps:
    - step: &deployment_to_prod
        script:
          - *pre-build-script
          - echo "$ec2_password" | sshpass -p "$ec2_password" ssh -o StrictHostKeyChecking=no  ${ec2_user}@${ec2_ip} "sudo -S su -c 'cd /home/<USER>/deploy && ./deploy-script.sh'"

pipelines:
  branches:
    develop: # This specifies that the following steps should be run whenever there's a push to the master branch.
      - step: *deployment_to_prod