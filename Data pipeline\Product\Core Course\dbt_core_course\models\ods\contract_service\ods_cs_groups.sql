{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        name,
        location,
        centerid,
        companyid,
        hasaddresssameascompany,
        country,
        state,
        city,
        addressline1,
        addressline2,
        postalcode,
        startlevelid,
        endlevelid,
        {{cast_to_timestamp('startdate')}} as startdate,
        {{cast_to_timestamp('enddate')}} as enddate,
        producttypeid,
        createdbyid,
        modifiedbyid,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
        grouprefid,
        istransferin,
        {{cast_to_int('transferstatus')}},
        b2bcontractnumber,
        totalnumberofhours,
        mastercontractcourseid,
        ismembership,
        maxnoofccandscclasses,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'groups')}}
)

SELECT
    {{etl_load_date()}},
    id,
    name,
    location as location,
    centerid as center_id,
    companyid as company_id,
    hasaddresssameascompany as has_address_same_as_company,
    country,
    state,
    city,
    addressline1 as address_line1,
    addressline2 as address_line2,
    postalcode as postal_code,
    startlevelid as start_level_id,
    endlevelid as end_level_id,
    startdate as start_date,
    enddate as end_date,
    producttypeid as product_type_id,
    createdbyid as created_by_id,
    modifiedbyid as modified_by_id,
    createddate as created_date,
    lastupdateddate as last_updated_date,
    grouprefid as group_ref_id,
    istransferin as is_transfer_in,
    transferstatus as transfer_status,
    b2bcontractnumber as b2_b_contract_number,
    totalnumberofhours as total_number_of_hours,
    mastercontractcourseid as master_contract_course_id,
    ismembership as is_membership,
    maxnoofccandscclasses as max_no_of_cc_and_sc_classes
FROM 
    RankedRecords
WHERE 
    rn = 1;