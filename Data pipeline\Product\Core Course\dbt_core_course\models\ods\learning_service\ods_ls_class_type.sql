{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        isdeleted,
        code,
        isonline,
        hasdescription,
        acceptsstandby,
        hastobeprebooked,
        categorytype,
        deluxeduration,
        deluxedurationfixed,
        deluxemaxnumberofstudents,
        deluxemaxnumberofstudentsfixed,
        vipconfiguration,
        vipduration,
        vipdurationfixed,
        vipmaxnumberofstudents,
        vipmaxnumberofstudentsfixed,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        color,
        title,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'classtype'
        ) }}
)
SELECT
    {{etl_load_date()}},
    isdeleted as is_deleted,
    code,
    isonline as is_online,
    hasdescription as has_description,
    acceptsstandby as accepts_standby,
    hastobeprebooked as has_to_be_pre_booked,
    categorytype as category_type,
    deluxeduration as deluxe_duration,
    deluxeduration as deluxe_durationfixed,
    deluxemaxnumberofstudents as deluxe_max_number_of_students,
    deluxemaxnumberofstudents as deluxe_max_number_of_studentsfixed,
    vipconfiguration as vip_configuration,
    vipduration as vip_duration,
    vipduration as vip_durationfixed,
    vipmaxnumberofstudents as vip_max_number_of_students,
    vipmaxnumberofstudents as vip_max_number_of_studentsfixed,
    created,
    lastupdated as last_updated,
    id,
    color,
    title
FROM
    rankedrecords
WHERE
    rn = 1;
