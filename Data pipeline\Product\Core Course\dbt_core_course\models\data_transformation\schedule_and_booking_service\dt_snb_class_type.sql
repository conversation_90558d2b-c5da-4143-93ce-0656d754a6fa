{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_snb_class_type') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT {{etl_load_date()}},
    code,
    location,
    category_type,
    has_description,
    accepts_standby,
    has_to_be_pre_booked,
    is_active,
    created,
    last_updated,
    id,
    title,
    color,
    course_id
FROM
    ods_data as class_type
