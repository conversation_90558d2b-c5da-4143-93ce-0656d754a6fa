{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'dbt_unique_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_center_products'
        ) }}
)

SELECT {{etl_load_date()}}, 
    dbt_unique_id,
    Center_Id,
    Product_Id
FROM ods_data as tempTable