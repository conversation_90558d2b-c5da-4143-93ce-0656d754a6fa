{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_dsw_activity_progress'
        ) }}

    {% if is_incremental() %}
        where last_updated > ((select max(last_updated) from {{ this }}))
    {% endif %}
)

SELECT
    {{etl_load_date()}}
    ,activity_progress.id
    ,course_contents.level as course_contents_level
    ,course_contents.unit as course_contents_unit
    ,course_contents.lesson as course_contents_lesson
    ,activity
    ,activity_url
    ,no_of_attempts
    ,is_completed
    ,duration
    ,CASE 
            when category_type = 1 Then 'communication'
            when category_type = 2 Then 'vocabulary'
            when category_type = 3 then 'grammar'
            ELSE CAST(category_type as varchar)
        end as category_type
    ,score
    ,is_active
    ,activity_reference_id
    ,activity_progress.student_id
    ,created 
    ,{{convert_to_local_timestamp('created','time_zone_id')}} as local_created
    ,last_updated 
    ,{{convert_to_local_timestamp('last_updated','time_zone_id')}} as local_last_updated
    ,CASE 
            when study_mode = 0 Then 'online'
            when study_mode = 1 Then 'incenter'
            when study_mode = 3 then 'mobile'
            ELSE CAST(study_mode as varchar)
        end as study_mode
    FROM 
    ods_data as activity_progress
        Left Join (
                select id,
                    level,
                    unit,
                    lesson
                from {{ref('ods_dsw_course_contents')}}
        ) AS course_contents ON course_contents.id = activity_progress.course_content_id
        Left Join (
                select ssds_id,
                    center_id
                from {{ref('ods_ls_user')}}
        ) AS user ON user.ssds_id = activity_progress.student_id
        Left Join(
                select id,
                    reference_center_id
                from {{ref('ods_ls_center')}}
        ) as center ON user.center_id = center.id
        Left Join (
                select center_reference_id,
                    time_zone_id
                from {{ ref ('ods_cc_center') }}
    ) as tz on center.reference_center_id = tz.center_reference_id