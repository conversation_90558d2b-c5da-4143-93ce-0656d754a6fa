
class MyanmarVietnamStage3Queries:
    @staticmethod
    def stage3_queries(cycle_id, Object):
        query_creator = {
            "contacts": """
        select 
        AB.id,
        AB.createdate,
        AB.lastmodifieddate,
        AB.channel,
        AB.source,
        AB.center_name,
        AB.hubspot_owner_id,
        AB.hs_lifecyclestage_lead_date,
        AB.hs_lifecyclestage_marketingqualifiedlead_date,
        AB.booked_date,
        AB.territory_code,
        AB.sub_source_level_1,
        AB.sub_source_level_2,
        AB.sub_source_level_3,
        AB.campaign,
        from_utc_timestamp(AB.createdate,tz.timezone) as timezonecreatedate,
        tz.country_name as territory_name
        from (
        select cast(cmmvn.contact_ui as bigint) as id,
        cast(cmmvn.create_date as timestamp) as createdate,
        cast(cmmvn.last_modified_date as timestamp) as lastmodifieddate,
        cmmvn.channel,
        cmmvn.source,
        cmmvn.center as center_name,
        cast(cmmvn.owner as bigint) as hubspot_owner_id,
        cast(cmmvn.become_lead_date as timestamp) as hs_lifecyclestage_lead_date,
        cast(cmmvn.become_marketing_qualified_lead_date as timestamp) as hs_lifecyclestage_marketingqualifiedlead_date,
        cast(cmmvn.contact_first_booked_date as date) as booked_date,
       case WHEN  cmmvn.business_unit='WSEMM' THEN 'MM'
           WHEN cmmvn.business_unit='WSEVN' THEN 'VN' end as territory_code,
    cmmvn.sub_source_level_1,cmmvn.sub_source_level_2,cmmvn.sub_source_level_3,cmmvn.campaign
       from hubspot.contacts_mmvn cmmvn
where cmmvn.contact_ui not in (SELECT deleted_contact_ui from hubspot.deletedrecords_mmvn where deleted_contact_ui is NOT NULL or deleted_contact_ui!='')
and cmmvn.cycleid = '{}') AB
join hubspot.hubspotterritorytimezone tz
on AB.territory_code=tz.iso_code;
        """.format(cycle_id),
            "associationdealtocontacts": """
        select cast(ammvn.deal_ui as bigint) as hs_object_id,
        cast(ammvn.contact_ui as bigint) as associatedid ,
        'deal_to_contact' as associationtype,
case when cmmvn.business_unit='WSEMM' then 'MM'
     when cmmvn.business_unit='WSEVN' then 'VN'
     else '' end
as territory_code,
cast( cmmvn.create_date as timestamp) as createdate,
cast(cmmvn.last_modified_date as timestamp) as hs_lastmodifieddate,
cast(ammvn.cycleid as bigint) as cycleid 
from hubspot.association_mmvn  ammvn
left join hubspot.contacts_mmvn cmmvn
on ammvn.contact_ui=cmmvn.contact_ui
where is_associated='1'
and ammvn.cycleid = '{}'
        """.format(cycle_id),
            "associationcontacttodeals": """
                select cast(ammvn.contact_ui as bigint) as contactid,
                cast(ammvn.deal_ui as bigint) as associatedid ,
                 'contact_to_deal' as associationtype,
        case when cmmvn.business_unit='WSEMM' then 'MM'
             when cmmvn.business_unit='WSEVN' then 'VN'
             else '' end
        as territory_code, 
        cast(cmmvn.create_date as timestamp) as createdate,
        cast(cmmvn.last_modified_date as timestamp) as updatedate,
        cast(ammvn.cycleid as bigint) as cycleid
         from hubspot.association_mmvn  ammvn
        left join hubspot.contacts_mmvn cmmvn
        on ammvn.contact_ui=cmmvn.contact_ui
        where is_associated='1'
        and ammvn.cycleid = '{}'
                """.format(cycle_id),
            "deals": """
        select
        AB.id,
        AB.createdate,
        AB.channel,
        AB.center_name,
        AB.hubspot_owner_id,
        AB.amount,
        AB.closedate,
        AB.territory_code,
        AB.pipeline,
        AB.termination_type,
        AB.termination_status,
        AB.termination_date,
        AB.refunded_amount,
        tz.country_name as territory_name,
        from_utc_timestamp(AB.createdate,tz.timezone) as timezonecreatedate,
        from_utc_timestamp(AB.closedate,tz.timezone) as timezoneclosedate
        from
        (select cast(deal_ui as bigint) as id,
       cast(create_date as timestamp) as createdate ,
       channel,
       sales_center as center_name,
       cast(ec_code as string) as hubspot_owner_id,
       CASE when amount='NONE' then NULL else CAST(amount as float) end as amount,
       cast(contract_date as string) as closedate,
       CASE WHEN business_unit='WSEMM' then 'MM' WHEN business_unit='WSEVN' then 'VN' end as territory_code,
       pipeline,
       termination_type,
       termination_status,
       cast(termination_date as date) as termination_date,
       cast(refunded_amount as float) as refunded_amount
from hubspot.deals_MMVN
where deal_ui NOT IN (select deleted_deal_ui from hubspot.deletedrecords_mmvn where deleted_deal_ui is NOT NULL or deleted_deal_ui!='')
and deals_MMVN.cycleid='{}' ) AB
join hubspot.hubspotterritorytimezone tz
on AB.territory_code=tz.iso_code
""".format(cycle_id)}

        query_fetch = query_creator.get(Object)
        return query_fetch
