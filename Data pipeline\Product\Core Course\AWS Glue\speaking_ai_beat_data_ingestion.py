import sys
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark.context import SparkContext
from awsglue.context import GlueContext
from awsglue.job import Job
from pyspark.sql.functions import col, to_timestamp, date_format, explode_outer, lit, when
from pyspark.sql.functions import col, explode_outer, posexplode_outer, monotonically_increasing_id, row_number
from pyspark.sql.window import Window
from pyspark.sql.types import StructType, StructField, StringType, DoubleType, BooleanType, IntegerType, ArrayType, TimestampType
from datetime import datetime
import boto3
import json
import pymongo
import CloudOperations


def execute_query(athena, query_string, database):
    response = athena.start_query_execution(
        QueryString=query_string,
        QueryExecutionContext={'Database': database},
        ResultConfiguration={'OutputLocation': 's3://prod-corecourse/athena-results'}
    )

    while True:
        query_status = athena.get_query_execution(QueryExecutionId=response['QueryExecutionId'])
        state = query_status['QueryExecution']['Status']['State']
        if state in ['SUCCEEDED', 'FAILED', 'CANCELLED']:
            break

    if state == 'SUCCEEDED':
        result = athena.get_query_results(QueryExecutionId=response['QueryExecutionId'])
        if result['ResultSet']['Rows']:
            # Check if there are any rows in the result
            columns = [col['VarCharValue'] for col in result['ResultSet']['Rows'][1]['Data']]
            return columns[0] if columns else None
        else:
            # For queries like DROP and CREATE TABLE that don't return rows
            return "Query executed successfully"
    else:
        raise Exception(f"Query execution failed with state: {state}")


def max_date(args, max_query):
    athena = boto3.client('athena')
    table_exists_query = f"""
    SELECT CASE WHEN EXISTS (SELECT * FROM information_schema.tables WHERE table_schema = '{args['OdsDatabase']}' 
    AND table_name = '{args['OdsObject']}') THEN 'yes' ELSE 'no' END AS table_exists"""

    print(table_exists_query)

    table_check_result = execute_query(athena, table_exists_query, args['OdsDatabase'])
    print(table_check_result)

    if table_check_result != 'yes':
        return 'full_load'

    if table_check_result == 'yes' and args['LoadType'] == 'Incremental':
        max_date_query_result = execute_query(athena, max_query, args['OdsDatabase'])
        print(max_date_query_result)
        return max_date_query_result

    else:
        raise Exception("Query execution failed")


def data_processing(args, collection, batch_size, s3_output_path):
    if args['LoadType'] == 'Incremental':
        max_updated_date_query = f"""SELECT DATE_FORMAT(MAX({args['FilterColumn']}) - INTERVAL '1' DAY, '%Y-%m-%d %H:%i:%s.%f') 
        as {args['FilterColumn']} FROM {args['OdsDatabase']}.{args['OdsObject']}"""
        print("max query:", max_updated_date_query)

        cutoff_date_updated = max_date(args, max_updated_date_query)
        if cutoff_date_updated != 'full_load':
            last_export_date = datetime.strptime(cutoff_date_updated, "%Y-%m-%d %H:%M:%S.%f")
            query = {args['FilterColumn']: {"$gt": last_export_date}}
        else:
            query = {}
    else:
        query = {}

    print("MongoDB query:", query)

    all_documents = []
    total_documents = 0

    # Use skip and limit for pagination
    skip = 0
    while True:
        cursor = collection.find(query).sort([(args['FilterColumn'], 1), ("_id", 1)]).skip(skip).limit(batch_size)
        documents = list(cursor)

        if not documents:
            break

        # Process documents
        for doc in documents:
            doc['_id'] = str(doc['_id'])
            doc[args['FilterColumn']] = doc[args['FilterColumn']].isoformat()
            if mongo_collection == "prospect-speak-beta":
                if 'end_date' in doc and doc['end_date']:
                    doc['end_date'] = doc['end_date'].isoformat()

        all_documents.extend(documents)
        total_documents += len(documents)
        skip += len(documents)  # Increment skip by actual number of documents read

        print(f"Read batch of {len(documents)} documents, total so far: {total_documents}")

        if len(documents) < batch_size:
            # We've reached the end of the collection
            break

    print(f"Total documents read: {total_documents}")

    if total_documents > 0:
        print("going to write in s3")
        cleaned_documents = [
        {key: (value if value is not None else "") for key, value in doc.items()}
        for doc in all_documents
        ]
        # Create a Spark DataFrame from all documents
        spark_df = spark.createDataFrame(cleaned_documents)
        # spark_df.show()
        print("data frame created")
        # Convert string timestamps to proper TimestampType, handling null values
        spark_df = spark_df.withColumn(args['FilterColumn'], when(col(args['FilterColumn']).isNotNull(), to_timestamp(col(args['FilterColumn']))).otherwise(None))
        if mongo_collection == "prospect-speak-beta":
            spark_df = spark_df.withColumn("end_date", when(col("end_date").isNotNull(), to_timestamp(col("end_date"))).otherwise(None))

        # Storing all data in S3
        spark_df.repartition(1).write.mode("overwrite").json(s3_output_path)
        print(f"Written {total_documents} documents to S3")

        return all_documents[-1][args['FilterColumn']]
    else:
        print("No documents to write")
        return None


# Initialize the Glue job
args = getResolvedOptions(sys.argv,
                          ["JOB_NAME", "Object", "SecretManager", "FilterColumn", "LoadType", "DatabaseConnection",
                           "AthenaDatabase", "OdsObject", "OdsDatabase", "HistDatabase"])
sc = SparkContext()
glueContext = GlueContext(sc)
spark = glueContext.spark_session
job = Job(glueContext)
job.init(args['JOB_NAME'], args)

# Fetch database connection values from AWS Secret Manager
fetch_database_connection = CloudOperations.SecretManager()
database_connection_values = fetch_database_connection.GetSecret(args["SecretManager"], "eu-west-1")

# MongoDB's connection details
mongo_uri = database_connection_values['mongo_uri']
mongo_database = database_connection_values['database']
mongo_collection = args["Object"]


# Default query parameters
batch_size = 10000  # This controls how many documents are fetched from MongoDB in each iteration

s3_output_path = f"s3://prod-corecourse/landing/stg_speaking_ai_beta_service/{mongo_collection}/"

# Connect to MongoDB
client = pymongo.MongoClient(mongo_uri)
db = client[mongo_database]
collection = db[mongo_collection]

# Run the incremental export
last_export_date = data_processing(args, collection, batch_size, s3_output_path)

print(f"Last export date: {last_export_date}")

# Close the MongoDB connection
client.close()
job.commit()