{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'user_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_student') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT
    {{etl_load_date()}},
    student.user_id as user_id,
    student_id,
    tempis_new_student,
    nativelanguage.name as native_language,
    language.name as secondary_language,
    personalmotivation.value as personal_motivation,
    profession.name as profession,
    ge_id,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated,
    student_code,
    CASE
        When student.preferred_contact_method = 0 then 'mobile_telephone'
        When student.preferred_contact_method = 1 then 'home_telephone'
        When student.preferred_contact_method = 2 then 'work_telephone'
        When student.preferred_contact_method = 3 then 'email'
        When student.preferred_contact_method = 4 then 'socialnetwork'
        Else CAST(
            student.preferred_contact_method AS Varchar
        )
    end as preferred_contact_method,
    preferredsocialnetwork.name as preferred_social_network,
    privacy_policy_approve,
    prospect_id,
    is_first_mini_cycle_completed
from
    ods_data as student
    Left Join (
        select
            id,
            Name
        from
            {{ ref('ods_ls_language') }}
    ) as nativelanguage
    ON student.native_language_id = nativelanguage.id
    Left Join (
        select
            id,
            Name
        from
            {{ ref('ods_ls_language') }}
    ) as language
    ON student.secondary_language_id = language.id
    Left Join (
        select
            id,
            Value
        from
            {{ ref('ods_ls_motivation') }}
    ) as personalmotivation
    ON student.personal_motivation_id = personalmotivation.id
    Left Join (
        select
            id,
            Name
        from
            {{ ref('ods_ls_profession') }}
    ) as profession
    ON student.profession_id = profession.id
    Left Join (
        select
            id,
            Name
        from
            {{ ref('ods_ls_social_network') }}
    ) as preferredsocialnetwork
    ON student.preferred_social_network_id = preferredsocialnetwork.id
    Left Join (
        select
            user_id,
            center_id
        from
            {{ ref('ods_ls_user') }}
    ) as User
    on student.user_id = User.user_id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = User.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
