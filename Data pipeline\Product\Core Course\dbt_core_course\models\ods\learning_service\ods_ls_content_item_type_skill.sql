{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'dbt_unique_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        (
            cast(contentitemtypeid as varchar) || skillid
        ) as dbt_unique_id,
        contentitemtypeid,
        skillid,
        ROW_NUMBER() over (
            PARTITION BY contentitemtypeid,
            skillid
            ORDER BY
                contentitemtypeid,
                skillid
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'contentitemtypeskill'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    dbt_unique_id,
    contentitemtypeid as content_item_type_id,
    skillid as skill_id
FROM
    rankedrecords
WHERE
    rn = 1;
