{{
    config(
        tags=["incremental","rank","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}
with 
only_brand as (
select 
event_date,
query,
url_domain,
url,
SPLIT(url,"?")[safe_offset(0)] page,
rank_organic,
rank_featured_snippet,
language_name,
location_name,
snippet_title,
snippet_description,
snippet_breadcrumb,
from {{ ref('rank_init_test') }}
where 
{{increment()}} and
url_domain in 
(select distinct page_hostname from {{ ref('websites_init_test') }})
),
organized as (
select
url_domain,language_name,location_name,event_date,query,
page,
LEAST(IFNULL(rank_organic,100),IFNULL(rank_featured_snippet,100)) as rank_organic,
case when  rank_featured_snippet is not null then TRUE else FALSE END as is_featured_snippet,
snippet_title,
snippet_description,
snippet_breadcrumb,
from only_brand
)

select
language_name,location_name,event_date,query,
min(rank_organic) rank_organic,
max(is_featured_snippet) is_featured_snippet, --return TRUE if is_featured_snippet contains TRUE 
STRING_AGG(page order by rank_organic asc LIMIT 1) page, -- first page to rank
STRING_AGG(url_domain order by rank_organic asc LIMIT 1) url_domain, -- first page to rank
STRING_AGG(snippet_title order by rank_organic asc LIMIT 1) snippet_title, -- title of first page to rank
STRING_AGG(snippet_description order by rank_organic asc LIMIT 1) snippet_description, -- description of first page to rank
STRING_AGG(snippet_breadcrumb order by rank_organic asc LIMIT 1) snippet_breadcrumb, -- breadcumb of first page to rank

COUNT (DISTINCT page) nb_pages, -- number of pages to rank
COUNT (DISTINCT url_domain) nb_domains, -- number of pages to rank
STRING_AGG(page order by rank_organic asc) all_pages, -- first page to rank
STRING_AGG(url_domain order by rank_organic asc) all_domains, -- first page to rank
from organized
group by language_name,location_name,event_date,query