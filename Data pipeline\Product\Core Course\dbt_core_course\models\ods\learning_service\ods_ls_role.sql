{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        loweredrolename,
        description,
        rolecode,
        roletypeid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'role'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    created,
    lastupdated as last_updated,
    id,
    loweredrolename as lowered_role_name,
    description,
    rolecode as role_code,
    roletypeid as role_type_id
FROM
    rankedrecords
WHERE
    rn = 1;
