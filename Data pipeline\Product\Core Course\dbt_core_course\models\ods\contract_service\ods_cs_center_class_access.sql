{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
    id,
    centerid,
    territoryclassaccessid,
    isactive,
    {{cast_to_timestamp('created')}} as created,
    {{cast_to_timestamp('lastupdated')}} as lastupdated,
    ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdated DESC) as rn
    FROM 
        {{source('stage_contract_service', 'centerclassaccess')}}
)

SELECT
    {{etl_load_date()}},
    id,
    centerid as center_id,
    territoryclassaccessid as territory_class_access_id,
    isactive as is_active,
    created,
    lastupdated as last_updated
FROM 
    RankedRecords
WHERE 
    rn = 1;