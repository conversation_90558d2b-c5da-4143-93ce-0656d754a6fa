import json
association_data = []

ContactsData = []
CompaniesData = []
DealsData = []
DataResponse = []
ContactsResponseData = []
DealsResponseData = []
CompaniesResponseData = []
# TerritoryCode = 'US'
# CycleId= '20230201'
class AssociationFormatting:
    @staticmethod
    def associationformatting(data,TerritoryCode, CycleId, Object, PropertiesStructure, DataProperties):
        global MergedDealsAssociationProperties, MergedContactsAssociationProperties, MergedComapaniesAssociationProperties
        for dictionary in data:
            ContactsNew = []
            CompaniesNew = []
            DealsNew = []
            # FinalDictionary = {}
            d2 = {key: value for key, value in dictionary.items() if key != 'associations' and key != 'properties'}
            d3 = {key: value for key, value in dictionary['properties'].items() if key in DataProperties}
            propertiesmerge = {**d3, **d2}
            MergingPropertiesStructure = {**PropertiesStructure, **propertiesmerge}
            if Object == 'associationcontacts':
                ContactsResponseData.append(MergingPropertiesStructure)
                association_contacts_properties_list = ['hs_object_id', 'lastmodifieddate', 'createdate']
                association_properties = {key: value for key, value in d3.items() if key in association_contacts_properties_list}
                MergedContactsAssociationProperties = {**association_properties, **d2}
            if Object == 'associationdeals':
                DealsResponseData.append(MergingPropertiesStructure)
                association_deals_properties_list = ['amount','closedate','createdate','dealname','dealstage','hs_lastmodifieddate','hs_object_id','pipeline']
                association_properties = {key: value for key, value in d3.items() if key in association_deals_properties_list}
                MergedDealsAssociationProperties = {**association_properties, **d2}
            if Object == 'associationcompanies':
                CompaniesResponseData.append(MergingPropertiesStructure)
                association_companies_properties_list = ['hs_object_id','name','hubspot_owner_id','contract_date','createdate','hubspot_team_id','hs_lastmodifieddate']
                association_properties = {key: value for key, value in d3.items() if key in association_companies_properties_list}
                MergedComapaniesAssociationProperties = {**association_properties, **d2}

            if 'associations' in dictionary:
                if Object == 'customobject':
                    if 'contacts' in dictionary['associations']:
                        for associate in dictionary['associations']['contacts']['results']:
                            associateResponse = {"contactid":associate['id'], "contacttype": associate['type']}
                            ContactsNew.append(associateResponse)
                        for associate in ContactsNew:
                            FinalDIct = {**propertiesmerge, **associate}
                            ContactsData.append(FinalDIct)
                    else:
                        emptydict = {"contactid": None, "contacttype": None}
                        FinalDIct = {**propertiesmerge, **emptydict}
                        ContactsData.append(FinalDIct)

                if Object == 'associationdeals':
                    if 'contacts' in dictionary['associations']:
                        for associate in dictionary['associations']['contacts']['results']:
                            associateResponse = {"contactid":associate['id'], "contacttype": associate['type']}
                            ContactsNew.append(associateResponse)
                        for associate in ContactsNew:
                            FinalDictionary = {**MergedDealsAssociationProperties, **associate}
                            ContactsData.append(FinalDictionary)
                    if 'companies' in dictionary['associations']:
                        for associate in dictionary['associations']['companies']['results']:
                            associateResponse = {"companiesid":associate['id'], "companiestype": associate['type']}
                            CompaniesNew.append(associateResponse)
                        for associate in CompaniesNew:
                            FinalDictionary = {**MergedDealsAssociationProperties, **associate}
                            CompaniesData.append(FinalDictionary)
                if Object == 'associationcontacts':
                    if 'deals' in dictionary['associations']:
                        for associate in dictionary['associations']['deals']['results']:
                            associateResponse = {"dealid":associate['id'], "dealtype": associate['type']}
                            DealsNew.append(associateResponse)
                        for associate in DealsNew:
                            FinalDictionary = {**MergedContactsAssociationProperties, **associate}
                            DealsData.append(FinalDictionary)
                    if 'companies' in dictionary['associations']:
                        for associate in dictionary['associations']['companies']['results']:
                            associateResponse = {"companiesid":associate['id'], "companiestype": associate['type']}
                            CompaniesNew.append(associateResponse)
                        for associate in CompaniesNew:
                            FinalDictionary = {**MergedContactsAssociationProperties, **associate}
                            CompaniesData.append(FinalDictionary)
                if Object == 'associationcompanies':
                    if 'deals' in dictionary['associations']:
                        for associate in dictionary['associations']['deals']['results']:
                            associateResponse = {"dealid": associate['id'], "dealtype": associate['type']}
                            DealsNew.append(associateResponse)
                        for associate in DealsNew:
                            FinalDIctionary = {**MergedComapaniesAssociationProperties, **associate}
                            DealsData.append(FinalDIctionary)
                    if 'contacts' in dictionary['associations']:
                        for associate in dictionary['associations']['contacts']['results']:
                            associateResponse = {"contactid": associate['id'], "contacttype": associate['type']}
                            ContactsNew.append(associateResponse)
                        for associate in ContactsNew:
                            FinalDIctionary = {**MergedComapaniesAssociationProperties, **associate}
                            ContactsData.append(FinalDIctionary)
            else:
                if Object == 'customobject':
                    emptydict = {"contactid":None, "contacttype":None}
                    FinalDIct = {**propertiesmerge, **emptydict}
                    ContactsData.append(FinalDIct)
        return ContactsData, CompaniesData, DealsData, ContactsResponseData, DealsResponseData, CompaniesResponseData


    @staticmethod
    def GetExtractDataFormatting(DataExtract, TerritoryCode, CycleId,PropertiesStructure, DataProperties):

        for dictionary in DataExtract:
            DefaultResponse = {key: value for key, value in dictionary.items() if key != 'properties' and key in DataProperties}
            if 'properties' in dictionary:
                PropertiesResponse = {key: value for key, value in dictionary['properties'].items() if key in DataProperties}
                MergeDict = {** DefaultResponse, ** PropertiesResponse}
                DictStructure = {**PropertiesStructure,**MergeDict}
                DataResponse.append(DictStructure)
            else:
                DictStructure = {**PropertiesStructure, **DefaultResponse}
                DataResponse.append(DictStructure)

        return DataResponse
