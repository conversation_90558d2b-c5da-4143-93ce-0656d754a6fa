import json

def lambda_handler(event, context):
    # Initializing empty lists and dictionaries
    Stage1 = event['Stage1']
    contact = []
    deal = []
    owner = []
    associationcontacts = []
    associationdeals = []
    associationcompanies = []
    archivecontacts = []
    archivedeals = []
    archiveowners = []
    archivecompanies = []
    companies = []
    customobject = []
    franceassociation = []

    # Iterate over Stage1 objects
    for config_info in Stage1:
        # Check conditions and append to respective lists based on object and operation
        if config_info['Object'] == 'contacts' and config_info['Operation'] == 'data_extract':
            contact.append(config_info)
        if config_info['Object'] == 'deals' and config_info['Operation'] == 'data_extract':
            deal.append(config_info)
        if config_info['Object'] == 'owners' and config_info['Operation'] == 'data_extract':
            owner.append(config_info)
        if config_info['Object'] == 'associationcontacts' and config_info['Operation'] == 'data_extract' and config_info['Territory']!='FR':
            associationcontacts.append(config_info)
        if config_info['Object'] == 'associationdeals' and config_info['Operation'] == 'data_extract' and config_info['Territory']!='FR':
            associationdeals.append(config_info)
        if config_info['Object'] == 'associationcompanies' and config_info['Operation'] == 'data_extract':
            associationcompanies.append(config_info)
        if config_info['Object'] == 'contacts' and config_info['Operation'] == 'data_extract_archive':
            archivecontacts.append(config_info)
        if config_info['Object'] == 'deals' and config_info['Operation'] == 'data_extract_archive':
            archivedeals.append(config_info)
        if config_info['Object'] == 'owners' and config_info['Operation'] == 'data_extract_archive':
            archiveowners.append(config_info)
        if config_info['Object'] == 'companies' and config_info['Operation'] == 'data_extract_archive':
            archivecompanies.append(config_info)
        if config_info['Object'] == 'companies' and config_info['Operation'] == 'data_extract':
            companies.append(config_info)
        if config_info['Object'] == 'customobject' and config_info['Operation'] == 'data_extract':
            customobject.append(config_info)
        if (config_info['Object'] in ['associationcontacts','associationdeals'] and config_info['Operation'] == 'data_extract' and config_info['Territory'] == 'FR') :
            franceassociation.append(config_info)

    # Create dictionaries for each category of objects
    contacts_dict = {'Contacts': contact}
    deal_dict = {'Deals': deal}
    owner_dict = {'Owners': owner}
    associationcontacts_dict = {'AssociationContacts': associationcontacts}
    associationdeals_dict = {'AssociationDeals': associationdeals}
    associationcompanies_dict = {'AssociationCompanies': associationcompanies}
    archivecontacts_dict = {'ArchiveContacts': archivecontacts}
    archivedeals_dict = {'ArchiveDeals': archivedeals}
    archiveowners_dict = {'ArchiveOwners': archiveowners}
    archivecompanies_dict = {'ArchiveCompanies': archivecompanies}
    companies_dict = {'Companies': companies}
    customobject_dict = {'CustomObject': customobject}
    francedict = {'france_associations': franceassociation}

    # Create a dictionary containing all the subcategories
    substage1 = {
        "Substage1": [
            contacts_dict,
            deal_dict,
            owner_dict,
            associationcontacts_dict,
            associationdeals_dict,
            associationcompanies_dict,
            archivecontacts_dict,
            companies_dict,
            customobject_dict,
            archivedeals_dict,
            archiveowners_dict,
            archivecompanies_dict,
            francedict
        ]
    }

    return substage1
