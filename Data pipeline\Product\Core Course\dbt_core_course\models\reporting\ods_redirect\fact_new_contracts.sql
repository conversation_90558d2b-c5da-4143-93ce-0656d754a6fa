{{ config(
    MATERIALIZED = 'table',
    table_type = 'iceberg',
    FORMAT = 'parquet'
) }}


With sales as 
(
    SELECT *,
        date_add('day', 6, date_trunc('month',sales_date) + interval '1' month - interval '1' day) AS month_close_sales,
        date_add('day', 6, date_trunc('month',cancel_date) + interval '1' month - interval '1' day) AS month_close_cancel
    FROM(
    SELECT
        'master' AS private_master,
        c.id AS contract_id,
        ce.center_reference_id,
        '' as student_id,
        '' as student_reference_id,
        REGEXP_REPLACE(t.name,'(\w)(\w*)',(x) -> UPPER(x[1]) || lower(x[2])) AS territory_name,
        REGEXP_REPLACE(ce.name,'(\w)(\w*)',(x) -> UPPER(x[1]) || lower(x[2])) AS centername,
        ur.code AS code,
        c.master_contract_number AS contractnumber,
        ur.name AS companyname,
        '' AS last_name,
        '' AS first_name,
        date_format(c.created_date,'%b %d %Y') AS created_date,
        c.sale_date AS sales_date,
        NULL AS origstart_date,
        NULL AS origend_date,
        mcc.start_date,
        mcc.end_date,
        date_diff('month', mcc.start_date, mcc.end_date) AS contractlength,
        CASE
            WHEN date_diff('month', mcc.start_date, mcc.end_date) <= 3 THEN '1. 0-3 months'
            WHEN date_diff('month', mcc.start_date, mcc.end_date) <= 6 THEN '2. 4-6 months'
            WHEN date_diff('month', mcc.start_date, mcc.end_date) <= 9 THEN '3. 7-9 months'
            WHEN date_diff('month', mcc.start_date, mcc.end_date) <= 12 THEN '4. 10-12 months'
            WHEN date_diff('month', mcc.start_date, mcc.end_date) <= 15 THEN '5. 13-15 months'
            WHEN date_diff('month', mcc.start_date, mcc.end_date) <= 18 THEN '6. 16-18 months'
            WHEN date_diff('month', mcc.start_date, mcc.end_date) <= 21 THEN '7. 19-21 months'
            WHEN date_diff('month', mcc.start_date, mcc.end_date) <= 24 THEN '8. 22-24 months'
            WHEN date_diff('month', mcc.start_date, mcc.end_date) > 24 THEN '9. 25+ months'
            ELSE ''
        END AS contractlengthgroup,
        '' AS merchandisingtype,
        c.consultant_id,
        mcc.levelslimit_cc,
        mcc.monthslimit_cc,
        mcc.contractslimit_cc,
        mcc.levelslimit_tp,
        mcc.monthslimit_tp,
        mcc.contractslimit_tp,	
        mcc.levelslimit_ml,
        mcc.monthslimit_ml,
        mcc.contractslimit_ml,
        mcc.levelslimit_other,
        mcc.monthslimit_other,
        mcc.contractslimit_other,
        '' AS origstartlevel,
        '' AS origendlevel,
        '' AS startlevel,
        '' AS endlevel,
        c.status AS contract_status,
        NULL AS contract_type,
        NULL AS location,
        'Master' AS service,
        NULL AS coursetype,
        false AS is_prmotional, 
        false AS is_transferin, 
        'master' AS accesstype,
        {# NULL AS audit_price,
        NULL AS cont_price, #}
        CAST(
            CASE
                WHEN prcm.previous_value IS NOT NULL AND CAST(prcm.previous_value AS DECIMAL(20,2)) > 0.00 THEN CAST(prcm.previous_value AS DECIMAL(20,2))
                WHEN mcc.org_price IS NOT NULL AND CAST(mcc.org_price AS DECIMAL(20,2)) > 0.00 THEN CAST(mcc.org_price AS DECIMAL(20,2))
                ELSE CAST(mpi.net_price AS DECIMAL(20,2))
            END AS DECIMAL(20,2)
        ) AS org_price,
        NULL AS ismembership,
        CAST(CASE WHEN fst.id IS NULL THEN 1 ELSE 0 END AS BOOLEAN) AS isrenewed,
        -- cancellation details
        CASE
            WHEN minvaldocdatecancel IS NULL OR validateddatecancel IS NOT NULL
                THEN COALESCE(
                    can.effective_date,
                    {{convert_to_local_timestamp('can.created_date','tz.time_zone_id')}}
                )
            ELSE NULL
        END AS cancel_date,
        CASE
            WHEN minvaldocdatecancel IS NULL OR validateddatecancel IS NOT NULL THEN COALESCE(ref.refund, ref_new.refund)
            ELSE NULL
        END AS refund,
        CASE
            WHEN minvaldocdatecancel IS NULL OR validateddatecancel IS NOT NULL THEN can.cancellationreason
            ELSE ''
        END AS cancellationreason,
        validateddatecreate,
        validateddatecancel
    FROM ods_contract_service.ods_cs_master_contracts c
    LEFT JOIN (
        SELECT
            master_contract_id,
            MIN(CASE WHEN document_type = 1 THEN created_date ELSE NULL END) AS minvaldocdatecreate,
            MIN(CASE WHEN document_type = 4 THEN created_date ELSE NULL END) AS minvaldocdatecancel,
            MIN(CASE WHEN document_type = 1 AND state = 2 THEN last_updated_date ELSE NULL END) AS validateddatecreate,
            MIN(CASE WHEN document_type = 4 AND state = 2 THEN last_updated_date ELSE NULL END) AS validateddatecancel
        FROM ods_contract_service.ods_cs_contract_validations
        WHERE master_contract_id IS NOT NULL
        GROUP BY master_contract_id
    ) cv ON cv.master_contract_id = c.id
    LEFT JOIN (
        SELECT
            id,
            ROW_NUMBER() OVER(PARTITION BY company_id ORDER BY created_date) AS rn
        FROM ods_contract_service.ods_cs_master_contracts
        WHERE state <> 12
    ) fst ON c.id = fst.id AND fst.rn = 1
    LEFT JOIN (
        SELECT
            mcc.master_contract_id,
            SUM(CASE WHEN prc.previous_value IS NULL THEN cast(mcc.price as decimal) ELSE cast(prc.previous_value as decimal) END) AS org_price,
            MIN(start_date) AS start_date,
            MAX(end_date) AS end_date,
            SUM(CASE WHEN product_type_id = '32a8330a-1413-4c16-8e60-554f7e612253' AND limit_type = 'maxnooflevels' THEN limit_count ELSE 0 END) AS levelslimit_cc,
            SUM(CASE WHEN product_type_id = '32a8330a-1413-4c16-8e60-554f7e612253' AND limit_type = 'maxnoofmonths' THEN limit_count ELSE 0 END) AS monthslimit_cc,
            SUM(CASE WHEN product_type_id = '32a8330a-1413-4c16-8e60-554f7e612253' AND limit_type = 'maxnoofcontracts' THEN limit_count ELSE 0 END) AS contractslimit_cc,
            SUM(CASE WHEN product_type_id = 'd674996f-08b9-459f-a6f3-1e0de25c2759' AND limit_type = 'maxnooflevels' THEN limit_count ELSE 0 END) AS levelslimit_tp,
            SUM(CASE WHEN product_type_id = 'd674996f-08b9-459f-a6f3-1e0de25c2759' AND limit_type = 'maxnoofmonths' THEN limit_count ELSE 0 END) AS monthslimit_tp,
            SUM(CASE WHEN product_type_id = 'd674996f-08b9-459f-a6f3-1e0de25c2759' AND limit_type = 'maxnoofcontracts' THEN limit_count ELSE 0 END) AS contractslimit_tp,
            SUM(CASE WHEN product_type_id = '469f9cd6-f7ed-45ab-b2e3-81310b9b7590' AND limit_type = 'maxnooflevels' THEN limit_count ELSE 0 END) AS levelslimit_ml,
            SUM(CASE WHEN product_type_id = '469f9cd6-f7ed-45ab-b2e3-81310b9b7590' AND limit_type = 'maxnoofmonths' THEN limit_count ELSE 0 END) AS monthslimit_ml,
            SUM(CASE WHEN product_type_id = '469f9cd6-f7ed-45ab-b2e3-81310b9b7590' AND limit_type = 'maxnoofcontracts' THEN limit_count ELSE 0 END) AS contractslimit_ml,
            SUM(CASE WHEN product_type_id = 'a5f6ca46-1d41-4feb-8246-e55cceb68a82' AND limit_type = 'maxnooflevels' THEN limit_count ELSE 0 END) AS levelslimit_other,
            SUM(CASE WHEN product_type_id = 'a5f6ca46-1d41-4feb-8246-e55cceb68a82' AND limit_type = 'maxnoofmonths' THEN limit_count ELSE 0 END) AS monthslimit_other,
            SUM(CASE WHEN product_type_id = 'a5f6ca46-1d41-4feb-8246-e55cceb68a82' AND limit_type = 'maxnoofcontracts' THEN limit_count ELSE 0 END) AS contractslimit_other
        FROM ods_contract_service.ods_cs_master_contract_courses mcc
        LEFT JOIN ods_contract_service.ods_cs_master_contract_limit_mappings lm ON mcc.id = lm.master_contract_course_id
        LEFT JOIN ods_contract_service.ods_cs_master_contract_type_of_limit tol ON lm.master_contract_type_of_limit_id = tol.id
        LEFT JOIN (
            SELECT
                master_contract_id,
                master_contract_course_id,
                modified_field_id,
                previous_value,
                change_type,
                created_date,
                ROW_NUMBER() OVER(PARTITION BY master_contract_id, modified_field_id ORDER BY created_date) AS rn
            FROM ods_contract_service.ods_cs_master_contract_course_audit_info
            WHERE modified_field_id = '4c38bdb1-e878-4661-86ae-53d9b347b565'
        ) prc ON prc.master_contract_id = mcc.master_contract_id AND prc.master_contract_course_id = mcc.id AND prc.rn = 1
        LEFT JOIN (
            SELECT
                master_contract_id,
                master_contract_course_id,
                modified_field_id,
                previous_value,
                change_type,
                created_date,
                ROW_NUMBER() OVER(PARTITION BY master_contract_id, modified_field_id ORDER BY created_date) AS rn
            FROM ods_contract_service.ods_cs_master_contract_course_audit_info
            WHERE modified_field_id = 'df7eb5dc-84db-4250-9157-c4843ec3df82'
        ) stdtc ON stdtc.master_contract_id = mcc.master_contract_id AND stdtc.master_contract_course_id = mcc.id AND stdtc.rn = 1
        LEFT JOIN (
            SELECT
                master_contract_id,
                master_contract_course_id,
                modified_field_id,
                previous_value,
                change_type,
                created_date,
                ROW_NUMBER() OVER(PARTITION BY master_contract_id, modified_field_id ORDER BY created_date) AS rn
            FROM ods_contract_service.ods_cs_master_contract_course_audit_info
            WHERE modified_field_id = 'a3efcb4b-e149-42c8-938f-e27935028669'
        ) endtc ON endtc.master_contract_id = mcc.master_contract_id AND endtc.master_contract_course_id = mcc.id AND endtc.rn = 1
        WHERE mcc.state <> 13
        GROUP BY mcc.master_contract_id
    ) mcc ON mcc.master_contract_id = c.id
    LEFT JOIN (
        SELECT DISTINCT
            mccai.master_contract_id,
            reason AS cancellationreason,
            mccai.created_date AS created_date,
            mccai.effective_date AS effective_date
        FROM ods_contract_service.ods_cs_master_contract_course_audit_info mccai
        LEFT JOIN ods_contract_service.ods_cs_contract_audit_fields caf ON mccai.modified_field_id = caf.id
        WHERE change_type = 2
        AND caf.field_name = 'state'
    ) can ON c.id = can.master_contract_id
    LEFT JOIN (
        SELECT
            mccai.master_contract_id,
            SUM(CAST(present_value AS decimal)) AS refund
        FROM ods_contract_service.ods_cs_master_contract_course_audit_info mccai
        LEFT JOIN ods_contract_service.ods_cs_contract_audit_fields caf ON mccai.modified_field_id = caf.id
        WHERE change_type = 2
        AND caf.field_name IN ('refund')
        GROUP BY mccai.master_contract_id
    ) ref ON c.id = ref.master_contract_id
    LEFT JOIN (
        SELECT
            mcai.master_contract_id,
            SUM(CAST(present_value AS decimal)) AS refund
        FROM ods_contract_service.ods_cs_master_contract_audit_info mcai
        LEFT JOIN ods_contract_service.ods_cs_contract_audit_fields caf ON mcai.modified_field_id = caf.id
        WHERE change_type = 2
        AND caf.field_name IN ('cancelrefundprice')
        GROUP BY mcai.master_contract_id
    ) ref_new ON c.id = ref_new.master_contract_id
    LEFT JOIN (
        SELECT
            net_price,
            master_contract_id
        FROM ods_contract_service.ods_cs_master_contract_price_information
    ) mpi ON c.id = mpi.master_contract_id
    LEFT JOIN (
        SELECT
            master_contract_id,
            modified_field_id,
            previous_value,
            change_type,
            created_date,
            ROW_NUMBER() OVER(PARTITION BY master_contract_id, modified_field_id ORDER BY created_date) AS rn
        FROM ods_contract_service.ods_cs_master_contract_audit_info
        WHERE modified_field_id = '14955304-662d-40ca-87d3-62692722dccf'
    ) prcm ON c.id = prcm.master_contract_id AND prcm.rn = 1
    INNER JOIN ods_contract_service.ods_cs_centers ce ON c.center_id = ce.id
    INNER JOIN ods_contract_service.ods_cs_territory t ON ce.territory_id = t.id
    LEFT JOIN ods_learning_service.ods_ls_center lsc ON ce.center_reference_id = lsc.reference_center_id
    LEFT JOIN ods_center_configuration_service.ods_cc_center tz ON tz.center_reference_id = ce.center_reference_id
    --INNER JOIN ods_learning_service.ods_ls_timezone tz ON lsc.time_zone_id = tz.id
    INNER JOIN ods_contract_service.ods_cs_new_version_companies ur ON c.company_id = ur.id
    LEFT JOIN ods_contract_service.ods_cs_users const_usr ON c.consultant_id = const_usr.id
    LEFT JOIN (
        SELECT DISTINCT contract_id
        FROM ods_contract_service.ods_cs_contract_class_access_type
        WHERE class_access_type_id = '514e7cea-9ea7-4096-a050-c729466a6219'
        AND is_active = true
    ) cain ON c.id = cain.contract_id
    LEFT JOIN (
        SELECT DISTINCT contract_id
        FROM ods_contract_service.ods_cs_contract_class_access_type
        WHERE class_access_type_id = 'e802ae87-4e17-4d43-b88f-ea7f136cdf69'
        AND is_active = true
    ) caon ON c.id = caon.contract_id
    WHERE
        (sale_date >= timestamp '2019-01-01 00:00:00.000' OR can.created_date >= timestamp '2019-01-01 00:00:00.000')
        AND (cv.minvaldocdatecreate IS NULL OR cv.validateddatecreate IS NOT NULL)


    union ALL

    select
        'private' as private_master,
        c.id as contract_id,
        ce.center_reference_id,
        c.student_id,
        ur.user_reference_id as student_reference_id,
        REGEXP_REPLACE(t.name,'(\w)(\w*)',(x) -> UPPER(x[1]) || lower(x[2])) AS territory_name,
        REGEXP_REPLACE(ce.name,'(\w)(\w*)',(x) -> UPPER(x[1]) || lower(x[2])) AS centername,
        ur.student_code as code,
        c.number as contractnumber,
        '' as companyname,
        ur.last_name,
        ur.first_name,
        date_format({{convert_to_local_timestamp('c.created_date','tz.time_zone_id')}}, '%b %d %Y') AS created_date,
        c.sale_date as sales_date,
        case when stdtc.previous_value is null then cast(c.start_date as varchar) else stdtc.previous_value end as origstart_date,
        case when endtc.previous_value is null then cast(c.end_date as varchar) else endtc.previous_value end as origend_date,
        c.start_date,
        c.end_date,
        date_diff('month',c.start_date,c.end_date) as contractlength,
        case when date_diff('month',c.start_date,c.end_date) <= 3 then '1. 0-3 months'
            when date_diff('month',c.start_date,c.end_date) <= 6 then '2. 4-6 months'
            when date_diff('month',c.start_date,c.end_date) <= 9 then '3. 7-9 months'
            when date_diff('month',c.start_date,c.end_date) <= 12 then '4. 10-12 months'
            when date_diff('month',c.start_date,c.end_date) <= 15 then '5. 13-15 months'
            when date_diff('month',c.start_date,c.end_date) <= 18 then '6. 16-18 months'
            when date_diff('month',c.start_date,c.end_date) <= 21 then '7. 19-21 months'
            when date_diff('month',c.start_date,c.end_date) <= 24 then '8. 22-24 months'
            when date_diff('month',c.start_date,c.end_date) >  24 then '9. 25+ months' else '' end as contractlengthgroup,
    case when service_type = 1
                and contract_type = 1
                and is_teen = false
                and (max_no_of_cc_and_sc_classes = 0 or max_no_of_cc_and_sc_classes is null)
                then 'adult essential'

            when service_type = 1
                and contract_type = 1
                and is_teen = false
                and (max_no_of_cc_and_sc_classes > 0)
                then 'adult premium'

            when service_type = 2
                and contract_type = 1
                and is_teen = false
                and (max_no_of_cc_and_sc_classes = 0 or max_no_of_cc_and_sc_classes is null)
                then 'adult essential 1:1'

            when service_type = 2
                and contract_type = 1
                and is_teen = false
                and (max_no_of_cc_and_sc_classes > 0)
                then 'adult premium 1:1'

            when service_type = 1
                and contract_type = 1
                and is_teen = true
                and (max_no_of_cc_and_sc_classes = 0 or max_no_of_cc_and_sc_classes is null)
                then 'teen essential'

            when service_type = 1
                and contract_type = 1
                and is_teen = true
                and (max_no_of_cc_and_sc_classes > 0)
                then 'teen premium'

            when service_type = 2
                and contract_type = 1
                and is_teen = true
                and (max_no_of_cc_and_sc_classes > 0)
                then 'teen premium 1:1'

            when service_type = 1
                and contract_type = 2
                and is_teen = false
                and (max_no_of_cc_and_sc_classes = 0 or max_no_of_cc_and_sc_classes is null)
                then 'adult essential'

            when service_type = 1
                and contract_type = 2
                and is_teen = false
                and (max_no_of_cc_and_sc_classes > 0)
                then 'adult premium'

            when service_type = 2
                and  contract_type = 2
                and is_teen = false
                and (max_no_of_cc_and_sc_classes = 0 or max_no_of_cc_and_sc_classes is null)
                then 'adult essential 1:1'

            when service_type = 2
                and  contract_type = 2
                and is_teen = false
                and (max_no_of_cc_and_sc_classes > 0)
                then 'adult premium 1:1'

            else 'other' end as merchandisingtype,
        c.consultant_id,
        null as levelslimit_cc,
        null as monthslimit_cc,
        null as contractslimit_cc,
        null as levelslimit_tp,
        null as monthslimit_tp,
        null as contractslimit_tp,
        null as levelslimit_ml,
        null as monthslimit_ml,
        null as contractslimit_ml,
        null as levelslimit_other,
        null as monthslimit_other,
        null as contractslimit_other,
        case when stlvlc.previous_value is null then replace (sl.name, 'level ', '') else stlvlc.previous_value end as origstartlevel,
        case when enlvlc.previous_value is null then replace (el.name, 'level ', '') else enlvlc.previous_value end as origendlevel,
        replace (sl.name, 'level ', '') startlevel,
        replace (el.name, 'level ', '') endlevel,
        c.status as contract_status,
        c.contract_type,
        c.location,
        case
            when c.is_teen = false and c.service_type = 1 then 'standard'
            when c.is_teen = false and c.service_type = 2 then 'vip'
            when c.is_teen = true and c.service_type = 1 then 'teens standard'
            when c.is_teen = true and c.service_type = 2 then 'teens vip' end as service,
        c.product_type_id as coursetype,
        c.is_promotional,
        c.is_transfer_in,
        case 
            when cain.contract_id is not null and caon.contract_id is not null and contract_products.product like '%11%' then 'Full Access +'
            when cain.contract_id is not null and caon.contract_id is not null then 'Full Access'
            when cain.contract_id is not null then 'In-Center'
            when caon.contract_id is not null and contract_products.product like '%11%' then 'Online +'
            when caon.contract_id is not null then 'Online'
            else 'No Access' end as accesstype,
        {# null as audit_price,
        null as cont_price, #}
        case when prc.previous_value is null then c.price else cast(prc.previous_value as decimal(20,2)) end as org_price,
        cast(c.is_membership as boolean) as ismembership,
        cast(case when fst.id is null then 1 else 0 end as boolean) as isrenewed,

    --cancellation details
        case when (minvaldocdatecancel is null or validateddatecancel is not null) then coalesce(can.effective_date,c.cancel_date) else null end as cancel_date,
        case when (minvaldocdatecancel is null or validateddatecancel is not null) then coalesce(cast(can_new.present_value as decimal ),c.refunded) else null end as refund,
        case when (minvaldocdatecancel is null or validateddatecancel is not null) then can.reason else '' end as cancellationreason,

        validateddatecreate,
        validateddatecancel

    FROM			(select * FROM ods_contract_service.ods_cs_contracts  where state <> 13 ) c
    LEFT JOIN
        (
        select
        id
        ,row_number() over(partition by student_id ORDER BY created_date) as rn
        FROM ods_contract_service.ods_cs_contracts
        where state <> 12
        and is_promotional = false
        ) fst
    on				c.id = fst.id
    and				fst.rn = 1
    inner join		ods_contract_service.ods_cs_centers  ce
    on				c.center_id = ce.id
    inner join		ods_contract_service.ods_cs_territory  t
    on				ce.territory_id = t.id
    LEFT JOIN		ods_learning_service.ods_ls_center  lsc
    on				ce.center_reference_id = lsc.reference_center_id
    LEFT JOIN       ods_center_configuration_service.ods_cc_center tz 
    ON              tz.center_reference_id = ce.center_reference_id
    inner join		ods_contract_service.ods_cs_users  ur
    on				c.student_id = ur.id
    LEFT JOIN		ods_contract_service.ods_cs_users  const_usr
    on				c.consultant_id = const_usr.id
    LEFT JOIN		ods_contract_service.ods_cs_product_levels  sl
    on				c.start_level_id = sl.id
    LEFT JOIN		ods_contract_service.ods_cs_product_levels  el
    on				c.end_level_id = el.id
    LEFT JOIN		ods_idam_service.ods_idam_user_basic_info  ubi
    on				ur.user_reference_id = ubi.ssds_id
    LEFT JOIN		(select distinct contract_id FROM ods_contract_service.ods_cs_contract_class_access_type where class_access_type_id = '514e7cea-9ea7-4096-a050-c729466a6219' and is_active = true) cain
    on				c.id = cain.contract_id
    LEFT JOIN		(select distinct contract_id FROM ods_contract_service.ods_cs_contract_class_access_type where class_access_type_id = 'e802ae87-4e17-4d43-b88f-ea7f136cdf69' and is_active = true) caon
    on				c.id = caon.contract_id
    LEFT JOIN
        (select contract_id,
LISTAGG( cast(product_id as varchar) , ', ') WITHIN GROUP (ORDER BY product_id) as product
from ods_contract_service.ods_cs_contract_products
group by contract_id
        ) as contract_products
    ON c.id = contract_products.contract_id
    LEFT JOIN
                (
                    select	contract_id
                            ,min(case when document_type = 1 then created_date else null end) as minvaldocdatecreate
                            ,min(case when document_type = 4 then created_date else null end) as minvaldocdatecancel
                            ,min(case when document_type = 1 and state = 2 then last_updated_date else null end) as validateddatecreate
                            ,min(case when document_type = 4 and state = 2 then last_updated_date else null end) as validateddatecancel
                    FROM	ods_contract_service.ods_cs_contract_validations
                    where contract_id is not null
                    GROUP BY contract_id
                )	cv
    on						cv.contract_id = c.id

    LEFT JOIN
                (
                    select	contract_id
                            ,modified_field_id
                            ,previous_value
                            ,change_type
                            ,created_date
                            ,row_number() over(partition by contract_id, modified_field_id ORDER BY created_date) as rn
                    FROM	ods_contract_service.ods_cs_contracts_audit_info
                    where	modified_field_id = '4c38bdb1-e878-4661-86ae-53d9b347b565' --price
                )	prc
    on						prc.contract_id = c.id
    and						prc.rn = 1
    LEFT JOIN
                (
                    select	contract_id
                            ,modified_field_id
                            ,replace (l.name, 'level ', 'l') as previous_value
                            ,change_type
                            ,created_date
                            ,row_number() over(partition by contract_id, modified_field_id ORDER BY created_date) as rn
                    FROM	ods_contract_service.ods_cs_contracts_audit_info  c
                    LEFT JOIN		ods_contract_service.ods_cs_product_levels  l
                    on				c.previous_value = l.id

                    where	modified_field_id = 'f70d9ba0-290b-47a7-8e11-20f73a2c8824' --start level
                )	stlvlc
    on						stlvlc.contract_id = c.id
    and						stlvlc.rn = 1
    LEFT JOIN
                (
                    select	contract_id
                            ,modified_field_id
                            ,replace (l.name, 'level ', 'l') as previous_value
                            ,change_type
                            ,created_date
                            ,row_number() over(partition by contract_id, modified_field_id ORDER BY created_date) as rn
                    FROM	ods_contract_service.ods_cs_contracts_audit_info  c
                    LEFT JOIN		ods_contract_service.ods_cs_product_levels  l
                    on				c.previous_value = l.id
                    where	modified_field_id = 'fa063854-be56-4e10-9444-abe4999cbfd2' --end level
                )	enlvlc
    on						enlvlc.contract_id = c.id
    and						enlvlc.rn = 1
    LEFT JOIN
                (
                    select	contract_id
                            ,modified_field_id
                            ,previous_value
                            ,change_type
                            ,created_date
                            ,row_number() over(partition by contract_id, modified_field_id ORDER BY created_date) as rn
                    FROM	ods_contract_service.ods_cs_contracts_audit_info
                    where	modified_field_id = 'df7eb5dc-84db-4250-9157-c4843ec3df82' -- start date
                )	stdtc
    on						stdtc.contract_id = c.id
    and						stdtc.rn = 1
    LEFT JOIN
                (
                    select	contract_id
                            ,modified_field_id
                            ,previous_value
                            ,change_type
                            ,created_date
                            ,row_number() over(partition by contract_id, modified_field_id ORDER BY created_date) as rn
                    FROM	ods_contract_service.ods_cs_contracts_audit_info
                    where	modified_field_id = 'a3efcb4b-e149-42c8-938f-e27935028669' --end date
                )	endtc
    on						endtc.contract_id = c.id
    and						endtc.rn = 1
    LEFT JOIN		ods_contract_service.ods_cs_contracts_audit_info  can
    on				c.id = can.contract_id
    and				can.change_type = 2 -- cancellation change type
    and				can.modified_field_id = '4dda9d2b-f7fa-4cd5-8f38-d76985a63029'	-- cancellation state change
    LEFT JOIN		ods_contract_service.ods_cs_contracts_audit_info  can_new
    on				c.id = can_new.contract_id
    and				can_new.change_type = 2 -- cancellation change type
    and				can_new.modified_field_id = 'fbb5d202-e405-4cdd-b1d4-08a28a838aaf'	-- refunded change
    where 1=1
    and
    (
    sale_date >= timestamp '2019-01-01 00:00:00.000'
    or
    c.cancel_date >= timestamp '2019-01-01 00:00:00.000'
    or
    can.effective_date >= timestamp '2019-01-01 00:00:00.000'
    )
    and
    --either no validation document was created or the created document was validated
    (
    cv.minvaldocdatecreate is null
    or
    cv.validateddatecreate is not null
    )
)),
quartiles as (
    select 
        territory_name
        ,private_master
        ,year(sales_date) as year
        ,org_price as price
        ,ntile(4) over (partition by territory_name, private_master, year(sales_date) ORDER BY org_price) as quartile
    FROM sales
    GROUP BY
        territory_name
        ,private_master
        ,year(sales_date) 
        ,org_price
),
outlierbounds as (
    select 
        territory_name
        ,private_master
        ,q.year
        ,max(case when quartile = 1 then price end) as q1
        ,max(case when quartile = 2 then price end) as median
        ,max(case when quartile = 3 then price end) as q3
    FROM quartiles q
    GROUP BY
        territory_name
        ,private_master
        ,q.year
    ORDER BY 
        territory_name
)

SELECT
    s.*
    ,ob.q1
    ,ob.median
    ,ob.q3
FROM sales s
LEFT JOIN outlierbounds ob 
    on s.territory_name = ob.territory_name
    and s.private_master = ob.private_master
    and year(sales_date) = ob.year