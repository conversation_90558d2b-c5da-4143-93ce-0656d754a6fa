version: 2

models:
  - name: activity_workbook
    columns:
      - name: activity_id
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: activity_reference_id
        tests:
          - not_null:
              severity: error
      - name: student_id
        tests:
          - not_null:
              severity: error
      - name: created_date
        tests:
          - not_null:
              severity: warn
      - name: local_created_date
        tests:
          - not_null:
              severity: warn
      - name: last_updated_date
        tests:
          - not_null:
              severity: warn
      - name: local_last_updated_date
        tests:
          - not_null:
              severity: warn
      - name: level
        tests:
          - accepted_values:
              values: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]
              quote: false
              severity: error
      - name: unit
        tests:
          - accepted_values:
              values: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80]
              quote: false
              severity: error
      - name: lesson
        tests:
          - accepted_values:
              values: [1, 2, 3]
              quote: false
              severity: error
      - name: activity
        tests:
          - accepted_values:
              values: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
              quote: false
              severity: error
      - name: activity_url
        tests:
          - not_null:
              severity: warn
      - name: is_active
        tests:
          - accepted_values:
              values: ['false', 'true']
              quote: false
              severity: warn
      - name: is_completed
        tests:
          - accepted_values:
              values: ['false', 'true']
              quote: false
              severity: warn
      - name: study_mode
        tests:
          - accepted_values:
              values: ['online', 'mobile', 'incenter']
              severity: warn
      - name: category_type
        tests:
          - accepted_values:
              values: ['communication', 'grammar', 'vocabulary']
              severity: warn
      - name: duration_secs
        tests:
          - not_null:
              severity: warn
      - name: duration_mins
        tests:
          - not_null:
              severity: warn
