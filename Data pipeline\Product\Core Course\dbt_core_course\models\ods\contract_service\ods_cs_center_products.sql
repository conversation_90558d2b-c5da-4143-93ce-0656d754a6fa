{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='dbt_unique_id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
    (centerid || cast(productid as varchar)) as dbt_unique_id,
    centerid,
    productid,
    row_number() over(partition by (centerid || cast(productid as varchar)) order by  productid) as rn
    FROM 
        {{source('stage_contract_service', 'centerproducts')}}
)

SELECT
    {{etl_load_date()}},
    dbt_unique_id,
    centerid as center_id,
    productid as product_id
FROM 
    RankedRecords
    Where rn = 1;