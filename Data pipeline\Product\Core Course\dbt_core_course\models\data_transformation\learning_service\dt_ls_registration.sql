{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_registration') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT
    {{etl_load_date()}},
    registration.id as id,
    user_id,
    contract_id,
    center_id,
    grouping_id,
    contitem.description as content_item,
    contentitemtype.name as content_item_type,
    is_current,
    start_date,
    {{ convert_to_local_timestamp(
        'start_date',
        'tz.time_zone_id'
    ) }} as local_start_date,
    end_date,
    {{ convert_to_local_timestamp(
        'end_date',
        'tz.time_zone_id'
    ) }} as local_end_date,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    regtype.name as registration_type,
    schedule_preference_submitted,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated,
    contract_status,
    lab_teacher_id,
    consultant_id,
    CASE
        When registration.workbook_type = 0 then 'digital'
        When registration.workbook_type = 1 then 'printed'
        When registration.workbook_type = 2 then 'notapplicable'
        Else CAST(
            registration.workbook_type AS Varchar
        )
    end as workbook_type,
    english_anytime,
    CASE
        When registration.offer_type = 0 then 'standard'
        When registration.offer_type = 1 then 'promo'
        When registration.offer_type = 2 then 'trial'
        When registration.offer_type = 3 then 'testprep'
        When registration.offer_type = 4 then 'marketleader'
        When registration.offer_type = 5 then 'businesspartner'
        When registration.offer_type = 6 then 'd2c'
        Else CAST(
            registration.offer_type AS Varchar
        )
    end as offer_type,
    is_b2_b,
    b2_b_course_type,
    CASE
        When registration.registration_status = 4 then 'active'
        When registration.registration_status = 128 then 'inactive'
        When registration.registration_status = 256 then 'invalid'
        When registration.registration_status = 512 then 'future'
        When registration.registration_status = 8192 then 'testprep'
        When registration.registration_status = 16384 then 'market leader'
        When registration.registration_status = 262144 then 'business partner'
        When registration.registration_status = 524288 then 'd2c'
        Else CAST(
            registration.registration_status AS Varchar
        )
    end as registration_status,
    CASE
        When registration.course_status = 32 then 'end of course'
        When registration.course_status = 64 then 'first level'
        When registration.course_status = 2048 then 'renewable'
        When registration.course_status = 32768 then 'second level'
        When registration.course_status = 65536 then 'other level'
        Else CAST(
            registration.course_status AS Varchar
        )
    end as course_status,
    allow_self_booking,
    is_cross_center_booking,
    CASE
        When registration.service_type_id = 1 then 'standard'
        When registration.service_type_id = 2 then 'vip'
        Else CAST(
            registration.service_type_id AS Varchar
        )
    end as service_type,
    is_online_class_access,
    is_in_center_class_access,
    max_no_of_cc_and_sc_classes,
    is_membership,
    onboarding_access,
    is_teen
from
    ods_data as registration
    Left Join (
        select
            id,
            description,
            content_item_type_id
        from
            {{ ref('ods_ls_content_item') }}
    ) as contitem
    ON registration.current_content_item_id = contitem.id
    Left Join (
        select
            id,
            name
        from
            {{ ref('ods_ls_content_item_type') }}
    ) as contentitemtype
    on contitem.content_item_type_id = contentitemtype.id
    Left Join (
        select
            id,
            Name
        from
            {{ ref('ods_ls_registration_type') }}
    ) as regtype
    ON registration.registration_type_id = regtype.id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = registration.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
