create table if not exists hubspot.dealsenriched_mmvn
(
    id                 bigint encode az64,
    createdate         timestamp encode az64,
    channel            varchar(128),
    center_name        varchar(128),
    hubspot_owner_id   varchar(42),
    amount             double precision,
    closedate          timestamp encode az64,
    territory_code     varchar(4),
    pipeline           varchar(24),
    termination_type   varchar(24),
    termination_status varchar(24),
    termination_date   timestamp encode az64,
    refunded_amount    double precision,
    territory_name     varchar(24),
    timezonecreatedate timestamp encode az64,
    timezoneclosedate  timestamp encode az64,
    cycleid            bigint encode az64
);


