import json
import boto3
import ast
import io
import logging
import pandas as pd
import datetime


class Redshift:
    @staticmethod
    def connect():
        return boto3.client('redshift-data')


class S3:
    @staticmethod
    def connect(method):
        return boto3.resource('s3') if method == 'resource' else boto3.client('s3')

    @staticmethod
    def read_json_file(bucket, file_path):
        try:
            s3_resource = S3.connect('resource')
            read_file = s3_resource.Object(bucket, file_path)
            file_content = read_file.get()['Body'].read().decode('utf-8')
            return json.loads(file_content)
        except Exception as error_message:
            logging.warning("raised exception in read_json_file due to %s", format(error_message))
            raise Exception

    @staticmethod
    def write_json_file(bucket, file_path, data_response):
        try:
            s3_resource = S3.connect('resource')
            s3_resource.Object(bucket, file_path).put(Body=json.dumps(data_response))
            return file_path
        except Exception as error_message:
            logging.warning("raised exception in write_json_file due to %s", format(error_message))
            raise Exception

    @staticmethod
    def dynamic_file_path(bucket_name, prefix):
        try:
            s3_client = S3.connect('client')
            paginator = s3_client.get_paginator('list_objects_v2')
            file_path = ''
            for page in paginator.paginate(Bucket=bucket_name, Prefix=prefix, Delimiter='/'):
                for folder in page.get('CommonPrefixes', []):
                    file_path = folder.get('Prefix')
            return file_path
        except Exception as error_message:
            logging.warning("raised exception in dynamic_file_path due to %s", format(error_message))
            raise Exception

    @staticmethod
    def read_csv_file(bucket, file_path):
        try:
            s3_client = S3.connect('client')
            obj = s3_client.get_object(Bucket=bucket, Key=file_path)
            return pd.read_csv(obj['Body'])
        except Exception as error_message:
            logging.warning("raised exception in read_csv_file due to %s", format(error_message))
            raise Exception

    @staticmethod
    def write_csv_file(file_path, bucket, data_response):
        try:
            s3_client = S3.connect('client')
            s3_path = f"s3://{bucket}/" + file_path
            logging.warning("s3_path:'%s'", format(s3_path))
            with io.StringIO() as csv_buffer:
                data_response.to_csv(csv_buffer, index=False, quoting=1)
                s3_client.put_object(Bucket=bucket, Key=file_path, Body=csv_buffer.getvalue())
            return s3_path
        except Exception as error_message:
            logging.warning("raised exception in write_csv_file due to %s", format(error_message))
            raise Exception


class SecretManager:
    @staticmethod
    def get_secret(secret_name, region_name):
        session = boto3.session.Session()
        client = session.client(service_name='secretsmanager', aws_access_key_id='********************',
                                aws_secret_access_key='p9h9IO3KkfiWBnmEdlczJxYOCZzfqjhdOp7XBiKX',
                                region_name=region_name)
        try:
            get_secret_value_response = client.get_secret_value(SecretId=secret_name)
            return ast.literal_eval(get_secret_value_response['SecretString'])
        except Exception as error_message:
            logging.warning("raised exception in get_secret due to %s", format(error_message))
            raise Exception


class StepFunction:
    @staticmethod
    def connect():
        return boto3.client('stepfunctions')

    @staticmethod
    def check_step_functions_running(state_machine_arn):
        try:
            step_function_client = StepFunction.connect()
            return step_function_client.list_executions(
                stateMachineArn=state_machine_arn,
                statusFilter='RUNNING',
                maxResults=1
            )
        except Exception as error_message:
            logging.warning("raised exception in check_step_functions_running due to %s", format(error_message))
            raise Exception

    @staticmethod
    def start_step_function(execution_input, state_machine_arn):
        try:
            step_function_client = StepFunction.connect()
            return step_function_client.start_execution(
                stateMachineArn=state_machine_arn,
                name=execution_input + datetime.datetime.now().strftime("%d%m%y%H%M%S%f"),
                input=json.dumps({"input": execution_input})
            )
        except Exception as error_message:
            logging.warning("raised exception in start_step_function due to %s", format(error_message))
            raise Exception
