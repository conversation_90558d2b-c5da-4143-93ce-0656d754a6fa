{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_rebooked_student') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT
    {{etl_load_date()}},
    rebookedstu.id as id,
    student_id,
    from_class_id,
    to_class_id,
    rebooked_by,
    role.description as rebooked_role_title,
    Case
        When role.description <> 'student' Then 'staff'
        When role.description = 'student' Then 'student'
        Else role.description
    End as rebookedpersontype,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated
from
    ods_data as rebookedstu
    Left Join (
        select
            user_id,
            center_id,
            role_id
        from
            {{ ref('ods_ls_user') }}
    ) as User
    ON rebookedstu.student_id = User.user_id
    left join (
        select
            id,
            description
        from
            {{ ref('ods_ls_role') }}
    ) as role
    on User.role_id = role.id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = User.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
