{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet',
    unique_key = ['message','date'],
) }}


WITH Calendar AS (
    Select 
        "date"
    from 
    {{ ref('dim_calendar') }}
    where 
    {% if is_incremental() %}
        "date" >= date_add('day',-14,current_date)  
    {% else %}
        "date" >= ( Select date(min(start_date)) from {{ ref('novu_reference') }} )
    {% endif %}
    and "date" <= current_date
    
),

base_metrics AS (
    SELECT
        -- Dimensions
        res.campaign,
        res.channel,
        res.message_abbreviated as message,
        date(res.created_at) as date,
        coalesce(res.treatment,'B') as treatment,
        res.treatment_group,
        res.test_start_date,
        res.test_end_date,
        COUNT(res.message_id) as total_messages,
        -- Outcome 1 metrics
        res.outcome_1_name,
        res.outcome_1_days,
        SUM(res.outcome_1_value) as outcome_1_count,
        
        -- Outcome 2 metrics
        res.outcome_2_name,
        res.outcome_2_days,
        SUM(res.outcome_2_value) as outcome_2_count,
        
        -- Outcome 3 metrics
        res.outcome_3_name,
        res.outcome_3_days,
        SUM(res.outcome_3_value) as outcome_3_count,

        -- Outcome 4 metrics
        res.outcome_4_name,
        res.outcome_4_days,
        SUM(res.outcome_4_value) as outcome_4_count,

        -- Outcome 5 metrics
        res.outcome_5_name,
        res.outcome_5_days,
        SUM(res.outcome_5_value) as outcome_5_count,

        -- Outcome 6 metrics
        res.outcome_6_name,
        res.outcome_6_days,
        SUM(res.outcome_6_value) as outcome_6_count
    FROM 
        {{ ref('novu_results') }} as res
    GROUP BY
        res.campaign,
        res.channel,
        res.message_abbreviated,
        date(res.created_at),
        res.treatment,
        res.treatment_group,
        res.test_start_date,
        res.test_end_date,
        res.outcome_1_name,
        res.outcome_2_name,
        res.outcome_3_name,
        res.outcome_1_days,
        res.outcome_2_days,
        res.outcome_3_days,
        res.outcome_4_name,
        res.outcome_5_name,
        res.outcome_6_name,
        res.outcome_4_days,
        res.outcome_5_days,
        res.outcome_6_days
)

SELECT 
	nref.campaign , 
    nref.channel, 
    nref.message_abbreviated as "message", 
    Calendar.date, 
    date(nref.start_date) as test_start_date,
    date(nref.end_date) as test_end_date,
    -- Store outcome names (they should be the same for both treatments)
    coalesce(MAX(CASE WHEN nref.treatment = 'A' THEN nref.outcome_1_name END),MAX(CASE WHEN nref.treatment = 'B' THEN bm.outcome_1_name END)) as outcome_1_name,
    coalesce(MAX(CASE WHEN nref.treatment = 'A' THEN nref.outcome_2_name END),MAX(CASE WHEN nref.treatment = 'B' THEN bm.outcome_2_name END)) as outcome_2_name,
    coalesce(MAX(CASE WHEN nref.treatment = 'A' THEN nref.outcome_3_name END),MAX(CASE WHEN nref.treatment = 'B' THEN bm.outcome_3_name END)) as outcome_3_name,
    coalesce(MAX(CASE WHEN nref.treatment = 'A' THEN nref.outcome_4_name END),MAX(CASE WHEN nref.treatment = 'B' THEN bm.outcome_4_name END)) as outcome_4_name,
    coalesce(MAX(CASE WHEN nref.treatment = 'A' THEN nref.outcome_5_name END),MAX(CASE WHEN nref.treatment = 'B' THEN bm.outcome_5_name END)) as outcome_5_name,
    coalesce(MAX(CASE WHEN nref.treatment = 'A' THEN nref.outcome_6_name END),MAX(CASE WHEN nref.treatment = 'B' THEN bm.outcome_6_name END)) as outcome_6_name,
    coalesce(MAX(CASE WHEN nref.treatment = 'A' THEN nref.outcome_1_days END),MAX(CASE WHEN nref.treatment = 'B' THEN bm.outcome_1_days END)) as outcome_1_days,
    coalesce(MAX(CASE WHEN nref.treatment = 'A' THEN nref.outcome_2_days END),MAX(CASE WHEN nref.treatment = 'B' THEN bm.outcome_2_days END)) as outcome_2_days,
    coalesce(MAX(CASE WHEN nref.treatment = 'A' THEN nref.outcome_3_days END),MAX(CASE WHEN nref.treatment = 'B' THEN bm.outcome_3_days END)) as outcome_3_days,
    coalesce(MAX(CASE WHEN nref.treatment = 'A' THEN nref.outcome_4_days END),MAX(CASE WHEN nref.treatment = 'B' THEN bm.outcome_4_days END)) as outcome_4_days,
    coalesce(MAX(CASE WHEN nref.treatment = 'A' THEN nref.outcome_5_days END),MAX(CASE WHEN nref.treatment = 'B' THEN bm.outcome_5_days END)) as outcome_5_days,
    coalesce(MAX(CASE WHEN nref.treatment = 'A' THEN nref.outcome_6_days END),MAX(CASE WHEN nref.treatment = 'B' THEN bm.outcome_6_days END)) as outcome_6_days,
    
    -- Treatment A metrics
    MAX(CASE WHEN nref.treatment = 'A' THEN nref.treatment_group END) as a_treatment_group,
    MAX(CASE WHEN bm.treatment = 'A' THEN bm.total_messages END) as a_total_messages,
    MAX(CASE WHEN bm.treatment = 'A' THEN bm.outcome_1_count END) as a_outcome_1_count,
    MAX(CASE WHEN bm.treatment = 'A' THEN bm.outcome_2_count END) as a_outcome_2_count,
    MAX(CASE WHEN bm.treatment = 'A' THEN bm.outcome_3_count END) as a_outcome_3_count,
    MAX(CASE WHEN bm.treatment = 'A' THEN bm.outcome_4_count END) as a_outcome_4_count,
    MAX(CASE WHEN bm.treatment = 'A' THEN bm.outcome_5_count END) as a_outcome_5_count,
    MAX(CASE WHEN bm.treatment = 'A' THEN bm.outcome_6_count END) as a_outcome_6_count,     
    -- Treatment B metrics
    MAX(CASE WHEN nref.treatment = 'B' THEN nref.treatment_group END) as b_treatment_group,
    MAX(CASE WHEN bm.treatment = 'B' THEN bm.total_messages END) as b_total_messages,
    MAX(CASE WHEN bm.treatment = 'B' THEN bm.outcome_1_count END) as b_outcome_1_count,
    MAX(CASE WHEN bm.treatment = 'B' THEN bm.outcome_2_count END) as b_outcome_2_count,
    MAX(CASE WHEN bm.treatment = 'B' THEN bm.outcome_3_count END) as b_outcome_3_count,
    MAX(CASE WHEN bm.treatment = 'B' THEN bm.outcome_4_count END) as b_outcome_4_count,
    MAX(CASE WHEN bm.treatment = 'B' THEN bm.outcome_5_count END) as b_outcome_5_count,
    MAX(CASE WHEN bm.treatment = 'B' THEN bm.outcome_6_count END) as b_outcome_6_count
FROM 
    Calendar
LEFT JOIN 
    external_sources.novu_reference AS nref
ON 
    Calendar."date" >= date(nref.start_date)
    AND Calendar."date" <= date(nref.end_date)
LEFT JOIN 
    base_metrics as bm
ON  
    Calendar.date = bm.date
    and nref.message_abbreviated = bm.message
    and nref.treatment = bm.treatment
GROUP BY
    nref.campaign,
    nref.channel,
    nref.message_abbreviated,
    Calendar.date,
    date(nref.start_date),
    date(nref.end_date)