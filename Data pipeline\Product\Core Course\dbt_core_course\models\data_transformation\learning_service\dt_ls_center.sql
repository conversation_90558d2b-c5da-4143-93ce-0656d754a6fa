{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_center') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT
    {{etl_load_date()}},
    center.id as id,
    reference_center_id,
    center.name as name,
    territory_id,
    zone,
    center_number,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    timezone.name as timezone,
    address1,
    address2,
    city,
    postal_code,
    primary_phone,
    email,
    state,
    country,
    image_name,
    social_network_id1,
    social_network_address1,
    social_network_id2,
    social_network_address2,
    social_network_id3,
    social_network_address3,
    social_network_id4,
    social_network_address4,
    maximum_students_in_standby,
    is_standby_enabled,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated
from
    ods_data as center
    Left Join (
        select
            id,
            Name
        from
            {{ ref('ods_ls_timezone') }}
    ) as timezone
    ON center.time_zone_id = timezone.id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
