{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'user_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_user') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT 
    {{etl_load_date()}},
    user_id,
    user_name,
    password,
    first_name,
    last_name,
    role.description as role,
    ssds_id,
    email,
    gender,
    social_network_id1,
    social_network_address1,
    social_network_id2,
    social_network_address2,
    social_network_id3,
    social_network_address3,
    social_network_id4,
    social_network_address4,
    photo_name,
    mobile_telephone,
    home_telephone,
    work_telephone,
    nationality.name as nationality,
    birth_date,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated,
    is_email_verified,
    center_id,
    territory_id,
    preferred_language_id
from
    ods_data as User
    Left Join (
        select
            id,
            description
        from
            {{ ref('ods_ls_role') }}
    ) as role
    ON User.role_id = role.id
    Left Join (
        select
            id,
            Name
        from
            {{ ref('ods_ls_nationality') }}
    ) as nationality
    on User.nationality_id = nationality.id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = User.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
