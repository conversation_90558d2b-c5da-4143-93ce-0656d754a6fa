import sys
import ast
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, lit, max
from awsglue.utils import getResolvedOptions
from pyspark.sql.functions import lower
from pyspark.sql.types import StringType
import boto3
import CloudOperations
from datetime import datetime

class DataLoader:
    def __init__(self, args, database_connection_values, spark):
        self.args = args
        self.database_connection_values = database_connection_values
        self.spark = spark
        
    def get_source_data(self, query):
    
        data = self.spark.read.format("jdbc") \
            .option("url", self.database_connection_values["url"]) \
            .option("query", query) \
            .option("user", self.database_connection_values["username"]) \
            .option("password", self.database_connection_values["password"]) \
            .option("driver", self.database_connection_values["driver"]) \
            .load()
        
        # Convert string columns to lowercase
        string_cols = [col_name for col_name, col_type in data.dtypes if col_type == 'string']
        lower_cols = [lower(data[col]).alias(col) for col in string_cols]
        non_string_cols = [data[col] for col in data.columns if col not in string_cols]
        data = data.select(non_string_cols + lower_cols)
        
        return data

    def save_data_as_table(self, data, table_name, table_path,mode):
        data.write.mode(mode).saveAsTable(table_name, path=table_path)

def pascal_to_snail(pascal_string):
    result = [pascal_string[0].lower()]

    for char in pascal_string[1:]:
        if char.isupper():
            result.extend(['_', char.lower()])
        else:
            result.append(char)

    return ''.join(result)

def max_date(data_loader,max_query):
    athena = boto3.client('athena')
    
    response = athena.start_query_execution(
        QueryString=max_query,
        QueryExecutionContext={
            'Database': data_loader.args['OdsDatabase']
        },
        ResultConfiguration={
            'OutputLocation': 's3://prod-corecourse/athena-results'  
        })
        
    while True:
        query_status = athena.get_query_execution(QueryExecutionId=response['QueryExecutionId'])
        state = query_status['QueryExecution']['Status']['State']
    
        if state in ['SUCCEEDED', 'FAILED', 'CANCELLED']:
            break
    if state == 'SUCCEEDED':
        # Get the results of the query
        result = athena.get_query_results(QueryExecutionId=response['QueryExecutionId'])
    
        # Extract and process the data from the results
        for row in result['ResultSet']['Rows']:
            columns = [col['VarCharValue'] for col in row['Data']]

        results = columns[0]
        print(results)
        max_date = results[:-3]
        
        return max_date
    
    else:
        raise 0
    

def load(data_loader):

    # Prepare the dynamic SQL query based on load_type
    if (data_loader.args['LoadType']) == 'Incremental':
        
        max_updated_date_query = """Select max({FilterColumn}) as {FilterColumn} from {schema}.{table}""".format(FilterColumn = pascal_to_snail(data_loader.args['FilterColumn']),schema = data_loader.args['OdsDatabase'],table = data_loader.args['OdsObject'])
        print("max query:", max_updated_date_query)
    
        # Get the updated cutoff date for incremental load
        cutoff_date_updated = max_date(data_loader,max_updated_date_query)
        # cutoff_date_updated = data_loader.spark.sql(max_updated_date_query).collect()[0][0].strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        # Prepare the dynamic SQL query for incremental refresh
        dynamic_sql_query = f"select * from \"{data_loader.args['Object']}\" where \"{data_loader.args['FilterColumn']}\" > '{cutoff_date_updated}'"
    else:
        dynamic_sql_query = f"select * from \"{data_loader.args['Object']}\""
        
    print(dynamic_sql_query)

    # Read data from the database using JDBC
    source_data = data_loader.get_source_data(dynamic_sql_query)

    # Get the count of rows in the source data
    count = source_data.count()
    print("This is the object:", data_loader.args["Object"])
    print("This is count of:", count)

    # Define the table name and path for saving the data
    table_name = f"{data_loader.args['AthenaDatabase']}.{data_loader.args['Object']}"
    table_path = f"s3://prod-corecourse/landing/{data_loader.args['AthenaDatabase']}/{data_loader.args['Object']}"

    # Save the source data as a table in Spark and overwrite the data to the existing table
    data_loader.save_data_as_table(source_data, table_name, table_path,"overwrite")
    
# def history_backup(data_loader):

#     query = """select * from {schema}.{table}""".format(FilterColumn = pascal_to_snail(data_loader.args['FilterColumn']),schema = data_loader.args['AthenaDatabase'],table = data_loader.args['Object'])
    
#     try :
#         source_data = data_loader.spark.sql(query)
        
#         if(source_data.count() > 0):
#             # Extract the timestamp as a string used for partition
#             timestamp_str = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
#             # Define the S3 bucket and path
#             history_s3_path = f"s3://prod-corecourse/history/{data_loader.args['HistDatabase']}/{data_loader.args['Object']}/load_date={timestamp_str}/"
#             # Write the DataFrame to Parquet in S3 with the timestamped folder
#             source_data.write.parquet(history_s3_path, mode="overwrite")
#         print('backup completed')
#     except Exception as e:
#         print('Error Message : ',e)

if __name__ == "__main__":
    # Create a SparkSession
    spark = SparkSession.builder.getOrCreate()
    spark.conf.set("spark.sql.legacy.parquet.int96RebaseModeInWrite", "LEGACY")

    # Get the command-line arguments
    args = getResolvedOptions(sys.argv, ["Object", "SecretManager", "FilterColumn", "LoadType", "DatabaseConnection", "AthenaDatabase","OdsObject","OdsDatabase","HistDatabase"])

    # Fetch database connection values from AWS Secret Manager
    fetch_database_connection = CloudOperations.SecretManager()
    database_connection_values = fetch_database_connection.GetSecret(args["SecretManager"], "eu-west-1")
    # print(database_connection_values)

    # Create a DataLoader object
    data_loader = DataLoader(args, database_connection_values, spark)
    # history_backup(data_loader)
    load(data_loader)