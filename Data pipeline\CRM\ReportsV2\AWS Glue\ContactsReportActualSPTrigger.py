import logging
import sys
from awsglue.utils import getResolvedOptions
sys.path.insert ( 0 , '/glue/lib/installation' )
keys = [ k for k in sys.modules.keys () if 'boto' in k ]
for k in keys:
    if 'boto' in k:
        del sys.modules [ k ]
import boto3
import CloudOperations
import DbOperations
import LogFileGeneration

Logs = LogFileGeneration.LogFile
s3_connect = CloudOperations.S3

config_info = getResolvedOptions(sys.argv, ['Bucket'])
Bucket = config_info['Bucket']

RedshiftExecute = DbOperations.Database
PreReqisiteQuery = "call hubspot_crm.sp_contactsreport();"
logging.warning(PreReqisiteQuery)
contactsStatement = "contactsreport execution"
TruncateExecute = RedshiftExecute.Execution('WriteTable', PreReqisiteQuery, contactsStatement)
StatementName = "actual table Execution"
ActualQuery = "call hubspot_crm.sp_actuals_table();"
logging.warning(ActualQuery)
actualsexecute = RedshiftExecute.Execution('WriteTable', ActualQuery, StatementName)
logging.warning(actualsexecute)
logging.warning("Stored Procedures completed")