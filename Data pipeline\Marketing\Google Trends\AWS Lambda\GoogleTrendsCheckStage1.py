import boto3
import json

Bucket = "google-trends-production"

# Creating an S3 resource object using Boto3
Boto3Resource = boto3.resource("s3")

# Creating an S3 bucket object using the S3 resource object
S3Bucket = Boto3Resource.Bucket(Bucket)


# Defining the Lambda function handler
def lambda_handler(event, context):

    # Getting the object for the execution check file from S3
    ExecutionCheckFilePath = Boto3Resource.Object(Bucket, 'ExecutionCheck.json')

    # Getting the content of the execution check file as a string
    ExecutionCheckFileContent = ExecutionCheckFilePath.get()['Body'].read().decode('utf-8')

    # Parsing the execution check file content as JSON
    ExecutionCheck = json.loads(ExecutionCheckFileContent)
    print(ExecutionCheck)

    # Constructing the folder path for the log files to check
    Folder = f"Logs/{ExecutionCheck['CycleId']}/Stage1"

    # Getting a list of file keys (names) in the S3 bucket for the log files in the specified folder
    FilesInS3 = [f.key.split(Folder + "/")[1] for f in S3Bucket.objects.filter(Prefix=Folder).all()]
    print(FilesInS3)
    print(len(FilesInS3))

    # Checking if the number of log files found in the specified folder is equal to 54
    if len(FilesInS3) != 54:
        print("Stage 1 not completed")
        # If the number of log files is not equal to 54, printing an error message and raising an exception
        raise Exception

    # If the number of log files is equal to 54, returning a success message
    return "Stage 1 Completed"
