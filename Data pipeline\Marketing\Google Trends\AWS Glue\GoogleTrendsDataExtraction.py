# Import necessary packages
import Packages

# Extract input arguments using getResolvedOptions() from the Packages library
Args = Packages.getResolvedOptions(Packages.sys.argv,
                                   ['Status', 'Stage', 'Operation', 'Territory', 'ServiceType', 'Competitors',
                                    'CycleId', 'CutoffDate', 'LoadType'])
Bucket = "google-trends-production"
S3 = Packages.CloudOperations.S3

# Extract input arguments into separate variables
Status = Args['Status']
Stage = Args['Stage']
Operation = Args['Operation']
Territory = Args['Territory']
ServiceType = Args['ServiceType']
Competitors = Packages.ast.literal_eval(Args['Competitors'])
Competitors = Competitors[0]
CycleId = Args['CycleId']
CutoffDate = Args['CutoffDate']
LoadType = Args['LoadType']

Packages.logging.warning("LoadType:'%s'", format(LoadType))
Packages.logging.warning("CutoffDate:'%s'", format(CutoffDate))

# Perform data extraction using the GoogleTrendsFramework module in the Packages library
DataRequest = Packages.GoogleTrendsFramework.Trends.DataExtractionProcess(Territory=Territory,
                                                                          Competitors=Competitors,
                                                                          ServiceType=ServiceType,
                                                                          CutoffDate=CutoffDate,
                                                                          LoadType=LoadType,
                                                                          Bucket=Bucket,
                                                                          CycleId=CycleId)
Packages.logging.warning("Summary:'%s'", format(DataRequest))

# Create a JSON object to store data extraction logs
Logs = {
    "Status": 200,
    "Stage": int(Stage),
    "CutoffDate": DataRequest['CutoffDate'],
    "LoadType": LoadType,
    "Operation": Operation,
    "Territory": Territory,
    "ServiceType": ServiceType,
    "Summary": str(DataRequest),
    "CycleId": CycleId
}
Packages.logging.warning("Logs:'%s'", format(Logs))

# Write the data extraction logs to Amazon S3
S3.WriteJsonFile(Bucket, f"Logs/{CycleId}/Stage{Stage}/{Territory}{ServiceType}.json", Logs)
