import sys
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark.context import Spark<PERSON>ontext
from awsglue.context import GlueContext
from awsglue.job import Job

args = getResolvedOptions(sys.argv, ['JOB_NAME'])
sc = SparkContext()
glueContext = GlueContext(sc)
spark = glueContext.spark_session
job = Job(glueContext)
job.init(args['JOB_NAME'], args)

# Script generated for node Google BigQuery
GoogleBigQuery_node1728022145571 = glueContext.create_dynamic_frame.from_options(connection_type="bigquery", connection_options={"connectionName": "Core Course BigQuery", "parentProject": "core-course-bigquery", "table": "dbt_prod.cue_card_events"}, transformation_ctx="GoogleBigQuery_node1728022145571")

# Script generated for node Amazon S3
AmazonS3_node1728022316283 = glueContext.write_dynamic_frame.from_options(frame=GoogleBigQuery_node1728022145571, connection_type="s3", format="json", connection_options={"path": "s3://dev-corecourse/data/bigquery_cue_card_events/", "partitionKeys": []}, transformation_ctx="AmazonS3_node1728022316283")

job.commit()