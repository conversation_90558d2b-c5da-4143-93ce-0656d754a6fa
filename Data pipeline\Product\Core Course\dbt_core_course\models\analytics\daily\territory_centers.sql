{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

SELECT territory.territory_reference_id                             as territory_reference_id
        , center.center_reference_id                                as center_reference_id
        , center.time_zone_id                                       as timezone
        , lower(center_group.finid)                                 as fin_id
        , lower(center_group.centerid)                              as center_id
        , territory.id                                              as ls_territory_id
        , center.id                                                 as ls_center_id
        , territory.name                                            as territory_name
        , center.name                                               as center_name
        , lower(territory_region.region)                            as region
        , lower(center_group.groupname)                             as group_name
        , case                                                       
            when center.name = 'global online center' then 'global online center'
            else 'other'
        	end                                                          as goc_local
        , center_group.matched                                      as matched

    FROM {{ref('dt_cc_territory')}} as territory
            LEFT JOIN {{ref('dt_cc_center')}} as center
                    ON center.territory_id = territory.id
            LEFT JOIN reference.territory_region as territory_region -- find out where reference is created
                    ON territory.territory_reference_id =
                        lower(territory_region.TerritoryReferenceId)
            LEFT JOIN {{ref('center_group')}} as center_group
                    ON lower(center_group.referencecenterid) =
                        center.center_reference_id;