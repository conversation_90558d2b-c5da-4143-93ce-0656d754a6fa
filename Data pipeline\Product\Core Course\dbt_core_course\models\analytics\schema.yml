version: 2

models:
  - name: activities
    description: >
      Consolidated view of all learning activities across multimedia and digital workbook platforms.
      Used for tracking student progress and engagement with learning content.
    columns:
      - name: activity_type
        description: Type of activity (multimedia or digital_workbook)
        tests:
          - accepted_values:
              values: ['multimedia', 'digital_workbook']
              severity: warn
      - name: activity_id
        description: Unique identifier for the activity
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: registration_id
        description: ID of the student registration
      - name: student_id
        description: ID of the student who completed the activity
        tests:
          - not_null:
              severity: error
      - name: content_item_id
        description: ID of the content item
        tests:
          - not_null:
              severity: error
      - name: description
        description: Description of the activity
      - name: center_reference_id
        description: Reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: start_date
        description: Date when the activity was started
      - name: completed_date
        description: Date when the activity was completed
        tests:
          - not_null:
              severity: warn
      - name: level
        description: Level of the content
        tests:
          - not_null:
              severity: error
      - name: unit
        description: Unit within the level
      - name: lesson
        description: Lesson within the unit
      - name: mini_cycles
        description: Mini-cycle number
      - name: mini_cycle_stage
        description: Stage within the mini-cycle
      - name: workbook_activity
        description: Flag or identifier for workbook activities
      - name: score
        description: Score achieved on the activity
      - name: duration_mins
        description: Duration of the activity in minutes
      - name: duration_cap_mins
        description: Capped duration of the activity in minutes (removes outliers)
      - name: percentile_25_duration_mins
        description: 25th percentile duration for this activity type
      - name: median_duration_mins
        description: Median duration for this activity type
      - name: percentile_75_duration_mins
        description: 75th percentile duration for this activity type

  - name: conversation_ai
    description: >
      Aggregated data about AI-powered conversations between users and the system.
      Used for analyzing conversation patterns, costs, and effectiveness.
    columns:
      - name: chat_id
        description: Unique identifier for the chat session
      - name: total_no_of_interactions
        description: Total number of interactions in the conversation
      - name: user_id
        description: ID of the user participating in the conversation
      - name: contract_id
        description: ID of the contract associated with the user
      - name: content_id
        description: ID of the content being discussed
      - name: start_date
        description: UTC timestamp when the conversation started
      - name: end_date
        description: UTC timestamp when the conversation ended
      - name: chat_start_date
        description: Local timestamp when the conversation started
      - name: chat_end_date
        description: Local timestamp when the conversation ended
      - name: total_cost
        description: Total cost of the conversation
      - name: chat_ended
        description: Boolean flag indicating if the chat has ended
      - name: total_input_tokens
        description: Total number of input tokens used
      - name: total_output_tokens
        description: Total number of output tokens generated
      - name: total_tokens
        description: Total number of tokens (input + output)
      - name: chat_duration_seconds
        description: Duration of the chat in seconds
      - name: total_messages
        description: Total number of messages exchanged
      - name: user_messages
        description: Number of messages sent by the user
      - name: assistant_messages
        description: Number of messages sent by the assistant
      - name: understanding_gap_count
        description: Count of instances where the AI didn't understand the user

  - name: speaking_ai_beta
    description: >
      Aggregated data about AI-powered speaking practice sessions.
      Used for analyzing speaking practice patterns, costs, and effectiveness.
    columns:
      - name: chat_id
        description: Unique identifier for the chat session
      - name: total_no_of_interactions
        description: Total number of interactions in the session
      - name: user_id
        description: ID of the user participating in the session
      - name: user_type
        description: Type of user (e.g., student, prospect)
      - name: level
        description: Language proficiency level for the session
      - name: feedback
        description: Feedback provided by the user
      - name: duration
        description: Duration of the session
      - name: start_date
        description: UTC timestamp when the session started
      - name: end_date
        description: UTC timestamp when the session ended
      - name: completed_date
        description: UTC timestamp when the feedback was completed
      - name: chat_start_date
        description: Local timestamp when the session started
      - name: chat_end_date
        description: Local timestamp when the session ended
      - name: local_completed_date
        description: Local timestamp when the feedback was completed
      - name: total_cost
        description: Total cost of the session
      - name: chat_ended
        description: Boolean flag indicating if the chat has ended
      - name: total_input_tokens
        description: Total number of input tokens used
      - name: total_output_tokens
        description: Total number of output tokens generated
      - name: total_tokens
        description: Total number of tokens (input + output)
      - name: chat_duration_seconds
        description: Duration of the chat in seconds

  - name: contracts
    description: >
      Consolidated view of all contracts with related information.
      Used for contract analysis, financial reporting, and student tracking.
    columns:
      - name: contract_id
        description: Unique identifier for the contract
        tests:
          - not_null:
              severity: error
          - unique:
              severity: warn
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: student_id
        description: ID of the student associated with the contract
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: warn
      - name: center_id
        description: ID of the center associated with the contract
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: territory_id
        description: ID of the territory associated with the center
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: contract_number
        description: Contract number
      - name: crm_contract_number
        description: Contract number in the CRM system
      - name: status
        description: Current status of the contract
      - name: start_date
        description: Start date of the contract
        tests:
          - not_null:
              severity: error
      - name: end_date
        description: End date of the contract
        tests:
          - not_null:
              severity: error
      - name: created_date
        description: Date when the contract was created
        tests:
          - not_null:
              severity: error
      - name: sale_date
        description: Date when the contract was sold
      - name: price
        description: Price of the contract
      - name: service_type
        description: Type of service provided under the contract
        tests:
          - accepted_values:
              values: ['standard', 'vip']
              severity: error
      - name: location
        description: Location where the service is provided
        tests:
          - accepted_values:
              values: ['incenter', 'outcenter']
              severity: error
      - name: class_access_type
        description: Type of class access granted by the contract
      - name: product_type
        description: Type of product associated with the contract
        tests:
          - accepted_values:
              values: ['other', 'core course', 'market leader', 'test prep', 'business partner', 'd2c']
              severity: error
      - name: contract_type
        description: Type of contract
      - name: is_membership
        description: Flag indicating if the contract is a membership
      - name: is_promotional
        description: Flag indicating if the contract is promotional
      - name: is_transfer_in
        description: Flag indicating if the contract is a transfer in
      - name: is_cross_center_booking
        description: Flag indicating if the contract allows cross-center booking
      - name: is_teen
        description: Flag indicating if the contract is for a teen student

  - name: bookings
    description: >
      Consolidated view of all class bookings with related information.
      Used for attendance tracking, class utilization, and student engagement analysis.
    columns:
      - name: booking_id
        description: Unique identifier for the booking
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: class_id
        description: ID of the class that was booked
        tests:
          - not_null:
              severity: error
      - name: student_id
        description: ID of the student who booked the class
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: center_id
        description: ID of the center where the class is held
      - name: student_center_id
        description: ID of the center where the student is registered
      - name: class_date
        description: Date of the class
      - name: class_type
        description: Type of class (encounter, conversation class, etc.)
      - name: book_mode
        description: Mode of booking (book or standby)
        tests:
          - accepted_values:
              values: ['book', 'standby']
              severity: error
      - name: book_date
        description: Date when the booking was made
      - name: attended_flag
        description: Flag indicating if the student attended the class
      - name: billable_flag
        description: Flag indicating if the booking is billable
      - name: standby_flag
        description: Flag indicating if the booking is on standby
      - name: cancelled_flag
        description: Flag indicating if the booking was cancelled
      - name: late_cancelled_flag
        description: Flag indicating if the booking was cancelled late
      - name: no_show_flag
        description: Flag indicating if the student did not show up
      - name: rescheduled_flag
        description: Flag indicating if the booking was rescheduled
      - name: ready_flag
        description: Flag indicating if the student was ready for the class
      - name: mm_ready_flag
        description: Flag indicating if the student completed multimedia preparation
      - name: wb_ready_flag
        description: Flag indicating if the student completed workbook preparation
      - name: booked_by_role
        description: Role of the user who made the booking
      - name: booking_cancelled_by_role
        description: Role of the user who cancelled the booking
      - name: booking_created_datetime
        description: Timestamp when the booking was created
        tests:
          - not_null:
              severity: error
      - name: booking_cancelled_datetime
        description: Timestamp when the booking was cancelled
      - name: lead_booked_datetime
        description: Lead time between booking and class

  - name: encounter_readiness
    description: >
      Tracks student readiness for encounters based on completed activities.
      Used for analyzing student preparation patterns and readiness for classes.
    columns:
      - name: student_id
        description: ID of the student
      - name: level
        description: Level of the content
      - name: unit
        description: Unit within the level
      - name: mm_ready
        description: Date when the student was ready for multimedia content (97% complete)
      - name: mm_complete_66
        description: Date when the student completed 66% of multimedia content
      - name: mm_complete_33
        description: Date when the student completed 33% of multimedia content
      - name: mm_complete_0
        description: Date when the student started multimedia content
      - name: wb_ready_80
        description: Date when the student was ready for workbook content (80% complete)
      - name: wb_ready_60
        description: Date when the student completed 60% of workbook content
      - name: wb_ready_40
        description: Date when the student completed 40% of workbook content
      - name: wb_ready_20
        description: Date when the student completed 20% of workbook content
      - name: wb_ready_0
        description: Date when the student started workbook content

  - name: levels_started
    description: >
      Tracks when students start new levels in their learning journey.
      Used for progression analysis and level completion tracking.
    columns:
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: digital_books_log_id
        description: ID of the digital books log record
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: registration_id
        description: ID of the student registration
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: date_granted
        description: UTC timestamp when the level was granted
        tests:
          - not_null:
              severity: error
      - name: local_date_granted
        description: Local timestamp when the level was granted
        tests:
          - not_null:
              severity: error
      - name: category
        description: Category of the level
        tests:
          - accepted_values:
              values: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20']
              severity: warn
      - name: category_type
        description: Type of category
        tests:
          - accepted_values:
              values: ['level']
              severity: warn
      - name: unlock_type
        description: Type of unlock (standard, free, etc.)
        tests:
          - accepted_values:
              values: ['standard', 'promo']
              severity: warn
      - name: operation_type
        description: Type of operation (bill, refund, etc.)
        tests:
          - accepted_values:
              values: ['refund', 'bill']
              severity: warn
      - name: workbook_type
        description: Type of workbook
        tests:
          - accepted_values:
              values: ['digital', 'printed']
              severity: warn
      - name: is_teen
        description: Flag indicating if the student is a teen
        tests:
          - not_null:
              severity: warn
      - name: config_value
        description: Configuration value
      - name: sequence
        description: Sequence number
        tests:
          - not_null:
              severity: warn
      - name: is_restart
        description: Flag indicating if this is a restart
        tests:
          - not_null:
              severity: warn
      - name: first_later
        description: Indicates if this is a first or later level
      - name: levels_started
        description: Count of levels started (1 for bill, -1 for refund)

  - name: novu_messages
    description: >
      Consolidated view of all notification messages sent through the Novu service.
      Used for analyzing notification effectiveness and engagement.
    columns:
      - name: message_id
        description: Unique identifier for the message
      - name: student_reference_id
        description: External reference ID for the student
      - name: channel
        description: Communication channel used (email, SMS, push, etc.)
      - name: title
        description: Title or subject of the message
      - name: notification_id
        description: ID of the notification template
      - name: notification_type
        description: Type of notification
      - name: variant_id
        description: ID of the message template variant
      - name: variant
        description: Name of the variant
      - name: created_at
        description: Timestamp when the message was created
      - name: updated_at
        description: Timestamp when the message was last updated

  - name: user_onboarding
    description: >
      Tracks user progress through the onboarding process.
      Used for analyzing onboarding effectiveness and identifying drop-off points.
    columns:
      - name: user_reference_id
        description: External reference ID for the user
      - name: combined_feature_step
        description: Combined feature and step identifier
      - name: feature_type
        description: Type of onboarding feature
      - name: step_type
        description: Type of onboarding step
      - name: Feature completed
        description: Flag indicating if the feature was completed
      - name: Feature skipped
        description: Flag indicating if the feature was skipped
      - name: step_is_completed
        description: Flag indicating if the step was completed
      - name: step_is_skipped
        description: Flag indicating if the step was skipped
      - name: step_updated_at
        description: Timestamp when the step was last updated
      - name: created_at
        description: Timestamp when the record was created
      - name: updated_at
        description: Timestamp when the record was last updated

  - name: users
    description: >
      Consolidated view of all users in the system.
      Used for user management, authentication, and profile information.
    columns:
      - name: user_id
        description: Unique identifier for the user
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: center_id
        description: ID of the center where the user is registered
      - name: territory_id
        description: ID of the territory associated with the user
      - name: is_active
        description: Flag indicating if the user is active
      - name: user_name
        description: Username for system login
        tests:
          - not_null:
              severity: warn
      - name: first_name
        description: User's first name
      - name: last_name
        description: User's last name
      - name: birth_date
        description: User's date of birth
      - name: email
        description: User's email address
      - name: created_at
        description: Timestamp when the user record was created
        tests:
          - not_null:
              severity: warn
      - name: updated_at
        description: Timestamp when the user record was last updated
      - name: deleted_at
        description: Timestamp when the user record was deleted
      - name: mobile_telephone
        description: User's mobile telephone number
      - name: role
        description: User's role in the system
        tests:
          - accepted_values:
              values: ['student', 'staff', 'teacher', 'consultant', 'admin', 'prospect']
              severity: warn
      - name: role_type
        description: Type of role
      - name: center_reference_id
        description: External reference ID for the center

  - name: activity_multimedia
    description: >
      Raw multimedia activity data from the Learning Service.
      Used as a source for consolidated activity reporting and analysis.
    columns:
      - name: activity_id
        description: Unique identifier for the activity
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: registration_id
        description: ID of the student registration
        tests:
          - not_null:
              severity: error
      - name: student_id
        description: ID of the student who completed the activity
        tests:
          - not_null:
              severity: error
      - name: teacher_id
        description: ID of the teacher associated with the activity
      - name: ref_class_id
        description: Reference ID for the class
      - name: class_id
        description: ID of the class associated with the activity
      - name: content_item_id
        description: ID of the content item
        tests:
          - not_null:
              severity: error
      - name: created_date
        description: UTC timestamp when the activity was created
      - name: local_created_date
        description: Local timestamp when the activity was created
      - name: last_updated_date
        description: UTC timestamp when the activity was last updated
      - name: local_last_updated_date
        description: Local timestamp when the activity was last updated
      - name: started_date
        description: UTC timestamp when the activity was started
      - name: local_started_date
        description: Local timestamp when the activity was started
      - name: completed_date
        description: UTC timestamp when the activity was completed
      - name: local_completed_date
        description: Local timestamp when the activity was completed
      - name: level
        description: Level of the content
        tests:
          - not_null:
              severity: error
      - name: unit
        description: Unit within the level
      - name: lesson
        description: Lesson within the unit
      - name: mini_cycle
        description: Mini-cycle number
      - name: mini_cycle_stage
        description: Stage within the mini-cycle
      - name: comment
        description: Comments or notes about the activity
      - name: content_item
        description: Name or description of the content item
      - name: content_item_type
        description: Type of content item
      - name: content_item_result_type
        description: Result type of the content item (passed, failed, etc.)
      - name: display_on_list
        description: Flag indicating if the activity should be displayed on lists
      - name: to_process_in_background
        description: Flag indicating if the activity should be processed in background
      - name: study_mode
        description: Study mode for the activity
      - name: activity_captured_type
        description: Type of activity capture
      - name: total_questions
        description: Total number of questions in the activity
      - name: total_question_answered
        description: Total number of questions answered
      - name: score
        description: Score achieved on the activity
      - name: duration_secs
        description: Duration of the activity in seconds
      - name: duration_mins
        description: Duration of the activity in minutes
      - name: percentile_25_duration_mins
        description: 25th percentile duration for this activity type
      - name: median_duration_mins
        description: Median duration for this activity type
      - name: percentile_75_duration_mins
        description: 75th percentile duration for this activity type

  - name: activity_workbook
    description: >
      Raw digital workbook activity data from the Learning Service.
      Used as a source for consolidated activity reporting and analysis.
    columns:
      - name: activity_id
        description: Unique identifier for the activity
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: registration_id
        description: ID of the student registration
        tests:
          - not_null:
              severity: error
      - name: student_id
        description: ID of the student who completed the activity
        tests:
          - not_null:
              severity: error
      - name: content_item_id
        description: ID of the content item
        tests:
          - not_null:
              severity: error
      - name: created_date
        description: UTC timestamp when the activity was created
      - name: local_created_date
        description: Local timestamp when the activity was created
      - name: last_updated_date
        description: UTC timestamp when the activity was last updated
      - name: local_last_updated_date
        description: Local timestamp when the activity was last updated
      - name: started_date
        description: UTC timestamp when the activity was started
      - name: local_started_date
        description: Local timestamp when the activity was started
      - name: completed_date
        description: UTC timestamp when the activity was completed
      - name: local_completed_date
        description: Local timestamp when the activity was completed
      - name: level
        description: Level of the content
        tests:
          - not_null:
              severity: error
      - name: unit
        description: Unit within the level
      - name: lesson
        description: Lesson within the unit
      - name: mini_cycle
        description: Mini-cycle number
      - name: mini_cycle_stage
        description: Stage within the mini-cycle
      - name: content_item
        description: Name or description of the content item
      - name: content_item_type
        description: Type of content item
      - name: content_item_result_type
        description: Result type of the content item (passed, failed, etc.)
      - name: score
        description: Score achieved on the activity
      - name: duration_secs
        description: Duration of the activity in seconds
      - name: duration_mins
        description: Duration of the activity in minutes
      - name: percentile_25_duration_mins
        description: 25th percentile duration for this activity type
      - name: median_duration_mins
        description: Median duration for this activity type
      - name: percentile_75_duration_mins
        description: 75th percentile duration for this activity type

  - name: activity_multimedia
    description: >
      Raw multimedia activity data from the Learning Service.
      Used for tracking individual multimedia learning activities and progress.
    columns:
      - name: activity_id
        description: Unique identifier for the activity
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: registration_id
        description: ID of the student registration
      - name: student_id
        description: ID of the student who completed the activity
        tests:
          - not_null:
              severity: error
      - name: teacher_id
        description: ID of the teacher (if applicable)
      - name: ref_class_id
        description: Reference class ID
      - name: class_id
        description: ID of the class (if applicable)
      - name: content_item_id
        description: ID of the content item
        tests:
          - not_null:
              severity: error
      - name: created_date
        description: UTC timestamp when the activity was created
      - name: local_created_date
        description: Local timestamp when the activity was created
      - name: last_updated_date
        description: UTC timestamp when the activity was last updated
      - name: local_last_updated_date
        description: Local timestamp when the activity was last updated
      - name: started_date
        description: UTC timestamp when the activity was started
      - name: local_started_date
        description: Local timestamp when the activity was started
      - name: completed_date
        description: UTC timestamp when the activity was completed
      - name: local_completed_date
        description: Local timestamp when the activity was completed
      - name: level
        description: Level of the content
      - name: unit
        description: Unit within the level
      - name: lesson
        description: Lesson within the unit
      - name: mini_cycle
        description: Mini-cycle number
      - name: mini_cycle_stage
        description: Stage within the mini-cycle
      - name: comment
        description: Comments or notes about the activity
      - name: content_item
        description: Content item description
      - name: content_item_type
        description: Type of content item
      - name: content_item_result_type
        description: Type of content item result
      - name: display_on_list
        description: Flag indicating if the activity should be displayed on lists
      - name: to_process_in_background
        description: Flag indicating if the activity should be processed in background
      - name: study_mode
        description: Study mode used for the activity
      - name: activity_captured_type
        description: Type of activity capture
      - name: total_questions
        description: Total number of questions in the activity
      - name: total_question_answered
        description: Total number of questions answered
      - name: score
        description: Score achieved on the activity
      - name: duration_secs
        description: Duration of the activity in seconds
      - name: duration_mins
        description: Duration of the activity in minutes
      - name: percentile_25_duration_mins
        description: 25th percentile duration for this activity type
      - name: median_duration_mins
        description: Median duration for this activity type
      - name: percentile_75_duration_mins
        description: 75th percentile duration for this activity type

  - name: activity_workbook
    description: >
      Raw digital workbook activity data from the Learning Service.
      Used for tracking individual workbook learning activities and progress.
    columns:
      - name: activity_id
        description: Unique identifier for the activity
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: registration_id
        description: ID of the student registration
      - name: student_id
        description: ID of the student who completed the activity
        tests:
          - not_null:
              severity: error
      - name: teacher_id
        description: ID of the teacher (if applicable)
      - name: ref_class_id
        description: Reference class ID
      - name: class_id
        description: ID of the class (if applicable)
      - name: content_item_id
        description: ID of the content item
        tests:
          - not_null:
              severity: error
      - name: created_date
        description: UTC timestamp when the activity was created
      - name: local_created_date
        description: Local timestamp when the activity was created
      - name: last_updated_date
        description: UTC timestamp when the activity was last updated
      - name: local_last_updated_date
        description: Local timestamp when the activity was last updated
      - name: started_date
        description: UTC timestamp when the activity was started
      - name: local_started_date
        description: Local timestamp when the activity was started
      - name: completed_date
        description: UTC timestamp when the activity was completed
      - name: local_completed_date
        description: Local timestamp when the activity was completed
      - name: level
        description: Level of the content
      - name: unit
        description: Unit within the level
      - name: lesson
        description: Lesson within the unit
      - name: mini_cycle
        description: Mini-cycle number
      - name: mini_cycle_stage
        description: Stage within the mini-cycle
      - name: comment
        description: Comments or notes about the activity
      - name: content_item
        description: Content item description
      - name: content_item_type
        description: Type of content item
      - name: content_item_result_type
        description: Type of content item result
      - name: display_on_list
        description: Flag indicating if the activity should be displayed on lists
      - name: to_process_in_background
        description: Flag indicating if the activity should be processed in background
      - name: study_mode
        description: Study mode used for the activity
      - name: activity_captured_type
        description: Type of activity capture
      - name: total_questions
        description: Total number of questions in the activity
      - name: total_question_answered
        description: Total number of questions answered
      - name: score
        description: Score achieved on the activity
      - name: duration_secs
        description: Duration of the activity in seconds
      - name: duration_mins
        description: Duration of the activity in minutes
      - name: percentile_25_duration_mins
        description: 25th percentile duration for this activity type
      - name: median_duration_mins
        description: Median duration for this activity type
      - name: percentile_75_duration_mins
        description: 75th percentile duration for this activity type

  - name: b2b_contracts
    description: >
      Specialized view of contracts for B2B (Business-to-Business) customers.
      Used for B2B-specific reporting and analysis.
    columns:
      - name: contract_id
        description: Unique identifier for the contract
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: student_id
        description: ID of the student associated with the contract
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
      - name: center_id
        description: ID of the center associated with the contract
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: territory_id
        description: ID of the territory associated with the center
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: contract_number
        description: Contract number
      - name: crm_contract_number
        description: Contract number in the CRM system
      - name: status
        description: Current status of the contract
      - name: start_date
        description: Start date of the contract
        tests:
          - not_null:
              severity: error
      - name: end_date
        description: End date of the contract
        tests:
          - not_null:
              severity: error
      - name: created_date
        description: Date when the contract was created
        tests:
          - not_null:
              severity: error
      - name: sale_date
        description: Date when the contract was sold
      - name: price
        description: Price of the contract
      - name: service_type
        description: Type of service provided under the contract
        tests:
          - accepted_values:
              values: ['standard', 'vip']
              severity: error
      - name: location
        description: Location where the service is provided
        tests:
          - accepted_values:
              values: ['incenter', 'outcenter']
              severity: error
      - name: class_access_type
        description: Type of class access granted by the contract
      - name: product_type
        description: Type of product associated with the contract
      - name: contract_type
        description: Type of contract
      - name: is_membership
        description: Flag indicating if the contract is a membership
      - name: is_promotional
        description: Flag indicating if the contract is promotional
      - name: is_transfer_in
        description: Flag indicating if the contract is a transfer in
      - name: is_cross_center_booking
        description: Flag indicating if the contract allows cross-center booking
      - name: is_teen
        description: Flag indicating if the contract is for a teen student

  - name: bookmark
    description: >
      Tracks student bookmarks and progress through learning content.
      Used for analyzing student progression and content completion patterns.
    columns:
      - name: student_id
        description: ID of the student
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: registration_id
        description: ID of the student registration
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: date
        description: Date of the bookmark record
        tests:
          - not_null:
              severity: error
      - name: mm_level
        description: Multimedia level bookmark
      - name: mm_unit
        description: Multimedia unit bookmark
      - name: mm_lesson
        description: Multimedia lesson bookmark
      - name: wb_level
        description: Workbook level bookmark
      - name: wb_unit
        description: Workbook unit bookmark
      - name: wb_lesson
        description: Workbook lesson bookmark
      - name: dbt_unique_id
        description: Unique identifier for the record in DBT
        tests:
          - unique:
              severity: error

  - name: class_bookings
    description: >
      Detailed view of class bookings with enhanced booking information.
      Used for class scheduling analysis and booking pattern tracking.
    columns:
      - name: booking_id
        description: Unique identifier for the booking
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: class_id
        description: ID of the class that was booked
        tests:
          - not_null:
              severity: error
      - name: student_id
        description: ID of the student who booked the class
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: center_id
        description: ID of the center where the class is held
      - name: student_center_id
        description: ID of the center where the student is registered
      - name: class_date
        description: Date of the class
        tests:
          - not_null:
              severity: error
      - name: class_type
        description: Type of class (encounter, conversation class, etc.)
      - name: book_mode
        description: Mode of booking (book or standby)
        tests:
          - accepted_values:
              values: ['book', 'standby']
              severity: error
      - name: book_date
        description: Date when the booking was made
      - name: attended_flag
        description: Flag indicating if the student attended the class
      - name: billable_flag
        description: Flag indicating if the booking is billable
      - name: standby_flag
        description: Flag indicating if the booking is on standby
      - name: cancelled_flag
        description: Flag indicating if the booking was cancelled
      - name: late_cancelled_flag
        description: Flag indicating if the booking was cancelled late
      - name: no_show_flag
        description: Flag indicating if the student did not show up
      - name: rescheduled_flag
        description: Flag indicating if the booking was rescheduled
      - name: ready_flag
        description: Flag indicating if the student was ready for the class
      - name: mm_ready_flag
        description: Flag indicating if the student completed multimedia preparation
      - name: wb_ready_flag
        description: Flag indicating if the student completed workbook preparation
      - name: booked_by_role
        description: Role of the user who made the booking
      - name: booking_cancelled_by_role
        description: Role of the user who cancelled the booking
      - name: booking_created_datetime
        description: Timestamp when the booking was created
        tests:
          - not_null:
              severity: error
      - name: booking_cancelled_datetime
        description: Timestamp when the booking was cancelled
      - name: lead_booked_datetime
        description: Lead time between booking and class

  - name: classes
    description: >
      Comprehensive view of all classes with detailed scheduling and capacity information.
      Used for class management, scheduling analysis, and capacity planning.
    columns:
      - name: class_id
        description: Unique identifier for the class
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: center_id
        description: ID of the center where the class is held
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: territory_id
        description: ID of the territory associated with the center
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: class_teacher_user_reference_id
        description: Reference ID for the teacher of the class
      - name: class_local_start_date
        description: Local start date and time of the class
        tests:
          - not_null:
              severity: error
      - name: class_date
        description: Date of the class
        tests:
          - not_null:
              severity: error
      - name: class_hour_band
        description: Hour band of the class (e.g., 9-10, 10-11)
      - name: time_of_day
        description: Time of day classification (morning, afternoon, evening)
      - name: class_date_hour_band
        description: Combined date and hour band
      - name: class_code
        description: Code identifier for the class
      - name: class_type
        description: Type of class (encounter, conversation class, etc.)
      - name: class_technology_type
        description: Technology used for the class
      - name: class_type_category
        description: Category of the class type
      - name: class_service_type
        description: Service type of the class
        tests:
          - not_null:
              severity: error
      - name: number_of_seats
        description: Number of available seats in the class
        tests:
          - not_null:
              severity: error
      - name: class_number_of_seats_in_stand_by
        description: Number of standby seats available
      - name: number_of_students
        description: Number of students booked in the class
      - name: categories_abbreviations
        description: Abbreviations of categories associated with the class
      - name: class_online_flag
        description: Flag indicating if the class is online
      - name: class_b2b_flag
        description: Flag indicating if the class is for B2B students
      - name: teen_flag
        description: Flag indicating if the class is for teen students
        tests:
         - accepted_values:
              values: ['Adults', 'Teens']
              severity: error
      - name: class_cancelled_flag
        description: Flag indicating if the class is cancelled
      - name: class_type_billable_flag
        description: Flag indicating if the class type is billable
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error

  - name: contracts_changes
    description: >
      Tracks changes to contract attributes over time.
      Used for analyzing contract modification patterns and historical changes.
    columns:
      - name: contract_id
        description: Unique identifier for the contract
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: student_id
        description: ID of the student associated with the contract
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
      - name: center_id
        description: ID of the center associated with the contract
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: change_date
        description: Date when the change occurred
        tests:
          - not_null:
              severity: error
      - name: change_type
        description: Type of change made to the contract
        tests:
          - not_null:
              severity: error
      - name: old_value
        description: Previous value before the change
      - name: new_value
        description: New value after the change
      - name: changed_by
        description: User who made the change
      - name: change_reason
        description: Reason for the change

  - name: contracts_product_changes
    description: >
      Tracks changes to contract product types over time.
      Used for analyzing product migration patterns and contract evolution.
    columns:
      - name: contract_id
        description: Unique identifier for the contract
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: student_id
        description: ID of the student associated with the contract
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
      - name: center_id
        description: ID of the center associated with the contract
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: change_date
        description: Date when the product change occurred
        tests:
          - not_null:
              severity: error
      - name: old_product_type
        description: Previous product type before the change
      - name: new_product_type
        description: New product type after the change
        tests:
          - not_null:
              severity: error
      - name: changed_by
        description: User who made the product change
      - name: change_reason
        description: Reason for the product change

  - name: novu_results
    description: >
      Detailed results from Novu notification campaigns with A/B testing metrics.
      Used for analyzing notification effectiveness and campaign performance.
    columns:
      - name: campaign
        description: Name of the notification campaign
        tests:
          - not_null:
              severity: error
      - name: channel
        description: Communication channel used (email, SMS, push, etc.)
        tests:
          - not_null:
              severity: error
      - name: message
        description: Message content or description
        tests:
          - not_null:
              severity: error
      - name: date
        description: Date of the notification
        tests:
          - not_null:
              severity: error
      - name: test_start_date
        description: Start date of the A/B test
      - name: test_end_date
        description: End date of the A/B test
      - name: outcome_1_name
        description: Name of the first outcome metric
      - name: outcome_2_name
        description: Name of the second outcome metric
      - name: outcome_3_name
        description: Name of the third outcome metric
      - name: outcome_4_name
        description: Name of the fourth outcome metric
      - name: outcome_5_name
        description: Name of the fifth outcome metric
      - name: outcome_6_name
        description: Name of the sixth outcome metric
      - name: total_messages_a
        description: Total messages sent for treatment A
      - name: total_messages_b
        description: Total messages sent for treatment B
      - name: outcome_1_count_a
        description: Count of outcome 1 for treatment A
      - name: outcome_1_count_b
        description: Count of outcome 1 for treatment B
      - name: outcome_2_count_a
        description: Count of outcome 2 for treatment A
      - name: outcome_2_count_b
        description: Count of outcome 2 for treatment B
      - name: outcome_3_count_a
        description: Count of outcome 3 for treatment A
      - name: outcome_3_count_b
        description: Count of outcome 3 for treatment B
      - name: outcome_4_count_a
        description: Count of outcome 4 for treatment A
      - name: outcome_4_count_b
        description: Count of outcome 4 for treatment B
      - name: outcome_5_count_a
        description: Count of outcome 5 for treatment A
      - name: outcome_5_count_b
        description: Count of outcome 5 for treatment B
      - name: outcome_6_count_a
        description: Count of outcome 6 for treatment A
      - name: outcome_6_count_b
        description: Count of outcome 6 for treatment B

  - name: placement_prospects
    description: >
      Information about prospects who have taken placement tests.
      Used for analyzing prospect conversion and placement test effectiveness.
    columns:
      - name: prospect_id
        description: Unique identifier for the prospect
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: territory_id
        description: ID of the territory associated with the center
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: placement_test_date
        description: Date when the placement test was taken
        tests:
          - not_null:
              severity: error
      - name: placement_test_score
        description: Score achieved on the placement test
      - name: placement_test_entry_point
        description: Entry point determined by the placement test
      - name: start_level
        description: Recommended start level based on placement test
      - name: start_level_category
        description: Category of the start level (beginner, intermediate, advanced)
      - name: prospect_status
        description: Current status of the prospect
      - name: converted_to_student
        description: Flag indicating if the prospect converted to a student
      - name: conversion_date
        description: Date when the prospect converted to a student
      - name: first_name
        description: Prospect's first name
      - name: last_name
        description: Prospect's last name
      - name: email
        description: Prospect's email address
      - name: mobile_telephone
        description: Prospect's mobile telephone number
      - name: birth_date
        description: Prospect's date of birth
      - name: created_at
        description: Timestamp when the prospect record was created
        tests:
          - not_null:
              severity: error

  - name: placement_test_score
    description: >
      Detailed placement test scores and results for prospects.
      Used for analyzing placement test performance and accuracy.
    columns:
      - name: prospect_id
        description: Unique identifier for the prospect
        tests:
          - not_null:
              severity: error
      - name: placement_test_id
        description: Unique identifier for the placement test
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: test_date
        description: Date when the test was taken
        tests:
          - not_null:
              severity: error
      - name: test_start_time
        description: Time when the test was started
      - name: test_end_time
        description: Time when the test was completed
      - name: test_duration_minutes
        description: Duration of the test in minutes
      - name: total_questions
        description: Total number of questions in the test
        tests:
          - not_null:
              severity: error
      - name: questions_answered
        description: Number of questions answered
      - name: correct_answers
        description: Number of correct answers
      - name: incorrect_answers
        description: Number of incorrect answers
      - name: skipped_questions
        description: Number of questions skipped
      - name: raw_score
        description: Raw score achieved on the test
      - name: percentage_score
        description: Percentage score achieved on the test
      - name: entry_point
        description: Entry point determined by the test score
      - name: recommended_level
        description: Recommended level based on the test score
      - name: test_version
        description: Version of the placement test
      - name: test_language
        description: Language of the placement test
      - name: test_completed
        description: Flag indicating if the test was completed
        tests:
          - accepted_values:
              values: [true, false]
              quote: false
              severity: error

  - name: study_events
    description: >
      Tracks various study-related events and milestones for students.
      Used for analyzing student engagement patterns and study behavior.
    columns:
      - name: event_id
        description: Unique identifier for the study event
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: student_id
        description: ID of the student associated with the event
        tests:
          - not_null:
              severity: error
      - name: student_reference_id
        description: External reference ID for the student
        tests:
          - not_null:
              severity: error
      - name: contract_reference_id
        description: External reference ID for the contract
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: event_type
        description: Type of study event (login, activity_start, level_complete, etc.)
        tests:
          - not_null:
              severity: error
      - name: event_date
        description: Date when the event occurred
        tests:
          - not_null:
              severity: error
      - name: event_timestamp
        description: Timestamp when the event occurred
        tests:
          - not_null:
              severity: error
      - name: local_event_timestamp
        description: Local timestamp when the event occurred
      - name: event_details
        description: Additional details about the event
      - name: level
        description: Level associated with the event (if applicable)
      - name: unit
        description: Unit associated with the event (if applicable)
      - name: lesson
        description: Lesson associated with the event (if applicable)
      - name: content_item_id
        description: ID of the content item associated with the event
      - name: duration_minutes
        description: Duration of the event in minutes (if applicable)
      - name: score
        description: Score achieved in the event (if applicable)
      - name: device_type
        description: Type of device used for the event
      - name: platform
        description: Platform used for the event (web, mobile, etc.)
      - name: session_id
        description: Session ID associated with the event

  - name: territory_centers
    description: >
      Mapping between territories and centers with geographical information.
      Used for territorial analysis and center management.
    columns:
      - name: territory_id
        description: Unique identifier for the territory
        tests:
          - not_null:
              severity: error
      - name: territory_reference_id
        description: External reference ID for the territory
        tests:
          - not_null:
              severity: error
      - name: territory_name
        description: Name of the territory
        tests:
          - not_null:
              severity: error
      - name: territory_code
        description: Code identifier for the territory
      - name: region
        description: Region where the territory is located
      - name: country
        description: Country where the territory is located
        tests:
          - not_null:
              severity: error
      - name: country_code
        description: ISO country code
      - name: currency
        description: Currency used in the territory
      - name: time_zone
        description: Time zone of the territory
      - name: center_id
        description: Unique identifier for the center
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: External reference ID for the center
        tests:
          - not_null:
              severity: error
      - name: center_name
        description: Name of the center
        tests:
          - not_null:
              severity: error
      - name: center_code
        description: Code identifier for the center
      - name: center_address
        description: Physical address of the center
      - name: center_city
        description: City where the center is located
      - name: center_state
        description: State or province where the center is located
      - name: center_postal_code
        description: Postal code of the center
      - name: center_phone
        description: Phone number of the center
      - name: center_email
        description: Email address of the center
      - name: center_status
        description: Current status of the center (active, inactive, etc.)
        tests:
          - accepted_values:
              values: ['active', 'inactive', 'closed', 'opening_soon']
              severity: warn
      - name: center_opening_date
        description: Date when the center opened
      - name: center_closing_date
        description: Date when the center closed (if applicable)