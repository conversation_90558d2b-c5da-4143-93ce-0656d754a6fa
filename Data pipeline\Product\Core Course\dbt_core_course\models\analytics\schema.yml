version: 2

models:
  - name: activities
    description: >
      Consolidated view of all learning activities across multimedia and digital workbook platforms.
      Used for tracking student progress and engagement with learning content.
    columns:
      - name: activity_type
        description: Type of activity (multimedia or digital_workbook)
      - name: activity_id
        description: Unique identifier for the activity
      - name: registration_id
        description: ID of the student registration
      - name: student_id
        description: ID of the student who completed the activity
      - name: content_item_id
        description: ID of the content item
      - name: description
        description: Description of the activity
      - name: center_reference_id
        description: Reference ID for the center
      - name: start_date
        description: Date when the activity was started
      - name: completed_date
        description: Date when the activity was completed
      - name: level
        description: Level of the content
      - name: unit
        description: Unit within the level
      - name: lesson
        description: Lesson within the unit
      - name: mini_cycles
        description: Mini-cycle number
      - name: mini_cycle_stage
        description: Stage within the mini-cycle
      - name: workbook_activity
        description: Flag or identifier for workbook activities
      - name: score
        description: Score achieved on the activity
      - name: duration_mins
        description: Duration of the activity in minutes
      - name: duration_cap_mins
        description: Capped duration of the activity in minutes (removes outliers)
      - name: percentile_25_duration_mins
        description: 25th percentile duration for this activity type
      - name: median_duration_mins
        description: Median duration for this activity type
      - name: percentile_75_duration_mins
        description: 75th percentile duration for this activity type

  - name: conversation_ai
    description: >
      Aggregated data about AI-powered conversations between users and the system.
      Used for analyzing conversation patterns, costs, and effectiveness.
    columns:
      - name: chat_id
        description: Unique identifier for the chat session
      - name: total_no_of_interactions
        description: Total number of interactions in the conversation
      - name: user_id
        description: ID of the user participating in the conversation
      - name: contract_id
        description: ID of the contract associated with the user
      - name: content_id
        description: ID of the content being discussed
      - name: start_date
        description: UTC timestamp when the conversation started
      - name: end_date
        description: UTC timestamp when the conversation ended
      - name: chat_start_date
        description: Local timestamp when the conversation started
      - name: chat_end_date
        description: Local timestamp when the conversation ended
      - name: total_cost
        description: Total cost of the conversation
      - name: chat_ended
        description: Boolean flag indicating if the chat has ended
      - name: total_input_tokens
        description: Total number of input tokens used
      - name: total_output_tokens
        description: Total number of output tokens generated
      - name: total_tokens
        description: Total number of tokens (input + output)
      - name: chat_duration_seconds
        description: Duration of the chat in seconds
      - name: total_messages
        description: Total number of messages exchanged
      - name: user_messages
        description: Number of messages sent by the user
      - name: assistant_messages
        description: Number of messages sent by the assistant
      - name: understanding_gap_count
        description: Count of instances where the AI didn't understand the user

  - name: speaking_ai_beta
    description: >
      Aggregated data about AI-powered speaking practice sessions.
      Used for analyzing speaking practice patterns, costs, and effectiveness.
    columns:
      - name: chat_id
        description: Unique identifier for the chat session
      - name: total_no_of_interactions
        description: Total number of interactions in the session
      - name: user_id
        description: ID of the user participating in the session
      - name: user_type
        description: Type of user (e.g., student, prospect)
      - name: level
        description: Language proficiency level for the session
      - name: feedback
        description: Feedback provided by the user
      - name: duration
        description: Duration of the session
      - name: start_date
        description: UTC timestamp when the session started
      - name: end_date
        description: UTC timestamp when the session ended
      - name: completed_date
        description: UTC timestamp when the feedback was completed
      - name: chat_start_date
        description: Local timestamp when the session started
      - name: chat_end_date
        description: Local timestamp when the session ended
      - name: local_completed_date
        description: Local timestamp when the feedback was completed
      - name: total_cost
        description: Total cost of the session
      - name: chat_ended
        description: Boolean flag indicating if the chat has ended
      - name: total_input_tokens
        description: Total number of input tokens used
      - name: total_output_tokens
        description: Total number of output tokens generated
      - name: total_tokens
        description: Total number of tokens (input + output)
      - name: chat_duration_seconds
        description: Duration of the chat in seconds

  - name: contracts
    description: >
      Consolidated view of all contracts with related information.
      Used for contract analysis, financial reporting, and student tracking.
    columns:
      - name: contract_id
        description: Unique identifier for the contract
      - name: contract_reference_id
        description: External reference ID for the contract
      - name: student_id
        description: ID of the student associated with the contract
      - name: student_reference_id
        description: External reference ID for the student
      - name: center_id
        description: ID of the center associated with the contract
      - name: center_reference_id
        description: External reference ID for the center
      - name: territory_id
        description: ID of the territory associated with the center
      - name: territory_reference_id
        description: External reference ID for the territory
      - name: contract_number
        description: Contract number
      - name: crm_contract_number
        description: Contract number in the CRM system
      - name: status
        description: Current status of the contract
      - name: start_date
        description: Start date of the contract
      - name: end_date
        description: End date of the contract
      - name: created_date
        description: Date when the contract was created
      - name: sale_date
        description: Date when the contract was sold
      - name: price
        description: Price of the contract
      - name: service_type
        description: Type of service provided under the contract
      - name: location
        description: Location where the service is provided
      - name: class_access_type
        description: Type of class access granted by the contract
      - name: product_type
        description: Type of product associated with the contract
      - name: contract_type
        description: Type of contract
      - name: is_membership
        description: Flag indicating if the contract is a membership
      - name: is_promotional
        description: Flag indicating if the contract is promotional
      - name: is_transfer_in
        description: Flag indicating if the contract is a transfer in
      - name: is_cross_center_booking
        description: Flag indicating if the contract allows cross-center booking
      - name: is_teen
        description: Flag indicating if the contract is for a teen student

  - name: bookings
    description: >
      Consolidated view of all class bookings with related information.
      Used for attendance tracking, class utilization, and student engagement analysis.
    columns:
      - name: booking_id
        description: Unique identifier for the booking
      - name: class_id
        description: ID of the class that was booked
      - name: student_id
        description: ID of the student who booked the class
      - name: student_reference_id
        description: External reference ID for the student
      - name: center_id
        description: ID of the center where the class is held
      - name: student_center_id
        description: ID of the center where the student is registered
      - name: class_date
        description: Date of the class
      - name: class_type
        description: Type of class (encounter, conversation class, etc.)
      - name: book_mode
        description: Mode of booking (book or standby)
      - name: book_date
        description: Date when the booking was made
      - name: attended_flag
        description: Flag indicating if the student attended the class
      - name: billable_flag
        description: Flag indicating if the booking is billable
      - name: standby_flag
        description: Flag indicating if the booking is on standby
      - name: cancelled_flag
        description: Flag indicating if the booking was cancelled
      - name: late_cancelled_flag
        description: Flag indicating if the booking was cancelled late
      - name: no_show_flag
        description: Flag indicating if the student did not show up
      - name: rescheduled_flag
        description: Flag indicating if the booking was rescheduled
      - name: ready_flag
        description: Flag indicating if the student was ready for the class
      - name: mm_ready_flag
        description: Flag indicating if the student completed multimedia preparation
      - name: wb_ready_flag
        description: Flag indicating if the student completed workbook preparation
      - name: booked_by_role
        description: Role of the user who made the booking
      - name: booking_cancelled_by_role
        description: Role of the user who cancelled the booking
      - name: booking_created_datetime
        description: Timestamp when the booking was created
      - name: booking_cancelled_datetime
        description: Timestamp when the booking was cancelled
      - name: lead_booked_datetime
        description: Lead time between booking and class

  - name: encounter_readiness
    description: >
      Tracks student readiness for encounters based on completed activities.
      Used for analyzing student preparation patterns and readiness for classes.
    columns:
      - name: student_id
        description: ID of the student
      - name: level
        description: Level of the content
      - name: unit
        description: Unit within the level
      - name: mm_ready
        description: Date when the student was ready for multimedia content (97% complete)
      - name: mm_complete_66
        description: Date when the student completed 66% of multimedia content
      - name: mm_complete_33
        description: Date when the student completed 33% of multimedia content
      - name: mm_complete_0
        description: Date when the student started multimedia content
      - name: wb_ready_80
        description: Date when the student was ready for workbook content (80% complete)
      - name: wb_ready_60
        description: Date when the student completed 60% of workbook content
      - name: wb_ready_40
        description: Date when the student completed 40% of workbook content
      - name: wb_ready_20
        description: Date when the student completed 20% of workbook content
      - name: wb_ready_0
        description: Date when the student started workbook content

  - name: levels_started
    description: >
      Tracks when students start new levels in their learning journey.
      Used for progression analysis and level completion tracking.
    columns:
      - name: student_reference_id
        description: External reference ID for the student
      - name: digital_books_log_id
        description: ID of the digital books log record
      - name: center_reference_id
        description: External reference ID for the center
      - name: registration_id
        description: ID of the student registration
      - name: contract_reference_id
        description: External reference ID for the contract
      - name: date_granted
        description: UTC timestamp when the level was granted
      - name: local_date_granted
        description: Local timestamp when the level was granted
      - name: category
        description: Category of the level
      - name: category_type
        description: Type of category
      - name: unlock_type
        description: Type of unlock (standard, free, etc.)
      - name: operation_type
        description: Type of operation (bill, refund, etc.)
      - name: workbook_type
        description: Type of workbook
      - name: is_teen
        description: Flag indicating if the student is a teen
      - name: config_value
        description: Configuration value
      - name: sequence
        description: Sequence number
      - name: is_restart
        description: Flag indicating if this is a restart
      - name: first_later
        description: Indicates if this is a first or later level
      - name: levels_started
        description: Count of levels started (1 for bill, -1 for refund)

  - name: novu_messages
    description: >
      Consolidated view of all notification messages sent through the Novu service.
      Used for analyzing notification effectiveness and engagement.
    columns:
      - name: message_id
        description: Unique identifier for the message
      - name: student_reference_id
        description: External reference ID for the student
      - name: channel
        description: Communication channel used (email, SMS, push, etc.)
      - name: title
        description: Title or subject of the message
      - name: notification_id
        description: ID of the notification template
      - name: notification_type
        description: Type of notification
      - name: variant_id
        description: ID of the message template variant
      - name: variant
        description: Name of the variant
      - name: created_at
        description: Timestamp when the message was created
      - name: updated_at
        description: Timestamp when the message was last updated

  - name: user_onboarding
    description: >
      Tracks user progress through the onboarding process.
      Used for analyzing onboarding effectiveness and identifying drop-off points.
    columns:
      - name: user_reference_id
        description: External reference ID for the user
      - name: combined_feature_step
        description: Combined feature and step identifier
      - name: feature_type
        description: Type of onboarding feature
      - name: step_type
        description: Type of onboarding step
      - name: Feature completed
        description: Flag indicating if the feature was completed
      - name: Feature skipped
        description: Flag indicating if the feature was skipped
      - name: step_is_completed
        description: Flag indicating if the step was completed
      - name: step_is_skipped
        description: Flag indicating if the step was skipped
      - name: step_updated_at
        description: Timestamp when the step was last updated
      - name: created_at
        description: Timestamp when the record was created
      - name: updated_at
        description: Timestamp when the record was last updated

  - name: users
    description: >
      Consolidated view of all users in the system.
      Used for user management, authentication, and profile information.
    columns:
      - name: user_id
        description: Unique identifier for the user
      - name: student_reference_id
        description: External reference ID for the student
      - name: center_id
        description: ID of the center where the user is registered
      - name: territory_id
        description: ID of the territory associated with the user
      - name: is_active
        description: Flag indicating if the user is active
      - name: user_name
        description: Username for system login
      - name: first_name
        description: User's first name
      - name: last_name
        description: User's last name
      - name: birth_date
        description: User's date of birth
      - name: email
        description: User's email address
      - name: created_at
        description: Timestamp when the user record was created
      - name: updated_at
        description: Timestamp when the user record was last updated
      - name: deleted_at
        description: Timestamp when the user record was deleted
      - name: mobile_telephone
        description: User's mobile telephone number
      - name: role
        description: User's role in the system
      - name: role_type
        description: Type of role
      - name: center_reference_id
        description: External reference ID for the center
