{{ config(
    MATERIALIZED = 'table',
    table_type = 'iceberg',
    FORMAT = 'parquet'
) }}


WITH 
Students as (
SELECT 
	C.Id as ContractId
	,C.Student_Id as StudentId
	,REG.User_Id as RegUserId
	,REG.ID as Regid
	,C.Center_Id as CenterId
	,C.Start_Date as StartDate
	,C.End_Date as EndDate
	,C.Location
	,REGEXP_REPLACE(SL.Name, '^level ', '') StartLevel
	,REGEXP_REPLACE(EL.Name, '^level ', '') EndLevel
	,case when C.Is_Membership = false then cast(REGEXP_REPLACE(EL.Name, '^level ', '') as int) - cast(REGEXP_REPLACE(SL.Name, '^level ', '') as int) + 1 else null end as LevelsNum
	,(CAST(REGEXP_REPLACE(EL.Name, '^level ', '') as int)) - (CAST(REGEXP_REPLACE(SL.Name, '^level ', '') as int)) + 1 as NumberOfLevels
	,(CAST(REGEXP_REPLACE(SL.Name, '^level ', '') as int)) * 4 - 3 as Enc1Level  
	,(CAST(REGEXP_REPLACE(SL.Name, '^level ', '') as int)) * 4 - 2 as Enc2Level  
	,(CAST(REGEXP_REPLACE(SL.Name, '^level ', '') as int)) * 4 - 1 as Enc3Level  
	,(CAST(REGEXP_REPLACE(SL.Name, '^level ', '') as int)) * 4  as Enc4Level  
	,CONCAT(USR.First_Name,' ',USR.Last_Name)  as PersonalTutor
	,C.Contract_Type as ContractType
	,CASE 
		WHEN C.Service_Type = 1 AND C.Is_Teen = false THEN 'Standard'
		WHEN C.Service_Type = 1 AND C.Is_Teen = true THEN 'Teens Standard'
		WHEN C.Service_Type = 2 AND C.Is_Teen = false THEN 'VIP'
		WHEN C.Service_Type = 2 AND C.Is_Teen = true THEN 'Teens VIP'   END AS ServiceType
	,CASE WHEN Group_Id is not null then 1 else 0 end as IsGroup
	,case when C.Is_Membership = false and cast(REGEXP_REPLACE(EL.Name, '^level ', '') as int) - cast(REGEXP_REPLACE(SL.Name, '^level ', '') as int) + 1 <> 1 then 1 
		when C.Is_Membership = true then 1
		else null end as InclusionLevels
	,InclusionLength
	,InclusionCancel
	,InclusionTransferOut
	,InclusionTransferIn
	,case when CF.CfId is not null then 1 else 0 end as FirstContractStudent
	,case when CA.ICCA = 1 and CA.OLCA = 1 then 'Full Access'
		when CA.ICCA = 1 then 'In-Center'
		when CA.OLCA = 1 then 'Online'
		else 'No Access' end as ClassAccessType
	,CASE WHEN C.Is_membership = False THEN 'Levels' ELSE 'MemberShip' END AS LvlMembrshp
FROM 
	(
		SELECT 
			cont.Id
			,cont.Student_Id
			,cont.Center_Id
			,cont.Start_Date
			,cont.End_Date
			,cont.Location
			,cont.Is_Membership
			,cont.Contract_Type
			,cont.Service_Type
			,cont.Is_Teen
			,cont.Group_Id
			,cont.Start_Level_Id
			,cont.End_Level_Id
			,cont.Lab_Teacher_Id
			,cont.Contract_Reference_Id
			,cont.Current_Validation_State
			,case when DATE_DIFF('DAY', "Start_Date", End_Date) > 90 then '90 Days'
					when DATE_DIFF('DAY', "Start_Date", End_Date) > 21 then '21 Days'
					else '' end as InclusionLength
			,case when (DATE_DIFF('DAY', "Start_Date", cancelled_date) > 90 OR cancelled_date is null) then '90 Days'
					when (DATE_DIFF('DAY', "Start_Date", cancelled_date) > 21 OR cancelled_date is null) then '21 Days'
					else '' end as InclusionCancel
			,case when (DATE_DIFF('DAY', "Start_Date", minlastupdate) > 90 OR minlastupdate is null) then '90 Days'
					when (DATE_DIFF('DAY', "Start_Date", minlastupdate) > 21 OR minlastupdate is null) then '21 Days'
					else '' end as InclusionTransferOut
			,case when Is_Transfer_In = false then 1
					else 0 end as InclusionTransferIn
			,minlastupdate
		FROM ods_contract_service.ods_cs_contracts cont
			LEFT JOIN
				(
				SELECT
				contract_id,
				student_id,
				cast(min(Last_Updated_Date) as DATE) as minlastupdate
				FROM ods_contract_service.ods_cs_contract_transfers
				WHERE Transfer_Status = 2
				and Last_Updated_Date > TIMESTAMP '2020-01-01 00:00:00.000'
				GROUP BY contract_id, Student_Id
				) Ctransf
			ON cont.Id = Ctransf.contract_id
			and cont.Student_Id = Ctransf.Student_Id
			LEFT JOIN
			(
				select contract_id
				, min(created_date) as cancelled_date
				from {{ ref('dt_cs_contracts_audit_info') }}
				where modified_field = 'state'
				and present_value in ('misentry','canceled','cancelled')
				group by contract_id
			)misentry
			ON misentry.contract_id = cont.ID
			WHERE Product_Type_Id = '32a8330a-1413-4c16-8e60-554f7e612253'
			AND Is_Promotional = false
			AND "Start_Date" > DATE('2020-11-01')
	) C
INNER JOIN 
-- Students with First Contracts only
	(
		Select   
			Id as CfId
			,ROW_NUMBER() OVER(PARTITION BY Student_Id ORDER BY Created_Date) AS RN
		From ods_contract_service.ods_cs_contracts 
		Where Is_Promotional = false 
			AND State not in (12, 13)
	) CF
	ON C.Id = CF.CfId
	AND CF.RN = 1
	LEFT JOIN ods_contract_service.ods_cs_product_levels SL
	ON C.Start_Level_Id = SL.Id
	LEFT JOIN ods_contract_service.ods_cs_product_levels EL
	ON C.End_Level_Id = EL.Id
	LEFT JOIN
	(
		select
			Contract_Id
			,MAX(case when Class_Access_Type_Id = '514e7cea-9ea7-4096-a050-c729466a6219' and Is_Active = true then 1 else 0 end) as ICCA
			,MAX(case when Class_Access_Type_Id = 'e802ae87-4e17-4d43-b88f-ea7f136cdf69' and Is_Active = true then 1 else 0 end) as OLCA
		from ods_contract_service.ods_cs_contract_class_access_type
		group by 
			Contract_Id
	) CA on C.Id = CA.Contract_Id
	LEFT JOIN ods_contract_service.ods_cs_users USR 
	ON USR.Id = C.Lab_Teacher_Id
	LEFT JOIN ods_learning_service.ods_ls_registration REG
	ON REG.Contract_Id = C.Contract_Reference_Id
	WHERE (C.Current_Validation_State = 2 OR C.Current_Validation_State >= 6)
),


-- Pool of Successful Encounters with CategoryAbreviatoion

Encounters as (
	Select
		S.StudentId
		,sum(case when Date_diff('Day',S.StartDate, ERA.Class_Start_Date)+1 between -30 and 21 then 1 else 0 end) as Encounters3weeks
		,sum(case when Date_diff('Day',S.StartDate, ERA.Class_Start_Date)+1 between -30 and 42 then 1 else 0 end) as Encounters6weeks
		,sum(case when Date_diff('Day',S.StartDate, ERA.Class_Start_Date)+1 between -30 and 63 then 1 else 0 end) as Encounters9weeks
		,sum(case when Date_diff('Day',S.StartDate, ERA.Class_Start_Date)+1 between -30 and 84 then 1 else 0 end) as Encounters12weeks
		,sum(case when Date_diff('Day',S.StartDate, ERA.Class_Start_Date)+1 between -30 and 91 then 1 else 0 end) as Encounters13weeks
	From Students S 
		left join ods_learning_service.ods_ls_registration R on S.RegUserId = R.User_Id
		--and R.StartDate <= S.StartDate
		left join ods_learning_service.ods_ls_encounter_result_aggregate ERA
		on R.Id = ERA.Registration_Id
		LEFT JOIN ods_learning_service.ods_ls_class C
		ON ERA.Class_Id = C.Id
		left join ods_learning_service.ods_ls_class_type CT 
		on C.Class_Type = CT.Code
	WHERE ERA.Class_Start_Date > DATE('2020-11-01')
		and CT.Title like '%encounter%'
		and Content_Item_Result_Type_Id = 1
	group by 
		S.StudentId
),

-- Pool of Level Starts counted by DW unlocks

Levels AS (
	Select 
		S.StudentId
		,sum(case when Date_diff('Day',S.StartDate, DB.Date_Granted)+1 between -30 and 84 then 1 else 0 end) as LaterLevels12weeks
		,sum(case when Date_diff('Day',S.StartDate, DB.Date_Granted)+1 between -30 and 91 then 1 else 0 end) as LaterLevels13weeks
	From Students S 
	left join ods_learning_service.ods_ls_registration R on S.RegUserId = R.User_Id 
	--and R.StartDate <= S.StartDate
	left join ods_learning_service.ods_ls_digital_books_log as DB 
		on R.id = DB.Registration_Id
	left join 
		(
		select Registration_Id, 
		min("sequence") as MinSequence
		from ods_learning_service.ods_ls_digital_books_log
		where Workbook_Type = 0
		and Operation_Type = 0
		and Unlock_Type = 0
		group by Registration_Id
		) MDB
		On DB.Registration_Id = MDB.Registration_Id
	WHERE DB.Workbook_Type = 0
		and DB.Operation_Type = 0
		and DB.Unlock_Type = 0
		AND Date_Granted > DATE('2020-11-01')
		and DB.Sequence <> MinSequence
	group by 
		S.StudentId
)

Select 
S.ContractId
,S.Regid
,S.StudentId
,S.CenterId
,S.StartDate
,S.EndDate
,S.StartLevel
,S.PersonalTutor
,S.ContractType
,S.ServiceType
,S.Location
,S.IsGroup
,S.ClassAccessType
,S.LvlMembrshp
,S.InclusionLength
,S.InclusionLevels
,S.InclusionCancel
,S.InclusionTransferOut
,S.InclusionTransferIn
,Case When Encounters3weeks >= 1 then 1 else 0 end as FirstEncUnder3w
,Case When Encounters6weeks >= 2 then 1 else 0 end as SecondEncUnder6w
,Case When Encounters9weeks >= 3 then 1 else 0 end as ThirdEncUnder9w
,Case When Encounters12weeks >= 4 then 1 else 0 end as ForthEncUnder12w
,Case When Encounters13weeks >= 4 then 1 else 0 end as ForthEncUnder13w
,Case When LaterLevels12weeks >= 1 then 1 else 0 end as SecondLvlUnder12w
,Case When LaterLevels13weeks >= 1 then 1 else 0 end as SecondLvlUnder13w
From Students S
Left Join Encounters E
On S.StudentId = E.StudentId
Left Join Levels L
On S.StudentId = L.StudentId
Where S.Startdate > DATE('2020-11-01')