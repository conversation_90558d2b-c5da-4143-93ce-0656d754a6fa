import CloudOperations
from CloudOperations import logging

Redshift = CloudOperations.Redshift
RedshiftConnect = Redshift.Connect()
"""reading the Redshift credential from secret key management services and assigning to variables"""
SecretInstance = CloudOperations.SecretManager
DbDetails = SecretInstance.GetSecret('redshift_develop', 'eu-west-1')
ClusterIdentifier, DbDatabase, DbUser, IamRole, Region = \
    [DbDetails[key] for key in ('cluster_identifier', 'database', 'db_user', 'iam_role', 'region')]


class Database:
    @staticmethod
    def Execution(ExecutionType, Query, StatementName):
        try:

            """Step 1 Execute Query"""

            ExecuteResponse = RedshiftConnect.execute_statement(
                ClusterIdentifier=ClusterIdentifier,
                Database=DbDatabase,
                DbUser=DbUser,
                Sql=Query,
                StatementName=StatementName,
                WithEvent=False)
            QueryId = ExecuteResponse['Id']

            """Step 2 Check Query Execution Status"""

            DescribeQueryStatus = 'Started' """setting default value"""
            DescribeResponse = '' """setting default value"""

            while (DescribeQueryStatus != 'FINISHED') & (DescribeQueryStatus != 'FAILED') & \
                    (DescribeQueryStatus != 'ABORTED'):
                DescribeResponse = RedshiftConnect.describe_statement(Id=QueryId)
                DescribeQueryStatus = str(DescribeResponse['Status'])
            if DescribeQueryStatus == 'FAILED' or DescribeQueryStatus == 'ABORTED':
                logging.warning("Raised Exception in Execution due to DescribeQueryStatus %s",
                                format(DescribeQueryStatus))
                raise Exception
            if ExecutionType == "WriteTable":
                return DescribeResponse

            if ExecutionType == "ReadTable":

                """Step 3 Get Query Execution Result"""

                TableData = []
                TableHeader = []
                ResponseList = []

                TableResponse = RedshiftConnect.get_statement_result(Id=QueryId)
                TableData.extend(TableResponse['Records'])
                ResponseHeader = TableResponse['ColumnMetadata']
                while 'NextToken' in TableResponse:
                    TableResponse = RedshiftConnect.get_statement_result(Id=QueryId,
                                                                         NextToken=TableResponse['NextToken'])
                    TableData.extend(TableResponse['Records'])

                for ColumnHeader in ResponseHeader:
                    header = ColumnHeader['name']
                    TableHeader.append(header)

                for Content in TableData:
                    ListLength = len(Content)
                    TransformedDict = {}
                    IndexValue = 0

                    for Iterate in range(ListLength):
                        logging.warning(Iterate)
                        Info = {TableHeader[IndexValue]: list(Content[IndexValue].values())[0]}
                        TransformedDict.update(Info)
                        IndexValue += 1
                    ResponseList.append(TransformedDict)
                return ResponseList

        except Exception as ErrorMessage:
            logging.warning("Raised Exception in Execution due to %s", format(ErrorMessage))
            raise Exception
