{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with min_sequence
as (
    select student_id, 
            min("sequence") as min_sequence
    from {{ref("dt_ls_digital_books_log")}} 
    where workbook_type = 'digital'
    and operation_type = 'bill'
    and unlock_type = 'standard'
    group by student_id
)
,level_started 
as (
SELECT 
    user.ssds_id                                as student_reference_id                          
    ,db_logs.id                                 as digital_books_log_id                        
    ,center.reference_center_id                 as center_reference_id
    ,db_logs.registration_id                    as registration_id
    ,registration.contract_id                   as contract_reference_id
    ,db_logs.date_granted                       
    ,db_logs.local_date_granted                 
    ,db_logs.category                           
    ,db_logs.category_type                      
    ,db_logs.unlock_type                        
    ,db_logs.operation_type                     
    ,db_logs.workbook_type                      
    ,registration.is_teen
    ,center_config.config_value                 
    ,db_logs.sequence                           
    ,is_restart
    ,case
           when db_logs."sequence" = ms.min_sequence then 'first'
           when db_logs."sequence" = ms.min_sequence+1 and db_logs.operation_type = 'refund' then 'first'
           when db_logs."sequence" <> ms.min_sequence then 'later'
    end as first_later
    ,case when db_logs.unlock_type = 'standard'
        and db_logs.is_restart = false
        and db_logs.workbook_type = 'digital' then
            case when db_logs.operation_type = 'bill' then 1
                 when db_logs.operation_type = 'refund' then -1
                 else 0 end
        end as levels_started
FROM 
    {{ref("dt_ls_digital_books_log")}} as db_logs
LEFT JOIN 
    {{ref("dt_ls_registration")}} as registration
    ON db_logs.registration_id = registration.id
LEFT JOIN
    {{ref("dt_ls_center_config")}} as center_config
    ON db_logs.center_id = center_config.center_id and config_setting = 'navisioncode'
LEFT JOIN 
    {{ref("dt_ls_center")}} as center
    ON db_logs.center_id = center.id
LEFT JOIN
    {{ref("dt_ls_user")}} as user
    ON db_logs.student_id = user.user_id
LEFT JOIN
    min_sequence as ms
    ON db_logs.student_id = ms.student_id
)
select ls.*
      ,ls.levels_started * p.value as dm_revenue
from level_started ls
left join 
    {{ref("territory_centers")}} tc on ls.center_reference_id = tc.center_reference_id
left join 
    {{ref("pricing")}} p on lower(tc.territory_name) = lower(p.territory_name) and 
    date_format(ls.date_granted, '%Y') = CAST(p.year as varchar) and date_format(ls.date_granted, '%b') = p.Month