{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_content_item') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT
    {{etl_load_date()}},
    contentitem.id as id,
    description,
    content_item_type_id,
    sequence,
    is_skippable,
    url,
    course_id,
    duration,
    is_milestone,
    ancestor_category_id,
    content_optimization_level_id,
    created,
    last_updated,
    is_scorable
from
    ods_data as contentitem
