{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        contractid,
        listprice,
        discount,
        netprice,
        vat,
        totalpayable,
        cancelrefundprice,
        cancelvat,
        canceltotalpayable,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
        {{cast_to_timestamp('canceldate')}} as canceldate,
    ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'contractprice')}}
)

SELECT
    {{etl_load_date()}},
    id,
    contractid as contract_id,
    listprice as list_price,
    discount,
    netprice as net_price,
    vat,
    totalpayable as total_payable,
    cancelrefundprice as cancel_refund_price,
    cancelvat as cancel_vat,
    canceltotalpayable as cancel_total_payable,
    createddate as created_date,
    lastupdateddate as last_updated_date,
    canceldate as cancel_date
FROM 
    RankedRecords
WHERE 
    rn = 1;