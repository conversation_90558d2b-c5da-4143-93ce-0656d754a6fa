{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        id,
        contentitemtypeid,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        bookmode,
        NAME,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'contentitemresulttype'
        ) }}
)
SELECT
    {{etl_load_date()}},
    id,
    contentitemtypeid as content_item_type_id,
    created,
    lastupdated as last_updated,
    bookmode as book_mode,
    name
FROM
    rankedrecords
WHERE
    rn = 1;
