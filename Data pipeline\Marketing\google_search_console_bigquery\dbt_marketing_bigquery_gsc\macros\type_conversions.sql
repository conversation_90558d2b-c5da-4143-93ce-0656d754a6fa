{% macro varchar_to_bigint(field) -%}
    cast({{ field }} as bigint)
{%- endmacro %}

{% macro varchar_to_timestamp(field) -%}
    cast({{ field }} as timestamp(6))
{%- endmacro %}

{% macro timestamp_to_date(field) -%}
    date({{ field }})
{%- endmacro %}

{% macro boolean_to_varchar(field) -%}
    case when {{ field }} then 'True' else 'False' end
{%- endmacro %}

{% macro varchar_to_double(field) -%}
    cast({{ field }} as double precision)
{%- endmacro %}

{% macro safe_cast_string_to_tz_timestamp(column_name) %}
CASE 
    WHEN {{ column_name }} IS NULL THEN NULL
    WHEN {{ column_name }} = '' THEN NULL
    ELSE CAST(REPLACE(REPLACE({{ column_name }}, 'T', ' '), 'Z', '') AS TIMESTAMP(6))
END
{% endmacro %}

{% macro safe_cast_string_to_tz_date(column_name) %}
CASE 
    WHEN {{ column_name }} IS NULL THEN NULL
    WHEN {{ column_name }} = '' THEN NULL
    ELSE CAST(REPLACE(REPLACE({{ column_name }}, 'T', ' '), 'Z', '') AS DATE)
END
{% endmacro %}

{% macro safe_cast_string_to_int(column_name) %}
CASE 
    WHEN {{ column_name }} IS NULL THEN NULL
    WHEN {{ column_name }} = '' THEN NULL
    ELSE CAST({{ column_name }} AS INTEGER)
END 
{% endmacro %}

{% macro etl_load_date() %}
  CAST(current_timestamp AS TIMESTAMP(6)) AS etl_load_date
{% endmacro %}

{% macro get_country_name(column_name) %}
    CASE
        WHEN {{ column_name }} = 'es' THEN 'Spain'
        WHEN {{ column_name }} = 'tr' THEN 'Turkey'
        WHEN {{ column_name }} = 'fr' THEN 'France'
        WHEN {{ column_name }} = 'de' THEN 'Germany'
        WHEN {{ column_name }} = 'it' THEN 'Italy'
        WHEN {{ column_name }} = 'uk' THEN 'United Kingdom'
        WHEN {{ column_name}}  = 've' THEN 'Venezuela'
        WHEN {{ column_name}}  = 'dz' THEN 'Algeria'
        WHEN {{ column_name}}  = 'ec' THEN 'Ecuador'
        WHEN {{ column_name}}  = 'il' THEN 'Israel'
        WHEN {{ column_name}}  = 'kz' THEN 'Kazakhstan'
        WHEN {{ column_name}}  = 'mn' THEN 'Mongolia'
        WHEN {{ column_name}}  = 'mx' THEN 'Mexico'
        WHEN {{ column_name}}  = 'pa' THEN 'Panama'
        WHEN {{ column_name}}  = 'pe' THEN 'Peru'
        WHEN {{ column_name}}  = 'sa' THEN 'Saudi Arabia'
        WHEN {{ column_name}}  = 'tn' THEN 'Tunisia'
        WHEN {{ column_name}}  = 'vn' THEN 'Vietnam'
        WHEN {{ column_name}}  = 'in' THEN 'India'
        WHEN {{ column_name}}  = 'co' THEN 'Colombia'
        WHEN {{ column_name}}  = 'ly' THEN 'Libya'
        WHEN {{ column_name}}  = 'la' THEN 'Laos'
        WHEN {{ column_name}}  = 'ma' THEN 'Morocco'
        WHEN {{ column_name}}  = 'com' THEN 'Global'
        WHEN {{ column_name}}  = 'gt' THEN 'Guatemala'
        WHEN {{ column_name}}  = 'cr' THEN 'Costa Rica'
        WHEN {{ column_name}}  = 'om' THEN 'Oman' 
        ELSE 'No Country'
    END AS country_name
{% endmacro %}