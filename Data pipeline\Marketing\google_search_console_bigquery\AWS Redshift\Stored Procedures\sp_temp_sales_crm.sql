-- DROP PROCEDURE hubspot_crm.sp_temp_sales_crm();

CREATE OR REPLACE PROCEDURE hubspot_crm.sp_temp_sales_crm()
	LANGUAGE plpgsql
AS $$
	




BEGIN

truncate table hubspot_crm.temp_sales_crm ;

insert into hubspot_crm.temp_sales_crm (
    SELECT
        FC.daily_date,
        FC.source_key,
        FC.center_key,
        FC.territory_name,
        FC.age_group,
        FC.individual_corporate,
        FC.hubspot_owner_id,
        TRIM(TRAILING '/' FROM REPLACE(REPLACE(SPLIT_PART(SPLIT_PART(SPLIT_PART(FC.page,'#',1),'%',1),'utm_',1),'https://',''),'http://','')) AS page,
        FC.pipeline,
        SUM(FC.mql) AS mql,
        SUM(FC.lead) AS lead,
        SUM(FC.interestedcount) AS interestedcount,
        SUM(FC.prospectcount) AS prospectcount,
        SUM(FC.offercount) AS offercount,
        SUM(FC.contacted_pipeline) AS contacted_pipeline,
        SUM(FC.booked_pipeline) AS booked_pipeline,
        SUM(FC.shows_pipeline) AS shows_pipeline,
        SUM(FC.contracts_pipeline) AS contracts_pipeline,
        SUM(FC.refunded_pipeline) AS refunded_pipeline,

        ----STRANGE PATCH HERE----
        SUM(CASE
            WHEN FC.territory_name = 'Dominican Republic' THEN (FC.sales_pipeline / 0.0180772985285079) ELSE FC.sales_pipeline
        END) AS sales_pipeline,

        SUM(FC.contacted_funnel) AS contacted_funnel,
        SUM(FC.booked_funnel) AS booked_funnel,
        SUM(FC.shows_funnel) AS shows_funnel,
        SUM(FC.amount_gifts) AS amount_gifts,
        SUM(FC.business_partner_2_hours) AS business_partner_2_hours,
        SUM(FC.market_leader_2_hours) AS market_leader_2_hours,
        SUM(FC.core_course_fit_2_hours) AS core_course_fit_2_hours,
        SUM(FC.ilc_2_hours) AS ilc_2_hours,
        SUM(FC.business_partner_5_start_level) AS business_partner_5_start_level,
        SUM(FC.business_partner_6_end_level) AS business_partner_6_end_level,
        SUM(FC.core_course_levels) AS core_course_levels,
        SUM(FC.market_leader_5_start_level) AS market_leader_5_start_level,
        SUM(FC.market_leader_6_end_level) AS market_leader_6_end_level,
        SUM(FC.pe_end_level) AS pe_end_level,
        SUM(FC.pe_start_level) AS pe_start_level,
        SUM(FC.core_course_fit_5_start_level) AS core_course_fit_5_start_level,
        SUM(FC.core_course_fit_6_end_level) AS core_course_fit_6_end_level,
        SUM(FC.core_course_online_2_levels) AS core_course_online_2_levels,
        SUM(FC.core_course_online_5_start_level) AS core_course_online_5_start_level,
        SUM(FC.core_course_online_6_end_level) AS core_course_online_6_end_level,
        SUM(FC.amount_interest) AS amount_interest,
        SUM(FC.business_partner_1_amount) AS business_partner_1_amount,
        SUM(FC.core_course_amount) AS core_course_amount,
        SUM(FC.market_leader_1_amount) AS market_leader_1_amount,
        SUM(FC.test_prep_group_1_amount) AS test_prep_group_1_amount,
        SUM(FC.certifications_1_amount) AS certifications_1_amount,
        SUM(FC.core_course_fit_amount) AS core_course_fit_amount,
        SUM(FC.core_course_online_1_amount) AS core_course_online_1_amount,
        SUM(FC.ilc_1_amount) AS ilc_1_amount,
        SUM(FC.test_prep_executive_1_amount) AS test_prep_executive_1_amount,
        SUM(FC.test_prep_units) AS test_prep_units,
        SUM(FC.fa_levels) AS fa_levels,
        SUM(FC.fa_amount) AS fa_amount,
        SUM(FC.fa2_levels) AS fa2_levels,
        SUM(FC.fa2_amount) AS fa2_amount,
        SUM(FC.fa3_levels) AS fa3_levels,
        SUM(FC.fa3_amount) AS fa3_amount,
        SUM(FC.fa4_levels) AS fa4_levels,
        SUM(FC.fa4_amount) AS fa4_amount,
        SUM(FC.fam_levels) AS fam_levels,
        SUM(FC.fam_amount) AS fam_amount,
        SUM(FC.ic_levels) AS ic_levels,
        SUM(FC.ic_amount) AS ic_amount,
        SUM(FC.ic2_levels) AS ic2_levels,
        SUM(FC.ic2_amount) AS ic2_amount,
        SUM(FC.ic3_levels) AS ic3_levels,
        SUM(FC.ic3_amount) AS ic3_amount,
        SUM(FC.ic4_levels) AS ic4_levels,
        SUM(FC.ic4_amount) AS ic4_amount,
        SUM(FC.icm_levels) AS icm_levels,
        SUM(FC.icm_amount) AS icm_amount,
        SUM(FC.certifications_2_quantity) AS certifications_2_quantity,
        SUM(FC.test_prep_executive_2_quantity) AS test_prep_executive_2_quantity,
        SUM(FC.test_prep_group_2_quantity) AS test_prep_group_2_quantity,
        SUM(FC.fam_contracts) AS fam_contracts,
        SUM(FC.icm_contracts) AS icm_contracts,
        LOWER(REPLACE(DS.source, '_', ' ')) AS Source,
        LOWER(REPLACE(DS.Channel, '_', ' ')) AS Channel,
        DS.Channel AS Channel_name,
        CASE
            WHEN source ='Web' AND channel_drill_down_1 LIKE '%local search%' THEN 'organic search'
            ELSE LOWER(REPLACE(channel, '_', ' '))
        END AS r_channel,
        channel_drill_down_1,
        channel_drill_down_2,
        LOWER(REPLACE(DS.Campaign, ' ', '')) AS Campaign,
        DS.Campaign AS Campaign_name,
        CASE
            WHEN DC.territory_name IN ('Libya', 'Korea', 'Morocco','Turkey','Spain','Mongolia','Tunisia','Saudi Arabia') THEN 'V3'
            WHEN DC.territory_name IS NOT NULL THEN 'V1'
        END AS HS_version,

        SUM(CASE
            WHEN HS_version ='V3' AND DS.source NOT IN ('Credit Note', 'Transfer In', 'Transfer Out') THEN mql
            WHEN HS_version ='V1' AND DS.source NOT IN (
                'Credit Note','Renewals', 'Renewal','Alumni','Transfer In', 'Transfer Out'
           ) THEN mql
        END) AS lead_mql,
        SUM(CASE
            WHEN DS.source !='Credit Note' THEN FC.contracts_funnel
        END) AS contracts_funnel,
        SUM(CASE
            WHEN DS.source !='Credit Note' THEN FC.sales_funnel
        END) AS sales_funnel,
        DC.center_name AS center_name,
        SUM(CASE
            WHEN DS.source ='Credit Note' THEN FC.contracts_funnel
        END) AS refunds,
        SUM(CASE
            WHEN DS.source ='Credit Note' THEN FC.sales_funnel
        END) AS refunds_amount,
        SUM(CASE
            WHEN HS_version ='V3' AND DS.source NOT IN ('Credit Note', 'Transfer In', 'Transfer Out') THEN lead
            WHEN HS_version ='V1' AND DS.source NOT IN (
                'Credit Note','Renewals', 'Renewal','Alumni','Transfer In', 'Transfer Out'
           ) THEN lead
        END) AS lead_clean,
        CASE
            WHEN lower(SPLIT_PART(page,'?',1)) LIKE '%/english-courses' OR page LIKE '%/english-courses/%'
            OR (lower(page) LIKE '%wallstreetenglish.dz%' AND lower(SPLIT_PART(page,'?',1)) LIKE '%/formation-anglais') --specific for .dz
            OR (lower(page) LIKE '%wallstreetenglish.dz%' AND lower(page) LIKE '%/formation-anglais/%') --specific for .dz
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/english-schulungskurse' OR page LIKE '%/schulungskurse/%'
            OR (page NOT LIKE '%wse.edu.co/%' AND lower(SPLIT_PART(page,'?',1)) LIKE '%/curso-ingles') --specific for .co
            OR (page NOT LIKE '%wse.edu.co/%' AND page LIKE '%/curso-ingles/%') --specific for .co
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/corsi-di-inglese' OR page LIKE '%/corsi-di-inglese/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/ingilizce-ogrenme' OR page LIKE '%/ingilizce-ogrenme/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/cours-anglais' OR page LIKE '%/cours-anglais/%'
            THEN 'English courses'

            WHEN lower(page) LIKE '%/blog%'
            OR lower(page) LIKE '%blog.w%'
            THEN 'Blog'

            WHEN lower(SPLIT_PART(page,'?',1)) LIKE '%/exercises' OR page LIKE '%/exercises/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/exercices-anglais' OR page LIKE '%/exercices-anglais/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/ubungen' OR page LIKE '%/ubungen/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/ejercicios-ingles' OR page LIKE '%/ejercicios-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/esercizi' OR page LIKE '%/esercizi/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/ingilizce-alistirmalar' OR page LIKE '%/ingilizce-alistirmalar/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/exercice-anglais' OR page LIKE '%/exercice-anglais/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/ngu-phap-tieng-anh' OR page LIKE '%/ngu-phap-tieng-anh/%'
            THEN 'Exercises'

            WHEN lower(SPLIT_PART(page,'?',1)) LIKE '%/english-tests' OR page LIKE '%/english-tests/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/tests-anglais' OR page LIKE '%/tests-anglais/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/englisch-tests' OR page LIKE '%/englisch-tests/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/test-ingles' OR page LIKE '%/test-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/test-inglese' OR page LIKE '%/test-inglese/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/seviyenizi-test-edin' OR page LIKE '%/seviyenizi-test-edin/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/tests-ingles' OR page LIKE '%/tests-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/prueba-ingles' OR page LIKE '%/prueba-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/test-tieng-anh' OR page LIKE '%/test-tieng-anh/%'
            THEN 'English tests'

            WHEN lower(SPLIT_PART(page,'?',1)) LIKE '%/examen-anglais' OR page LIKE '%/examen-anglais/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/examenes-oficiales-ingles' OR page LIKE '%/examenes-oficiales-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/examenes-ingles' OR page LIKE '%/examenes-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/englischprufungen' OR page LIKE '%/englischprufungen/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/english-exams' OR page LIKE '%/english-exams/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/certificazioni' OR page LIKE '%/certificazioni/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/examen-oficial-ingles' OR page LIKE '%/examen-oficial-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/examens-officiels-anglais' OR page LIKE '%/examens-officiels-anglais/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/ingilizce-sertifikalar' OR page LIKE '%/ingilizce-sertifikalar/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/chung-chi-tieng-anh' OR page LIKE '%/chung-chi-tieng-anh/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/certifications' OR page LIKE '%/certifications/%'
            THEN 'Certifications'

            WHEN lower(SPLIT_PART(page,'?',1)) LIKE '%/tarifs' OR page LIKE '%/tarifs/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/precios' OR page LIKE '%/precios/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/preise' OR page LIKE '%/preise/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/prices' OR page LIKE '%/prices/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/prezzi-corsi-di-inglese' OR page LIKE '%/prezzi-corsi-di-inglese/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/ingilizce-kurs-fiyatlari' OR page LIKE '%/ingilizce-kurs-fiyatlari/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/hoc-phi' OR page LIKE '%/hoc-phi/%'
            THEN 'Prices'


            WHEN lower(SPLIT_PART(page,'?',1)) LIKE '%/centres' OR page LIKE '%/centres/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/academias-ingles' OR page LIKE '%/academias-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/escuelas' OR page LIKE '%/escuelas/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/zentren' OR page LIKE '%/zentren/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/schools' OR page LIKE '%/schools/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/scuola-inglese' OR page LIKE '%/scuola-inglese/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/centers' OR page LIKE '%/centers/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/english-center' OR page LIKE '%/english-center/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/escuela-ingles' OR page LIKE '%/escuela-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/centre-anglais' OR page LIKE '%/centre-anglais/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/academia-ingles' OR page LIKE '%/academia-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/centro-ingles' OR page LIKE '%/centro-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/institutes' OR page LIKE '%/institutes/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/ingilizce-kursu' OR page LIKE '%/ingilizce-kursu/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/trung-tam-tieng-anh' OR page LIKE '%/trung-tam-tieng-anh/%'
            THEN 'Schools'

            WHEN lower(SPLIT_PART(page,'?',1)) LIKE '%/apprendre-anglais' OR page LIKE '%/apprendre-anglais/%'
            OR (page LIKE '%wse.edu.co/%' AND lower(SPLIT_PART(page,'?',1)) LIKE '%/curso-ingles') --specific for .co
            OR (page LIKE '%wse.edu.co/%' AND page LIKE '%/curso-ingles/%') --specific for .co
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/aprende-ingles' OR page LIKE '%/aprende-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/englisch-zentren' OR page LIKE '%/englisch-zentren/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/english-schools' OR page LIKE '%/english-schools/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/impara-inglese' OR page LIKE '%/impara-inglese/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/learn-english' OR page LIKE '%/learn-english/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/aprender-ingles' OR page LIKE '%/aprender-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/formation-anglais' OR page LIKE '%/formation-anglais/%'
            OR (lower(page) NOT LIKE '%wallstreetenglish.dz%' AND lower(SPLIT_PART(page,'?',1)) LIKE '%/formation-anglais') --specific for .dz
            OR (lower(page) NOT LIKE '%wallstreetenglish.dz%' AND lower(page) LIKE '%/formation-anglais/%') --specific for .dz
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/instituto-ingles' OR page LIKE '%/instituto-ingles/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/english-institutes' OR page LIKE '%/english-institutes/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/ingilizce-kurslari' OR page LIKE '%/ingilizce-kurslari/%'
            OR lower(SPLIT_PART(page,'?',1)) LIKE '%/trung-tam' OR page LIKE '%/trung-tam/%'
            THEN 'City schools'

            WHEN (SPLIT_PART(page, '?', 1) ~ '^([hH][tT][tT][pP][sS]://)?([^/]+)/?$'
                OR SPLIT_PART(page, '?', 1) ~ '^([hH][tT][tT][pP][sS]://)?([^/]+)/[a-zA-Z]{2}/?$')
              THEN 'Home'

            ELSE 'Other'
        END AS "page_type"

    FROM devdwh.hubspot_crm.fact_crm AS FC
    LEFT JOIN devdwh.hubspot_crm.dim_source AS DS
    ON FC.source_key = DS.source_key
    LEFT JOIN devdwh.hubspot_crm.dim_center AS DC
    ON FC.center_key = DC.center_key
    GROUP BY "daily_date",
             FC."source_key",
             FC."center_key",
             FC."territory_name",
             "age_group",
             "individual_corporate",
             "hubspot_owner_id",
             "page",
             "pipeline",
             "source",
             "channel",
             "channel_name",
             "r_channel",
             "channel_drill_down_1",
             "channel_drill_down_2",
             "campaign",
             "campaign_name",
             "hs_version",
             "center_name"
)
;


    COMMIT;


END;





$$
;