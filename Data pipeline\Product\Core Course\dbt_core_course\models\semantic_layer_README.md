# Semantic Layer for Student Progress Reporting (dbt 1.9.4)

This semantic layer provides a business-friendly interface to the student progress data in the data warehouse. It abstracts away the complexity of the underlying data model and provides a consistent set of metrics and dimensions for reporting and analysis.

## Overview

The semantic layer consists of four main components:

1. **Entities**: Define the core business entities in your data model
2. **Semantic Models**: Define the business entities and their attributes
3. **Entity Relationships**: Define how the entities relate to each other
4. **Metrics**: Define business metrics that can be calculated from the semantic models

## Entities

The semantic layer defines the following entities:

- **Student**: Represents a learner in the system
- **Time**: Represents time dimensions for temporal analysis
- **Contract**: Represents a student's enrollment agreement
- **Center**: Represents a physical location where services are provided

## Semantic Models

### Weekly Student Progress (`weekly_student_progress`)

This model provides weekly aggregated metrics for student progress, including:
- Multimedia activities completed
- Workbook activities completed
- Classes booked and attended
- Levels started
- Duration of activities

### Weekly Student Details (`weekly_student_details`)

This model provides weekly student details, including:
- Contract information
- Validity flags
- Student attributes
- Bookmarks

### Daily Student Progress (`daily_student_progress`)

This model provides daily student progress metrics, including:
- Month-to-date activity counts
- Rolling 30-day activity counts
- Booking information

## Key Metrics

The semantic layer defines several key metrics for measuring student progress:

- **Active Students (Weekly)**: Count of students with any activity in the week
- **Active Students (Monthly)**: Count of students with any activity in the month
- **Weekly Engagement Rate**: Percentage of students who completed at least one activity in the week
- **Avg Weekly Activities**: Average number of activities per student per week
- **Avg Weekly Duration**: Average duration of activities per student per week
- **Student Retention Rate**: Percentage of students who remain active from one week to the next
- **New Student Activation Rate**: Percentage of new students who become active in their first week
- **Weekly Class Attendance Rate**: Percentage of booked classes that were attended
- **Avg Weekly Learning Time**: Average total learning time per student per week
- **Student Progression Rate**: Average number of levels started per student per month

## Using the Semantic Layer

### In dbt

You can reference metrics in your dbt models using the `metric()` function:

```sql
-- Example: Calculate weekly engagement rate by center
SELECT
  center_reference_id,
  {{ metrics.metric('weekly_engagement_rate') }}
FROM {{ ref('weekly_student_details') }}
GROUP BY center_reference_id
```

You can also use the MetricFlow API to query metrics:

```python
from metricflow.api.metricflow_api import MetricFlowAPI

mf = MetricFlowAPI()
result = mf.get_metric_data(
    metrics=["weekly_engagement_rate"],
    group_by=["center_reference_id"],
    time_range_start="2023-01-01",
    time_range_end="2023-12-31"
)
```

### In BI Tools

The semantic layer can be used in BI tools that support dbt's semantic layer, such as:

- Looker
- Tableau
- Power BI
- Mode

## Extending the Semantic Layer

To add new metrics or dimensions to the semantic layer:

1. Add new entities to the `semantic_model_relationships.yml` file
2. Add new semantic models to the `semantic_models.yml` file
3. Add new entity relationships to the `semantic_model_relationships.yml` file
4. Add new metrics to the `semantic_metrics.yml` file

## Best Practices

- Use the semantic layer for all reporting and analysis to ensure consistency
- Reference metrics by name rather than recalculating them in SQL
- When adding new metrics, ensure they are well-documented and follow the existing naming conventions
- Test new metrics against existing reports to ensure consistency
- Use the appropriate time grains for your metrics
- Consider performance implications when defining complex derived metrics
