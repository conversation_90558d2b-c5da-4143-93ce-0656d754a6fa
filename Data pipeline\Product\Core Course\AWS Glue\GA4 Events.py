import sys
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark.context import SparkContext
from awsglue.context import GlueContext
from awsglue.job import Job
from pyspark.sql import SparkSession

# Accept parameters from Glue job arguments
args = getResolvedOptions(sys.argv, ['JOB_NAME'])

# Initialize Glue and Spark
sc = SparkContext()
glueContext = GlueContext(sc)
spark = glueContext.spark_session
job = Job(glueContext)
job.init(args['JOB_NAME'], args)

# Load data from BigQuery
GoogleBigQuery_node = glueContext.create_dynamic_frame.from_options(
    connection_type="bigquery",
    connection_options={
        "connectionName": "Core Course BigQuery",
        "parentProject": "core-course-bigquery",
        "viewsEnabled": "true",
        "table": "dbt_prod.ga4_events"
    },
    transformation_ctx="GoogleBigQuery_node"
)

# Convert to DataFrame
df = GoogleBigQuery_node.toDF()

# Write DataFrame as Glue Table
df.write.mode("overwrite") \
    .format("parquet") \
    .option("path", "s3://wse-bq-export-poc/ga4_events/") \
    .saveAsTable("dev_kowshik.ga4_events")

# Commit job
job.commit()