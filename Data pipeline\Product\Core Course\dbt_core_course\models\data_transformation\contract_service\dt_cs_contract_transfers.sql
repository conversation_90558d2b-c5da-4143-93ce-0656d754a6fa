{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_contract_transfers'
        ) }}
    {% if is_incremental() %}
        where last_updated_date > ((select max(last_updated_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    conttrans.id  as id,
    contract_id,
    student_id,
    source_center_id,
    destination_center_id,
    conttypes.name as contract_type,
    is_promotional,
    transtat.name as transfer_status,
    reason,
    group_id,
    company_id,
    created_by_id,
    modified_by_id,
    created_date,
    {{convert_to_local_timestamp('created_date','time_zone_id')}} as local_created_date,
    last_updated_date,
    {{convert_to_local_timestamp('last_updated_date','time_zone_id')}} as local_last_updated_date,
    source_territory_id,
    destination_territory_id,
    group_transfer_request_ref
from ods_data as conttrans
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_contract_types' ) }}
    ) as conttypes on conttrans.contract_type = conttypes.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_transfer_statuses' ) }}
    ) as transtat on conttrans.transfer_status = transtat.id
    left join (
        select id,
            center_id
        from {{ ref( 'ods_cs_contracts' ) }}
    ) as cont on conttrans.contract_id = cont.id
    left join (
        select id,
            center_reference_id 
        from {{ ref( 'ods_cs_centers' ) }}
    ) as center on center.id = cont.center_id
    left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on center.center_reference_id = tz.center_reference_id