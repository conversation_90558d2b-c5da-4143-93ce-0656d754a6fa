import CloudOperations

S3Connect = CloudOperations.S3

class LogFile():
    @staticmethod
    def LogFileGenerate(Status, Stage, CutOffDate, Operation, Territory, RecordsProcessed, NoOfApiCall, CycleId , Object, Bucket):
        # Generate log file for a specific operation
        Logs= {
            "Status": Status,
            "Stage": Stage,
            "CutoffDate": CutOffDate,
            "Operation": Operation,
            "Territory": Territory,
            "RecordsProcessed": RecordsProcessed,
            "NoOfApiCall": NoOfApiCall,
            "CycleId": CycleId,
            "Object": Object
        }

        # Define the file path for the log file
        FilePath ="Logs/" + f"{CycleId}" + f"/{'Stage'+str(Stage)}" + f"/{Object + '_'+ Operation+'_' +Territory}.json"

        # Write the log file to S3 using the specified bucket
        LogsJson = S3Connect.WriteJsonFile(Bucket=Bucket, FilePath=FilePath, DataResponse=Logs)
        return LogsJson

    @staticmethod
    def ScdLogFile(ScdTable, Status, Stage, Operation, TablePath, CycleId, Bucket):
        # Generate log file for SCD (Slowly Changing Dimension) operation
        ScdLogs = {
            "ScdTable": ScdTable,
            "Status": Status,
            "Stage": Stage,
            "Operation": Operation,
            "TablePath": TablePath,
            "CycleId": CycleId
        }

        # Define the file path for the log file
        FilePath ="Logs/" + f"{CycleId}" + f"/{'Stage'+str(Stage)}" + f"/{ScdTable}.json"

        # Write the log file to S3 using the specified bucket
        LogsJson = S3Connect.WriteJsonFile(Bucket=Bucket, FilePath=FilePath, DataResponse=ScdLogs)
        return LogsJson

    @staticmethod
    def SaveTableLog(TableName, WritePath, Status, CycleId, Bucket):
        # Generate log file for saving a table
        SaveLog = {
            "TableName": TableName,
            "WritePath": WritePath,
            "Status": Status,
            "CycleId": CycleId
        }

        # Define the file path for the log file
        FilePath ="Logs/" + f"{CycleId}" + f"/{'SaveTableLogs'}" + f"/{TableName}.json"

        # Write the log file to S3 using the specified bucket
        LogsJson = S3Connect.WriteJsonFile(Bucket=Bucket, FilePath=FilePath, DataResponse=SaveLog)
        return LogsJson

    @staticmethod
    def Enrichment(Table, TablePath, Status, Cycleid, Operation, Object, Stage, Bucket):
        # Generate log file for enrichment operation
        SaveLog= {
            "TableName":Table,
            "TablePath":TablePath,
            "Status":Status,
            "Cycleid":Cycleid,
            "Operation":Operation,
            "Object":Object,
            "Stage":Stage
        }

        # Define the file path for the log file
        FilePath = "Logs/" + f"{Cycleid}" + f"/{'Stage' + str(Stage)}" + f"/{Object +'_' +Operation}.json"

        # Write the log file to S3 using the specified bucket
        LogsJson = S3Connect.WriteJsonFile(Bucket=Bucket, FilePath=FilePath, DataResponse=SaveLog)
        return LogsJson

    @staticmethod
    def RedshiftEnrichedCopy(Filepath, Object, Operation, Stage, Status, Table, Cycleid, Bucket):
        # Generate log file for Redshift enriched copy operation
        SaveLog= {
            "TableName":Table,
            "Filepath":Filepath,
            "Status":Status,
            "Operation":Operation,
            "Cycleid": Cycleid,
            "Object":Object,
            "Stage":Stage
        }

        # Define the file path for the log file
        FilePath = "Logs/" + f"{Cycleid}" + f"/{'Stage' + str(Stage)}" + f"/{Object +'_' +Operation}.json"

        # Write the log file to S3 using the specified bucket
        LogsJson = S3Connect.WriteJsonFile(Bucket=Bucket, FilePath=FilePath, DataResponse=SaveLog)
        return LogsJson

    @staticmethod
    def SPLogs(Object, Operation,Stage, Status,Cycleid,Bucket):
        # Generate log file for Stored Procedures operation
        SpLog = {
            "Object": Object,
            "Status": Status,
            "Operation": Operation,
            "Cycleid": Cycleid,
            "Stage": Stage
        }
        # Define the file path for the log file
        FilePath = "Logs/" + f"{Cycleid}" + f"/{'Stage' + str(Stage)}" + f"/{Object +'_' +Operation}.json"

        # Write the log file to S3 using the specified bucket
        LogsJson = S3Connect.WriteJsonFile(Bucket=Bucket, FilePath=FilePath, DataResponse=SpLog)
        return LogsJson