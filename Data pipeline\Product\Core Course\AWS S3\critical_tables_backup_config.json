[{"schema": "ods_learning_service", "table": "ods_ls_content_item_result"}, {"schema": "ods_learning_service", "table": "ods_ls_registration"}, {"schema": "ods_learning_service", "table": "ods_ls_booked_student"}, {"schema": "ods_contract_service", "table": "ods_cs_class_access_types"}, {"schema": "ods_contract_service", "table": "ods_cs_contract_products"}, {"schema": "ods_contract_service", "table": "ods_cs_contracts"}, {"schema": "dt_learning_service", "table": "dt_ls_content_item_result"}, {"schema": "dt_learning_service", "table": "dt_ls_registration"}, {"schema": "dt_learning_service", "table": "dt_ls_booked_student"}, {"schema": "dt_contract_service", "table": "dt_cs_class_access_types"}, {"schema": "dt_contract_service", "table": "dt_cs_contract_products"}, {"schema": "dt_contract_service", "table": "dt_cs_contracts"}, {"schema": "stage_analytics", "table": "activity_cap_incremental"}, {"schema": "analytics", "table": "novu_results"}, {"schema": "analytics", "table": "study_events"}, {"schema": "analytics", "table": "speak_billing"}, {"schema": "reporting", "table": "fact_daily_novu_results"}, {"schema": "reporting", "table": "fact_daily_progress"}, {"schema": "reporting", "table": "fact_daily_student_progress"}, {"schema": "reporting", "table": "fact_daily_students"}, {"schema": "reporting", "table": "fact_wk_progress"}, {"schema": "reporting", "table": "fact_wk_student_progress"}, {"schema": "reporting", "table": "fact_wk_students"}, {"schema": "reporting", "table": "fact_renewals"}, {"schema": "reporting", "table": "dim_daily_bookmark"}, {"schema": "reporting", "table": "fact_conversation_ai"}, {"schema": "reporting", "table": "fact_speaking_ai_beta"}, {"schema": "snapshot", "table": "contracts_field_changes"}, {"schema": "snapshot", "table": "contracts_product_type_changes"}]