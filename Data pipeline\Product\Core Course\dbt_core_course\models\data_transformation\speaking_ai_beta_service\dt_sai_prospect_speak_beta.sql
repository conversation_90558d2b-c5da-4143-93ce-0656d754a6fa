{{ config(
    tags=["speaking_ai_beta"],
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = ['chat_id', 'message_index'],
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}


with ods_data as (
    select * 
    from 
    {{ ref('ods_sai_prospect_speak_beta') }}

    {% if is_incremental() %}
        where start_date > ((select max(start_date) from {{ this }}))
    {% endif %}
)

Select
  {{etl_load_date()}},
  ods.chat_id,
  ods.duration,
  ods.gpt4o_mini_cost as gpt_4o_mini_cost,
  ods.has_ended,
  ods.start_date,
  ods.end_date,
  CASE 
    WHEN DATE(ods.end_date) != DATE('0001-01-01') 
    THEN {{convert_to_local_timestamp('ods.end_date','tz.time_zone_id')}}
    ELSE ods.end_date
  END AS local_end_date,
  CASE 
    WHEN DATE(ods.start_date) != DATE('0001-01-01') 
    THEN {{convert_to_local_timestamp('ods.start_date','tz.time_zone_id')}}
    ELSE ods.start_date
  END AS local_start_date,
  ods.total_input_tokens,
  ods.total_output_tokens,
  ods.user_id,
  ods.user_type,
  ods.level,
  ods.message_index,
  -- Message fields
  ods.message_interaction_id,
  ods.message_role,
  ods.message_outputtokens,
  ods.message_inputtokens,
  ods.message_content,
  ods.message_audio_duration
  FROM
  ods_data as ods
  left join (
    select 
    ssds_id,
    center_id
    from {{ref('ods_ls_user')}}
  ) as user on user.ssds_id = ods.user_id
  left join(
    select
    id,
    reference_center_id
    from {{ ref('ods_ls_center') }}
  )as ls_center on user.center_id = ls_center.id
  left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on ls_center.reference_center_id = tz.center_reference_id
