{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        duration,
        progress,
        contentitemtypeid,
        averagescore,
        averagecount,
        unitnumber,
        {{ cast_to_timestamp('lastdate') }} as lastdate,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        unitid,
        studentid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'unitresultaggregate'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    duration,
    progress,
    contentitemtypeid as content_item_type_id,
    averagescore as average_score,
    averagecount as average_count,
    unitnumber as unit_number,
    lastdate as last_date,
    created,
    lastupdated as last_updated,
    id,
    unitid as unit_id,
    studentid as student_id
FROM
    rankedrecords
WHERE
    rn = 1;
