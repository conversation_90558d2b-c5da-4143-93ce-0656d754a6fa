{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        loweredrolename,
        description,
        rolecode,
        {{cast_to_int('usertype')}}
    FROM 
        {{source('stage_contract_service', 'roles')}}
)

SELECT
    {{etl_load_date()}},
    id,
    loweredrolename as lowered_role_name,
    description,
    rolecode as role_code,
    usertype as user_type
FROM 
    RankedRecords