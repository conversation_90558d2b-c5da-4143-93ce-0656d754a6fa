import boto3
import json
import logging
import os


bucket = os.environ.get('bucket')
boto3_resource = boto3.resource("s3")
s3_bucket = boto3_resource.Bucket(bucket)
s3 = boto3.client('s3')

savetable = []
file_list = []
key_list = []

def get_folder_length_and_files(bucket_name, folder_path):
    # Remove leading/trailing slashes from the folder path
    folder_path = folder_path.strip('/')

    # List objects in the folder
    response = s3.list_objects_v2(Bucket=bucket_name, Prefix=folder_path)

    # Check if the folder exists and has objects
    if 'Contents' in response:
        # Get the length (number of objects) in the folder
        folder_length = len(response['Contents'])
        logging.warning(f"Folder Length: {folder_length}")

        # Get the list of file names in the folder
        files = [obj['Key'] for obj in response['Contents']]
        logging.warning("Files in the folder:")
        for file_name in files:
            logging.warning(file_name)
            filename = file_name.split('/')[-1]
            file_list.append(filename)
    else:
        logging.warning("Folder does not exist or is empty")
    return file_list

def lambda_handler(event, context):

    stage1_file_path = boto3_resource.Object(bucket, 'Config/Stage1.json')

    # Retrieving the content of the file as bytes, decoding it to UTF-8 and storing it in a string variable
    stage1_file_content = stage1_file_path.get()['Body'].read().decode('utf-8')

    # Parsing the JSON string and converting it into a Python dictionary
    stage1 = json.loads(stage1_file_content)
    execution_check_file_path = boto3_resource.Object(bucket, 'ExecutionCheck.json')
    execution_check_file_content = execution_check_file_path.get()['Body'].read().decode('utf-8')
    execution_check = json.loads(execution_check_file_content)
    logging.warning(execution_check)
    folder = f"Logs/{execution_check['CycleId']}/Stage1"
    files_in_s3 = [f.key.split(folder + "/")[1] for f in s3_bucket.objects.filter(Prefix=folder).all()]
    logging.warning(files_in_s3)
    logging.warning(len(files_in_s3))
    cycle_id = execution_check['CycleId']
    # Filtering the items marked as not run in particular run to match validation count in s3 folder
    filtered_list = [item for item in stage1['Stage1'] if item.get('Status') != 300]
    logging.warning(len(filtered_list))
    savetablelogs_folder_path = f"Logs/{execution_check['CycleId']}/SaveTableLogs"

    file_data = get_folder_length_and_files(bucket, savetablelogs_folder_path)
    logging.warning(file_data)
    for key_table in file_data:
        key_input = key_table.split('.')[-2]
        key_list.append(key_input)

    # Validating the count matches between s3 folder and config
    if len(files_in_s3) == len(filtered_list):
        logging.warning("Stage 1 completed")
        for table_info in stage1['SaveTableInfo']:
            if table_info['Table'] in key_list:
                logging.warning("The Save Table has been run already")
            else:
                table_struct = {
                    "InputType": table_info['InputType'],
                    "FilePathKey": "s3://" + bucket + "/MyanmarVietnamRawFiles/" + str(cycle_id) + table_info[
                        'FilePathKey'],
                    "Table": table_info['Table'],
                    "WritePathKey": table_info['WritePathKey'],
                    "WriteMode": "append",
                    "CycleId": cycle_id
                }
                savetable.append(table_struct)
        saveastable = {"TableProperties": savetable}

    else:
        logging.warning("Stage1 not completed")
        raise Exception
    return saveastable

