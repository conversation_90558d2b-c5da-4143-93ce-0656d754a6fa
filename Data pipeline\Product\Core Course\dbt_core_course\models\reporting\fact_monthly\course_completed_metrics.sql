{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

With StudentAccess AS (
select
        CA.contract_id
        ,case when CA.ICCA = 1 and CA.OLCA = 1 then 'In-Center and Online'
        when CA.ICCA = 1 then 'In-Center Only'
        when CA.OLCA = 1 then 'Online Only'
        else 'No Access' end as class_access_type
from (
        select
        contract_id
        ,MAX(case when class_access_type_id = '514e7cea-9ea7-4096-a050-c729466a6219' and is_active = true then 1 else 0 end) as ICCA
        ,MAX(case when class_access_type_id = 'e802ae87-4e17-4d43-b88f-ea7f136cdf69' and is_active = true then 1 else 0 end) as OLCA
        from
        {{ref('ods_cs_contract_class_access_type')}} CCAT
        Group by
        contract_id
        ) 
as CA ),

Ending AS (
select 
        CC.Id as contract_id
        ,REG.Id as registration_id
        ,REG.user_id as user_id
        ,date_trunc('month',coalesce(OBJ.created_date,CC.end_date)) as Report_start_date
        ,CC.end_date
        ,{{convert_to_local_timestamp('REG.start_date','tz.time_zone_id')}} AS Contract_start_date
        ,{{convert_to_local_timestamp('REG.end_date','tz.time_zone_id')}} AS Contract_end_date
        ,{{convert_to_local_timestamp('REG.created','tz.time_zone_id')}} AS Contract_created
        ,OBJ.created_date as obj_reached_date
        ,coalesce(C.Id,'No Center') as center_id
        ,SA.class_access_type
        ,PT.name as Course
        ,L.name as location
        ,CASE 
            WHEN CC.is_teen = FALSE AND cc.service_type = 1 THEN 'Standard'
            WHEN CC.is_teen = FALSE AND cc.service_type = 2 THEN 'VIP'
            WHEN CC.is_teen = TRUE AND cc.service_type = 1 THEN 'Teen Standard'
            WHEN CC.is_teen = TRUE AND cc.service_type = 2 THEN 'Teen VIP'
        END AS  services
        ,USR.first_name || ' ' || USR.last_name  as personal_tutor
        ,CC.consultant_id
        ,CC.is_promotional
        ,CC.is_teen
        ,coalesce(CC.group_id,'Individual') as "group"
        ,REG.is_b2_b 
        ,case when REG.is_membership = false then 'Levels'
                else 'Membership' end as is_membership
        ,case when REG.course_status = 64 then '1. First Level'
                when REG.course_status = 32768 then '2. Second Level'
                when REG.course_status in (65536,32,2048) then '3. Other Level'
                ELSE 'No Course Level' end as course_level
        ,case when REG.is_membership = true then date_diff('MONTH', REG.start_date, REG.end_date)
                else 0 end AS months_purchased
        ,PLE."order" - PLS."order" + 1 as levels_purchased
    from ods_contract_service.ods_cs_contracts CC 
        left join (select 
                    contract_id, max(created_date) as created_date 
                    from ods_contract_service.ods_cs_contracts_audit_info 
                    where change_type = 4 
                    group by contract_id) OBJ 
        on CC.Id = OBJ.contract_id
        left join StudentAccess SA on SA.contract_id = CC.Id
        left join ods_learning_service.ods_ls_registration REG on REG.contract_id = CC.contract_reference_id
        left join ods_contract_service.ods_cs_product_levels PLS on CC.start_level_id = PLS.Id
        left join ods_contract_service.ods_cs_product_levels PLE on CC.end_level_id = PLE.Id
        left join ods_contract_service.ods_cs_product_types PT on PT.Id = CC.product_type_id 
        left join ods_contract_service.ods_cs_Locations L on L.Id = CC.Location
        left join ods_contract_service.ods_cs_service_types ST on ST.Id = CC.service_type 
        left join ods_contract_service.ods_cs_users USR on USR.Id = CC.lab_teacher_id
        left join ods_learning_service.ods_ls_center C on C.Id = REG.center_id
        LEFT JOIN ods_center_configuration_service.ods_cc_center tz 
        ON        tz.center_reference_id = C.reference_center_id
        where 
        (CC.state in (7,8) or CC.status = 2)
        and
        (
        date(CC.end_date) between date '2024-08-01' and date_add('day',-1,date_trunc('month',date_add('month',3,current_date)))
        or 
        OBJ.created_date >= date '2024-08-01'
        )
        and PT.name = 'core course'
 ),

LvlComp AS (
SELECT            
    RSLT.*,
    CAST(regexp_extract(name, '(\d+)$', 1) AS INT) start_level,  
    CASE 
        WHEN (RSLT.MaxUnit % 4) = 0 THEN (MaxLevel - CAST(regexp_extract(name, '(\d+)$', 1) AS INT)) + 1
        ELSE  (MaxLevel - CAST(regexp_extract(name, '(\d+)$', 1) AS INT)) 
    END  AS levels_completed
    FROM ods_learning_service.ods_ls_registration_course RE
    INNER JOIN    ods_learning_service.ods_ls_course CE 
        ON  RE.course_id = CE.Id
    INNER JOIN    
        (    
        SELECT    
            E.center_id,
            ERA.registration_id,
            ERA.student_id,
            MAX(CAST(REGEXP_REPLACE(SPLIT_PART(path, '.', 1), '[^0-9]', '') AS INT)) MaxLevel,
            MAX(CAST(REGEXP_REPLACE(SPLIT_PART(path, '.', 2), '[^0-9]', '') AS INT)) MaxUnit
        FROM Ending E
        LEFT JOIN    ods_learning_service.ods_ls_encounter_result_aggregate ERA
            ON                    ERA.registration_id = E.registration_id
            AND                ERA.student_id = E.user_id
        INNER JOIN    ods_learning_service.ods_ls_category CAT
            ON                    CAT.Id = ERA.unit_id
        WHERE            ERA.content_item_result_type_id = 1
        GROUP BY        E.center_id,
            ERA.registration_id,
            ERA.student_id
        ) RSLT
        ON            RSLT.registration_id = RE.registration_id
    WHERE        RE.sequence = 0),

initial_load as(
    select 
        date(reportstartdate) as Report_start_date
        ,lower(centerid) as center_id
        ,'No Consultant Id' as consultant_id
        ,coalesce(lower(classaccesstype),'no access') as class_access_type
        ,lower(course) as course
        ,lower("location") as "location"
        ,services
        ,"group"
        ,CASE 
            when ispromotional = '0' THEN false
            when ispromotional = '1' THEN true
        END as is_promotional
        ,false as is_teen
        ,isb2b as is_b2_b
        ,ismembership as is_membership
        ,'3. Other Level' as course_level
        ,SUM(ContractEndStud) as ContractEndStud
        ,SUM(ContractEndStud_NonMembership) as ContractEndStud_NonMembership
        ,SUM(ContractEndStud_Membership) as ContractEndStud_Membership
        ,SUM(LvlsCmpltdByContractEndStud) as LvlsCmpltdByContractEndStud
        ,SUM(monthspurchasedByContractEndStud) as monthspurchasedByContractEndStud
        ,SUM(LvlsPrchsdByContractEndStud) as LvlsPrchsdByContractEndStud
        ,SUM(LvlsPrchsdNonMembership) as LvlsPrchsdNonMembership
        ,lower(personaltutor) as personal_tutor
    from 
        {{ source('initial_load', 'course_completed_metrics_initial') }}
    where  
        course = 'Core Course'
    group by
        date(reportstartdate)
        ,lower(centerid)
        ,coalesce(lower(classaccesstype),'no access')
        ,lower(course)
        ,lower("location")
        ,services
        ,lower(personaltutor)
        ,"group"
        ,ispromotional
        ,isb2b
        ,ismembership


)

    select 
        TO_HEX(SHA256(cast(
            cast(E.Report_start_date as varchar)
            || cast(E."group" as varchar)
            || cast(E.center_id as varchar)
            || cast(E.consultant_id as varchar)
            || cast(coalesce(E.is_b2_b,false) as varchar)
            || cast(E."location" as varchar)
            || cast(E.class_access_type as varchar)
            || cast(E.services as varchar)
            || cast(E.is_membership as varchar)
            || cast(E.is_promotional as varchar)
            || cast(E.is_teen as varchar)
            || cast(E.course_level as varchar)
            || cast(coalesce(E.personal_tutor,'No Personal Tutor') as varchar)
        as varbinary))) as dbt_unique_key
        ,E.Report_start_date
        ,E.center_id
        ,E.consultant_id
        ,E.class_access_type
        ,E.Course
        ,E.location
        ,E.services
        ,E."group"
        ,E.is_promotional
        ,E.is_teen
        ,E.is_b2_b
        ,E.is_membership
        ,E.course_level
        ,Count(E.contract_id) as ContractEndStud
        ,case when E.is_membership = 'Levels' then Count(E.contract_id)
                else 0 end as ContractEndStud_NonMembership
        ,case when E.is_membership = 'Membership' then Count(E.contract_id)
                else 0 end as ContractEndStud_Membership
        ,SUM(Lvl.levels_completed) as LvlsCmpltdByContractEndStud
        ,SUM(E.months_purchased) as monthspurchasedByContractEndStud
        ,SUM(E.levels_purchased) as LvlsPrchsdByContractEndStud
        ,case 
            when E.is_membership = 'Levels' then SUM(E.levels_purchased)
            else 0 
        end as LvlsPrchsdNonMembership
        ,E.personal_tutor
    from Ending E
    left join LvlComp Lvl 
        on E.registration_id = Lvl.registration_id and E.center_id = Lvl.center_id
    group by
        E.Report_start_date
        ,E.center_id
        ,E.consultant_id
        ,E.class_access_type
        ,E.Course
        ,E.location
        ,E.services
        ,E.personal_tutor
        ,E."group"
        ,E.is_promotional
        ,E.is_teen
        ,E.is_b2_b
        ,E.is_membership
        ,E.course_level
union all
    select 
        TO_HEX(SHA256(cast(
            cast(Report_start_date as varchar)
            || cast(coalesce("group",'Individual') as varchar)
            || cast(center_id as varchar)
            || cast(is_b2_b as varchar)
            || cast("location" as varchar)
            || cast(class_access_type as varchar)
            || cast(services as varchar)
            || cast(coalesce(is_membership,'No Membership') as varchar)
            || cast(is_promotional as varchar)
            || cast(is_teen as varchar)
            || cast(course_level as varchar)
            || cast(coalesce(personal_tutor,'No Personal Tutor') as varchar)
        as varbinary))) as dbt_unique_key
        ,Report_start_date
        ,center_id
        ,consultant_id
        ,class_access_type
        ,course
        ,"location"
        ,services
        ,"group"
        ,is_promotional
        ,is_teen
        ,is_b2_b
        ,is_membership
        ,course_level
        ,ContractEndStud
        ,ContractEndStud_NonMembership
        ,ContractEndStud_Membership
        ,LvlsCmpltdByContractEndStud
        ,monthspurchasedByContractEndStud
        ,LvlsPrchsdByContractEndStud
        ,LvlsPrchsdNonMembership
        ,personal_tutor
    from 
        initial_load
