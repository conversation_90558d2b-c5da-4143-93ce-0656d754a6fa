import boto3
from botocore.exceptions import ClientError
import ast



class SecretManager:
    @staticmethod
    def get_secret(secret_name, region_name):

        secret_name = secret_name
        region_name = region_name

        # Create a Secrets Manager client
        session = boto3.session.Session()
        client = session.client(
            service_name='secretsmanager',
            region_name=region_name
        )

        try:

            get_secret_value_response = client.get_secret_value(
                SecretId=secret_name
            )
        except ClientError as e:
            raise e

        secret = ast.literal_eval(get_secret_value_response['SecretString'])
        return secret

