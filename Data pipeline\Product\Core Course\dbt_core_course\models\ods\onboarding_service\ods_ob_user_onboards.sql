{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = ['id', 'step_id'],
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}


SELECT
    {{etl_load_date()}},
    _id as id,
    featuretype as feature_type,
    iscompleted as is_completed,
    isskipped as is_skipped,
    userreferenceid as user_reference_id,
    __v  AS v,
    CAST(json_extract_scalar(step, '$.steptype') AS VARCHAR) AS step_type,
    CAST(json_extract_scalar(step, '$._id') AS VARCHAR) AS step_id,
    CAST(json_extract_scalar(step, '$.iscompleted') AS BOOLEAN) AS step_is_completed,
    CAST(json_extract_scalar(step, '$.isskipped') AS BOOLEAN) AS step_is_skipped,
    CAST(from_iso8601_timestamp(json_extract_scalar(step, '$.updatedat')) AS timestamp(6)) AS step_updated_at,
    CAST(from_iso8601_timestamp(createdat) AS timestamp(6)) as created_at,
    CAST(from_iso8601_timestamp(updatedat) AS timestamp(6)) as updated_at
FROM
    {{ source('stage_onboarding_service','user_onboards') }}
CROSS JOIN
    UNNEST(CAST(json_parse(steps) AS ARRAY<JSON>)) AS t (step)