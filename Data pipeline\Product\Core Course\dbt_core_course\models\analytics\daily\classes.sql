{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}


WITH stg_classes AS (
    SELECT *
    FROM {{ ref('union_classes') }}
),

scheduled_class_property AS (
    SELECT *
    FROM {{ ref('snb_scheduled_class_property') }}
),

ls_center AS (
    SELECT *
    FROM {{ref('dt_ls_center')}}
),
ls_user AS (
    SELECT *
    FROM {{ref('dt_ls_user')}}
),
class_category AS (
    SELECT *
    FROM {{ref('dt_ls_class_category')}}
),
category AS (
    SELECT *
    FROM {{ref('dt_ls_category')}}
),
category_type AS (
    SELECT *
    FROM {{ref('dt_ls_category_type')}}
),
snb_user AS (
    SELECT *
    FROM {{ref('dt_snb_user')}}
),
scheduled_class_category AS (
    SELECT *
    FROM {{ref('dt_snb_scheduled_class_category')}}
),
center as (
    SELECT * 
    FROM {{ref('dt_cc_center')}}
),
pricing as (
    SELECT *
    FROM {{ref('class_pricing')}}
)

, final as (
    SELECT distinct
        stg_classes.class_id,
        COALESCE(stg_classes.class_center_reference_id, ls_center.reference_center_id) AS class_center_reference_id,
        class_start_datetime,
        class_local_start_datetime,
        class_end_datetime,
        class_local_end_datetime,
        {{ convert_to_local_timestamp('class_start_datetime','center.time_zone_id') }} as class_date_student,
        class_type,
        class_number_of_seats,
        class_number_of_seats_in_stand_by,
        class_description,
        COALESCE(snb_teacher.user_reference_id, ls_teacher.ssds_id) AS class_teacher_user_reference_id,
        class_category_from_booking,
        class_cancelled_flag,
        class_communication_account_type,
        class_source,
        class_created_datetime,
        class_local_created_datetime,
        class_last_updated_datetime,
        class_local_last_updated_datetime,
        class_created_by,
        class_created_by_role,
        class_last_updated_by,
        class_last_updated_by_role,
        categories_abbreviations,
        coalesce(scheduled_class_property.is_b2_b, stg_classes.is_b2_b) as class_b2b_flag,
        coalesce(scheduled_class_property.visible_in_group, stg_classes.is_visible_in_group) as class_visible_in_group,
        coalesce(scheduled_class_property.is_teen, stg_classes.is_teen) as class_teen_flag,
        case 
            when stg_classes.is_teen <> true then class_code 
            when stg_classes.is_teen = true and class_code = 'snb-deluxe' then 'snb-deluxe-teens' 
            when stg_classes.is_teen = true and class_code = 'snb-combined' then 'snb-combined-teens' 
            when stg_classes.is_teen = true and class_code = 'snb-vip' then 'snb-vip-teens' 
        end as class_code,
        coalesce(scheduled_class_property.service_type,'combined') AS class_service_type,
        coalesce( LISTAGG(scheduled_class_category.category, ',') WITHIN GROUP (ORDER BY scheduled_class_category.category),
                LISTAGG(category.path, ',') WITHIN GROUP (ORDER BY category.path)  ) AS class_category,
        coalesce( LISTAGG(distinct scheduled_class_category.category_type, ',') WITHIN GROUP (ORDER BY scheduled_class_category.category_type) ,
                LISTAGG(distinct category_type.name, ',') WITHIN GROUP (ORDER BY category_type.name)) as class_category_type,
        CASE
            WHEN class_type IN ('encounter', 'ghost encounter', 'online encounter', 'social club', 'online social club', 'complementary class', 'online complementary class') THEN true
            ELSE false
        END AS class_type_billable,
        stg_classes.is_online as class_online_flag,
        number_of_students
    FROM
        stg_classes
        LEFT JOIN ls_center ON stg_classes.ls_center_id = ls_center.id
        LEFT JOIN center ON ls_center.reference_center_id = center.center_reference_id
        LEFT JOIN ls_user AS ls_teacher ON ls_teacher.user_id = stg_classes.teacher_id
        LEFT JOIN snb_user AS snb_teacher ON snb_teacher.id = stg_classes.teacher_id
        LEFT JOIN scheduled_class_property ON scheduled_class_property.class_id = stg_classes.class_id
        LEFT JOIN scheduled_class_category ON scheduled_class_category.scheduled_class_id = stg_classes.class_id
        LEFT JOIN class_category ON stg_classes.class_id = class_category.class_id
        LEFT JOIN category ON class_category.category_id = category.id
        LEFT JOIN category_type ON category.category_type_id = category_type.id
        LEFT JOIN pricing ON pricing.territoryid = center.territory_id
    GROUP BY
        stg_classes.class_id,
        COALESCE(stg_classes.class_center_reference_id, ls_center.reference_center_id),
        class_start_datetime,
        class_local_start_datetime,
        class_end_datetime,
        class_local_end_datetime,
        {{ convert_to_local_timestamp('class_start_datetime','center.time_zone_id') }},
        class_type,
        class_number_of_seats,
        class_number_of_seats_in_stand_by,
        class_description,
        COALESCE(snb_teacher.user_reference_id, ls_teacher.ssds_id),
        class_category_from_booking,
        class_cancelled_flag,
        class_communication_account_type,
        class_source,
        case 
            when stg_classes.is_teen <> true then class_code 
            when stg_classes.is_teen = true and class_code = 'snb-deluxe' then 'snb-deluxe-teens' 
            when stg_classes.is_teen = true and class_code = 'snb-combined' then 'snb-combined-teens' 
            when stg_classes.is_teen = true and class_code = 'snb-vip' then 'snb-vip-teens' 
        end,
        class_created_datetime,
        class_local_created_datetime,
        class_last_updated_datetime,
        class_local_last_updated_datetime,
        class_created_by,
        class_created_by_role,
        class_last_updated_by,
        class_last_updated_by_role,
        categories_abbreviations,
        coalesce(scheduled_class_property.is_b2_b, stg_classes.is_b2_b),
        coalesce(scheduled_class_property.visible_in_group, stg_classes.is_visible_in_group),
        coalesce(scheduled_class_property.is_teen, stg_classes.is_teen),
        scheduled_class_property.service_type,
        CASE
            WHEN class_type IN ('encounter', 'ghost encounter', 'online encounter', 'social club', 'online social club', 'complementary class', 'online complementary class') THEN true
            ELSE false
        END,
        stg_classes.is_online,
        number_of_students
)

select * 
from final
