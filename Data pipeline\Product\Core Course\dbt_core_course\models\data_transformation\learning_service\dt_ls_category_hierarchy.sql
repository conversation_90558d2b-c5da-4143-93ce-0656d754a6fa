{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_category_hierarchy') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT
    {{etl_load_date()}},
    cathierarchy.id as id,
    ancestorcategory.path as ancestor_category_path,
    descendentcategory.path as descendent_category_path,
    length,
    created,
    last_updated
from
    ods_data as cathierarchy
    Left Join (
        select
            id,
            path
        from
            {{ ref('ods_ls_category') }}
    ) as ancestorcategory
    ON cathierarchy.ancestor_id = ancestorcategory.id
    Left Join (
        select
            id,
            path
        from
            {{ ref('ods_ls_category') }}
    ) as descendentcategory
    ON cathierarchy.descendent_id = descendentcategory.id
