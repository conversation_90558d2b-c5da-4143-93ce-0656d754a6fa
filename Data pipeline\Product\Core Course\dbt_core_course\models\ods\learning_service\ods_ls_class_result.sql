{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        classtype,
        {{ cast_to_timestamp('datecompleted') }} as datecompleted,
        contentitemresulttypeid,
        {{ cast_to_timestamp('datestarted') }} as datestarted,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        result,
        teacherid,
        registrationid,
        refclassid,
        classid,
        studentid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'classresult'
        ) }}
)
SELECT
    {{etl_load_date()}},
    classtype as class_type,
    datecompleted as date_completed,
    contentitemresulttypeid as content_item_result_type_id,
    datestarted as date_started,
    created,
    lastupdated as last_updated,
    id,
    result,
    teacherid as teacher_id,
    registrationid as registration_id,
    refclassid as ref_class_id,
    classid as class_id,
    studentid as student_id
FROM
    rankedrecords
WHERE
    rn = 1;
