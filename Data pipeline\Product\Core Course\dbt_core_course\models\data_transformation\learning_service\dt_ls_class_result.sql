{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_class_result') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT 
    {{etl_load_date()}},
    clsresult.id as id,
    clstype.title as class_type,
    date_completed,
    {{ convert_to_local_timestamp(
        'date_completed',
        'tz.time_zone_id'
    ) }} as local_date_completed,
    Result,
    teacher_id,
    registration_id,
    contitemrestype.name as content_item_result_type,
    date_started,
    {{ convert_to_local_timestamp(
        'date_started',
        'tz.time_zone_id'
    ) }} as local_date_started,
    ref_class_id,
    class_id,
    student_id,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated
from
    ods_data as clsresult
    Left Join (
        select
            code,
            title
        from
            {{ ref('ods_ls_class_type') }}
    ) as clstype
    ON clsresult.class_type = clstype.code
    Left Join (
        select
            id,
            name
        from
            {{ ref('ods_ls_content_item_result_type') }}
    ) as contitemrestype
    ON clsresult.content_item_result_type_id = contitemrestype.id
    Left Join (
        select
            id,
            center_id
        from
            {{ ref('ods_ls_class') }}
    ) as class
    ON clsresult.class_id = class.id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON class.center_id = center.id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
