{{
    config(
        tags=["incremental","init","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}
    {{gsch_select("it","ita")}}
    {{source("gsch",'it')}}
    {{gsch_where("2023-04-01")}} 

    union all 

    {{gsch_select("de","deu")}}
    {{source("gsch",'de')}}
    {{gsch_where("2023-04-01")}} 

    union all 

    {{gsch_select("pe","per")}}
    {{source("gsch",'pe')}}
    {{gsch_where("2023-04-01")}}
    union all 

    {{gsch_select("sa","sau")}}
    {{source("gsch",'sa')}}
    {{gsch_where("2023-04-01")}}    
    union all 

    {{gsch_select("mn","mng")}}
    {{source("gsch",'mn')}}
    {{gsch_where("2023-04-01")}}   

    union all 

    {{gsch_select("tn","tun")}}
    {{source("gsch",'tn')}}
    {{gsch_where("2023-04-01")}} 

    union all 

    {{gsch_select("dz","dza")}}
    {{source("gsch",'dz')}}
    {{gsch_where("2023-04-01")}}

    union all 

    {{gsch_select("es","esp")}}
    {{source("gsch",'es')}}
    {{gsch_where("2023-04-01")}}

        union all 

    {{gsch_select("mx","mex")}}
    {{source("gsch",'mx')}}
    {{gsch_where("2023-04-01")}}

        union all 

    {{gsch_select("com","xxxx")}}
    {{source("gsch",'com')}}
    {{gsch_where("2023-04-01")}}

        union all 

    {{gsch_select("pa","pan")}}
    {{source("gsch",'pa')}}
    {{gsch_where("2023-04-25")}}    

        union all 

    {{gsch_select("il","isr")}}
    {{source("gsch",'il')}}
    {{gsch_where("2023-07-25")}}    


