create schema sharepoint;

create table sharepoint.sales_planner_tool(
"Month" date,
"Center Name" varchar ( 64 ),
"Reference Center ID" varchar ( 15 ),
Source varchar ( 24 ),
"Leads (MQL)" varchar ( 24 ),
"Contacted / Leads" varchar ( 24 ),
Contacted varchar ( 24 ),
"Booked / Contacted" varchar ( 24 ),
"Booked (SQL)" varchar ( 24 ),
"Shows / Booked" varchar ( 24 ),
Shows varchar ( 24 ),
"Closing Rate" varchar ( 24 ),
Contracts varchar ( 24 ),
"Contracts or shows / Leads" varchar ( 24 ),
"Average Contract Price" varchar ( 24 ),
Sales varchar ( 24 ),
"Sales lower Limit" varchar ( 24 ),
"Sales upper Limit" varchar ( 24 ),
"territory_code" varchar (5)
);


 create table if not exists sharepoint.execution_planner
(
    process_id     varchar(15),
    source         varchar(32),
    object         varchar(64),
    operation      varchar(64),
    load_type      varchar(32),
    status         varchar(15),
    execution_type varchar(10),
    territory      varchar(5),
    s3_path        varchar(128),
    cutoff_date    timestamp encode az64
);

insert into sharepoint.execution_planner(process_id, source, object, operation, load_type, status, execution_type, territory, s3_path,cutoff_date)
VALUES ('spt1','sales planner tool','MNubn02','data extraction','full load','1','true','MN','','1800-01-01');
insert into sharepoint.execution_planner(process_id, source, object, operation, load_type, status, execution_type, territory, s3_path)
    VALUES  ('spt2','sales planner tool','MNubn02','delete query','NA','1','true','MN','');
insert into sharepoint.execution_planner(process_id, source, object, operation, load_type, status, execution_type, territory, s3_path)
    VALUES  ('spt3','sales planner tool','MNubn02','copy command','NA','1','true','MN','');