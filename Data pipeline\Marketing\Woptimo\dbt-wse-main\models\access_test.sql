{{
    config(
        tags=["test"],
    )
}}

with by_mail as (
select email, 
CASE WHEN MAX(all_access) IS TRUE THEN 
(select * from {{ ref('websites_test') }} limit 1)
ELSE
ARRAY_AGG(country_code IGNORE NULLS) END countries
from {{ source('access', 'sheets_access') }}
where email is not null
group by email
), 
by_country 
as (
select 
countries,
email
from by_mail, UNNEST(countries) countries
)
select 
countries AS lang,
ARRAY_AGG(email IGNORE NULLS) access_emails
from by_country
group by lang