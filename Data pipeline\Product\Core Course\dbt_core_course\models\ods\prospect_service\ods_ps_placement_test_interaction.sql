{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        isactive,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        "order",
        id,
        placementtestactivityid,
        type,
        url,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_prospect_service',
            'placementtestinteraction'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    isactive as is_active,
    created,
    lastupdated as last_updated,
    "order",
    id,
    placementtestactivityid as placement_test_activity_id,
    type,
    url
FROM
    rankedrecords
WHERE
    rn = 1;
