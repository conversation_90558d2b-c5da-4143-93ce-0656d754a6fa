import cloud_operations
import secret_manager
import logging
s3 = cloud_operations.S3
redshift = cloud_operations.Redshift
redshift_connect = redshift.connect()

"""reading the redshift credential from secret key management services
and assigning to variables"""
secret_instance = secret_manager.SecretManager
db_details = secret_instance.get_secret('redshift_develop', 'eu-west-1')
clusteridentifier = db_details['clusteridentifier']
database = db_details['database']
dbUser = db_details['dbUser']
iam_role = db_details['iam_role']
region = db_details['region']


class DbQueryExecution:

    @staticmethod
    def execute_query(query, statementname):
        execute_response = redshift_connect.execute_statement(
            ClusterIdentifier=clusteridentifier,
            Database=database,
            DbUser=dbUser,
            Sql=query,
            StatementName=statementname,
            WithEvent=False)

        return execute_response['Id']

    @staticmethod
    def describe_query(query_id):
        global describe_resonse
        describe_query_status = 'Started'
        while (describe_query_status != 'FINISHED') & (describe_query_status != 'FAILED'):
            describe_resonse = redshift_connect.describe_statement(Id=query_id)
            describe_query_status = str(describe_resonse['Status'])
        return describe_resonse

    @staticmethod
    def get_statement_result(query_id):
        table_data = []
        table_header = []
        response_list = []
        table_response = redshift_connect.get_statement_result(Id=query_id)
        table_data.extend(table_response['Records'])
        response_header = table_response['ColumnMetadata']
        while 'NextToken' in table_response:
            table_response = redshift_connect.get_statement_result(Id=query_id, NextToken=table_response['NextToken'])
            table_data.extend(table_response['Records'])
        for column_header in response_header:
            header = column_header['name']
            table_header.append(header)
        for content in table_data:
            list_length = len(content)
            transformed_dict = {}
            indexvalue = 0
            for iterate in range(list_length):
                logging.warning(iterate)
                info = {table_header[indexvalue]: list(content[indexvalue].values())[0]}
                transformed_dict.update(info)
                indexvalue += 1
            response_list.append(transformed_dict)
        return response_list
