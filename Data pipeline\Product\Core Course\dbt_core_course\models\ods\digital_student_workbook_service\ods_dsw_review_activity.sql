{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH RankedRecords AS (
    SELECT 
        id
        ,studentid
        ,coursecontentid
        ,activityreferenceid
        ,activitysequence
        ,activityurl
        ,iscompleted
        ,duration
        ,resetcount
        ,categorytype
        ,{{cast_to_timestamp('created')}} as created
        ,{{cast_to_timestamp('lastupdated')}} as lastupdated
        ,ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdated DESC) AS rn
    FROM 
        {{source('stage_digital_student_workbook_service', 'reviewactivity')}}
)

SELECT
    {{etl_load_date()}}
    ,id
    ,studentid as student_id
    ,coursecontentid as course_content_id
    ,activityreferenceid as activity_reference_id
    ,activitysequence as activity_sequence
    ,activityurl as activity_url
    ,iscompleted as is_completed
    ,duration
    ,resetcount as reset_count
    ,categorytype as category_type
    ,created
    ,lastupdated as last_updated
FROM 
    RankedRecords
WHERE 
    rn = 1;