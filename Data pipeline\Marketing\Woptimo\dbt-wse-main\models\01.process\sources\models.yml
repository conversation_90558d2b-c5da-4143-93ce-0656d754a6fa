version: 2

models:
  - name: mart_seo_page_test
    description: dataset created using DBT and BigQuery for an SEO view of each page 

    columns:
      - name: index_status
        description: indicates the indexing status of a page
      - name: pageviews_organic
        description: number of page views from organic traffic        
      - name: pageviews_ggorganic
        description: number of page views from Google organic traffic
      - name: sessions_organic
        description: number of sessions coming from organic trafic
      - name: sessions_ggorganic
        description : number of sessions coming from google organic trafic
      - name: conv_onlp_allsrc_click_tel
        description: Number of clicks on tel (=conversion), coming from all sources and made on landing page
      - name: conv_onlp_allsrc_click_wa
        description: Number of clicks on Whatsapp (=conversion), coming from all sources and made on landing page
      - name: conv_onlp_allsrc_form_contact
        description: Number of form contact sent (=conversion), coming from all sources and made on landing page
      - name: conv_onlp_allsrc_form_test
        description: Number of form test sent (=conversion), coming from all sources and made on landing page
      - name: conv_onlp_ggorganic_click_tel
        description: Number of clicks on tel (=conversion), coming from Google organic and made on landing page
      - name: conv_onlp_ggorganic_click_wa
        description: Number of clicks on Whatsapp (=conversion), coming from Google organic and made on landing page
      - name: conv_onlp_ggorganic_form_contact
        description: Number of form contact sent (=conversion), coming from Google organic and made on landing page
      - name: conv_onlp_ggorganic_form_test
        description: Number of form test sent (=conversion), coming from all sources and made on landing page
      - name: conv_session_allsrc_click_tel
        description: Number of clicks on tel (=conversion), coming from all sources and made during a session
      - name: impressions_branded
        description: number of impressions on a google query containing the brand name (Wall Street English)
      - name: clicks_branded
        description: number of clicks on a google query containing the brand name (Wall Street English)
      - name: impressions_local
        description: number of impressions resulting from a request from the same country as the language of the website
      - name: clicks_local
        description: number of clicks resulting from a request from the same country as the language of the website
      - name: page_title_length_pixel
        description: length of page title (title tag) in pixels
      - name: status_code
        description: indicates the code status of a page (300,301,404...). Data taken from Screaming Frog 
      - name: meta_description
        description: Enables you to view the meta description associated with a page (dimension, data from Screaming Frog)
      - name: page_type
        description: categorise pages according to classification based on what we found in GTM Lookup table
      - name: queries.query
        description: detailed statement of a query 
      - name: queries.clicks
        description: Number of clicks associated with a query
      - name: queries.impressions
        description: Number of impressions associated with a query
      - name: queries.is_featured_snippet
        description: to find out whether a query has an enhanced result in the Google results page



  #MART SEO Query
  
  # - name: mart_seo_query
  #   description: dataset created using DBT and BigQuery and focus on queries. 

  #   columns:
  #     - name: country_code
  #       description: website country
  #     - name: first_url
  #       description: The first URL )of WSE website) which appears on SERP on a specific query
  #     - name: language_codes
  #       description: to find out what language the website is in (example :fr for content of the tunisian site)


  #MART SEO 404
  
#   - name: mart_seo_404
#     description: dataset created using DBT and BigQuery to get an overview of the number of 404 pages and the pages linked to them




# #ALERT MONTHLY SEO PAGE
  
#   - name: alert_monthly_seo_page
#     description: dataset created using DBT and BigQuery to get an overview of the number of 404 pages and the pages linked to them

