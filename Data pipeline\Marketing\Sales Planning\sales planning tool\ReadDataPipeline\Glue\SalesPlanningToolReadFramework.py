import Packages

S3 = Packages.CloudOperations.S3
secret_instance = Packages.CloudOperations.SecretManager
Redshift = Packages.DbOperations.Database
IamRole = Packages.DbOperations.IamRole
Region = Packages.DbOperations.Region


class SalesPlanningTool:
    @staticmethod
    def ReadWorkbook(FilePath, data_only=True):
        Workbook = Packages.load_workbook(FilePath, data_only=data_only)
        return Workbook

    @staticmethod
    def ReadTable(Workbook, SheetName, TableName):
        Sheet = Workbook[SheetName]
        TableRange = Sheet.tables[TableName].ref
        TableData = []
        for row in Sheet[TableRange]:
            TableData.append([cell.value for cell in row])
        TableDf = Packages.pd.DataFrame(TableData)
        new_header = TableDf.iloc[0]
        TableDf = TableDf[1:]
        TableDf.columns = new_header
        return TableDf

    @staticmethod
    def FilterResponse(DataDf, TableName):
        if TableName == "planning_tool":
            """filter the response"""
            FirstDayOfMonth = Packages.pd.Timestamp.now().strftime('%Y-%m-01')
            DataDf['Month'] = Packages.pd.to_datetime(DataDf['Month'], infer_datetime_format=True)
            ResponseDf = DataDf.loc[(DataDf['Month'] >= FirstDayOfMonth)]
            ResponseDf = ResponseDf.reset_index(drop=True)
            return ResponseDf
        if TableName == "calendar":
            ResponseDf = DataDf[
                DataDf['Year 2023'].isin(['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August',
                                          'September', 'October', 'November', 'December'])]
            # Reset Index
            ResponseDf = ResponseDf.reset_index(drop=True)
            return ResponseDf

    @staticmethod
    def CalendarTransform(ResponseDf, year):
        ListOfDates = []
        for month in range(1, 13):  # loop through all the days of the year
            WorkingDaysList = []
            for Day in range(1, 32):
                try:
                    DateObject = Packages.datetime.date(year, month, Day).strftime("%Y-%m-%d")
                    WorkingDaysList.append(DateObject)
                except ValueError:  # the date is invalid (e.g. Feb 30), so ignore it
                    pass
            ListOfDates.append(WorkingDaysList)
        LisOfWorkingDays = ResponseDf.iloc[:, 1:].values.tolist()
        TransformedCalender = []
        for Index in range(0, len(ListOfDates), 1):
            NestedIndex = 0
            for date in ListOfDates[Index]:
                CalendarDict = {"date": date, "working_day": LisOfWorkingDays[Index][NestedIndex]}
                TransformedCalender.append(CalendarDict)
                NestedIndex += 1
        CalenderDf = Packages.pd.DataFrame(TransformedCalender)
        return CalenderDf

    @staticmethod
    def AddNewColumns(DataResponse, Territory, ReferenceCenterId, TerritoryReferenceId, CycleId):
        DataResponse.insert(0, 'CycleId', CycleId)
        DataResponse['Territory'] = Territory
        DataResponse['ReferenceCenterId'] = ReferenceCenterId
        DataResponse['TerritoryReferenceId'] = TerritoryReferenceId
        DataResponse['CycleId'] = CycleId
        return DataResponse

    @staticmethod
    def CaptureResponse(DataResponse, Territory, Center, SheetName, Bucket, CycleId):
        """Function Is Used To Store Response In S3 As CSV File"""
        FileKey = \
            f"SalesPlanningTool/{Territory}/{Center}/{SheetName}/{CycleId}/{Territory}_{Center}_{SheetName}Response.csv"
        S3WriteDataResponse = S3.WriteCsvFile(FilePath=FileKey,
                                              Bucket=Bucket,
                                              DataResponse=DataResponse)
        Packages.logging.warning("S3WriteDataResponse:'%s'", format(S3WriteDataResponse))
        return FileKey

    @staticmethod
    def DataExtractionProcess(CycleId, Territory, Center, ReferenceCenterId, TerritoryReferenceId, SubSite, Folder,
                              File, SheetName, TableName, Bucket):
        """Function Is Used To Extract Data From Sales Planning Tool Sharepoint Folder"""
        SharepointCredential = secret_instance.GetSecret('SharePointSalesPlanningTool', 'eu-west-1')
        """Variable Declaration for Sharepoint authentication"""
        Url, Username, Password, SiteUrl = [SharepointCredential[key] for key in
                                            ('Url', 'Username', 'Password', 'SiteUrl')]
        """Authentication to SharePoint"""
        AuthCookie = Packages.Office365(Url, username=Username, password=Password).GetCookies()
        Site = Packages.Site(SiteUrl + SubSite, version=Packages.Version.v2016, authcookie=AuthCookie)
        """Get the File from Sharepoint"""
        SiteFolder = Site.Folder(Folder)
        response = SiteFolder.get_file(File)
        """Getting the Sheet name"""
        ReadWorkbook = SalesPlanningTool.ReadWorkbook(FilePath=Packages.io.BytesIO(response))
        ReadTable = SalesPlanningTool.ReadTable(Workbook=ReadWorkbook, SheetName=SheetName, TableName=TableName)
        if TableName == "planning_tool" or TableName == "calendar":
            FilteredResponse = SalesPlanningTool.FilterResponse(DataDf=ReadTable, TableName=TableName)
            if TableName == "calendar":
                DataResponse = SalesPlanningTool.CalendarTransform(ResponseDf=FilteredResponse, year=2023)
            else:
                DataResponse = FilteredResponse
        else:
            DataResponse = ReadTable
        if len(DataResponse) == 0:
            Packages.logging.warning("no response to store")
            Summary = {
                "RecordsProcessed": 0
            }
            return Summary
        AddedColumns = SalesPlanningTool.AddNewColumns(DataResponse=DataResponse, Territory=Territory,
                                                       ReferenceCenterId=ReferenceCenterId,
                                                       TerritoryReferenceId=TerritoryReferenceId, CycleId=CycleId)
        S3WriteResponse = SalesPlanningTool.CaptureResponse(DataResponse=AddedColumns, Territory=Territory,
                                                            Center=Center, SheetName=SheetName, Bucket=Bucket,
                                                            CycleId=CycleId)
        """execute delete query if data is present in redshift table for current cycle to avoid duplication"""
        DeleteQuery = """delete from sales_planning_tool.{} where CycleId ='{}' and ReferenceCenterId ='{}' 
        and TerritoryReferenceId = '{}';""". \
            format(TableName, CycleId, ReferenceCenterId, TerritoryReferenceId)
        Packages.logging.warning("DeleteQuery:'%s'", format(DeleteQuery))
        ExecuteDeleteQuery = Redshift.Execution(ExecutionType="WriteTable",
                                                Query=DeleteQuery,
                                                StatementName=f"{Territory}_{Center}_{SheetName}")
        Packages.logging.warning("ExecuteDeleteQuery:'%s'", format(ExecuteDeleteQuery))
        """execute copy command to move data from s3 file to redshift table"""
        CopyCommandQuery = """COPY sales_planning_tool.{} FROM 's3://{}/{}' iam_role '{}' region '{}' IGNOREHEADER 1 CSV
                                                                    timeformat 'auto';""" \
            .format(TableName, Bucket, S3WriteResponse, IamRole, Region)
        Packages.logging.warning("CopyCommandQuery:'%s'", format(CopyCommandQuery))
        ExecuteCopyCommand = Redshift.Execution(ExecutionType="WriteTable",
                                                Query=CopyCommandQuery,
                                                StatementName=f"{Territory}_{Center}_{SheetName}")
        Packages.logging.warning("ExecuteCopyCommand:'%s'", format(ExecuteCopyCommand))
        Summary = {
            "S3": {"DataResponse": S3WriteResponse},
            "Redshift": ExecuteCopyCommand,
            "RecordsProcessed": len(AddedColumns)
        }
        """adding new requirement flag column"""
        CurrentDate = Packages.datetime.date.today()
        CurrentDay = CurrentDate.day
        if CurrentDay <= 5 or CurrentDay >= 25:
            DWTableNamesSwitch = {"planning_tool": "PlanningTool",
                                  "calendar": "Calendar",
                                  "consultant_targets": "ConsultantTargets"}
            DWTableName = DWTableNamesSwitch.get(TableName)
            Packages.logging.warning("DWTableName:'%s'", format(DWTableName))
            """execute delete query if data is present in redshift DW table for current cycle to avoid duplication"""
            DWTableDeleteQuery = """delete from SalesPlanningTool.{}
            where Month = (select max(Month) from sales_planning_tool.planning_tool
                                            where  ReferenceCenterId = '{}'
                                    and TerritoryReferenceId = '{}')
            and ReferenceCenterId = '{}' and TerritoryReferenceId = '{}';""". \
                format(DWTableName, ReferenceCenterId, TerritoryReferenceId, ReferenceCenterId,
                       TerritoryReferenceId)
            Packages.logging.warning("DWTableDeleteQuery:'%s'", format(DWTableDeleteQuery))
            ExecuteDWTableDeleteQuery = Redshift.Execution(ExecutionType="WriteTable",
                                                           Query=DWTableDeleteQuery,
                                                           StatementName=f"{Territory}_{Center}_{SheetName}")
            Packages.logging.warning("ExecuteDWTableDeleteQuery:'%s'", format(ExecuteDWTableDeleteQuery))
            """DW table Insert query"""
            if TableName == "calendar" or TableName == "consultant_targets":
                DWTableInsertQuery = """insert into SalesPlanningTool.{}(select *,
                                TO_DATE(convert(timestamp, substring(CycleId, 1, 8)), 'YYYY-MM-DD') AS LoadDate
                                ,(select max(Month) from sales_planning_tool.planning_tool where ReferenceCenterId ='{}' and TerritoryReferenceId ='{}') as Month
                                from sales_planning_tool.{}
                                where CycleId in (select
                                    case
                                        when substring(CYCLEID, 7, 2) between '01' and '05' Then CycleId
                                        when substring(CYCLEID, 7, 2) between '25' and '31' then CycleId
                                        end as CycleId
                                from (select max(CycleId) CYCLEID
                                    from sales_planning_tool.{}
                                    where ReferenceCenterId = '{}'
                                and TerritoryReferenceId = '{}')B
                                )
                                and ReferenceCenterId = '{}'
                                and TerritoryReferenceId = '{}');""". \
                    format(DWTableName, ReferenceCenterId, TerritoryReferenceId, TableName, TableName,
                           ReferenceCenterId, TerritoryReferenceId,
                           ReferenceCenterId,
                           TerritoryReferenceId)
            else:
                DWTableInsertQuery = """insert into SalesPlanningTool.{}(select *,
                TO_DATE(convert(timestamp, substring(CycleId, 1, 8)), 'YYYY-MM-DD') AS LoadDate
                from sales_planning_tool.{}
                where CycleId in (select
                    case
                        when substring(CYCLEID, 7, 2) between '01' and '05' Then CycleId
                        when substring(CYCLEID, 7, 2) between '25' and '31' then CycleId
                        end as CycleId
                from (select max(CycleId) CYCLEID
                    from sales_planning_tool.{}
                    where ReferenceCenterId = '{}'
                and TerritoryReferenceId = '{}')B
                )
                and ReferenceCenterId = '{}'
                and TerritoryReferenceId = '{}');""". \
                    format(DWTableName, TableName, TableName, ReferenceCenterId, TerritoryReferenceId,
                           ReferenceCenterId,
                           TerritoryReferenceId)
            Packages.logging.warning("DWTableInsertQuery:'%s'", format(DWTableInsertQuery))
            ExecuteDWTableInsertQuery = Redshift.Execution(ExecutionType="WriteTable",
                                                           Query=DWTableInsertQuery,
                                                           StatementName=f"{Territory}_{Center}_{SheetName}")
            Packages.logging.warning("ExecuteDWTableInsertQuery:'%s'", format(ExecuteDWTableInsertQuery))
        return Summary
