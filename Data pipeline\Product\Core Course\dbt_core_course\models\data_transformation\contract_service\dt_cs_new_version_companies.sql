{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_new_version_companies'
        ) }}
    
    {% if is_incremental() %}
        where last_updated_date > ((select max(last_updated_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    newvercompanies.id  as id,
    name,
    code,
    center_owned,
    territory_id,
    license_number,
    created_by_id,
    modified_by_id,
    created_date,
    {{convert_to_local_timestamp('created_date','time_zone_id')}} as local_created_date,
    last_updated_date,
    {{convert_to_local_timestamp('last_updated_date','time_zone_id')}} as local_last_updated_date
from ods_data as newvercompanies
    left join (
        select id,
            center_reference_id 
        from {{ ref( 'ods_cs_centers' ) }}
    ) as center on center.id = newvercompanies.center_owned
    left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on center.center_reference_id = tz.center_reference_id