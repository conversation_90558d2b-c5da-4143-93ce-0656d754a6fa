import sys
from pyspark import SparkConf, SparkContext
from pyspark.sql import SparkSession
from awsglue.utils import getResolvedOptions
from pyspark.sql import SparkSession
import logging
import CloudOperations
import LogFileGeneration
from pyspark.sql.functions import from_unixtime, to_timestamp, when, lit

logs = LogFileGeneration.LogFile


args = getResolvedOptions(sys.argv, ['InputType', 'FilePathKey', 'Table', 'WritePathKey', 'WriteMode', 'CycleId', 'Bucket'])

conf = SparkConf()
conf.setMaster("local").setAppName("My app")
sc = SparkContext.getOrCreate(conf=conf)
spark = SparkSession(sc)
spark = SparkSession.builder.appName("MultipleSQLStatements").getOrCreate()

bucket = args['Bucket']
input_type = args['InputType']
file_path = args['FilePath<PERSON><PERSON>']
table_name = "hubspot." + args['Table']
write_path = args['WritePathKey']
write_mode = args['WriteMode']
cycle_id = args['CycleId']

def readfile(input_type,  file_path):
    if input_type == "json":
        return spark.read.json(file_path)
    if input_type == "parquet":
        return spark.read.format("parquet").option("header", "true").load(file_path)

write_response = readfile(input_type=input_type, file_path=file_path)
write_response.write.mode(write_mode).saveAsTable(table_name, path=write_path)
log_file = logs.SaveTableLog(TableName=table_name, Status=200,
                                                       WritePath=write_path,
                                                       CycleId=cycle_id,
                                                        Bucket=bucket)
logging.warning("The data has been loaded in to table")