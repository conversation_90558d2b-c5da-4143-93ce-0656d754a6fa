{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH RankedRecords AS (
    SELECT 
        {{cast_to_timestamp("starttime")}} as starttime,
        {{cast_to_timestamp("endtime")}} as endtime,
        numberofseats,
        categoryfrombooking,
        iscancelled,
        communicationaccounttype,
        {{cast_to_timestamp("created")}} as created,
        {{cast_to_timestamp("lastupdated")}} as lastupdated,
        noofseatsinstandby,
        id,
        centerreferenceid,
        classtypeid,
        description,
        teacherid,
        source,
        createdby,
        lastupdatedby,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdated DESC) AS rn
    FROM 
        {{source('stage_schedule_and_booking_service', 'scheduledclass')}}
)

SELECT
    {{etl_load_date()}},
    starttime as start_time,
    endtime as end_time,
    numberofseats as number_of_seats,
    categoryfrombooking as category_from_booking,
    iscancelled as is_cancelled,
    communicationaccounttype as communication_account_type,
    created,
    lastupdated as last_updated,
    noofseatsinstandby as no_of_seats_in_stand_by,
    id,
    centerreferenceid as center_reference_id,
    classtypeid as class_type_id,
    description,
    teacherid as teacher_id,
    source,
    createdby as created_by,
    lastupdatedby as last_updated_by
FROM
    RankedRecords
WHERE
    rn = 1