{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH calendar as
    (
    select
    "date",
    "month_name"
    from reporting.dim_calendar
    where 
    "date" = date(date_add('day',-1, current_date))
    )

SELECT
fds."date",
cal.month_name as month,
fds.group_id,
fds.center_reference_id,
fds.contract_type,
fds.location,
fds.class_access_type,
fds.service_type,
fds.is_promotional,
fds.product_type,
cast(fds.bookmark_mm_level as integer) as "CurrentLevel",
case when fds.bookmark_mm_unit is null then (cast(fds.bookmark_mm_level as integer) * 4) +1 
     else cast(fds.bookmark_mm_unit as integer) end as "CurrentUnit",
case 
    when cast(fds.bookmark_mm_level as integer) = 1 then '1. Level 1'
    when cast(fds.bookmark_mm_level as integer) = 2 then '2. Level 2'
    when cast(fds.bookmark_mm_level as integer) in (3,4,5) then '3. WayStage'
    when cast(fds.bookmark_mm_level as integer) in (6,7,8,9) then '4. Upper WayStage'
    when cast(fds.bookmark_mm_level as integer) in (10,11,12,13) then '5. Threshold'
    when cast(fds.bookmark_mm_level as integer) in (14,15,16,17) then '6. Milestone'
    when cast(fds.bookmark_mm_level as integer) in (18,19,20) then '7. Mastery'
    else 'Other'
end as "Current Stage",
count(fds.valid_current_date) as "Current Valid Students",
count(case when (fdp.mm_activities_30days > 0 OR fdp.encounters_attended_30days > 0) then fds.valid_current_date else null end) AS "Current Active Students Serviced 30days",
CAST(current_timestamp AS TIMESTAMP(6)) AS load_date
FROM calendar cal
left join {{ ref('fact_daily_students') }} fds on cal."date" = fds."date" 
left join {{ ref('fact_daily_progress') }} fdp on fds."date" = fdp."date" and fds.student_reference_id = fdp.student_id
WHERE 
fds.product_type ='core course'
group by
fds."date",
fds.group_id,
fds.center_reference_id,
fds.contract_type,
fds.location,
fds.class_access_type,
fds.service_type,
fds.is_promotional,
fds.product_type,
fds.bookmark_mm_level,
case when fds.bookmark_mm_unit is null then (cast(fds.bookmark_mm_level as integer) * 4) +1 
     else cast(fds.bookmark_mm_unit as integer) end,
cal.month_name