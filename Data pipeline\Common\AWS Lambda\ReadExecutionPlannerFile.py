import json
import boto3

# Creating an S3 resource object
Boto3Resource = boto3.resource('s3')

# Creating a path object for the JSON file that contains information about the S3 bucket we want to access
SourceSystemBucketInfoPath = Boto3Resource.Object\
    ('gluejob-dependencies-production', 'LambdaDependencies/SourceSystemBucketInfo.json')

# Getting the content of the JSON file
SourceSystemBucketInfoContent = SourceSystemBucketInfoPath.get()['Body'].read().decode('utf-8')

# Parsing the JSON data and storing it in a dictionary
SourceSystemBucketInfo = json.loads(SourceSystemBucketInfoContent)

# Defining the Lambda function
def lambda_handler(event, context):

    # Extracting the name of the S3 bucket we want to access from the 'SourceSystemBucketInfo' dictionary
    Bucket = SourceSystemBucketInfo[event['input']]['Bucket']

    # Creating a path object for the JSON file in the specified S3 bucket
    FilePath = Boto3Resource.Object(Bucket, 'Execution.json')

    # Getting the contents of the JSON file and decoding it
    FileContent = FilePath.get()['Body'].read().decode('utf-8')

    # Parsing the JSON data and storing it in a dictionary
    Execution = json.loads(FileContent)
    print(Execution)

    # Returning the dictionary as the output of the Lambda function
    return Execution
