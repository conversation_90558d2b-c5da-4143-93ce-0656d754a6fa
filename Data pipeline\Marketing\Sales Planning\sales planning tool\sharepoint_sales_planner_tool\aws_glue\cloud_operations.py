import json
import boto3
import io
import logging


class Redshift:
    @staticmethod
    def connect():
        redshift = boto3.client('redshift-data')
        return redshift


class S3:
    @staticmethod
    def connect(bucketname, filepath):
        s3_resource = boto3.resource('s3')
        fields_file = s3_resource.Object(bucketname, filepath)
        return fields_file

    @staticmethod
    def read_json_file(fields_file):
        file_content = fields_file.get()['Body'].read().decode('utf-8')
        fields = json.loads(file_content)
        return fields

    @staticmethod
    def write_csv_file(file_key, bucket, data_request):
        try:
            boto3_client = boto3.client('s3')
            s3_path = f"s3://{bucket}/" + file_key
            logging.warning("s3_path:'%s'", format(s3_path))
            with io.StringIO() as csv_buffer:
                data_request.to_csv(csv_buffer, index=False, quoting=1)
                boto3_client.put_object(Bucket=bucket, Key=file_key, Body=csv_buffer.getvalue())
            return s3_path
        except Exception as e:
            logging.warning("Raised Exception", e)
            return None
