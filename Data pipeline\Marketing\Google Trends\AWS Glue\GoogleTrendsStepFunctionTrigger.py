import CloudOperations
import logging

# Assigning the StepFunction and ExecutionInput variables
StepFunction = CloudOperations.StepFunction
ExecutionInput = "GoogleTrends"

# Assigning the StateMachineArn variable with the ARN of a Step Function state machine
StateMachineArn = 'arn:aws:states:eu-north-1:262158335980:stateMachine:GoogleTrendsWorkFlow'

# Checking if there are any running executions for the state machine using the CheckStepFunctionsRunning method
CheckStateMachine = StepFunction.CheckStepFunctionsRunning(StateMachineArn)

# If there are no running executions for the state machine, start a new execution using the StartStepFunction method
if len(CheckStateMachine['executions']) == 0:
    logging.warning("There is no running step function proceed to trigger the step function")
    StepFunction.StartStepFunction(ExecutionInput, StateMachineArn)

# If there are running executions for the state machine, log a warning message
else:
    logging\
        .warning("There is currently step function in running status please verify for long run of step function")
