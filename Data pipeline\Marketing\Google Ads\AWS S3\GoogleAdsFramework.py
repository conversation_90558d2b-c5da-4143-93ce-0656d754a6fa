import Packages

S3 = Packages.CloudOperations.S3
Redshift = Packages.DbOperations.Database
IamRole = Packages.DbOperations.IamRole
Region = Packages.DbOperations.Region

SecretInstance = Packages.CloudOperations.SecretManager

# Reading the Google Ads credential from secret key management services and assigning to variables
GoogleAccountCredential = SecretInstance.GetSecret('GoogleAds', 'eu-north-1')

# Loading the Google Ads client using the retrieved credentials
GoogleAdsClient = Packages.GoogleAdsClient.load_from_dict(GoogleAccountCredential)


class Ads:
    @staticmethod
    def GetCampaignInformation(AccountId, FromDate, ToDate, Territory):
        """Function is used to extract data from Google Ads"""
        ResponseList = []

        # Get the Google Ads service
        GoogleAdsService = GoogleAdsClient.get_service("GoogleAdsService")

        # Construct the query to fetch campaign information
        Query = """
            SELECT
            segments.date,
            campaign.id,
            campaign.name,
            campaign.status,
            campaign_budget.amount_micros,
            campaign.optimization_score,
            campaign.advertising_channel_type,
            metrics.clicks,
            metrics.impressions,
            metrics.conversions,
            metrics.ctr,
            metrics.average_cpc,
            metrics.average_cost,
            metrics.cost_micros,
            campaign.bidding_strategy_type
            FROM campaign
            WHERE segments.date BETWEEN '{}' AND '{}'
            """.format(FromDate, ToDate)
        """ Issues a search request using streaming."""
        # Execute the query and retrieve the results as a stream
        Stream = GoogleAdsService.search_stream(customer_id=AccountId, query=Query)

        # Process the stream of results
        for Batch in Stream:
            Packages.logging.warning("Inside Loop")

            # Iterate over each row in the batch
            for Row in Batch.results:
                # Transform the row data into a dictionary
                TransformedDict = {
                    "Date": Row.segments.date,
                    "AccountId": AccountId,
                    "ResourceName": Row.campaign.resource_name,
                    "Id": Row.campaign.id,
                    "Status": Row.campaign.status,
                    "Name": Row.campaign.name,
                    "AmountMicros": Row.campaign_budget.amount_micros,
                    "OptimizationScore": Row.campaign.optimization_score,
                    "AdvertisingChannelType": Row.campaign.advertising_channel_type,
                    "Impressions": Row.metrics.impressions,
                    "Clicks": Row.metrics.clicks,
                    "Conversions": Row.metrics.conversions,
                    "Ctr": Row.metrics.ctr,
                    "AverageCpc": Row.metrics.average_cpc,
                    "AverageCost": Row.metrics.average_cost,
                    "CostMicros": Row.metrics.cost_micros,
                    "BiddingStrategyType": Row.campaign.bidding_strategy_type,
                    "Territory": Territory
                }

                # Append the transformed dictionary to the response list
                ResponseList.append(TransformedDict)

            # Convert the response list to a DataFrame
            AdsAccountDf = Packages.pd.DataFrame(ResponseList)

            # Define the column order of the DataFrame
            ResponseColumnName = ['Date', 'AccountId', 'ResourceName', 'Id', 'Status', 'Name', 'AmountMicros',
                                  'OptimizationScore', 'AdvertisingChannelType', 'Impressions', 'Clicks',
                                  'Conversions', 'Ctr', 'AverageCpc', 'AverageCost', 'CostMicros',
                                  'BiddingStrategyType', 'Territory']

            # Reindex the DataFrame columns based on the defined order
            ReindexAdsAccountDf = AdsAccountDf.reindex(columns=ResponseColumnName)
            return ReindexAdsAccountDf

        # If no data is received in the stream, log a warning
        Packages.logging.warning("No Data Response")

        # Return an empty DataFrame
        EmptyDf = Packages.pd.DataFrame()
        return EmptyDf

    @staticmethod
    def CaptureResponse(DataResponse, Territory, AccountId, Bucket, CycleId):
        """Function Is Used To Store Response In S3 As CSV File"""

        # Find the minimum and maximum dates from the 'Date' column in the DataResponse
        FromDate = min(DataResponse['Date'])
        ToDate = max(DataResponse['Date'])

        # Define the file key for the CSV file in S3
        FileKey = f"GoogleAdsResponse/{Territory}/{AccountId}/{CycleId}/" + \
                  f"{FromDate}-{ToDate}/{Territory}Response.csv"

        # Write the DataResponse as a CSV file in S3
        S3WriteDataResponse = S3.WriteCsvFile(FilePath=FileKey,
                                              Bucket=Bucket,
                                              DataResponse=DataResponse)
        Packages.logging.warning("S3WriteDataResponse:'%s'", format(S3WriteDataResponse))
        return FileKey

    @staticmethod
    def DataExtractionProcess(AccountId, Territory, LoadType, CutoffDate, Bucket, CycleId):
        """Function Is Used To Extract Data From Ads"""
        Packages.logging.warning("AccountId:'%s'", format(AccountId))
        Packages.logging.warning("Territory:'%s'", format(Territory))
        Packages.logging.warning("LoadType:'%s'", format(LoadType))
        Packages.logging.warning("CutoffDate:'%s'", format(CutoffDate))
        """declaring empty Data Frame"""
        ResponseDf = Packages.pd.DataFrame()
        """date conversion from date: previous date +1 and ToDate: current date -1"""
        DateFormat = "%Y-%m-%d"

        # Convert CutoffDate to FromDate by adding 1 day
        FromDateConversion = \
            Packages.datetime.datetime.strptime(CutoffDate, DateFormat) + Packages.datetime.timedelta(days=1)
        FromDate = FromDateConversion.strftime(DateFormat)
        Packages.logging.warning(FromDate)

        # Get the current day's date and convert it to ToDate by subtracting 1 day
        GetCurrentDayDate = Packages.datetime.datetime.today().strftime(DateFormat)
        ToDateConversion = \
            Packages.datetime.datetime.strptime(GetCurrentDayDate, DateFormat) - Packages.datetime.timedelta(days=1)
        ToDate = ToDateConversion.strftime(DateFormat)
        Packages.logging.warning(ToDate)
        if LoadType == "Initial":
            # Get the date range between FromDate and ToDate
            DateRange = Packages.Utils.Tools.GetMonthRanges(FromDate, ToDate)

            # Iterate over the date range and extract campaign information for each date
            for Date in DateRange:
                # Extract campaign information for the specified AccountId, FromDate, ToDate, and Territory
                InitialResponseDf = Ads.GetCampaignInformation(AccountId=AccountId,
                                                               FromDate=Date['FromDate'],
                                                               ToDate=Date['ToDate'],
                                                               Territory=Territory)
                if len(InitialResponseDf) != 0:
                    ResponseDf = Packages.pd.concat([ResponseDf, InitialResponseDf]).reset_index(drop=True)
        if LoadType == "Incremental":
            # Extract campaign information for the specified AccountId, FromDate, ToDate, and Territory
            ResponseDf = Ads.GetCampaignInformation(AccountId=AccountId,
                                                    FromDate=FromDate,
                                                    ToDate=ToDate,
                                                    Territory=Territory)
        if len(ResponseDf) == 0:
            Packages.logging.warning("no response to store")

            # Prepare summary when no response is found
            Summary = {
                "CutoffDate": ToDate,
                "RecordsProcessed": 0
            }
            return Summary

        # Capture the response data in S3
        S3WriteResponse = Ads.CaptureResponse(DataResponse=ResponseDf,
                                              Territory=Territory,
                                              AccountId=AccountId,
                                              Bucket=Bucket,
                                              CycleId=CycleId)
        """execute copy command to move data from s3 file to redshift table"""

        # Prepare the COPY command query to move data from S3 to Redshift table
        CopyCommandQuery = """COPY google.ads FROM 's3://{}/{}' iam_role '{}' region '{}' IGNOREHEADER 1 CSV
                                                            timeformat 'auto';""" \
            .format(Bucket, S3WriteResponse, IamRole, Region)
        Packages.logging.warning("CopyCommandQuery:'%s'", format(CopyCommandQuery))

        # Execute the COPY command to write data to the Redshift table
        ExecuteCopyCommand = Redshift.Execution(ExecutionType="WriteTable",
                                                Query=CopyCommandQuery,
                                                StatementName=f"{Territory}")
        Packages.logging.warning("ExecuteCopyCommand:'%s'", format(ExecuteCopyCommand))

        # Prepare the summary with relevant information
        Summary = {
            "S3": {"DataResponse": S3WriteResponse},
            "Redshift": ExecuteCopyCommand,
            "CutoffDate": max(ResponseDf['Date']),
            "RecordsProcessed": len(ResponseDf)
        }
        return Summary
