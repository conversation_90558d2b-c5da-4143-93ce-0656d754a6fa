import runpy
import sys
import logging


class Test:
    @staticmethod
    def execute_script():
        runpy.run_module('sharepoint_sales_planner_tool_execution_planner', run_name='__name__')
        runpy.run_module('sharepoint_sales_planner_tool', run_name='__name__')
        return "-------execute_completed--------"


"""dry run test with single territory and center"""
"""Executing Full Load """
case_1 = Test.execute_script()
logging.warning(case_1)

