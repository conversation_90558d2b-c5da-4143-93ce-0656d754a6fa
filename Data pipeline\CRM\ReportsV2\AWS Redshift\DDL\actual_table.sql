create table if not exists hubspot_crm.actual_table
(
    date_value             date encode az64,
    weekday                date encode az64,
    yearmonth              varchar(48),
    territoryname          varchar(128),
    centername             varchar(40),
    source                 varchar(40),
    hubspot_owner_id       varchar(128),
    individual_corporate   varchar(128),
    leads                  bigint encode az64,
    mql                    bigint encode az64,
    useful_contact         bigint encode az64,
    booked                 bigint encode az64,
    shows                  bigint encode az64,
    contracts              bigint encode az64,
    total_sales_amount     double precision,
    leads_cum              bigint encode az64,
    mql_cum                bigint encode az64,
    useful_contract_cum    bigint encode az64,
    booked_cum             bigint encode az64,
    shows_cum              bigint encode az64,
    contract_cum           bigint encode az64,
    total_sales_amount_cum double precision
);