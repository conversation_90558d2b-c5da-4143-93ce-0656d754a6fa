{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_group_class_access_type'
        ) }}

    {% if is_incremental() %}
        where last_updated_date > ((select max(last_updated_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    grpclsacstype.id as id,
    group_id,
    clsacstypes.name as class_access_type,
    max_no_of_cc_and_sc_classes,
    is_active,
    modified_by_id,
    created_date,
    {{convert_to_local_timestamp('created_date','time_zone_id')}} as local_created_date,
    last_updated_date,
    {{convert_to_local_timestamp('last_updated_date','time_zone_id')}} as local_last_updated_date,
    is_access_period_permanent         
from ods_data as grpclsacstype
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_class_access_types' ) }}
    ) as clsacstypes on grpclsacstype.class_access_type_id = clsacstypes.id
    left join (
            select id,
                center_id
            from {{ ref( 'ods_cs_groups' ) }}
        ) as grp on grpclsacstype.group_id = grp.id
    left join (
        select id,
            center_reference_id 
        from {{ ref( 'ods_cs_centers' ) }}
    ) as center on center.id = grp.center_id
    left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on center.center_reference_id = tz.center_reference_id