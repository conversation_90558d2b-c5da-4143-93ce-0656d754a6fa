{{ config(
    MATERIALIZED = 'table',
    table_type = 'iceberg',
    FORMAT = 'parquet'
) }}

SELECT
        A.created_date
        ,A.contract_number
        ,A.master_contract_id
        ,A.company_name
        ,A.center_reference_id
        ,A.territory_name
        ,A.company_code
        ,A.formatted_mod_date
        ,A.start_date
        ,A.end_date
        ,A.consultant
        ,A.editor
        ,A.mod_reason
        ,cast(null as varchar) as course_type
        ,'master' as access_type
        ,'master' as service_type
        ,0 as is_promotional
        ,0 as is_transferin
        ,'master' as private_master
        --,min(A.OriginalLimit) as OriginalLimit
        --,min(A.ModifiedLimit) as ModifiedLimit
        --,sum(A.ModifiedLimit - A.OriginalLimit) as LimitDifference
        ,min(A.original_price) as original_price
        ,min(A.modified_price) as modified_price
        ,sum(A.modified_price - A.original_price) as price_difference
FROM
        (
        SELECT
                CT.Id AS master_contract_id
                ,CT.master_contract_number AS contract_number
                ,U.name as company_name
                ,U.Code as company_code
                ,C.center_reference_id AS center_reference_id
                ,LS_TR.name AS territory_name
                ,MODS.created_date
                ,COALESCE(MODS.effective_date,{{convert_to_local_timestamp('MODS.created_date','tz.time_zone_id')}}) as formatted_mod_date
                ,CT.start_date
                ,CT.end_date
                ,MODS.modified_price
                ,MODS.original_price
                ,MODS.mod_reason
                ,U1.last_name || ' ' || U1.first_name AS consultant
                ,MODS.editor
        FROM
                {{ref("ods_cs_centers")}} C
        INNER JOIN     {{ ref('ods_cs_master_contracts') }} CT
        ON      C.Id = CT.center_id
        LEFT JOIN
                (
                        SELECT  *
                                        ,ROW_NUMBER() OVER(PARTITION BY master_contract_id ORDER BY created_date desc) AS RN
                        FROM    "awsdatacatalog"."ods_contract_service"."ods_cs_contract_validations"
                        where master_contract_id is not null
                                        and state = 2
                )       CV
        ON      CV.master_contract_id = CT.Id
        AND     CV.RN = 1
        LEFT JOIN
                (
                SELECT
                        MCC.master_contract_id
                        ,MCCAI.created_date
                        ,MCCAI.reason as mod_reason
                        ,MCCAI.effective_date
                        ,U2.last_name || ' ' || U2.first_name AS editor
                        ,sum(CAST(CASE WHEN MCCAI.modified_field_id = '4c38bdb1-e878-4661-86ae-53d9b347b565' THEN MCCAI.present_value ELSE NULL END AS DECIMAL(38,2))) AS modified_price    
                        ,sum(CAST(CASE WHEN MCCAI.modified_field_id = '4c38bdb1-e878-4661-86ae-53d9b347b565' THEN MCCAI.previous_value ELSE NULL END AS DECIMAL(38,2))) AS original_price   
                        --,CAST(CASE WHEN MCCAI.modified_field_id = 'E49035BD-C771-4C75-9DA3-8A8515821CFB' THEN MCCAI.present_value ELSE NULL END AS DECIMAL(38,2)) AS ModifiedLimit        
                        --,CAST(CASE WHEN MCCAI.modified_field_id = 'E49035BD-C771-4C75-9DA3-8A8515821CFB' THEN MCCAI.previous_value ELSE NULL END AS DECIMAL(38,2)) AS OriginalLimit       
                from  "awsdatacatalog"."ods_contract_service"."ods_cs_master_contract_courses" MCC
                LEFT JOIN       "awsdatacatalog"."ods_contract_service"."ods_cs_master_contract_course_audit_info" MCCAI
                ON      MCC.Id = MCCAI.master_contract_course_id
                LEFT JOIN       "awsdatacatalog"."ods_contract_service"."ods_cs_product_types" PT
                ON      PT.ID = MCC.product_type_id
                INNER JOIN      "awsdatacatalog"."ods_contract_service"."ods_cs_users" U2
                ON      MCCAI.modified_by_id = U2.Id    --editor
                where MCCAI.modified_field_id in ( '4c38bdb1-e878-4661-86ae-53d9b347b565'       --Price
                                                                                )
                and MCCAI.change_type = 8
                group by
                        MCC.master_contract_id
                        ,MCCAI.created_date
                        ,MCCAI.reason
                        ,MCCAI.effective_date
                        ,U2.last_name || ' ' || U2.first_name

                UNION ALL

                SELECT
                MC.Id as master_contract_id
                        ,MCAI.created_date
                        ,MCAI.reason as mod_reason
                        ,MCAI.effective_date
                        ,U2.last_name || ' ' || U2.first_name AS editor
                        ,sum(CAST(CASE WHEN MCAI.modified_field_id = '14955304-662d-40ca-87d3-62692722dccf' THEN MCAI.present_value ELSE NULL END AS DECIMAL(38,2))) AS modified_price      
                        ,sum(CAST(CASE WHEN MCAI.modified_field_id = '14955304-662d-40ca-87d3-62692722dccf' THEN MCAI.previous_value ELSE NULL END AS DECIMAL(38,2))) AS original_price     
                from  "awsdatacatalog"."ods_contract_service"."ods_cs_master_contracts" MC
                LEFT JOIN       "awsdatacatalog"."ods_contract_service"."ods_cs_master_contract_audit_info" MCAI
                ON      MC.Id = MCAI.master_contract_id
                INNER JOIN      {{ ref("ods_cs_users")}} U2
                ON      MCAI.modified_by_id = U2.Id     --editor
                where MCAI.modified_field_id in ( '14955304-662d-40ca-87d3-62692722dccf'        --Price
                                                                                )
                and MCAI.change_type = 8
                group by
                        MC.Id
                        ,MCAI.created_date
                        ,MCAI.reason
                        ,MCAI.effective_date
                        ,U2.last_name || ' ' || U2.first_name
                ) MODS
        ON      CT.Id = MODS.master_contract_id
        INNER JOIN      "awsdatacatalog"."ods_learning_service"."ods_ls_center" LS_CNTR
        ON      LS_CNTR.reference_center_id = C.center_reference_id
        INNER JOIN      "awsdatacatalog"."ods_learning_service"."ods_ls_territory" LS_TR
        ON      LS_CNTR.territory_id = LS_TR.Id
        INNER JOIN "awsdatacatalog"."ods_center_configuration_service"."ods_cc_center" tz
        ON      tz.center_reference_id = c.center_reference_id
        INNER JOIN      "awsdatacatalog"."ods_contract_service"."ods_cs_new_version_companies" U
        ON      CT.company_id = U.Id  -- Company
        INNER JOIN      "awsdatacatalog"."ods_contract_service"."ods_cs_users" U1
        ON      CT.consultant_id = U1.Id
        AND     U1.user_type = 3         --consultant
        WHERE
        --CV.state is not null and
        MODS.created_date >= TIMESTAMP '2019-01-01 00:00:00.000'
) A
group by
        A.created_date
        ,A.contract_number
        ,A.master_contract_id
        ,A.company_name
        ,A.center_reference_id
        ,A.territory_name
        ,A.company_code
        ,A.formatted_mod_date
        ,A.start_date
        ,A.end_date
        ,A.consultant
        ,A.editor
        ,A.mod_reason