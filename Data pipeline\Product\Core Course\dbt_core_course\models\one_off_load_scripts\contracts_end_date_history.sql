{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with contract_end_date_history as  (
	select 
        *
        ,ROW_NUMBER() over (partition by contract_id order by contract_id, source, end_date) as row_num
        ,lag(end_date) over (partition by contract_id order by contract_id, source, end_date) as lag_end_date
	from 
	(
		select 
            cai.contract_id
            ,c.contract_create_date
            ,cast((
                case when previous_value = '' then present_value
                    else previous_value
                    end) 
                    as date) as contract_end_date
            ,case when row_num = 1 then contract_create_date else lag_created_date end as start_date
            ,created_date as end_date
            ,1 as source
		from
		(
			select
                contract_id
                ,previous_value
                ,present_value
                ,created_date as created_date
                ,lag(created_date) over (partition by contract_id order by contract_id, created_date) as lag_created_date
                ,ROW_NUMBER() over (partition by contract_id order by contract_id, created_date) as row_num
			from {{ ref('dt_cs_contracts_audit_info')}}
			where modified_field = 'enddate'
		) cai
		left join 
		(
			select 
                c.id as contract_id
                ,c.created_date as contract_create_date
                ,c.end_date as contract_end_date
			from {{ref('dt_cs_contracts')}}  c
		) c
		on cai.contract_id = c.contract_id

		union all

		select 
            c.Id as contract_id
            ,c.created_date as contract_create_date
            ,c.end_date as contract_end_date
            ,CAST(NULL as TIMESTAMP(6))  as start_date
            ,CAST('3000-01-01' AS TIMESTAMP(6)) as end_date
            ,2 as source
		from {{ref('dt_cs_contracts')}} c
		) A
)

select 
    contract_id
    ,contract_end_date
    ,case when start_date is null and lag_end_date is not null then lag_end_date
        when start_date is null then contract_create_date
        else start_date 
        end as start_date
    ,end_date
    ,source
    ,row_num
    ,lag_end_date
from 
contract_end_date_history