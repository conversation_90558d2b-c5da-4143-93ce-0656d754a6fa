{"Stage1": [{"SecretManager": "DigitalStudentWorkbookService", "Object": "ActivityProgress", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "digital_student_workbook_service", "AthenaDatabase": "stg_digital_student_workbook_service", "OdsDatabase": "ods_digital_student_workbook_service", "HistDatabase": "hist_digital_student_workbook_service", "OdsObject": "ods_dsw_activity_progress"}, {"SecretManager": "DigitalStudentWorkbookService", "Object": "CenterConfigs", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "digital_student_workbook_service", "AthenaDatabase": "stg_digital_student_workbook_service", "OdsDatabase": "ods_digital_student_workbook_service", "HistDatabase": "hist_digital_student_workbook_service", "OdsObject": "ods_dsw_center_configs"}, {"SecretManager": "DigitalStudentWorkbookService", "Object": "CourseContents", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "digital_student_workbook_service", "AthenaDatabase": "stg_digital_student_workbook_service", "OdsDatabase": "ods_digital_student_workbook_service", "HistDatabase": "hist_digital_student_workbook_service", "OdsObject": "ods_dsw_course_contents"}, {"SecretManager": "DigitalStudentWorkbookService", "Object": "LessonProgress", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "digital_student_workbook_service", "AthenaDatabase": "stg_digital_student_workbook_service", "OdsDatabase": "ods_digital_student_workbook_service", "HistDatabase": "hist_digital_student_workbook_service", "OdsObject": "ods_dsw_lesson_progress"}, {"SecretManager": "DigitalStudentWorkbookService", "Object": "ReviewActivity", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "digital_student_workbook_service", "AthenaDatabase": "stg_digital_student_workbook_service", "OdsDatabase": "ods_digital_student_workbook_service", "HistDatabase": "hist_digital_student_workbook_service", "OdsObject": "ods_dsw_review_activity"}, {"SecretManager": "DigitalStudentWorkbookService", "Object": "StudentAccessDetails", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "digital_student_workbook_service", "AthenaDatabase": "stg_digital_student_workbook_service", "OdsDatabase": "ods_digital_student_workbook_service", "HistDatabase": "hist_digital_student_workbook_service", "OdsObject": "ods_dsw_student_access_details"}, {"SecretManager": "CenterConfigurationService", "Object": "Center", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "CenterConfigurationService", "AthenaDatabase": "stg_center_configuration_service", "OdsDatabase": "ods_center_configuration_service", "HistDatabase": "hist_center_configuration_service", "OdsObject": "ods_cc_center"}, {"SecretManager": "CenterConfigurationService", "Object": "CrossCenterGroup", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "CenterConfigurationService", "AthenaDatabase": "stg_center_configuration_service", "OdsDatabase": "ods_center_configuration_service", "HistDatabase": "hist_center_configuration_service", "OdsObject": "ods_cc_cross_center_group"}, {"SecretManager": "CenterConfigurationService", "Object": "CrossCenterGroupMapping", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "CenterConfigurationService", "AthenaDatabase": "stg_center_configuration_service", "OdsDatabase": "ods_center_configuration_service", "HistDatabase": "hist_center_configuration_service", "OdsObject": "ods_cc_cross_center_group_mapping"}, {"SecretManager": "CenterConfigurationService", "Object": "Territory", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "CenterConfigurationService", "AthenaDatabase": "stg_center_configuration_service", "OdsDatabase": "ods_center_configuration_service", "HistDatabase": "hist_center_configuration_service", "OdsObject": "ods_cc_territory"}, {"SecretManager": "CenterConfigurationService", "Object": "TimeZone", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "CenterConfigurationService", "AthenaDatabase": "stg_center_configuration_service", "OdsDatabase": "ods_center_configuration_service", "HistDatabase": "hist_center_configuration_service", "OdsObject": "ods_cc_timezone"}, {"SecretManager": "CenterConfigurationService", "Object": "CenterConfiguration", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "CenterConfigurationService", "AthenaDatabase": "stg_center_configuration_service", "OdsDatabase": "ods_center_configuration_service", "HistDatabase": "hist_center_configuration_service", "OdsObject": "ods_cc_center_configuration"}, {"SecretManager": "IDAMService", "Object": "Center", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "IDAMService", "AthenaDatabase": "stg_idam_service", "OdsDatabase": "ods_idam_service", "HistDatabase": "hist_idam_service", "OdsObject": "ods_idam_center"}, {"SecretManager": "IDAMService", "Object": "Role", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "IDAMService", "AthenaDatabase": "stg_idam_service", "OdsDatabase": "ods_idam_service", "HistDatabase": "hist_idam_service", "OdsObject": "ods_idam_role"}, {"SecretManager": "IDAMService", "Object": "Territory", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "IDAMService", "AthenaDatabase": "stg_idam_service", "OdsDatabase": "ods_idam_service", "HistDatabase": "hist_idam_service", "OdsObject": "ods_idam_territory"}, {"SecretManager": "IDAMService", "Object": "UserAdditionalInfo", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "IDAMService", "AthenaDatabase": "stg_idam_service", "OdsDatabase": "ods_idam_service", "HistDatabase": "hist_idam_service", "OdsObject": "ods_idam_user_additional_info"}, {"SecretManager": "IDAMService", "Object": "UserBasicInfo", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "IDAMService", "AthenaDatabase": "stg_idam_service", "OdsDatabase": "ods_idam_service", "HistDatabase": "hist_idam_service", "OdsObject": "ods_idam_user_basic_info"}, {"SecretManager": "IDAMService", "Object": "UserRoles", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "IDAMService", "AthenaDatabase": "stg_idam_service", "OdsDatabase": "ods_idam_service", "HistDatabase": "hist_idam_service", "OdsObject": "ods_idam_user_roles"}, {"SecretManager": "LearningService", "Object": "ContentItemResult", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_content_item_result"}, {"SecretManager": "LearningService", "Object": "LessonResultAggregate", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_lesson_result_aggregate"}, {"SecretManager": "LearningService", "Object": "BookedStudent", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_booked_student"}, {"SecretManager": "LearningService", "Object": "UnitResultAggregate", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_unit_result_aggregate"}, {"SecretManager": "LearningService", "Object": "ClassResult", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_class_result"}, {"SecretManager": "LearningService", "Object": "Class", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_class"}, {"SecretManager": "LearningService", "Object": "EncounterResultAggregate", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_encounter_result_aggregate"}, {"SecretManager": "LearningService", "Object": "RegistrationCourse", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_registration_course"}, {"SecretManager": "LearningService", "Object": "DigitalBooksAccessSetting", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_digital_books_access_setting"}, {"SecretManager": "LearningService", "Object": "BookMark", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_bookmark"}, {"SecretManager": "LearningService", "Object": "DigitalBooksLog", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_digital_books_log"}, {"SecretManager": "LearningService", "Object": "Registration", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_registration"}, {"SecretManager": "LearningService", "Object": "User", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_user"}, {"SecretManager": "LearningService", "Object": "Student", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_student"}, {"SecretManager": "LearningService", "Object": "RebookedStudent", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_rebooked_student"}, {"SecretManager": "LearningService", "Object": "DigitalBooksRestart", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_digital_books_restart"}, {"SecretManager": "LearningService", "Object": "CategoryHierarchy", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_category_hierarchy"}, {"SecretManager": "LearningService", "Object": "CategoryAttributes", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_category_attributes"}, {"SecretManager": "LearningService", "Object": "Category", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_category"}, {"SecretManager": "LearningService", "Object": "ContentItemSkill", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_content_item_skill"}, {"SecretManager": "LearningService", "Object": "ContentItem", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_content_item"}, {"SecretManager": "LearningService", "Object": "Center", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_center"}, {"SecretManager": "LearningService", "Object": "TimeZone", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_timezone"}, {"SecretManager": "LearningService", "Object": "Territory", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_territory"}, {"SecretManager": "LearningService", "Object": "ClassType", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_class_type"}, {"SecretManager": "LearningService", "Object": "Role", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_role"}, {"SecretManager": "LearningService", "Object": "Course", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_course"}, {"SecretManager": "LearningService", "Object": "ContentItemTypeSkill", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_content_item_type_skill"}, {"SecretManager": "LearningService", "Object": "Skill", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_skill"}, {"SecretManager": "LearningService", "Object": "ContentItemResulttype", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_content_item_result_type"}, {"SecretManager": "LearningService", "Object": "RegistrationType", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_registration_type"}, {"SecretManager": "LearningService", "Object": "UserRoleType", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_user_role_type"}, {"SecretManager": "LearningService", "Object": "ContentitemType", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_content_item_type"}, {"SecretManager": "LearningService", "Object": "CategoryType", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_category_type"}, {"SecretManager": "LearningService", "Object": "FollowupActivityType", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_followup_activity_type"}, {"SecretManager": "LearningService", "Object": "ContentOptimizationLevel", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_content_optimization_level"}, {"SecretManager": "LearningService", "Object": "ServiceType", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_service_type"}, {"SecretManager": "LearningService", "Object": "CenterConfig", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_center_config"}, {"SecretManager": "LearningService", "Object": "FollowUpActivity", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_followup_activity"}, {"SecretManager": "LearningService", "Object": "StaffMember", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_staff_member"}, {"SecretManager": "LearningService", "Object": "Study<PERSON>lanner", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_study_planner"}, {"SecretManager": "LearningService", "Object": "Nationality", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_nationality"}, {"SecretManager": "LearningService", "Object": "Language", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_language"}, {"SecretManager": "LearningService", "Object": "Motivation", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_motivation"}, {"SecretManager": "LearningService", "Object": "Profession", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_profession"}, {"SecretManager": "LearningService", "Object": "SocialNetwork", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_social_network"}, {"SecretManager": "LearningService", "Object": "ClassCategory", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "LearningService", "AthenaDatabase": "stg_learning_service", "OdsDatabase": "ods_learning_service", "HistDatabase": "hist_learning_service", "OdsObject": "ods_ls_class_category"}, {"SecretManager": "ScheduleAndBookingService", "Object": "Category", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ScheduleAndBookingService", "AthenaDatabase": "stg_schedule_and_booking_service", "OdsDatabase": "ods_schedule_and_booking_service", "HistDatabase": "hist_schedule_and_booking_service", "OdsObject": "ods_snb_category"}, {"SecretManager": "ScheduleAndBookingService", "Object": "ClassType", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ScheduleAndBookingService", "AthenaDatabase": "stg_schedule_and_booking_service", "OdsDatabase": "ods_schedule_and_booking_service", "HistDatabase": "hist_schedule_and_booking_service", "OdsObject": "ods_snb_class_type"}, {"SecretManager": "ScheduleAndBookingService", "Object": "ClassTypeConfigurationForServiceType", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ScheduleAndBookingService", "AthenaDatabase": "stg_schedule_and_booking_service", "OdsDatabase": "ods_schedule_and_booking_service", "HistDatabase": "hist_schedule_and_booking_service", "OdsObject": "ods_snb_cls_typ_config_for_srvc_typ"}, {"SecretManager": "ScheduleAndBookingService", "Object": "Course", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ScheduleAndBookingService", "AthenaDatabase": "stg_schedule_and_booking_service", "OdsDatabase": "ods_schedule_and_booking_service", "HistDatabase": "hist_schedule_and_booking_service", "OdsObject": "ods_snb_course"}, {"SecretManager": "ScheduleAndBookingService", "Object": "DigitalMeetingInformation", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ScheduleAndBookingService", "AthenaDatabase": "stg_schedule_and_booking_service", "OdsDatabase": "ods_schedule_and_booking_service", "HistDatabase": "hist_schedule_and_booking_service", "OdsObject": "ods_snb_digital_meeting_information"}, {"SecretManager": "ScheduleAndBookingService", "Object": "Role", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ScheduleAndBookingService", "AthenaDatabase": "stg_schedule_and_booking_service", "OdsDatabase": "ods_schedule_and_booking_service", "HistDatabase": "hist_schedule_and_booking_service", "OdsObject": "ods_snb_role"}, {"SecretManager": "ScheduleAndBookingService", "Object": "ScheduledClass", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ScheduleAndBookingService", "AthenaDatabase": "stg_schedule_and_booking_service", "OdsDatabase": "ods_schedule_and_booking_service", "HistDatabase": "hist_schedule_and_booking_service", "OdsObject": "ods_snb_scheduled_class"}, {"SecretManager": "ScheduleAndBookingService", "Object": "ScheduledClassCategory", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ScheduleAndBookingService", "AthenaDatabase": "stg_schedule_and_booking_service", "OdsDatabase": "ods_schedule_and_booking_service", "HistDatabase": "hist_schedule_and_booking_service", "OdsObject": "ods_snb_scheduled_class_category"}, {"SecretManager": "ScheduleAndBookingService", "Object": "ScheduledClassProperty", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ScheduleAndBookingService", "AthenaDatabase": "stg_schedule_and_booking_service", "OdsDatabase": "ods_schedule_and_booking_service", "HistDatabase": "hist_schedule_and_booking_service", "OdsObject": "ods_snb_scheduled_class_property"}, {"SecretManager": "ScheduleAndBookingService", "Object": "User", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ScheduleAndBookingService", "AthenaDatabase": "stg_schedule_and_booking_service", "OdsDatabase": "ods_schedule_and_booking_service", "HistDatabase": "hist_schedule_and_booking_service", "OdsObject": "ods_snb_user"}, {"SecretManager": "ProspectService", "Object": "Prospect", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ProspectService", "AthenaDatabase": "stg_prospect_service", "OdsDatabase": "ods_prospect_service", "HistDatabase": "hist_prospect_service", "OdsObject": "ods_ps_prospect"}, {"SecretManager": "ProspectService", "Object": "OfflineTestResults", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ProspectService", "AthenaDatabase": "stg_prospect_service", "OdsDatabase": "ods_prospect_service", "HistDatabase": "hist_prospect_service", "OdsObject": "ods_ps_offline_test_results"}, {"SecretManager": "ProspectService", "Object": "PlacementTestActivity", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ProspectService", "AthenaDatabase": "stg_prospect_service", "OdsDatabase": "ods_prospect_service", "HistDatabase": "hist_prospect_service", "OdsObject": "ods_ps_placement_test_activity"}, {"SecretManager": "ProspectService", "Object": "PlacementTestInteraction", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ProspectService", "AthenaDatabase": "stg_prospect_service", "OdsDatabase": "ods_prospect_service", "HistDatabase": "hist_prospect_service", "OdsObject": "ods_ps_placement_test_interaction"}, {"SecretManager": "ProspectService", "Object": "PlacementTestResult", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ProspectService", "AthenaDatabase": "stg_prospect_service", "OdsDatabase": "ods_prospect_service", "HistDatabase": "hist_prospect_service", "OdsObject": "ods_ps_placement_test_result"}, {"SecretManager": "ProspectService", "Object": "PlacementTestLevels", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ProspectService", "AthenaDatabase": "stg_prospect_service", "OdsDatabase": "ods_prospect_service", "HistDatabase": "hist_prospect_service", "OdsObject": "ods_ps_placement_test_levels"}, {"SecretManager": "ProspectService", "Object": "PrePlacementTestResult", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ProspectService", "AthenaDatabase": "stg_prospect_service", "OdsDatabase": "ods_prospect_service", "HistDatabase": "hist_prospect_service", "OdsObject": "ods_ps_pre_placement_test_result"}, {"SecretManager": "ProspectService", "Object": "ProspectGradebook", "FilterColumn": "NA", "LoadType": "Full", "DatabaseConnection": "ProspectService", "AthenaDatabase": "stg_prospect_service", "OdsDatabase": "ods_prospect_service", "HistDatabase": "hist_prospect_service", "OdsObject": "ods_ps_prospect_gradebook"}]}