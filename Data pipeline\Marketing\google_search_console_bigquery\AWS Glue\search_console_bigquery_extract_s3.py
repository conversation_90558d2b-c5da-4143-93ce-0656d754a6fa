import sys
from awsglue.transforms import *
from awsglue.utils import getResolvedOptions
from pyspark.context import Spark<PERSON>ontext
from awsglue.context import Glue<PERSON>ontext
from awsglue.dynamicframe import DynamicFrame
import boto3
import time
from datetime import datetime
import pandas as pd

def init_glue_context():
    sc = SparkContext()
    glueContext = GlueContext(sc)
    return glueContext

def get_args():
    return getResolvedOptions(sys.argv, ['JOB_NAME', 'territory_input', 'table', 'load_date'])

def execute_athena_query(client, query):
    response = client.start_query_execution(
        QueryString=query,
        QueryExecutionContext={'Database': 'ods_marketing_bigquery_gsc'},
        ResultConfiguration={'OutputLocation': 's3://etl-dev-gsc-extract/ods_max_date_url_result/'}
    )
    query_execution_id = response['QueryExecutionId']
    return query_execution_id

def wait_for_query(client, query_execution_id):
    state = 'QUEUED'
    while state in ['QUEUED', 'RUNNING']:
        response = client.get_query_execution(QueryExecutionId=query_execution_id)
        state = response['QueryExecution']['Status']['State']
        time.sleep(5)
    return state

def get_query_results(client, query_execution_id):
    result_response = client.get_query_results(QueryExecutionId=query_execution_id)
    if 'Rows' in result_response['ResultSet']:
        columns = [col['Label'] for col in result_response['ResultSet']['ResultSetMetadata']['ColumnInfo']]
        data = [[field.get('VarCharValue', 'NULL') for field in row['Data']] for row in result_response['ResultSet']['Rows'][1:]]
        df = pd.DataFrame(data, columns=columns)
        return df.to_dict(orient='records') if not df.empty else []
    return []

def extract_data(glueContext, table_details, filter_condition):
    return glueContext.create_dynamic_frame.from_options(
        connection_type="marketplace.spark",
        connection_options={
            "table": table_details,
            "parentProject": "wse-marketing",
            "connectionName": "bigquery",
            "filter": filter_condition
        },
        transformation_ctx="extract_data"
    )

def process_and_write_data(glueContext, dynamic_frame, required_fields, territory_code):
    df = dynamic_frame.toDF()
    df.createOrReplaceTempView("bigquery_table")
    selected_df = glueContext.spark_session.sql(f"SELECT {', '.join(required_fields)} FROM bigquery_table")
    selected_dynamic_frame = DynamicFrame.fromDF(selected_df, glueContext, "selected_dynamic_frame")
    glueContext.write_dynamic_frame.from_options(
        frame=selected_dynamic_frame,
        connection_type="s3",
        format="json",
        connection_options={"path": f"s3://etl-dev-gsc-extract/url_json_response_raw/{territory_code}", "partitionKeys": []},
        transformation_ctx="BigQuery_S3_node"
    )

def main():
    args = get_args()
    glueContext = init_glue_context()
    client = boto3.client('athena')

    territory_code_input = args['territory_input']
    table_details = args['table']
    incremental_date = args['load_date']

    # Athena query to get max date for the territory
    query = f"SELECT territory_code, MAX(data_date) AS max_date FROM ods_searchconsole_url_impression WHERE territory_code = '{territory_code_input}' GROUP BY territory_code"
    query_execution_id = execute_athena_query(client, query)
    
    state = wait_for_query(client, query_execution_id)
    list_of_dicts = get_query_results(client, query_execution_id) if state == 'SUCCEEDED' else []

    if not list_of_dicts:
        date_obj = datetime.strptime(incremental_date, '%Y-%m-%d')
    else:
        territory_code = list_of_dicts[0]['territory_code']
        incremental_date = list_of_dicts[0]['max_date']
        date_obj = datetime.strptime(incremental_date, '%Y-%m-%d')

    formatted_incremental_date = date_obj.strftime('%Y-%m-%d')
    filter_condition = f"data_date > '{formatted_incremental_date}'"

    dynamic_frame = extract_data(glueContext, table_details, filter_condition)
    dynamic_frame.show()

    required_fields = ["data_date", "site_url", "url", "query", "is_anonymized_query", "country", "search_type", "device", "impressions", "clicks", "sum_position"]
    process_and_write_data(glueContext, dynamic_frame, required_fields, territory_code_input)

if __name__ == "__main__":
    main()
