{{ config(
    materialized="incremental",
    unique_key=["event_name", "event_timestamp"],
    incremental_strategy="insert_overwrite",
    partition_by={"field": "event_date", "data_type": "date","granularity": "day"},
    cluster_by=["event_name", "event_timestamp"]
) }}

{% if is_incremental() %}
    {% set max_existing_date_query %}
        SELECT MAX(event_date) AS max_event_date
        FROM {{ this }}
    {% endset %}

    {% set max_existing_date_result = run_query(max_existing_date_query) %}
    {% if max_existing_date_result is not none and max_existing_date_result.rows|length > 0 %}
        {% set max_existing_date = max_existing_date_result.columns[0].values()[0] %}
    {% else %}
        {% set max_existing_date = none %}
    {% endif %}
{% else %}
    {% set max_existing_date = none %}
{% endif %}

{% if max_existing_date is none %}
    {% set start_date = "DATE_SUB(CURRENT_DATE(), INTERVAL 69 DAY)" %}
{% else %}
    {% set start_date = "DATE_ADD(DATE('" ~ max_existing_date ~ "'), INTERVAL 1 DAY)" %}
{% endif %}

{% set end_date = "DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)" %}

WITH source_data AS (
    SELECT *
    FROM `core-course-bigquery.analytics_358724658.events_intraday_*`
    WHERE 
    event_name IN ('goc_cue_cards_in_breakout_rooms', 'goc_cue_cards_in_breakout_rooms_time_spe')
    and PARSE_DATE('%Y%m%d', _TABLE_SUFFIX) BETWEEN {{ start_date }} AND {{ end_date }}
)

SELECT
  PARSE_DATE('%Y%m%d', event_date) AS event_date,
  event_timestamp,
  event_name,
  user_id,
  MAX(CASE WHEN up.key = "duration" THEN up.value.double_value END) AS duration,
  MAX(CASE WHEN up.key = "using_suggestion" THEN up.value.string_value END) AS using_suggestion,
  MAX(CASE WHEN up.key = "class_type" THEN up.value.string_value END) AS class_type,
  MAX(CASE WHEN up.key = "user_role" THEN up.value.string_value END) AS user_role,
  MAX(CASE WHEN up.key = "class_id" THEN up.value.string_value END) AS class_id
FROM source_data,
  UNNEST(event_params) AS ep,
  UNNEST(user_properties) AS up
GROUP BY
  event_date,
  event_timestamp,
  event_name,
  user_id
