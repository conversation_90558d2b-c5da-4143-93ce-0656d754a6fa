version: 2

entities:
  - name: student
    type: dimension
    description: "Student entity representing a learner in the system"

  - name: time
    type: dimension
    description: "Time dimension for temporal analysis"

  - name: contract
    type: dimension
    description: "Contract entity representing a student's enrollment agreement"

  - name: center
    type: dimension
    description: "Center entity representing a physical location where services are provided"

entity_relationships:
  - name: student_to_contract
    description: "Relationship between students and their contracts"
    from_entity: student
    to_entity: contract
    type: many_to_many

  - name: student_to_center
    description: "Relationship between students and centers"
    from_entity: student
    to_entity: center
    type: many_to_one

  - name: contract_to_center
    description: "Relationship between contracts and centers"
    from_entity: contract
    to_entity: center
    type: many_to_one
