{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

SELECT 
    activity.id as activity_id
    ,activity.registration_id
    ,users.ssds_id as student_id
    ,activity.teacher_id
    ,activity.ref_class_id
    ,activity.class_id
    ,activity.content_item_id
    ,activity.created as  created_date
    ,activity.local_created as local_created_date
    ,activity.last_updated  as last_updated_date
    ,activity.local_last_updated    as local_last_updated_date
    ,activity.date_started  as started_date
    ,activity.local_date_started    as local_started_date
    ,activity.date_completed    as completed_date
    ,activity.local_date_completed  as local_completed_date
    ,activity.level
    ,activity.unit
    ,activity.lesson
    ,activity.mini_cycle
    ,activity.mini_cycle_stage
    ,activity.comment
    ,activity.content_item
    ,activity.content_item_type
    ,activity.content_itemresult_type as content_item_result_type
    ,activity.display_on_list
    ,activity.to_process_in_background
    ,activity.study_mode
    ,activity.activity_captured_type
    ,activity.total_questions
    ,activity.total_question_answered
    ,activity.score
    ,CAST(activity.duration as decimal(25,5)) as duration_secs
    ,CAST(activity.duration as decimal(25,5))/60 as duration_mins
    ,cap.percentile_25_duration_mins
    ,median_duration_mins
    ,cap.percentile_75_duration_mins
FROM 
    {{ref("dt_ls_content_item_result")}} as activity
    left join 
        {{ref("dt_ls_user")}} as users on activity.student_id = users.user_id
    left join {{ ref('activity_cap') }} cap on activity.content_item_id = cap.content_item_id and date(activity.date_completed) = cap.date_completed
where content_item_type = 'activity'