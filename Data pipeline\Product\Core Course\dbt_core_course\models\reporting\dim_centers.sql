{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}
 
WITH capitalized AS (
    SELECT
        center_reference_id as center_id,
        territory_reference_id as territory_id,
        SPLIT(center_name, ' ') AS words_center,
        SPLIT(group_name, ' ') AS words_group,
        group_name as group_name,
        timezone as time_zone
    from {{ ref('territory_centers') }}
    where center_reference_id is not null

)
SELECT
    distinct
    center_id,
    territory_id,
    ARRAY_JOIN(
        TRANSFORM(words_center, words_center -> CONCAT(UPPER(SUBSTR(words_center, 1, 1)), LOWER(SUBSTR(words_center, 2)))),
        ' '
    ) AS center_name,
    ARRAY_JOIN(
        TRANSFORM(words_group, words_group -> CONCAT(UPPER(SUBSTR(words_group, 1, 1)), LOWER(SUBSTR(words_group, 2)))),
        ' '
    ) AS group_name,
    time_zone
FROM
    capitalized
