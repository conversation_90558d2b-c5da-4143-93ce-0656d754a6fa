{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH activity AS (
    SELECT
        'multimedia' AS activity_type,
        activity_id,
        registration_id,
        student_id,
        content_item_id,
        REGEXP_REPLACE(content_item ,'(\w)(\w*)',(x) -> UPPER(x[1]) || lower(x[2])) as description,
        local_started_date as start_date,
        local_completed_date AS completed_date,
        CAST(level AS INT) AS level,
        CAST(unit AS INT) AS unit,
        CAST(lesson AS INT) AS lesson,
        CAST(mini_cycle AS INT) AS mini_cycles,
        CAST(mini_cycle_stage AS INT) AS mini_cycle_stage,
        NULL AS workbook_activity,
        score,
        duration_mins,
        percentile_25_duration_mins,
        median_duration_mins,
        percentile_75_duration_mins
    FROM 
        {{ ref('activity_multimedia') }} AS mm
    WHERE 
        content_item_result_type = 'passed'

    UNION ALL
    
    SELECT
        'digital_workbook' AS activity_type,
        activity_id,
        registration_id,
        student_id,
        activity_reference_id AS content_item_id,
        '' AS description,
        date_add('second',cast(-1*duration_mins*60 as integer),local_last_updated_date) as start_date,
        local_last_updated_date AS completed_date,
        CAST(level AS INT) AS level,
        CAST(unit AS INT) AS unit,
        CAST(lesson AS INT) AS lesson,
        NULL AS mini_cycles,
        NULL AS mini_cycle_stage,
        activity AS workbook_activity,
        score,
        duration_mins,
        percentile_25_duration_mins,
        median_duration_mins,
        percentile_75_duration_mins
    FROM
        {{ ref('activity_workbook') }} AS dw
    WHERE
        is_completed = TRUE
)
,activity_lag
as (
    select *
    ,lag(completed_date) over (partition by student_id order by completed_date) as completed_date_lag 
    from activity
)
SELECT
    activity_lag.activity_type,
    activity_lag.activity_id,
    activity_lag.registration_id,
    activity_lag.student_id,
    activity_lag.content_item_id,
    activity_lag.description,
    centers.center_reference_id,
    activity_lag.start_date,
    activity_lag.completed_date,
    activity_lag.level,
    activity_lag.unit,
    activity_lag.lesson,
    activity_lag.mini_cycles,
    activity_lag.mini_cycle_stage,
    activity_lag.workbook_activity,
    activity_lag.score,
    activity_lag.duration_mins,
    case when duration_mins > percentile_75_duration_mins + 1.5*(percentile_75_duration_mins-percentile_25_duration_mins)
        then percentile_75_duration_mins + 1.5*(percentile_75_duration_mins-percentile_25_duration_mins)
        else duration_mins
    end as duration_cap_mins,
    percentile_75_duration_mins + 1.5*(percentile_75_duration_mins-percentile_25_duration_mins) as max_duration_for_cap,
    duration_mins > percentile_75_duration_mins + 1.5*(percentile_75_duration_mins-percentile_25_duration_mins) as cap_flag, 
    activity_lag.percentile_25_duration_mins,
    activity_lag.median_duration_mins,
    activity_lag.percentile_75_duration_mins,

sum(
    case 
        when completed_date is null  then 0 
        when date_diff('second', completed_date_lag, completed_date) < 60*60*2 then 0
        else 1 
    end
)
    over ( partition by student_id order by completed_date rows between unbounded preceding and current row
    ) as Session
FROM 
    activity_lag
LEFT JOIN 
    {{ ref('users') }} AS users
ON 
    users.student_reference_id = activity_lag.student_id
LEFT JOIN
    {{ ref('territory_centers') }} AS centers
ON
    centers.ls_center_id = users.center_id