{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        contentitemresulttypeid,
        score,
        {{ cast_to_timestamp('datecompleted') }} as datecompleted,
        totalquestions,
        totalquestionanswered,
        totalcorrectanswers,
        duration,
        {{ cast_to_timestamp('datestarted') }} as datestarted,
        displayonlist,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        toprocessinbackground,
        activitycapturedtypeid,
        studymode,
        activityduration,
        id,
        registrationid,
        contentitemid,
        teacherid,
        COMMENT,
        refclassid,
        classid,
        studentid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'contentitemresult'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    contentitemresulttypeid as content_item_result_type_id,
    score,
    datecompleted as date_completed,
    totalquestions as total_questions,
    totalquestionanswered as total_question_answered,
    totalcorrectanswers as total_correct_answers,
    duration,
    datestarted as date_started,
    displayonlist as display_on_list,
    created,
    lastupdated as last_updated,
    toprocessinbackground as to_process_in_background,
    activitycapturedtypeid as activity_captured_type_id,
    studymode as study_mode,
    activityduration as activity_duration,
    id,
    registrationid as registration_id,
    contentitemid as content_item_id,
    teacherid as teacher_id,
    comment,
    refclassid as ref_class_id,
    classid as class_id,
    studentid as student_id
FROM
    rankedrecords
WHERE
    rn = 1;
