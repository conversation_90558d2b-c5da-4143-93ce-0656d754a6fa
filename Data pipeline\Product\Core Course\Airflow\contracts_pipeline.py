import logging
import os
from dependencies import cloud_operations
from dependencies.pipeline_prerequisite import toggle_dag_state
from dependencies.slack_alerts import task_failure_callback, task_success_callback
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.operators.bash import Bash<PERSON>perator
from datetime import timed<PERSON><PERSON>, datetime
from dependencies import glue_trigger

default_args = {
    'owner': 'wse_data_team',
    'start_date': datetime(2024, 6, 19)
}

dag = DAG('contracts_pipeline',
          default_args=default_args,
          schedule_interval='0 */4 * * *', # scheduled refresh for every 4hrs
          catchup=False,
          tags=['core-course'],
          max_active_tasks=15)
# Limit the number of parallel tasks to 15

s3 = cloud_operations.S3

file_info = s3.read_json_file(bucket="prod-corecourse", file_path="config/contracts_config.json")
logging.warning(file_info)


# function to call prerequisite
def execute_prerequisite():
    toggle_dag_state(dag)


# basic prerequisite check
prerequisite_check = PythonOperator(
    task_id='prerequisite_check',
    python_callable=execute_prerequisite,
    provide_context=True,
    dag=dag
)

# get the core course dbt path
HOME = os.environ["HOME"]  # retrieve the location of your home folder
dbt_path = os.path.join(HOME, "dbt/dbt_core_course")  # path to your dbt project
logging.warning("dbt path: ", dbt_path)

ods_contract_service = BashOperator(
    task_id="ods_contract_service",
    bash_command="cd /home/<USER>/dbt"
                 + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                 + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                 + f" && dbt run --models ods.contract_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    retries=1,  # Number of retries
    retry_delay=timedelta(minutes=5),
    dag=dag
)

dt_contract_service = BashOperator(
    task_id="dt_contract_service",
    bash_command="cd /home/<USER>/dbt"
                 + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                 + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                 + f" && dbt run --models data_transformation.contract_service.*",  # run the model!
    on_failure_callback=task_failure_callback,
    retries=1,  # Number of retries
    retry_delay=timedelta(minutes=5),
    dag=dag
)

snapshot = BashOperator(
    task_id="snapshot",
    bash_command="cd /home/<USER>/dbt"
                 + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                 + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                 + f" && dbt snapshot",  # run the model!
    on_failure_callback=task_failure_callback,
    on_success_callback =task_success_callback,
    retries=1,  # Number of retries
    retry_delay=timedelta(minutes=5),
    dag=dag
)

process_tasks = []
for i, data in enumerate(file_info):
    if data['execution_type'] == 'yes':
        op_kwargs = {
            "glue_job_name": "contracts_data_ingestion",
            "glue_args": {
                "--SecretManager": file_info[i]["SecretManager"],
                "--Object": file_info[i]["Object"],
                "--FilterColumn": file_info[i]["FilterColumn"],
                "--LoadType": file_info[i]["LoadType"],
                "--DatabaseConnection": file_info[i]["DatabaseConnection"],
                "--AthenaDatabase": file_info[i]["AthenaDatabase"],
                "--OdsDatabase": file_info[i]["OdsDatabase"],
                "--HistDatabase": file_info[i]["HistDatabase"],
                "--OdsObject": file_info[i]["OdsObject"]
            }
        }
        task = PythonOperator(
            task_id=data['Object'],
            python_callable=glue_trigger.run_glue_job,
            on_failure_callback=task_failure_callback,
            op_kwargs=op_kwargs,
            retries=1,  # Number of retries
            retry_delay=timedelta(minutes=5),
            dag=dag
        )
        process_tasks.append(task)

# Set up the task dependency
prerequisite_check >> process_tasks >> ods_contract_service >> dt_contract_service >> snapshot
