{"Comment": "A description of my state machine", "StartAt": "Glue StartJobRun", "States": {"Glue StartJobRun": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "ExecutionPlanner", "Arguments": {"--input.$": "$.input"}}, "ResultPath": null, "Next": "Lambda Invoke"}, "Lambda Invoke": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:ReadExecutionPlannerFile:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Map"}, "Map": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Choice", "States": {"Choice": {"Type": "Choice", "Choices": [{"Variable": "$.Stage1", "IsPresent": true, "Next": "Map (1)"}, {"Variable": "$.Stage3", "IsPresent": true, "Next": "Map (3)"}, {"Variable": "$.Stage4", "IsPresent": true, "Next": "Stage4Map"}]}, "Map (1)": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Glue StartJobRun (1)", "States": {"Glue StartJobRun (1)": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotMyanmarVietnamRawDataLoad", "Arguments": {"--Territory.$": "$.Territory", "--Object.$": "$.Object", "--Status.$": "States.Format('{}',$.Status)", "--Stage.$": "States.Format('{}',$.Stage)", "--Properties.$": "States.Format('{}',$.Properties)", "--Operation.$": "$.Operation", "--Filter.$": "$.Filter", "--FolderKey.$": "$.FolderKey", "--PrefixValue.$": "$.PrefixValue", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "LambdaFailCaseDataExtract"}]}, "LambdaFailCaseDataExtract": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "ItemsPath": "$.Stage1", "MaxConcurrency": 10, "Next": "Lambda Invoke (1)"}, "Lambda Invoke (1)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:HubspotMyanmarVietnamStage1ExtractValidation:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "Map (2)"}, "Map (2)": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "HubspotMyanamrVietnamSaveTableLoad", "States": {"HubspotMyanamrVietnamSaveTableLoad": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotMyanamarVietnamSaveTableLoad", "Arguments": {"--Table.$": "$.Table", "--WritePathKey.$": "$.WritePathKey", "--WriteMode.$": "$.WriteMode", "--FilePathKey.$": "$.FilePath<PERSON>ey", "--InputType.$": "$.InputType", "--CycleId.$": "States.Format('{}', $.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "LambdaSaveTableFailCase"}]}, "LambdaSaveTableFailCase": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "ItemsPath": "$.TableProperties", "MaxConcurrency": 10, "Next": "SaveTableStage1Validation"}, "SaveTableStage1Validation": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:HubspotMyanamrVietnamSaveTableValidation:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}, "Map (3)": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Stage3Dataenrichment_MMVN", "States": {"Stage3Dataenrichment_MMVN": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotMyanmarVietnamStage3", "Arguments": {"--Object.$": "$.Object", "--Stage.$": "States.Format('{}',$.Stage)", "--Operation.$": "$.Operation", "--Table.$": "$.Table", "--TablePath.$": "$.TablePath", "--Status.$": "States.Format('{}',$.Status)", "--CycleId.$": "States.Format('{}',$.CycleId)"}}, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (6)"}], "End": true}, "Lambda Invoke (6)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "ItemsPath": "$.Stage3", "MaxConcurrency": 10, "Next": "Stage3Validation"}, "Stage3Validation": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:HubspotMyanmarVietnamStage3ValidationLogs:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}, "Stage4Map": {"Type": "Map", "ItemProcessor": {"ProcessorConfig": {"Mode": "INLINE"}, "StartAt": "Stage4S3toRedshiftCopy", "States": {"Stage4S3toRedshiftCopy": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "HubspotMyanmarStage4S3toRedshiftCopy", "Arguments": {"--Object.$": "$.Object", "--Stage.$": "States.Format('{}',$.Stage)", "--Operation.$": "$.Operation", "--Table.$": "$.Table", "--Filepath.$": "$.Filepath", "--Status.$": "States.Format('{}',$.Status)", "--CycleId.$": "States.Format('{}',$.CycleId)"}}, "End": true, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Lambda Invoke (2)"}]}, "Lambda Invoke (2)": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "ItemsPath": "$.Stage4", "MaxConcurrency": 10, "Next": "STage4Validation"}, "STage4Validation": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:HubspotMyanmarVietnamStage4ValidationLogs:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "End": true}}}, "ItemsPath": "$.ExecutionInfo", "MaxConcurrency": 1, "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "LambdaFailCatch"}], "Next": "LambdaPassCatch"}, "LambdaPassCatch": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_pass_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "LambdaExecutionStatusInput"}, "LambdaExecutionStatusInput": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:HubspotMyanamarVietnamExecutionStatusUpdate:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "ExecutionStatusUpdate"}, "ExecutionStatusUpdate": {"Type": "Task", "Resource": "arn:aws:states:::glue:startJobRun.sync", "Parameters": {"JobName": "ExecutionStatusUpdate", "Arguments": {"--input.$": "$.input"}}, "Next": "MyanmarVietnamSuccess"}, "MyanmarVietnamSuccess": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:Hubspotmyanmarvietnamsuccess:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 1, "MaxAttempts": 3, "BackoffRate": 2}], "End": true}, "LambdaFailCatch": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "OutputPath": "$.Payload", "Parameters": {"Payload.$": "$", "FunctionName": "arn:aws:lambda:eu-north-1:262158335980:function:step_func_lambda_for_fail_case:$LATEST"}, "Retry": [{"ErrorEquals": ["Lambda.ServiceException", "Lambda.AWSLambdaException", "Lambda.SdkClientException", "Lambda.TooManyRequestsException"], "IntervalSeconds": 2, "MaxAttempts": 6, "BackoffRate": 2}], "Next": "LambdaExecutionStatusInput"}}}