{"ExecutionInfo": [{"Stage1": [{"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "AR", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}, "Berlitz Corporation": {"Keyword": "Berlitz Corporation", "CompanyType": "Training company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "AR", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Open English": {"Keyword": "Open English", "CompanyType": "School in Miami, Florida"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Platzi": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Educational technology company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "CH", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "CH", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}, "Babbel": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Software"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "CL", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}, "Berlitz Corporation": {"Keyword": "Berlitz Corporation", "CompanyType": "Training company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "CL", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Open English": {"Keyword": "Open English", "CompanyType": "School in Miami, Florida"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Platzi": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Educational technology company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "CO", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}, "Berlitz Corporation": {"Keyword": "Berlitz Corporation", "CompanyType": "Training company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "CO", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Open English": {"Keyword": "Open English", "CompanyType": "School in Miami, Florida"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Platzi": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Educational technology company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "CZ", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "CZ", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Memrise": {"Keyword": "Memrise", "CompanyType": "Website"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Babbel": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Software"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "EC", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "EC", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Open English": {"Keyword": "Open English", "CompanyType": "School in Miami, Florida"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Platzi": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Educational technology company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "ES", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "ES", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}, "Babbel": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Software"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "FR", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "FR", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Babbel": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Software"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "HK", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "HK", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "AmazingTalker": {"Keyword": "<PERSON><PERSON><PERSON><PERSON>", "CompanyType": "Software"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "italki": {"Keyword": "italki", "CompanyType": "Site"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "ID", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "ID", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "IL", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "IL", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Burlington English Inc": {"Keyword": "Burlington English Inc", "CompanyType": "Software company in Boca Raton, Florida"}, "italki": {"Keyword": "italki", "CompanyType": "Site"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "IT", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}, "Berlitz Corporation": {"Keyword": "Berlitz Corporation", "CompanyType": "Training company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "IT", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Babbel": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Software"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "KR", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "KR", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "italki": {"Keyword": "italki", "CompanyType": "Site"}, "Memrise": {"Keyword": "Memrise", "CompanyType": "Website"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "MA", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "MA", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Memrise": {"Keyword": "Memrise", "CompanyType": "Website"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "MM", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "MM", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "italki": {"Keyword": "italki", "CompanyType": "Site"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "MN", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "MN", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Memrise": {"Keyword": "Memrise", "CompanyType": "Website"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "MX", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "MX", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Open English": {"Keyword": "Open English", "CompanyType": "School in Miami, Florida"}, "Platzi": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Educational technology company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "MY", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}, "Berlitz Corporation": {"Keyword": "Berlitz Corporation", "CompanyType": "Training company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "MY", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Babbel": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Software"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "PE", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "PE", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Open English": {"Keyword": "Open English", "CompanyType": "School in Miami, Florida"}, "Platzi": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Educational technology company"}, "italki": {"Keyword": "italki", "CompanyType": "Site"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "PT", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "PT", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}, "Babbel": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Software"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "RU", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "RU", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Skyeng": {"Keyword": "<PERSON><PERSON>", "CompanyType": "Company"}, "Puzzle English": {"Keyword": "Puzzle English", "CompanyType": "Website"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "SA", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "SA", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}, "italki": {"Keyword": "italki", "CompanyType": "Site"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "TH", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}, "Enconcept E-Academy": {"Keyword": "Enconcept E-Academy", "CompanyType": "Language school in Bangkok, Thailand"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "TH", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}, "Babbel": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Software"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "TN", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}, "AMIDEAST": {"Keyword": "AMIDEAST", "CompanyType": "Non-profit organization"}, "Mediterranean School of Business (MSB)": {"Keyword": "Mediterranean School of Business (MSB)", "CompanyType": "Private school in Tunis, Tunisia"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "TN", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}, "Memrise": {"Keyword": "Memrise", "CompanyType": "Website"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "TR", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "TR", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}, "Rosetta Stone Inc.": {"Keyword": "Rosetta Stone Inc.", "CompanyType": "Software company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "VE", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "VE", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Open English": {"Keyword": "Open English", "CompanyType": "School in Miami, Florida"}, "Platzi": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Educational technology company"}, "italki": {"Keyword": "italki", "CompanyType": "Site"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "VN", "ServiceType": "GlobalHybrid", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "British Council": {"Keyword": "British Council", "CompanyType": "Corporation"}, "EF Education First": {"Keyword": "EF Education First", "CompanyType": "Education company"}, "Topica Edtech Group": {"Keyword": "Topica Edtech Group", "CompanyType": "Company"}}], "CycleId": *********}, {"Status": 404, "CutoffDate": "2023-02-09", "LoadType": "Incremental", "Stage": 1, "Operation": "DataExtraction", "Territory": "VN", "ServiceType": "GlobalOnline", "Competitors": [{"Wall Street English": {"Keyword": "Wall Street English", "CompanyType": "Teaching company"}, "Duolingo": {"Keyword": "Duolingo", "CompanyType": "Computer program"}, "Busuu": {"Keyword": "<PERSON><PERSON><PERSON>", "CompanyType": "Web site"}, "Memrise": {"Keyword": "Memrise", "CompanyType": "Website"}}], "CycleId": *********}]}]}