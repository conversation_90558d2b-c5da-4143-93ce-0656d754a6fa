{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
    id,
    newversioncompanyid,
    centerid,
    addressline1,
    addressline2,
    city,
    state,
    country,
    postalcode,
    primaryphonenumber,
    secondaryphonenumber,
    faxnumber,
    email,
    callbetweenfrom,
    callbetweento,
    contactpersonfirstname,
    contactpersonlastname,
    contactpersonemail,
    contactpersondesignation,
    contactpersondepartment,
    contactpersonmobile,
    companyrefid,
    createdbyid,
    modifiedbyid,
    {{cast_to_timestamp('createddate')}} as createddate,
    {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
    ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'companyadditionalinfo')}}
)

SELECT
    {{etl_load_date()}},
    id,
    newversioncompanyid as new_version_company_id,
    centerid as center_id,
    addressline1 as address_line1,
    addressline2 as address_line2,
    city as city,
    state as state,
    country as country,
    postalcode as postal_code,
    primaryphonenumber as primary_phone_number,
    secondaryphonenumber as secondary_phone_number,
    faxnumber as fax_number,
    email as email,
    callbetweenfrom as call_between_from,
    callbetweento as call_between_to,
    contactpersonfirstname as contact_person_first_name,
    contactpersonlastname as contact_person_last_name,
    contactpersonemail as contact_person_email,
    contactpersondesignation as contact_person_designation,
    contactpersondepartment as contact_person_department,
    contactpersonmobile as contact_person_mobile,
    companyrefid as company_ref_id,
    createdbyid as created_by_id,
    modifiedbyid as modified_by_id,
    createddate as created_date,
    lastupdateddate as last_updated_date
FROM 
    RankedRecords
WHERE 
    rn = 1;