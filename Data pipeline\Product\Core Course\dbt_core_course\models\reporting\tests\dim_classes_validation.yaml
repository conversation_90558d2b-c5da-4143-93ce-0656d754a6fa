version: 2

models:
  - name: dim_classes
    columns:
      - name: class_id
        tests:
          - unique:
              severity: error
          - not_null:
              severity: error
      - name: center_id
        tests:
          - not_null:
              severity: error
      - name: class_date
        tests:
          - not_null:
              severity: error
      - name: class_local_start_date
        tests:
          - not_null:
              severity: error
      - name: class_service_type
        tests:
          - not_null:
              severity: error
      - name: number_of_seats
        tests:
          - not_null:
              severity: error
      - name: teen_flag
        tests:
         - accepted_values:
              values: ['Adults', 'Teens']
              severity: error
      - name: class_type_billable_flag
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
