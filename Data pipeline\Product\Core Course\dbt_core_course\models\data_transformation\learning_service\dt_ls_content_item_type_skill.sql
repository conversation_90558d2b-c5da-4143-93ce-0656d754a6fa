{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'dbt_unique_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_content_item_type_skill') }}
)
SELECT 
    {{etl_load_date()}},
    dbt_unique_id,
    content_item_type_id,
    skill_id
from
    ods_data as contentitemtypeskill
