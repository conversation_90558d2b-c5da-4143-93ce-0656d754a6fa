version: 2

models:
  - name: ods_ga4_events
    description: "ODS GA4 events model"
    columns:
      - name: dbt_unique_id
        description: "Deterministic unique ID for each GA4 event record"
        tests:
          - unique:
              severity: warn
          - not_null:
              severity: warn

      - name: event_date
        description: "Event date from GA4"
        tests:
          - not_null:
              severity: warn

      - name: event_timestamp
        description: "Timestamp of the event"
        tests:
          - not_null:
              severity: warn

      - name: event_name
        description: "Name of the event triggered"
        tests:
          - not_null:
              severity: warn

      - name: user_id
        description: "ID of the user triggering the event"
        tests:
          - not_null:
              severity: warn

    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - event_date
            - event_timestamp
            - event_name
            - user_id
          severity: warn
