version: 2

models:
  - name: ls_booked_student
    columns:
      - name: id
        tests:
          - not_null:
              severity: error
      - name: class_id
        tests:
          - not_null:
              severity: error
      - name: ref_class_id
        tests:
          - not_null:
              severity: warn
      - name: student_id
        tests:
          - not_null:
              severity: error
      - name: book_mode
        tests:
          - accepted_values:
                values: ['book', 'standby']
                severity: error
      - name: created
        tests:
          - not_null:
              severity: error
      - name: local_created
        tests:
          - not_null:
              severity: error
      - name: last_updated
        tests:
          - not_null:
              severity: error
      - name: local_last_updated
        tests:
          - not_null:
              severity: error
      - name: is_cancelled
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: is_accessed
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: booking_order_desc
        tests:
          - not_null:
              severity: error
      - name: standby_to_booked_flag
        tests:
          - accepted_values:
              values: [0, 1]
              quote: false
              severity: error