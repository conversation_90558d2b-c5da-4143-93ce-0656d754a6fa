{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        sequence,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        absolutesequence,
        id,
        categorytypeid,
        path,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'category'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    sequence,
    created,
    lastupdated as last_updated,
    absolutesequence as absolute_sequence,
    id,
    categorytypeid as category_type_id,
    path
FROM
    rankedrecords
WHERE
    rn = 1;
