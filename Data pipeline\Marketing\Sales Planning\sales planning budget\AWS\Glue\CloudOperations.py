import json
import boto3
import ast
import io
import logging
import pandas as pd
import datetime


class Redshift:
    @staticmethod
    def Connect():
        return boto3.client('redshift-data')


class S3:
    @staticmethod
    def Connect(method):
        return boto3.resource('s3') if method == 'resource' else boto3.client('s3')

    @staticmethod
    def ReadJsonFile(Bucket, FilePath):
        try:
            S3Resource = S3.Connect('resource')
            ReadFile = S3Resource.Object(Bucket, FilePath)
            FileContent = ReadFile.get()['Body'].read().decode('utf-8')
            return json.loads(FileContent)
        except Exception as ErrorMessage:
            logging.warning("Raised Exception in ReadJsonFile due to %s", format(ErrorMessage))
            raise Exception

    @staticmethod
    def WriteJsonFile(Bucket, FilePath, DataResponse):
        try:
            S3Resource = S3.Connect('resource')
            S3Resource.Object(Bucket, FilePath).put(Body=json.dumps(DataResponse))
            return FilePath
        except Exception as ErrorMessage:
            logging.warning("Raised Exception in WriteJsonFile due to %s", format(ErrorMessage))
            raise Exception

    @staticmethod
    def ReadCsvFile(Bucket, FilePath):
        try:
            S3Client = S3.Connect('client')
            obj = S3Client.get_object(Bucket=Bucket, Key=FilePath)
            return pd.read_csv(obj['Body'])
        except Exception as ErrorMessage:
            logging.warning("Raised Exception in ReadCsvFile due to %s", format(ErrorMessage))
            raise Exception

    @staticmethod
    def WriteCsvFile(FilePath, Bucket, DataResponse):
        try:
            S3Client = S3.Connect('client')
            S3Path = f"s3://{Bucket}/" + FilePath
            logging.warning("S3Path:'%s'", format(S3Path))
            with io.StringIO() as CsvBuffer:
                DataResponse.to_csv(CsvBuffer, index=False, quoting=1)
                S3Client.put_object(Bucket=Bucket, Key=FilePath, Body=CsvBuffer.getvalue())
            return S3Path
        except Exception as ErrorMessage:
            logging.warning("Raised Exception in WriteCsvFile due to %s", format(ErrorMessage))
            raise Exception


class SecretManager:
    @staticmethod
    def GetSecret(SecretName, RegionName):
        Session = boto3.session.Session()
        Client = Session.client(service_name='secretsmanager', region_name=RegionName)
        try:
            GetSecretValueResponse = Client.get_secret_value(SecretId=SecretName)
            return ast.literal_eval(GetSecretValueResponse['SecretString'])
        except Exception as ErrorMessage:
            logging.warning("Raised Exception in GetSecret due to %s", format(ErrorMessage))
            raise Exception


class StepFunction:
    @staticmethod
    def Connect():
        return boto3.client('stepfunctions')

    @staticmethod
    def CheckStepFunctionsRunning(StateMachineArn):
        try:
            StepFunctionClient = StepFunction.Connect()
            return StepFunctionClient.list_executions(
                stateMachineArn=StateMachineArn,
                statusFilter='RUNNING',
                maxResults=1
            )
        except Exception as ErrorMessage:
            logging.warning("Raised Exception in CheckStepFunctionsRunning due to %s", format(ErrorMessage))
            raise Exception

    @staticmethod
    def StartStepFunction(ExecutionInput, StateMachineArn):
        try:
            StepFunctionClient = StepFunction.Connect()
            return StepFunctionClient.start_execution(
                stateMachineArn=StateMachineArn,
                name=ExecutionInput + datetime.datetime.now().strftime("%d%m%y%H%M%S%f"),
                input=json.dumps({"input": ExecutionInput})
            )
        except Exception as ErrorMessage:
            logging.warning("Raised Exception in StartStepFunction due to %s", format(ErrorMessage))
            raise Exception
