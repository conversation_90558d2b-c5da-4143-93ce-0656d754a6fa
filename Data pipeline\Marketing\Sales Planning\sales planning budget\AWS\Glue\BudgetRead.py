import Packages

Args = Packages.getResolvedOptions(Packages.sys.argv,
                                   ['Status', 'Stage', 'Operation', 'Territory', 'SubSite', 'Folder', 'File', 'SheetInfo', 'CycleId'])
Bucket = "sales-planning-tool-budget-develop"
S3 = Packages.CloudOperations.S3
Status = Args['Status']
Stage = Args['Stage']
Operation = Args['Operation']
Territory = Args['Territory']
SubSite = Args['SubSite']
Folder = Args['Folder']
File = Args['File']
SheetInfo = Packages.ast.literal_eval(Args['SheetInfo'])
CycleId = Args['CycleId']
DataRequestList = []
for Data in SheetInfo:
    DataRequest = Packages.BudgetReadFramework.BudgetOperations. \
        DataExtractionProcess(CycleId=CycleId,
                              Territory=Territory,
                              SubSite =SubSite,
                              Folder=Folder,
                              File=File,
                              SheetName=Data['SheetName'],
                              TableName=Data['TableName'],
                              Bucket=Bucket)
    Packages.logging.warning("Summary:'%s'", format(DataRequest))
    DataRequestList.append(DataRequest)
Logs = {
    "Status": 200,
    "Stage": int(Stage),
    "Operation": Operation,
    "Territory": Territory,
    "Summary": str(DataRequestList),
    "CycleId": CycleId
}
Packages.logging.warning("Logs:'%s'", format(Logs))
S3.WriteJsonFile(Bucket, f"Logs/{CycleId}/Stage{Stage}/{Territory}.json", Logs)
