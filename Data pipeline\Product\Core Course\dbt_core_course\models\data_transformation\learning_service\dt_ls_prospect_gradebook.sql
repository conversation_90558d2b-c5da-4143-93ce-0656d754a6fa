{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_prospect_gradebook') }}

{% if is_incremental() %}
where
    date_completed > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT 
    {{etl_load_date()}},
    score,
    date_completed,
    assignment_date,
    is_time_out,
    start_date,
    time_remaining,
    created,
    last_updated,
    id,
    comment,
    status,
    prospect_id,
    settled_level_id
from
    ods_data as prospectgradebook
