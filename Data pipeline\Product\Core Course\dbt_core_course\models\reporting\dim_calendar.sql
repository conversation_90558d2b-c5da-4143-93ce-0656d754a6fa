{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}


select 
    date_key
    ,date
    ,year
    ,quarter
    ,month
    ,month_name
    ,week
    ,day_of_week
    ,day_of_week_name
    ,day_of_month
    ,day_of_year
    ,is_weekday
    ,is_weekend
    ,year_week_key
    ,year_month
    ,year_month_key
    ,CASE 
        WHEN date_format(current_date, '%Y-%m') = year_month then 'Current Month'
        WHEN date_format(date_add('month',-1,current_date),'%Y-%m') = year_month then 'Last Month'
        WHEN date_format(date_add('month',1,current_date),'%Y-%m') = year_month then 'Next Month'
        Else year_month
    END    AS  mont_cm_lm
    ,year_week
    ,first_month_date
    ,last_month_date
    ,first_week_date
    ,last_week_date
FROM 
    analytics.calendar
WHERE
    year < 2200