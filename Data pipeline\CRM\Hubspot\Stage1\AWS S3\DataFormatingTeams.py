
OwnersData = []
TeamsData = []

class OwnersDataFormatting:
    @staticmethod
    def TeamsOwnerFormatting(OwnersDataExtract, CycleId, TerritoryCode):
        # Define the default properties to be added to each owner
        DefaultProperty = {"territory_code":TerritoryCode, "cycleid": CycleId}

        for SingleResponse in OwnersDataExtract:
            # Extract the owner information excluding the 'teams' key
            Owners = {key: value for key, value in SingleResponse.items() if key != 'teams'}

            # Add the default properties to the owner
            Owners.update(DefaultProperty)

            # Append the owner to the OwnersData list
            OwnersData.append(Owners)

            # Define additional properties specific to the owner
            AdditionProperty = {"ownerid": SingleResponse['id']}
            AdditionProperty.update(DefaultProperty)

            if 'teams' in SingleResponse:
                Teams = {key: value for key, value in SingleResponse.items() if key == 'teams'}
                for TeamsIterate in Teams['teams']:

                    # Add the additional properties to each team
                    TeamsIterate.update(AdditionProperty)

                    # Append the team to the TeamsData list
                    TeamsData.append(TeamsIterate)

        # Return the formatted OwnersData and TeamsData lists
        return OwnersData, TeamsData


