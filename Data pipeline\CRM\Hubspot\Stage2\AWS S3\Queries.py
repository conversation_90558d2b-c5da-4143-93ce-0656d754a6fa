class Stage2Queries:
    @staticmethod
    def scdqueries(ScdTable, DistinctProperties, RawTable, Condition, cycleid, function, properties, Orderproperties,
                   PartitionProperties):
        SwitchQuery = {
            "contactsscd": """
        select
    *
from
    {} dw
where
    {} not in (
        SELECT
            {}
        FROM
            {} raw
        WHERE
             {}
            and raw.cycleid = '{}'
    )
union
select

    {}
from
    (
        select
            *,
            row_number() over (
                partition by 
                {}
                order by
                    {} desc
            ) row_num
        from
            {}
        where
            cycleid = '{}'
    ) tempTable
where
    row_num = 1""".format(ScdTable, DistinctProperties, DistinctProperties, RawTable, Condition, cycleid, properties,
                          PartitionProperties, Orderproperties, RawTable, cycleid),
            "teams_updated":
                """
                        SELECT *
            FROM {} dw
            WHERE NOT EXISTS (
                SELECT 1
                FROM {} raw
                WHERE {}
                    AND raw.CycleId = '{}'
            )
            UNION
            SELECT {}
            FROM (
                SELECT *,
                    ROW_NUMBER() OVER (
                        PARTITION BY {}
                        ORDER BY {} DESC
                    ) row_num
                FROM {}
                WHERE CycleId = '{}'
            ) tempTable
            WHERE row_num = 1
            """.format(ScdTable, RawTable, Condition, cycleid, properties, PartitionProperties, Orderproperties,
                       RawTable, cycleid),
            "contacts_updated":
                """
            select

    {}
from
    (
        select
            *,
            row_number() over (
                partition by 
                {}
                order by
                    {} desc
            ) row_num
        from
            {}
    ) tempTable
where
    row_num = 1""".format(properties,
                          PartitionProperties, Orderproperties, RawTable),
            "webhooks_scd": """select *
            from
            {} dw
            where {} not in (select {} from {} raw where {} and subscriptiontype='contact.privacyDeletion')
            and subscriptiontype='contact.privacyDeletion'
            union
            select
            {}
            from (select *, row_number() over(partition by {} order by {} desc ) row_num from {} ) tempTable
            where row_num=1
            and subscriptiontype='contact.privacyDeletion'

            """.format(ScdTable, DistinctProperties, DistinctProperties, RawTable, Condition, properties,
                       PartitionProperties, Orderproperties, RawTable),
            "associations": """
            select * from {} dw where {} not in     (    SELECT
            {}
        FROM
            {} raw
        WHERE
             {}
            and raw.cycleid = '{}'
    )
union
select

    {}
from
            {}
        where
            cycleid = '{}'
            """.format(ScdTable, DistinctProperties, DistinctProperties, RawTable, Condition, cycleid, properties,
                       RawTable, cycleid),
            "associationsupdated": """select {} from {} where cycleid = '{}'""".format(properties, RawTable, cycleid)

        }

        Query = SwitchQuery.get(function)
        # print(InnerQuery)
        return Query


class Stage3:
    @staticmethod
    def EnrichmentQueries(object):
        Stage3Queries = {
            "webhooks": "select ws.*, from_unixtime(cast(occurredat as BIGINT)/1000) as occurredat,tz.iso_code as territorycode from hubspot.webhooks_scd ws join hubspot.hubspotterritorytimezone tz on ws.portalid = tz.hubspot_portalid where  ws.subscriptiontype='contact.privacyDeletion'",
            "contacts": """select 
actual_status , 
  address , 
  agreement_type , 
  cast(case when territory_code in ('AR','CL','DE','DO','DZ','EC','MX','SA','TN','US','UY') then hs_lifecyclestage_salesqualifiedlead_date else booked_date end as date) as booked_date , 
  brand_name , 
  call_campaign , 
  cast(call_count as bigint) as call_count , 
  campana_mql , 
  case when territory_code = 'EC' then centro_de_estudio
    when (center_name is null or center_name='Nan' or center_name='NULL') then 'No Center'
    else center_name
     end as center_name, 
  channel , 
  channel_drill_down_1 , 
  channel_drill_down_2 , 
  SUBSTRING(TRIM(city),1,100) as city , 
  cast(case when territory_code in ('AR','CL','DE','DO','DZ','EC','MX','SA','TN','US','UY')  then hs_lifecyclestage_customer_date else contract_date end as date) as contract_date , 
  core_course_idam_userid , 
  core_course_student_id , 
  course_age_group , 
  course_type , 
  cast(replace(replace(createdate,'T',' '),'Z','') as timestamp)  as createdate,
  cast(decision_maker_date as date) as decision_maker_date , 
  email , 
  excludefromstats__c , 
  cast(first_contract_amount as double) as first_contract_amount  , 
  first_contract_center , 
  first_contract_owner , 
  cast(replace(replace(first_conversion_date,'T',' '),'Z','') as timestamp) as first_conversion_date, 
  first_conversion_event_name , 
  case when territory_code ='EC' then fuente_dos else first_source end as first_source, 
  first_sub_source , 
  substring(TRIM(firstname), 1, 200) AS firstname,   
  gender , 
  case when territory_code ='CO' then como_nos_conociste else how_did_you_hear_about_us end as how_did_you_hear_about_us , 
  hs_analytics_first_url , 
  hs_analytics_source , 
  hs_analytics_source_data_1 , 
  hs_analytics_source_data_2 , 
  cast(replace(replace(hs_createdate,'T',' '),'Z','') as timestamp) as  hs_createdate, 
  cast(replace(replace(hs_lastmodifieddate,'T',' '),'Z','') as timestamp) as hs_lastmodifieddate , 
  cast(replace(replace(hs_lifecyclestage_customer_date,'T',' '),'Z','') as timestamp) as hs_lifecyclestage_customer_date , 
  cast(replace(replace(hs_lifecyclestage_evangelist_date,'T',' '),'Z','') as timestamp) as hs_lifecyclestage_evangelist_date , 
  cast(replace(replace(hs_lifecyclestage_lead_date,'T',' '),'Z','') as timestamp) as hs_lifecyclestage_lead_date  , 
  cast(replace(replace(hs_lifecyclestage_marketingqualifiedlead_date,'T',' '),'Z','') as timestamp) as hs_lifecyclestage_marketingqualifiedlead_date, 
  cast(replace(replace(hs_lifecyclestage_opportunity_date,'T',' '),'Z','') as timestamp) as hs_lifecyclestage_opportunity_date, 
  cast(replace(replace(hs_lifecyclestage_other_date,'T',' '),'Z','') as timestamp) as hs_lifecyclestage_other_date , 
  cast(replace(replace(hs_lifecyclestage_salesqualifiedlead_date,'T',' '),'Z','') as timestamp) as hs_lifecyclestage_salesqualifiedlead_date , 
  cast(hs_object_id as bigint) as hs_object_id , 
  cast(case when hubspot_owner_id is null then 'NoOwner' else hubspot_owner_id end as bigint) as hubspot_owner_id, 
  cast(hubspotscore as int) as hubspotscore, 
  individual_corporate , 
  substring(TRIM(last_touch_utm_campaign),1,2) as last_touch_utm_campaign , 
  substring(TRIM(last_touch_utm_medium),1,2) as last_touch_utm_medium , 
  substring(TRIM(last_touch_utm_referral),1,2) as last_touch_utm_referral , 
  substring(TRIM(last_touch_utm_source),1,2) as last_touch_utm_source , 
  cast(replace(replace(lastmodifieddate,'T',' '),'Z','') as timestamp) as lastmodifieddate , 
  SUBSTRING(TRIM(lastname), 1, 200) AS lastname, 
  latest_source , 
  latest_source_drill_down_1 , 
  latest_source_drill_down_2 , 
  cast(case when territory_code in ('AR','CL','DE','DO','DZ','EC','MX','SA','TN','US','UY') then hs_lifecyclestage_lead_date else lead_date end as date) as lead_date , 
  lead_source , 
  lifecyclestage , 
  cast(lost_date as date) as lost_date , 
  medio_landing_mql , 
  cast(case when territory_code in ('AR','CL','DE','DO','DZ','EC','MX','SA','TN','US','UY') then hs_lifecyclestage_marketingqualifiedlead_date else mql_date end as date) as mql_date, 
  case when territory_code ='EC' then razones_de_no_interes else not_interested_suitable_reason end as not_interested_suitable_reason , 
  cast(replace(replace(notes_last_updated,'T',' '),'Z','') as timestamp) as notes_last_updated, 
  cast(replace(replace(notes_next_activity_date,'T',' '),'Z','') as timestamp) as notes_next_activity_date, 
  substring(trim(phone),1,50) as phone , 
  provedor_mql , 
  province , 
  qu_te_motiva_a_aprender_ingl_s_ , 
  qualifying_stage , 
  case when territory_code ='EC' then raz_n_para_aprender_ingl_s else reason_for_learning_english end as reason_for_learning_english , 
  cast(replace(replace(recent_conversion_date,'T',' '),'Z','') as timestamp) as recent_conversion_date , 
  recent_conversion_event_name , 
  cast(case when territory_code in ('AR','CL','DE','DO','DZ','EC','MX','SA','TN','US','UY') then hs_lifecyclestage_opportunity_date else show_date end as date) as show_date , 
  case when territory_code ='EC' then   fuente
when source is null then 'No Source' else source end as source, 
  case when territory_code ='EC' then   otras_fuentes else sub_source end as sub_source, 
  tmk_owner , 
  cast(useful_contact_date as date) as useful_contact_date, 
  where_did_you_hear_about_us , 
  zip , 
  cast(AB.id as bigint) as id , 
  cast(replace(replace(createdat,'T',' '),'Z','') as timestamp) as createdat , 
  cast(replace(replace(updatedat,'T',' '),'Z','') as timestamp) as updatedat , 
  territory_code , 
  case when deleteflag in ('N','R') then AB.archived 
  when deleteflag in ('A') then AB.archivedtable
  when deleteflag in ('D') then AB.archived end as archived, 
  cast(cycleid as bigint) as cycleid ,
  case when (deleteflag is NULL or deleteflag='NULL' or deleteflag='') then 'N' else deleteflag end as deleteflag,
   cast(replace(replace(deleteddate,'T',' '),'Z','') as timestamp) as deleteddate,
   cast(replace(replace(restoreddate,'T',' '),'Z','') as timestamp) as restoreddate,
   cast(replace(replace(archiveddate,'T',' '),'Z','')  as timestamp) as archiveddate,
case when territory_code = 'CL' then from_utc_timestamp(AB.createdate,'-04:00')
 when territory_code='LA' then from_utc_timestamp(AB.createdate,'+07:00')
     WHEN territory_code = 'CO' THEN from_utc_timestamp(AB.createdate, '-05:00')
    WHEN territory_code = 'EC' THEN from_utc_timestamp(AB.createdate, '-05:00')
    WHEN territory_code = 'PE' THEN from_utc_timestamp(AB.createdate, '-05:00')
    WHEN territory_code = 'DO' THEN from_utc_timestamp(AB.createdate, '-04:00')
     WHEN (territory_code = 'DO' AND center_name in ('Panamá: Creditcorp Plaza') ) then from_utc_timestamp(AB.createdate, '-05:00')
    WHEN territory_code = 'TN' THEN from_utc_timestamp(AB.createdate, '+01:00')
    WHEN territory_code = 'ES' THEN from_utc_timestamp(AB.createdate, '+02:00')
    WHEN territory_code = 'MN' THEN from_utc_timestamp(AB.createdate, '+08:00')
    WHEN territory_code = 'SA' THEN from_utc_timestamp(AB.createdate, '+03:00')
    WHEN territory_code = 'AR' THEN from_utc_timestamp(AB.createdate, '-03:00')
    WHEN territory_code = 'UY' THEN from_utc_timestamp(AB.createdate, '-03:00')
    WHEN territory_code = 'US' THEN from_utc_timestamp(AB.createdate, '-05:00')
    WHEN territory_code = 'MX' THEN from_utc_timestamp(AB.createdate, '-06:00')
    WHEN territory_code = 'FR' THEN from_utc_timestamp(AB.createdate, '+02:00')
    WHEN territory_code = 'DE' THEN from_utc_timestamp(AB.createdate, '+02:00')
    WHEN territory_code = 'DZ' THEN from_utc_timestamp(AB.createdate, '+02:00')
    WHEN territory_code = 'CH' THEN from_utc_timestamp(AB.createdate, '+02:00')
    WHEN territory_code = 'KR' THEN from_utc_timestamp(AB.createdate, '+09:00')
    WHEN territory_code = 'KZ' THEN from_utc_timestamp(AB.createdate, '+06:00')
    WHEN territory_code = 'IT' THEN from_utc_timestamp(AB.createdate, '+02:00')
      WHEN territory_code = 'MA' THEN from_utc_timestamp(AB.createdate, '+01:00') end as timezonecreatedate,
   case when (territory_code = 'DO' and center_name = 'Santo Domingo: Novocentro') then 'Dominican Republic'
   when (territory_code = 'DO') and center_name in ('VE: Central',
   'Maracaibo: Costa Verde',
   'Caracas: Plaza la Boyera',
   'VE: Corporate Sales',
   'Maracaibo: Corporate Sales',
   'Caracas: Chuao',
   'San Antonio de Los Altos: CEPAN KM 13 Panamericana',
   'Caracas: Centro Lido') then 'Venezuela'
   when (territory_code='DO' and center_name in ('Panamá: Creditcorp Plaza')) then 'Panama'
       when (territory_code = 'CH' and center_name = 'WSE Praha' ) then 'Czech Republic'
        else tz.country_name end as territory_name,
hs_merged_object_ids

  from 
(select cs.*,as.archived as archivedtable, case when we.occurredat >  replace(replace(cs.hs_lastmodifieddate,'T',' '),'Z','') then 'D'
                                        when (replace(replace(as.archivedat,'T',' '),'Z','') > replace(replace(cs.hs_lastmodifieddate,'T',' '),'Z','') ) and (as.archivedat<>'Nan' or as.archivedat is NOT NULL or as.archivedat<>'NULL')  then 'A'
                                        when ((replace(replace(cs.hs_lastmodifieddate,'T',' '),'Z','') > we.occurredat) or (replace(replace(cs.hs_lastmodifieddate,'T',' '),'Z','') > replace(replace(as.archivedat,'T',' '),'Z','')) and (as.archivedat<>'' or as.archivedat is NOT  NULL or as.archivedat<>'NULL')) and cs.archived='false'  then 'R' else 'N' end as deleteflag ,
                                        case when (we.occurredat > replace(replace(cs.hs_lastmodifieddate,'T',' '),'Z','')) and (we.occurredat is not NULL or we.occurredat<>'Nan' or we.occurredat<>'NULL')  then occurredat else NULL end as deleteddate,
                                        case when (replace(replace(as.archivedat,'T',' '),'Z','') > replace(replace(cs.hs_lastmodifieddate,'T',' '),'Z','')) and (as.archivedat<>'Nan' or as.archivedat is NOT NULL or as.archivedat<>'NULL') then replace(replace(as.archivedat,'T',' '),'Z','') else NULL end as archiveddate,
                                        case when (replace(replace(cs.hs_lastmodifieddate,'T',' '),'Z','') > we.occurredat) or (replace(replace(cs.hs_lastmodifieddate,'T',' '),'Z','') > replace(replace(as.archivedat,'T',' '),'Z','')) and (as.archivedat<>'Nan' or as.archivedat is NOT NULL or as.archivedat<>'NULL') and (we.occurredat is not NULL or we.occurredat<>'Nan' or we.occurredat<>'NULL') then replace(replace(cs.hs_lastmodifieddate,'T',' '),'Z','') else NULL end as restoreddate
                      from hubspot.contacts_scd cs
                      left join hubspot.webhooks_scd we
                      on cs.territory_code = we.portalid
                      and cs.id = we.objectid
                      left join hubspot.archivecontacts_scd as
                      on cs.territory_code = as.territory_code
                      and cs.id = as.id) AB
                      left join hubspot.hubspotterritorytimezone tz
                      on AB.territory_code=tz.iso_code""",
            "deals": """select 
cast(dealid as bigint) as dealid, 
isdeleted, 
associatedcompanyids,
associateddealids,
associatedvids,
cast(amount as double) as amount, 
cast(amount_gifts as double) as amount_gifts, 
cast(amount_interest as double) as amount_interest, 
cast(amount_in_home_currency as double) as amount_in_home_currency, 
cast(booked_date as timestamp) as booked_date,									  
cast(business_partner_1_amount as int) as business_partner_1_amount,
cast(business_partner_2_hours as int) as business_partner_2_hours,
cast(business_partner_3_start_date as timestamp) as business_partner_3_start_date,
cast(business_partner_4_end_date as timestamp) as business_partner_4_end_date,
cast(business_partner_5_start_level as int) as business_partner_5_start_level,
cast(business_partner_6_end_level as int) as business_partner_6_end_level,
business_partner_7_tags,
case when territory_code='EC' then centro_de_estudio
     when territory_code='CL' then center_name_deal
     when territory_code='AR' then sales_center_name
     else center_name end as center_name, 
                     channel_drill_down_1,
                channel_drill_down_2,											  									  
cast(replace(replace(closedate,'T',' '),'Z','') as timestamp) as closedate, 
substring(trim(closed_lost_reason), 1, 200) as closed_lost_reason, 
closed_won_reason, 
                coach,
                cast(contacted_date as timestamp) as contacted_date,
                conversion,
                cast(conversion_date as timestamp) as conversion_date,					  							 
cast(contract_number as bigint) as contract_number, 
core_course_7_class_access_type, 
cast(core_course_amount as double) as core_course_amount, 
cast(core_course_end_date as timestamp) as core_course_end_date, 
cast(core_course_levels as int) as core_course_levels, 
cast(core_course_start_date as timestamp) as core_course_start_date,
                core_course_contract_id,
                core_course_student_id,																		   
cast(replace(replace(createdate,'T',' '),'Z','') as timestamp) as createdate, 
cast(days_to_close as bigint) as days_to_close, 
dealname, 
deal_source,
dealstage, 
dealtype, 
SUBSTRING(TRIM(description),1,200) as description, 
dm_known,
cast(interest_date as timestamp) as interest_date,
cast(lead_date as timestamp) as lead_date,														  
likelihood_to_close, 
lost_reason,
lost_stage,												   
hs_all_accessible_team_ids, 
hs_all_owner_ids, 
hs_all_team_ids, 
hs_analytics_source, 
hs_analytics_source_data_1, 
hs_analytics_source_data_2, 
hs_campaign, 
cast(replace(replace(hs_createdate,'T',' '),'Z','') as timestamp) as hs_createdate,
hs_deal_amount_calculation_preference, 
cast(replace(replace(hs_lastmodifieddate,'T',' '),'Z','') as timestamp) as hs_lastmodifieddate, 
cast(hs_object_id as bigint) as hs_object_id, 
hs_sales_email_last_replied, 
cast(replace(replace(hubspot_owner_assigneddate,'T',' '),'Z','') as timestamp) as hubspot_owner_assigneddate, 
cast(hubspot_owner_id as bigint) as hubspot_owner_id, 
cast(hubspot_team_id as bigint) as hubspot_team_id , 
individual_corporate, 
market_leader_1_amount, 
market_leader_2_hours, 
cast(market_leader_3_start_date as timestamp), 
cast(market_leader_4_end_date as timestamp), 
market_leader_5_start_level, 
market_leader_6_end_level, 
membership,
cast(mql_date as timestamp),		   		 
cast(replace(replace(notes_last_contacted,'T',' '),'Z','') as timestamp) as notes_last_contacted, 
cast(replace(replace(notes_last_updated,'T',' '),'Z','') as timestamp) as notes_last_updated, 
cast(replace(replace(notes_next_activity_date,'T',' '),'Z','') as timestamp) as notes_next_activity_date, 
num_associated_contacts, 
num_contacted_notes, 
num_notes, 
pe_end_level, 
pe_start_level, 
pipeline, 
cast(prospect_date as timestamp) as propect_date,
cast(showed_date as timestamp) as showed_date,						  
case when territory_code='EC' then fuente_wse
    else source end as source, 
stage,
case when territory_code='EC' then otras_fuentes else sub_source end as sub_source, 
test_prep_group_1_amount, 
test_prep_group_2_quantity, 
cast(test_prep_group_3_start_date as timestamp) as test_prep_group_3_start_date, 
cast(test_prep_group_4_end_date as timestamp) as test_prep_group_4_end_date, 
test_prep_types_1, 
extraurlparameters, 
channel, 
show_location, 
core_course_idam_userid, 
cast(offer_date as timestamp) as offer_date,
offer_amount, 
course_age_group, 
core_course_7_product_type, 
cast(amount_net as double) as amount_net, 
call_campaign,   
cast(certifications_1_amount as double) as certifications_1_amount, 
cast(certifications_2_quantity as bigint) as certifications_2_quantity, 
certifications_3_type, 
cast(certifications_4_start_date as timestamp) as certifications_4_start_date, 
cast(certifications_5_end_date as timestamp) as certifications_5_end_date, 
convenzioni, 
core_course_7_cross_center_booking, 
core_course_7_type, 
core_course_fit_0_group_name, 
core_course_fit_2_hours, 
cast(core_course_fit_3_start_date as timestamp) as core_course_fit_3_start_date, 
cast(core_course_fit_4_end_date as timestamp) as core_course_fit_4_end_date, 
cast(core_course_fit_5_start_level as bigint) as core_course_fit_5_start_level, 
cast(core_course_fit_6_end_level as bigint) as core_course_fit_6_end_level, 
core_course_fit_7_type, 
cast(core_course_fit_amount as double) as core_course_fit_amount, 
cast(core_course_online_1_amount as double) as core_course_online_1_amount, 
core_course_online_2_levels, 
cast(core_course_online_3_start_date as timestamp) as core_course_online_3_start_date, 
cast(core_course_online_4_end_date as timestamp) as core_course_online_4_start_date, 
core_course_online_5_start_level, 
core_course_online_6_end_level, 
core_course_online_7_type,  
hs_merged_object_ids, 				  
cast(ilc_1_amount as double) as ilc_1_amount, 
ilc_2_hours, 
cast(ilc_3_start_date as timestamp) as ilc_3_start_date, 
cast(ilc_4_end_date as timestamp) as ilc_4_end_date, 
ilc_6_tags, 
ilc_language, 
market_leader_7_tags,  
subsource, 
cast(test_prep_executive_1_amount as double) as test_prep_executive_1_amount, 
test_prep_executive_2_quantity, 
cast(test_prep_executive_4_start_date as timestamp) as test_prep_executive_4_start_date, 
cast(test_prep_executive_5_end_date as timestamp) as test_prep_executive_5_end_date, 
test_prep_executive_6_tags, 
test_prep_executive_types, 
test_prep_group_0_group_name, 
test_prep_group_2_tags, 
voucher_welfare, 
cast(AB.id as bigint) as id, 
cast(replace(replace(AB.createdat,'T',' '),'Z', '') as timestamp) as createdat, 
cast(replace(replace(AB.updatedat,'T',' '),'Z', '') as timestamp) as updatedat, 
AB.territory_code, 
AB.archived, 
cast(AB.cycleid as bigint) as cycleid,
case when territory_code = 'CL' then from_utc_timestamp(AB.createdate,'-04:00')
 when territory_code='LA' then from_utc_timestamp(AB.createdate,'+07:00')
     WHEN territory_code = 'CO' THEN from_utc_timestamp(AB.createdate, '-05:00')
    WHEN territory_code = 'EC' THEN from_utc_timestamp(AB.createdate, '-05:00')
    WHEN territory_code = 'PE' THEN from_utc_timestamp(AB.createdate, '-05:00')
    WHEN territory_code = 'DO' THEN from_utc_timestamp(AB.createdate, '-04:00')
     WHEN (territory_code = 'DO' AND center_name in ('Panamá: Creditcorp Plaza') ) then from_utc_timestamp(AB.createdate, '-05:00')
    WHEN territory_code = 'TN' THEN from_utc_timestamp(AB.createdate, '+01:00')
    WHEN territory_code = 'ES' THEN from_utc_timestamp(AB.createdate, '+02:00')
    WHEN territory_code = 'MN' THEN from_utc_timestamp(AB.createdate, '+08:00')
    WHEN territory_code = 'SA' THEN from_utc_timestamp(AB.createdate, '+03:00')
    WHEN territory_code = 'AR' THEN from_utc_timestamp(AB.createdate, '-03:00')
    WHEN territory_code = 'UY' THEN from_utc_timestamp(AB.createdate, '-03:00')
    WHEN territory_code = 'US' THEN from_utc_timestamp(AB.createdate, '-05:00')
    WHEN territory_code = 'MX' THEN from_utc_timestamp(AB.createdate, '-06:00')
    WHEN territory_code = 'FR' THEN from_utc_timestamp(AB.createdate, '+02:00')
    WHEN territory_code = 'DE' THEN from_utc_timestamp(AB.createdate, '+02:00')
    WHEN territory_code = 'DZ' THEN from_utc_timestamp(AB.createdate, '+02:00')
    WHEN territory_code = 'CH' THEN from_utc_timestamp(AB.createdate, '+02:00')
    WHEN territory_code = 'KR' THEN from_utc_timestamp(AB.createdate, '+09:00')
    WHEN territory_code = 'KZ' THEN from_utc_timestamp(AB.createdate, '+06:00')
    WHEN territory_code = 'IT' THEN from_utc_timestamp(AB.createdate, '+02:00')
    WHEN territory_code = 'MA' THEN from_utc_timestamp(AB.createdate, '+01:00')end as timezonecreatedate,
  CASE
    WHEN territory_code = 'CL' THEN from_utc_timestamp(AB.closedate, '-04:00')
    WHEN territory_code = 'LA' THEN from_utc_timestamp(AB.closedate, '+07:00')
    WHEN territory_code = 'CO' THEN from_utc_timestamp(AB.closedate, '-05:00')
    WHEN territory_code = 'EC' THEN from_utc_timestamp(AB.closedate, '-05:00')
    WHEN territory_code = 'PE' THEN from_utc_timestamp(AB.closedate, '-05:00')
    WHEN territory_code = 'DO' THEN from_utc_timestamp(AB.closedate, '-04:00')
    WHEN (territory_code = 'DO' AND center_name in ('Panamá: Creditcorp Plaza') ) then from_utc_timestamp(AB.closedate, '-05:00')
    WHEN territory_code = 'TN' THEN from_utc_timestamp(AB.closedate, '+01:00')
    WHEN territory_code = 'ES' THEN from_utc_timestamp(AB.closedate, '+02:00')
    WHEN territory_code = 'MN' THEN from_utc_timestamp(AB.closedate, '+08:00')
    WHEN territory_code = 'SA' THEN from_utc_timestamp(AB.closedate, '+03:00')
    WHEN territory_code = 'AR' THEN from_utc_timestamp(AB.closedate, '-03:00')
    WHEN territory_code = 'UY' THEN from_utc_timestamp(AB.closedate, '-03:00')
    WHEN territory_code = 'US' THEN from_utc_timestamp(AB.closedate, '-05:00')
    WHEN territory_code = 'MX' THEN from_utc_timestamp(AB.closedate, '-06:00')
    WHEN territory_code = 'FR' THEN from_utc_timestamp(AB.closedate, '+02:00')
    WHEN territory_code = 'DE' THEN from_utc_timestamp(AB.closedate, '+02:00')
    WHEN territory_code = 'DZ' THEN from_utc_timestamp(AB.closedate, '+02:00')
    WHEN territory_code = 'CH' THEN from_utc_timestamp(AB.closedate, '+02:00')
    WHEN territory_code = 'KR' THEN from_utc_timestamp(AB.closedate, '+09:00')
    WHEN territory_code = 'KZ' THEN from_utc_timestamp(AB.closedate, '+06:00')
    WHEN territory_code = 'VN' THEN from_utc_timestamp(AB.closedate, '+07:00')
    WHEN territory_code = 'MM' THEN from_utc_timestamp(AB.closedate, '+06:30')
    WHEN territory_code = 'IT' THEN from_utc_timestamp(AB.closedate, '+02:00')
     WHEN territory_code = 'MA' THEN from_utc_timestamp(AB.closedate, '+01:00')end
 as timezoneclosedate,
   case when (territory_code = 'DO' and center_name = 'Santo Domingo: Novocentro') then 'Dominican Republic'
   when (territory_code = 'DO') and center_name in ('VE: Central',
   'Maracaibo: Costa Verde',
   'Caracas: Plaza la Boyera',
   'VE: Corporate Sales',
   'Maracaibo: Corporate Sales',
   'Caracas: Chuao',
   'San Antonio de Los Altos: CEPAN KM 13 Panamericana',
   'Caracas: Centro Lido') then 'Venezuela'
   when (territory_code='DO' and center_name in ('Panamá: Creditcorp Plaza')) then 'Panama'
       when (territory_code = 'CH' and center_name = 'WSE Praha' ) then 'Czech Republic'
        else tz.country_name end as territory_name,
case when (deleteflag is NULL or deleteflag='NULL' or deleteflag='') then 'N' else deleteflag end as deleteflag,
cast(replace(replace(archiveddate,'T',' '),'Z', '')  as timestamp) as archiveddate,
cast(replace(replace(restoreddate,'T',' '),'Z', '') as timestamp) as restoreddate
from
(select ds.*,
case when (replace(replace(ad.archivedat,'T',' '),'Z','') > replace(replace(ds.hs_lastmodifieddate,'T',' '),'Z','')) and (ad.archivedat<>'Nan' or ad.archivedat is NOT NULL or ad.archivedat<>'NULL') then 'A'
      when (replace(replace(ds.hs_lastmodifieddate,'T',' '),'Z','') > replace(replace(ad.archivedat,'T',' '),'Z','')) and (ad.archivedat<>'Nan' or ad.archivedat is NOT NULL or ad.archivedat<>'NULL') then 'R'
      when ad.archivedat='NULL' or ad.archivedat='Nan' or ad.archivedat is NULL then 'N' end as deleteflag,
      ad.archivedat,
case when replace(replace(ad.archivedat,'T',' '),'Z','') > replace(replace(ds.hs_lastmodifieddate,'T',' '),'Z','') and (ad.archivedat<>'Nan' or ad.archivedat is NOT NULL or ad.archivedat<>'NULL') then ad.archivedat else NULL end as archiveddate,
case when replace(replace(ds.hs_lastmodifieddate,'T',' '),'Z','') > replace(replace(ad.archivedat,'T',' '),'Z','') and (ad.archivedat<>'Nan' or ad.archivedat is NOT NULL or ad.archivedat<>'NULL') then ds.hs_lastmodifieddate end as restoreddate
from hubspot.deals_scd ds
left join hubspot.archivedeals_scd ad
on ad.territory_code=ds.territory_code
and ad.id=ds.id) AB
                      join hubspot.hubspotterritorytimezone tz
                      on AB.territory_code=tz.iso_code;""",
            "companies": """select
cast(annualrevenue as double) as annualrevenue, 
  cast(booked_date as date) as booked_date,
  domain, 
  cast(hs_object_id as bigint) as hs_object_id, 
  name, 
  cast(hubspot_owner_id as bigint) as hubspot_owner_id, 
  company_source, 
  cast(contacted_date as date) as contacted_date, 
  cast(contract_date as date) as contract_date, 
  cast(replace(replace(createdate,'T',' '), 'Z','') as timestamp) as createdate, 
  create_deal, 
  cast(replace(replace(hs_date_entered_customer,'T',' '), 'Z','') as timestamp) as hs_date_entered_customer, 
  cast(replace(replace(hs_date_entered_evangelist,'T',' '), 'Z','') as timestamp) as hs_date_entered_evangelist, 
  cast(replace(replace(hs_date_entered_lead,'T',' '), 'Z','') as timestamp) as hs_date_entered_lead, 
  cast(replace(replace(hs_date_entered_marketingqualifiedlead,'T',' '), 'Z','') as timestamp) as hs_date_entered_marketingqualifiedlead, 
  cast(replace(replace(hs_date_entered_opportunity,'T',' '), 'Z','') as timestamp) as hs_date_entered_opportunity, 
  cast(replace(replace(hs_date_entered_other,'T',' '), 'Z','') as timestamp) as hs_date_entered_other, 
  cast(replace(replace(hs_date_entered_salesqualifiedlead,'T',' '), 'Z','') as timestamp) as hs_date_entered_salesqualifiedlead, 
  cast(replace(replace(hs_date_entered_subscriber,'T',' '), 'Z','') as timestamp) as hs_date_entered_subscriber, 
  cast(decision_maker_date as date) as decision_maker_date, 
  cast(hubspot_team_id as bigint) as hubspot_team_id, 
  industry, 
  cast(lead_date as date) as lead_date, 
  lifecyclestage, 
  cast(lost_date as date) as lost_date, 
  cast(mql_date as date) as mql_date, 
  hs_createdate, 
  hs_analytics_source_data_1, 
  hs_analytics_source_data_2, 
  hs_analytics_source, 
  cast(potential as double) as potential, 
  qualifying_stage_company, 
  segment, 
  cast(show_date as date) as show_date, 
  state, 
  value_per_employee, 
  cast(replace(replace(hs_lastmodifieddate,'T',' '), 'Z','') as timestamp) as hs_lastmodifieddate, 
  cast(AB.id as bigint) as id, 
  cast(replace(replace(createdat,'T',' '), 'Z','') as timestamp) as createdat, 
  cast(replace(replace(updatedat,'T',' '), 'Z','') as timestamp) as updatedat, 
  territory_code, 
  archived, 
  cast(cycleid as bigint) as cycleid,
tz.country_name as territory_name,
  case when (deleteflag is NULL or deleteflag='NULL' or deleteflag='') then 'N' else deleteflag end as deleteflag,
  cast(replace(replace(archiveddate,'T',' '), 'Z','') as timestamp) as archiveddate,
  cast(replace(replace(restoreddate,'T',' '), 'Z','') as timestamp) as restoreddate
  from (

  select cd.*,
  case when (acd.archivedat<>'Nan' or acd.archivedat is NOT NULL or acd.archivedat<>'NULL' or acd.archivedat <> '') and replace(replace(acd.archivedat,'T',' '),'Z','') > replace(replace(cd.hs_lastmodifieddate,'T',' '),'Z','') then 'A' 
      when (replace(replace(cd.hs_lastmodifieddate,'T',' '),'Z','') > replace(replace(acd.archivedat,'T',' '),'Z','')) and  (acd.archivedat<>'Nan' or acd.archivedat is NOT NULL or acd.archivedat<>'NULL' or acd.archivedat <> '') then 'R'
      when (acd.archivedat='Nan' or acd.archivedat is NULL or acd.archivedat='NULL' or acd.archivedat <> '') then 'N'
      else 'N' end as deleteflag,
case when replace(replace(acd.archivedat,'T',' '),'Z','') > replace(replace(cd.hs_lastmodifieddate,'T',' '),'Z','') and (acd.archivedat<>'Nan' or acd.archivedat is NOT NULL or acd.archivedat<>'NULL' or acd.archivedat <> '') then acd.archivedat else '' end as archiveddate,
case when replace(replace(cd.hs_lastmodifieddate,'T',' '),'Z','') > replace(replace(acd.archivedat,'T',' '),'Z','') and (acd.archivedat<>'Nan' or acd.archivedat is NOT NULL or acd.archivedat<>'NULL' or acd.archivedat <> '') then cd.hs_lastmodifieddate end as restoreddate 
from hubspot.companies_scd cd
left join hubspot.archivecompanies_scd acd
on acd.territory_code = cd.territory_code
and acd.id=cd.id) AB
                      join hubspot.hubspotterritorytimezone tz
                      on AB.territory_code=tz.iso_code""",
            "teams": """select cast(ts.id as bigint) as teamsid,ts.name,ts.primary,cast(ts.ownerid as bigint) as ownerid,
            ts.territory_code,ts.cycleid,tz.country_name as territory_name from hubspot.teams_scd ts
                                  join hubspot.hubspotterritorytimezone tz
                      on ts.territory_code=tz.iso_code""",
            "customobject": """select cast(cs.amount_for_sales___contract as double) as amount_for_sales___contract,
            cs.contract_category,
            cast(replace(replace(cs.hs_createdate,'T',' '),'Z','') as timestamp) as hs_createdate,
            cast(replace(replace(cs.hs_lastmodifieddate,'T',' '),'Z','') as timestamp) as hs_lastmodifieddate,
            cast(cs.hs_object_id as bigint) as hs_object_id,
            cs.name,
            cast(cs.sign_date as date) as sign_date,
            cs.status_reason,
            cast(cs.id as bigint) as id,
            cast(replace(replace(cs.createdat,'T',' '),'Z','') as timestamp) as createdat,
            cast(replace(replace(cs.updatedat,'T',' '),'Z','') as timestamp) as updatedat,
            cs.archived,
            cs.territory_code,
            cast(cs.cycleid as bigint) as cycleid,
            cast(cs.contactid as bigint) as contactid,
            cs.contacttype,
            tz.country_name as territory_name 
            from hubspot.customobject_scd cs
                                              join hubspot.hubspotterritorytimezone tz
                      on cs.territory_code=tz.iso_code""",
            "associations": """select as.amount, 
            replace(replace(as.closedate,'T',' '),'Z','') as closedate,
            replace(replace(as.createdate,'T',' '),'Z','') as createdate,
            as.dealname,
            as.dealstage,
            replace(replace(as.hs_lastmodifieddate,'T',' '),'Z','') as hs_lastmodifieddate,
            as.hs_object_id,
            as.pipeline,
            as.id,
            replace(replace(as.createdat,'T',' '),'Z','') as createdat,
            replace(replace(as.updatedat,'T',' '),'Z','') as updatedat,
            as.archived,
            as.territory_code,
            as.cycleid,
            tz.country_name as territory_name
             from hubspot.association_scd as
                                                          join hubspot.hubspotterritorytimezone tz
                      on as.territory_code=tz.iso_code""",
            "owners": """select
  cast(AB.id as bigint) as id, 
  cast(email as string) as email, 
  cast(firstname as string) as firstname, 
  cast(lastname as string) as lastname, 
  cast(userid as bigint) as userid, 
  cast(replace(replace(createdat,'T',' '),'Z','') as timestamp) as createdat, 
  cast(replace(replace(updatedat,'T',' '),'Z','') as timestamp) as updatedat, 
  archived, 
  territory_code, 
  cast(cycleid as bigint) as cycleid,
  tz.Country_Name as territory_name,
  case when (deleteflag is NULL or deleteflag='NULL' or deleteflag='') then 'N' else deleteflag end as deleteflag,
  cast(replace(replace(archiveddate,'T',' '),'Z','') as timestamp) as archiveddate,
  cast(replace(replace(restoreddate,'T',' '),'Z','') as timestamp) as restoreddate
  from 

(select os.*, 
case when aos.cycleid > os.cycleid  then 'A'
when os.cycleid > aos.cycleid  then 'R'
else 'N' 
end as deleteflag, 
case when (replace(replace(aos.updatedat,'T',' '),'Z','') > replace(replace(os.updatedat,'T',' '),'Z','') and (aos.updatedat<>'Nan' or aos.updatedat is NOT NULL or aos.updatedat <> 'NULL' or aos.updatedat <> '')) then aos.archivedat else NULL end as archiveddate,
case when (replace(replace(os.updatedat,'T',' '),'Z','') > replace(replace(aos.updatedat,'T',' '),'Z','') and (aos.updatedat<>'Nan' or aos.updatedat is NOT NULL or aos.updatedat<>'NULL' or aos.updatedat <> '')) then os.updatedat end as restoreddate 
   from hubspot.owners_scd os
left join hubspot.archiveowners_scd aos
on os.territory_code=aos.territory_code
and os.id=aos.id) AB
                      join hubspot.hubspotterritorytimezone tz
                      on AB.territory_code=tz.iso_code""",
            "contactstodeals": """
            select cast(Contactid as bigint) as contactid,
             cast(AssociationType as string) as associationtype, 
             cast(AssociatedId as bigint) as associateid,
             cast(createdate as timestamp) as createdate,
             cast(updatedate as timestamp) as updatedate,
             territory_code,
             cast(cycleid as bigint) as cycleid
             from (
            select id as Contactid, 
            dealtype as AssociationType
            ,dealid as AssociatedId,
            replace(replace(createdat,'T',' '),'Z','') as createdate,
            replace(replace(updatedat,'T',' '),'Z','') as updatedate,
            territory_code,
            cycleid 
             from hubspot.contacttodeals_scd)""",
            "contactstocompanies": """
            select
            cast(createdate as timestamp) as createdate,
            cast(hs_object_id as bigint) as hs_object_id,
            cast(lastmodifieddate as timestamp) as lastmodifieddate,
            cast(id as bigint) as id,
            cast(createdat as timestamp) as createdat,
            cast(updatedat as timestamp) as updatedat,
            territory_code,
            cast(cycleid as bigint) as cycleid,
            cast(companiesid as bigint) as companiesid,
            companiestype
            from (
            select 
            replace(replace(createdate,'T',' '), 'Z','') as createdate,
            hs_object_id,
            replace(replace(lastmodifieddate,'T',' '),'Z','') as lastmodifieddate,
            id,
            replace(replace(createdat,'T',' '), 'Z','') as createdat,
            replace(replace(updatedat,'T',' '),'Z','') as updatedat,
            territory_code,
            cycleid,
            companiesid,
            companiestype
             from hubspot.contactstocompanies_scd)
            """,
            "dealstocontacts": """
            select
            cast(createdate as timestamp) as createdate,
            cast(hs_object_id as bigint) as hs_object_id,
            cast(hs_lastmodifieddate as timestamp) as hs_lastmodifieddate,
            cast(id as bigint) as id,
            cast(createdat as timestamp) as createdat,
            cast(updatedat as timestamp) as updatedat,
            territory_code,
            cast(cycleid as bigint) as cycleid,
            cast(contactid as bigint) as contactid,
            contacttype
            from
            (
            select 
            replace(replace(createdate,'T',' '), 'Z','') as createdate,
            hs_object_id,
            replace(replace(hs_lastmodifieddate,'T',' '),'Z','') as hs_lastmodifieddate,
            id,
            replace(replace(createdat,'T',' '), 'Z','') as createdat,
            replace(replace(updatedat,'T',' '), 'Z','') as updatedat,
            territory_code,
            cycleid,
            contactid,
            contacttype
            from hubspot.dealstocontacts_scd)
            """,
            "dealstocompanies": """
                        select
            cast(createdate as timestamp) as createdate,
            cast(hs_object_id as bigint) as hs_object_id,
            cast(hs_lastmodifieddate as timestamp) as hs_lastmodifieddate,
            cast(id as bigint) as id,
            cast(createdat as timestamp) as createdat,
            cast(updatedat as timestamp) as updatedat,
            territory_code,
            cast(cycleid as bigint) as cycleid,
            cast(companiesid as bigint) as companiesid,
            companiestype
            from
            (select
            replace(replace(createdate,'T',' '), 'Z','') as createdate,
            hs_object_id,
            replace(replace(hs_lastmodifieddate,'T',' '),'Z','') as hs_lastmodifieddate,
            id,
            replace(replace(createdat,'T',' '), 'Z','') as createdat,
            replace(replace(updatedat,'T',' '), 'Z','') as updatedat,
            territory_code,
            cycleid,
            companiesid,
            companiestype            
            from 
            hubspot.dealstocompanies_scd)
            """,
            "companiestocontacts": """
            select
            cast(contract_date as timestamp) as contract_date,
            cast(createdate as timestamp) as createdate,
            cast(hs_lastmodifieddate as timestamp) as hs_lastmodifieddate,
            cast(hs_object_id as bigint) as hs_object_id,
            cast(hubspot_owner_id as bigint) as hubspot_owner_id,
            cast(hubspot_team_id as bigint) as hubspot_team_id,
            name,
            cast(id as bigint) as id,
            cast(createdat as timestamp) as createdat,
            cast(updatedat as timestamp) as updatedat,
                        territory_code,
            cast(cycleid as bigint) as cycleid,
            cast(contactid as bigint) as contactid,
            contacttype

            from (
            select
            replace(replace(contract_date,'T',' '), 'Z','') as contract_date,
            replace(replace(createdate,'T',' '), 'Z','') as createdate,
            replace(replace(hs_lastmodifieddate,'T',' '),'Z','') as hs_lastmodifieddate,
            hs_object_id,
            hubspot_owner_id,
            hubspot_team_id,
            name,
            id,
            replace(replace(createdat,'T',' '), 'Z','') as createdat,
            replace(replace(updatedat,'T',' '), 'Z','') as updatedat,
            territory_code,
            cycleid,
            contactid,
            contacttype           
            from
            hubspot.companiestocontacts_scd)
            """,
            "companiestodeals": """
            select 
                        cast(contract_date as timestamp) as contract_date,
            cast(createdate as timestamp) as createdate,
            cast(hs_lastmodifieddate as timestamp) as hs_lastmodifieddate,
            cast(hs_object_id as bigint) as hs_object_id,
            cast(hubspot_owner_id as bigint) as hubspot_owner_id,
            cast(hubspot_team_id as bigint) as hubspot_team_id,
            name,
            cast(id as bigint) as id,
            cast(createdat as timestamp) as createdat,
            cast(updatedat as timestamp) as updatedat,
                        territory_code,
            cast(cycleid as bigint) as cycleid,
            cast(dealid as bigint) as dealid,
            dealtype

            from (
            select
            replace(replace(contract_date,'T',' '), 'Z','') as contract_date,
            replace(replace(createdate,'T',' '), 'Z','') as createdate,
            replace(replace(hs_lastmodifieddate,'T',' '),'Z','') as hs_lastmodifieddate,
            hs_object_id,
            hubspot_owner_id,
            hubspot_team_id,
            name,
            id,
            replace(replace(createdat,'T',' '), 'Z','') as createdat,
            replace(replace(updatedat,'T',' '), 'Z','') as updatedat,
            territory_code,
            cycleid,
            dealid,
            dealtype
            from hubspot.companiestodeals_scd)
			            """,
            "mergedcontacts": """
            select
            AB.contact_id,
            cast(AB.merged_contactid as bigint) as merged_contactid,
            AB.territory_code,
            AB.cycleid
            from (
            select hs_object_id as contact_id,explode(split(hs_merged_object_ids, ';')) as merged_contactid,territory_code, cycleid from hubspot.contacts_scd
where hs_merged_object_ids <> '') AB;""",
            "mergeddeals": """
                        select
            AB.deal_id,
            cast(AB.merged_dealid as bigint) as merged_dealid,
            AB.territory_code,
            AB.cycleid
            from (
                        select hs_object_id as deal_id,explode(split(hs_merged_object_ids, ';')) as merged_dealid,territory_code, cycleid from hubspot.deals_scd
                        where hs_merged_object_ids <> '') AB;														 
            """

        }

        Query = Stage3Queries.get(object)
        return Query
