{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_territory_class_access'
        ) }}
    {% if is_incremental() %}
        where last_updated > ((select max(last_updated) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    tclsaccess.id  as id,
    territory_id,
    ctypes.name as contract_type,
    clsaccesstypes.name as class_access_type,
    is_active,
    created,
    last_updated          
from ods_data as tclsaccess
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_contract_types' ) }}
    ) as ctypes on tclsaccess.contract_type_id = ctypes.id
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_class_access_types' ) }}
    ) as clsaccesstypes on tclsaccess.class_access_type_id = clsaccesstypes.id