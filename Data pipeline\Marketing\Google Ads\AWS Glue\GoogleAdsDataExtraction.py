import Packages

# Get command-line arguments and assign default values if not provided
Args = Packages.getResolvedOptions(Packages.sys.argv,
                                   ['Status', 'Stage', 'Operation', 'Territory', 'AccountId',
                                    'CycleId', 'CutoffDate', 'LoadType'])
Bucket = "google-ads-production"
S3 = Packages.CloudOperations.S3

# Retrieve values from Args dictionary
Status = Args['Status']
Stage = Args['Stage']
Operation = Args['Operation']
Territory = Args['Territory']
CycleId = Args['CycleId']
CutoffDate = Args['CutoffDate']
LoadType = Args['LoadType']
AccountId = Args['AccountId']

Packages.logging.warning("LoadType:'%s'", format(LoadType))
Packages.logging.warning("CutoffDate:'%s'", format(CutoffDate))

# Create a data request object
DataRequest = Packages.GoogleAdsFramework.Ads.DataExtractionProcess(Territory=Territory,
                                                                    AccountId=AccountId,
                                                                    CutoffDate=CutoffDate,
                                                                    LoadType=LoadType,
                                                                    Bucket=Bucket,
                                                                    CycleId=CycleId)
Packages.logging.warning("Summary:'%s'", format(DataRequest))

# Create a dictionary for logs
Logs = {
    "Status": 200,
    "Stage": int(Stage),
    "AccountId":AccountId,
    "CutoffDate": DataRequest['CutoffDate'],
    "LoadType": LoadType,
    "Operation": Operation,
    "Territory": Territory,
    "Summary": str(DataRequest),
    "CycleId": CycleId
}
Packages.logging.warning("Logs:'%s'", format(Logs))

# Write the Logs dictionary as a JSON file
S3.WriteJsonFile(Bucket, f"Logs/{CycleId}/Stage{Stage}/{Territory}{AccountId}.json", Logs)
