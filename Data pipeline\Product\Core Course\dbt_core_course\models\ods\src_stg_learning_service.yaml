version: 2

sources:

  - name: stage_learning_service
    database: awsdatacatalog  
    schema: stg_learning_service  
    tables:
      - name: bookedstudent
      - name: bookmark
      - name: category
      - name: categoryattributes
      - name: categoryhierarchy
      - name: categorytype
      - name: center
      - name: centerconfig
      - name: class
      - name: classcategory
      - name: classresult
      - name: classtype
      - name: contentitem
      - name: contentitemresult
      - name: contentitemresulttype
      - name: contentitemskill
      - name: contentitemtype
      - name: contentitemtypeskill
      - name: contentoptimizationlevel
      - name: course
      - name: digitalbooksaccesssetting
      - name: digitalbookslog
      - name: digitalbooksrestart
      - name: encounterresultaggregate
      - name: followupactivity
      - name: followupactivitytype
      - name: language
      - name: lessonresultaggregate
      - name: motivation
      - name: nationality
      - name: profession
      - name: rebookedstudent
      - name: registration
      - name: registrationcourse
      - name: registrationtype
      - name: role
      - name: servicetype
      - name: skill
      - name: socialnetwork
      - name: staffmember
      - name: student
      - name: studyplanner
      - name: territory
      - name: timezone
      - name: unitresultaggregate
      - name: user
      - name: userroletype
      - name: prospect
      - name: prospectgradebook
      - name: preplacementtestresult
      - name: placementtestresult
      - name: placementtestinteraction
      - name: placementtestactivity
      - name: placementtestlevels
