version: 2

sources:

  - name: stage_learning_service
    description: >
      Source data from the Learning Service which manages educational content, student progress,
      courses, classes, and learning activities across the platform.
    database: awsdatacatalog
    schema: stg_learning_service
    tables:
      - name: bookedstudent
        description: Records of students booked into classes.
        columns:
          - name: id
            description: Primary key for the booked student record
          - name: classid
            description: Foreign key reference to the class
          - name: studentid
            description: Foreign key reference to the student
          - name: created
            description: Timestamp when the booking was created
          - name: lastupdated
            description: Timestamp when the booking was last updated

      - name: bookmark
        description: Bookmarks created by users for quick access to content.
        columns:
          - name: id
            description: Primary key for the bookmark record
          - name: userid
            description: Foreign key reference to the user who created the bookmark
          - name: contentitemid
            description: Foreign key reference to the content item
          - name: created
            description: Timestamp when the bookmark was created
          - name: lastupdated
            description: Timestamp when the bookmark was last updated

      - name: category
        description: Categories used to organize educational content.
        columns:
          - name: id
            description: Primary key for the category record
          - name: categorytypeid
            description: Foreign key reference to the category type
          - name: name
            description: Name of the category
          - name: description
            description: Description of the category
          - name: created
            description: Timestamp when the category was created
          - name: lastupdated
            description: Timestamp when the category was last updated

      - name: categoryattributes
        description: Additional attributes associated with categories.
        columns:
          - name: id
            description: Primary key for the category attribute record
          - name: categoryid
            description: Foreign key reference to the category
          - name: attributename
            description: Name of the attribute
          - name: attributevalue
            description: Value of the attribute
          - name: created
            description: Timestamp when the attribute was created
          - name: lastupdated
            description: Timestamp when the attribute was last updated

      - name: categoryhierarchy
        description: Hierarchical relationships between categories.
        columns:
          - name: id
            description: Primary key for the category hierarchy record
          - name: parentcategoryid
            description: Foreign key reference to the parent category
          - name: childcategoryid
            description: Foreign key reference to the child category
          - name: created
            description: Timestamp when the hierarchy relationship was created
          - name: lastupdated
            description: Timestamp when the hierarchy relationship was last updated

      - name: categorytype
        description: Types of categories in the system.
        columns:
          - name: id
            description: Primary key for the category type record
          - name: name
            description: Name of the category type
          - name: created
            description: Timestamp when the category type was created
          - name: lastupdated
            description: Timestamp when the category type was last updated

      - name: center
        description: Education centers in the learning system.
        columns:
          - name: id
            description: Primary key for the center record
          - name: centerreferenceid
            description: External reference ID for the center
          - name: name
            description: Name of the center
          - name: territoryid
            description: Foreign key reference to the territory
          - name: created
            description: Timestamp when the center record was created
          - name: lastupdated
            description: Timestamp when the center record was last updated

      - name: centerconfig
        description: Configuration settings for centers.
        columns:
          - name: id
            description: Primary key for the center configuration record
          - name: centerid
            description: Foreign key reference to the center
          - name: configname
            description: Name of the configuration setting
          - name: configvalue
            description: Value of the configuration setting
          - name: created
            description: Timestamp when the configuration was created
          - name: lastupdated
            description: Timestamp when the configuration was last updated

      - name: class
        description: Classes scheduled for students.
        columns:
          - name: id
            description: Primary key for the class record
          - name: classtypeid
            description: Foreign key reference to the class type
          - name: starttime
            description: Start time of the class
          - name: endtime
            description: End time of the class
          - name: centerid
            description: Foreign key reference to the center
          - name: staffmemberid
            description: Foreign key reference to the staff member teaching the class
          - name: created
            description: Timestamp when the class was created
          - name: lastupdated
            description: Timestamp when the class was last updated

      - name: classcategory
        description: Categories assigned to classes.
        columns:
          - name: id
            description: Primary key for the class category record
          - name: classid
            description: Foreign key reference to the class
          - name: categoryid
            description: Foreign key reference to the category
          - name: created
            description: Timestamp when the class category was created
          - name: lastupdated
            description: Timestamp when the class category was last updated

      - name: classresult
        description: Results and outcomes from classes.
        columns:
          - name: id
            description: Primary key for the class result record
          - name: classid
            description: Foreign key reference to the class
          - name: studentid
            description: Foreign key reference to the student
          - name: score
            description: Score achieved in the class
          - name: comments
            description: Comments about the student's performance
          - name: created
            description: Timestamp when the result was created
          - name: lastupdated
            description: Timestamp when the result was last updated

      - name: classtype
        description: Types of classes offered.
        columns:
          - name: id
            description: Primary key for the class type record
          - name: name
            description: Name of the class type
          - name: description
            description: Description of the class type
          - name: created
            description: Timestamp when the class type was created
          - name: lastupdated
            description: Timestamp when the class type was last updated

      - name: contentitem
        description: Individual content items that make up educational materials.
        columns:
          - name: id
            description: Primary key for the content item record
          - name: contentitemtypeid
            description: Foreign key reference to the content item type
          - name: title
            description: Title of the content item
          - name: description
            description: Description of the content item
          - name: content
            description: Actual content or reference to content
          - name: created
            description: Timestamp when the content item was created
          - name: lastupdated
            description: Timestamp when the content item was last updated

      - name: contentitemresult
        description: Results from student interactions with content items.
        columns:
          - name: id
            description: Primary key for the content item result record
          - name: contentitemid
            description: Foreign key reference to the content item
          - name: studentid
            description: Foreign key reference to the student
          - name: contentitemresulttypeid
            description: Foreign key reference to the result type
          - name: score
            description: Score achieved on the content item
          - name: created
            description: Timestamp when the result was created
          - name: lastupdated
            description: Timestamp when the result was last updated

      - name: contentitemresulttype
        description: Types of results that can be recorded for content items.
        columns:
          - name: id
            description: Primary key for the content item result type record
          - name: contentitemtypeid
            description: Foreign key reference to the content item type
          - name: name
            description: Name of the result type
          - name: bookmode
            description: Mode for booking related to this result type
          - name: created
            description: Timestamp when the result type was created
          - name: lastupdated
            description: Timestamp when the result type was last updated

      - name: contentitemskill
        description: Skills associated with content items.
        columns:
          - name: id
            description: Primary key for the content item skill record
          - name: contentitemid
            description: Foreign key reference to the content item
          - name: skillid
            description: Foreign key reference to the skill
          - name: created
            description: Timestamp when the association was created
          - name: lastupdated
            description: Timestamp when the association was last updated

      - name: contentitemtype
        description: Types of content items in the system.
        columns:
          - name: id
            description: Primary key for the content item type record
          - name: name
            description: Name of the content item type
          - name: created
            description: Timestamp when the content item type was created
          - name: lastupdated
            description: Timestamp when the content item type was last updated

      - name: contentitemtypeskill
        description: Skills associated with content item types.
        columns:
          - name: id
            description: Primary key for the content item type skill record
          - name: contentitemtypeid
            description: Foreign key reference to the content item type
          - name: skillid
            description: Foreign key reference to the skill
          - name: created
            description: Timestamp when the association was created
          - name: lastupdated
            description: Timestamp when the association was last updated

      - name: contentoptimizationlevel
        description: Optimization levels for content delivery.
        columns:
          - name: id
            description: Primary key for the content optimization level record
          - name: name
            description: Name of the optimization level
          - name: description
            description: Description of the optimization level
          - name: created
            description: Timestamp when the level was created
          - name: lastupdated
            description: Timestamp when the level was last updated

      - name: course
        description: Courses offered in the learning system.
        columns:
          - name: id
            description: Primary key for the course record
          - name: name
            description: Name of the course
          - name: description
            description: Description of the course
          - name: created
            description: Timestamp when the course was created
          - name: lastupdated
            description: Timestamp when the course was last updated
      - name: digitalbooksaccesssetting
        description: Access settings for digital books.
        columns:
          - name: id
            description: Primary key for the digital books access setting record
          - name: centerid
            description: Foreign key reference to the center
          - name: isactive
            description: Boolean flag indicating if the access is active
          - name: created
            description: Timestamp when the setting was created
          - name: lastupdated
            description: Timestamp when the setting was last updated

      - name: digitalbookslog
        description: Logs of digital book usage.
        columns:
          - name: id
            description: Primary key for the digital books log record
          - name: studentid
            description: Foreign key reference to the student
          - name: categoryid
            description: Foreign key reference to the category
          - name: action
            description: Action performed in the digital book
          - name: created
            description: Timestamp when the log entry was created
          - name: lastupdated
            description: Timestamp when the log entry was last updated

      - name: digitalbooksrestart
        description: Records of digital book restarts.
        columns:
          - name: studentid
            description: Foreign key reference to the student
          - name: categoryid
            description: Foreign key reference to the category
          - name: created
            description: Timestamp when the restart was created
          - name: lastupdated
            description: Timestamp when the restart was last updated

      - name: encounterresultaggregate
        description: Aggregated results from student encounters.
        columns:
          - name: id
            description: Primary key for the encounter result aggregate record
          - name: studentid
            description: Foreign key reference to the student
          - name: categoryid
            description: Foreign key reference to the category
          - name: score
            description: Aggregated score for the encounter
          - name: created
            description: Timestamp when the aggregate was created
          - name: lastupdated
            description: Timestamp when the aggregate was last updated

      - name: followupactivity
        description: Follow-up activities assigned to students.
        columns:
          - name: id
            description: Primary key for the follow-up activity record
          - name: studentid
            description: Foreign key reference to the student
          - name: followupactivitytypeid
            description: Foreign key reference to the follow-up activity type
          - name: duedate
            description: Due date for the activity
          - name: iscompleted
            description: Boolean flag indicating if the activity is completed
          - name: created
            description: Timestamp when the activity was created
          - name: lastupdated
            description: Timestamp when the activity was last updated

      - name: followupactivitytype
        description: Types of follow-up activities.
        columns:
          - name: id
            description: Primary key for the follow-up activity type record
          - name: name
            description: Name of the follow-up activity type
          - name: created
            description: Timestamp when the activity type was created
          - name: lastupdated
            description: Timestamp when the activity type was last updated

      - name: language
        description: Languages available in the system.
        columns:
          - name: id
            description: Primary key for the language record
          - name: name
            description: Name of the language
          - name: created
            description: Timestamp when the language record was created
          - name: lastupdated
            description: Timestamp when the language record was last updated

      - name: lessonresultaggregate
        description: Aggregated results from student lessons.
        columns:
          - name: id
            description: Primary key for the lesson result aggregate record
          - name: studentid
            description: Foreign key reference to the student
          - name: categoryid
            description: Foreign key reference to the category
          - name: score
            description: Aggregated score for the lesson
          - name: created
            description: Timestamp when the aggregate was created
          - name: lastupdated
            description: Timestamp when the aggregate was last updated

      - name: motivation
        description: Motivations for learning.
        columns:
          - name: id
            description: Primary key for the motivation record
          - name: name
            description: Name of the motivation
          - name: created
            description: Timestamp when the motivation record was created
          - name: lastupdated
            description: Timestamp when the motivation record was last updated

      - name: nationality
        description: Nationalities of users.
        columns:
          - name: id
            description: Primary key for the nationality record
          - name: name
            description: Name of the nationality
          - name: created
            description: Timestamp when the nationality record was created
          - name: lastupdated
            description: Timestamp when the nationality record was last updated

      - name: profession
        description: Professions of users.
        columns:
          - name: id
            description: Primary key for the profession record
          - name: name
            description: Name of the profession
          - name: created
            description: Timestamp when the profession record was created
          - name: lastupdated
            description: Timestamp when the profession record was last updated

      - name: rebookedstudent
        description: Records of students who have been rebooked into classes.
        columns:
          - name: id
            description: Primary key for the rebooked student record
          - name: classid
            description: Foreign key reference to the class
          - name: studentid
            description: Foreign key reference to the student
          - name: originalclassid
            description: Foreign key reference to the original class
          - name: created
            description: Timestamp when the rebooking was created
          - name: lastupdated
            description: Timestamp when the rebooking was last updated

      - name: registration
        description: Student registrations in the system.
        columns:
          - name: id
            description: Primary key for the registration record
          - name: studentid
            description: Foreign key reference to the student
          - name: registrationtypeid
            description: Foreign key reference to the registration type
          - name: startdate
            description: Start date of the registration
          - name: enddate
            description: End date of the registration
          - name: created
            description: Timestamp when the registration was created
          - name: lastupdated
            description: Timestamp when the registration was last updated

      - name: registrationcourse
        description: Courses associated with student registrations.
        columns:
          - name: id
            description: Primary key for the registration course record
          - name: registrationid
            description: Foreign key reference to the registration
          - name: courseid
            description: Foreign key reference to the course
          - name: created
            description: Timestamp when the association was created
          - name: lastupdated
            description: Timestamp when the association was last updated

      - name: registrationtype
        description: Types of registrations in the system.
        columns:
          - name: id
            description: Primary key for the registration type record
          - name: name
            description: Name of the registration type
          - name: created
            description: Timestamp when the registration type was created
          - name: lastupdated
            description: Timestamp when the registration type was last updated

      - name: role
        description: User roles in the learning system.
        columns:
          - name: id
            description: Primary key for the role record
          - name: name
            description: Name of the role
          - name: created
            description: Timestamp when the role was created
          - name: lastupdated
            description: Timestamp when the role was last updated

      - name: servicetype
        description: Types of services offered.
        columns:
          - name: id
            description: Primary key for the service type record
          - name: name
            description: Name of the service type
          - name: created
            description: Timestamp when the service type was created
          - name: lastupdated
            description: Timestamp when the service type was last updated

      - name: skill
        description: Skills that can be learned and assessed.
        columns:
          - name: id
            description: Primary key for the skill record
          - name: name
            description: Name of the skill
          - name: description
            description: Description of the skill
          - name: created
            description: Timestamp when the skill was created
          - name: lastupdated
            description: Timestamp when the skill was last updated

      - name: socialnetwork
        description: Social networks that users can connect.
        columns:
          - name: id
            description: Primary key for the social network record
          - name: name
            description: Name of the social network
          - name: created
            description: Timestamp when the social network record was created
          - name: lastupdated
            description: Timestamp when the social network record was last updated

      - name: staffmember
        description: Staff members in the learning system.
        columns:
          - name: id
            description: Primary key for the staff member record
          - name: userid
            description: Foreign key reference to the user
          - name: centerid
            description: Foreign key reference to the center
          - name: created
            description: Timestamp when the staff member record was created
          - name: lastupdated
            description: Timestamp when the staff member record was last updated
      - name: student
        description: Students in the learning system.
        columns:
          - name: id
            description: Primary key for the student record
          - name: userid
            description: Foreign key reference to the user
          - name: studentreferenceid
            description: External reference ID for the student
          - name: centerid
            description: Foreign key reference to the center
          - name: created
            description: Timestamp when the student record was created
          - name: lastupdated
            description: Timestamp when the student record was last updated

      - name: studyplanner
        description: Study plans for students.
        columns:
          - name: id
            description: Primary key for the study planner record
          - name: studentid
            description: Foreign key reference to the student
          - name: categoryid
            description: Foreign key reference to the category
          - name: targetdate
            description: Target date for completion
          - name: created
            description: Timestamp when the study plan was created
          - name: lastupdated
            description: Timestamp when the study plan was last updated

      - name: territory
        description: Territories or regions where centers operate.
        columns:
          - name: id
            description: Primary key for the territory record
          - name: territoryreferenceid
            description: External reference ID for the territory
          - name: name
            description: Name of the territory
          - name: timezoneid
            description: Foreign key reference to the timezone
          - name: created
            description: Timestamp when the territory record was created
          - name: lastupdated
            description: Timestamp when the territory record was last updated

      - name: timezone
        description: Timezones used by centers and territories.
        columns:
          - name: id
            description: Primary key for the timezone record
          - name: name
            description: Name of the timezone
          - name: created
            description: Timestamp when the timezone record was created
          - name: lastupdated
            description: Timestamp when the timezone record was last updated

      - name: unitresultaggregate
        description: Aggregated results from student units.
        columns:
          - name: id
            description: Primary key for the unit result aggregate record
          - name: studentid
            description: Foreign key reference to the student
          - name: categoryid
            description: Foreign key reference to the category
          - name: score
            description: Aggregated score for the unit
          - name: created
            description: Timestamp when the aggregate was created
          - name: lastupdated
            description: Timestamp when the aggregate was last updated

      - name: user
        description: Users in the learning system.
        columns:
          - name: id
            description: Primary key for the user record
          - name: username
            description: Username for system login
          - name: password
            description: Hashed password for authentication
          - name: firstname
            description: User's first name
          - name: lastname
            description: User's last name
          - name: email
            description: User's email address
          - name: ssdsid
            description: SSDS identifier for the user
          - name: created
            description: Timestamp when the user record was created
          - name: lastupdated
            description: Timestamp when the user record was last updated

      - name: userroletype
        description: Types of user roles in the system.
        columns:
          - name: id
            description: Primary key for the user role type record
          - name: userid
            description: Foreign key reference to the user
          - name: roleid
            description: Foreign key reference to the role
          - name: created
            description: Timestamp when the role assignment was created
          - name: lastupdated
            description: Timestamp when the role assignment was last updated

      - name: prospect
        description: Prospective students in the learning system.
        columns:
          - name: id
            description: Primary key for the prospect record
          - name: firstname
            description: First name of the prospect
          - name: lastname
            description: Last name of the prospect
          - name: email
            description: Email address of the prospect
          - name: centerid
            description: Foreign key reference to the center
          - name: created
            description: Timestamp when the prospect record was created
          - name: lastupdated
            description: Timestamp when the prospect record was last updated

      - name: prospectgradebook
        description: Gradebook records for prospect assessments.
        columns:
          - name: id
            description: Primary key for the prospect gradebook record
          - name: prospectid
            description: Foreign key reference to the prospect
          - name: score
            description: Score achieved by the prospect
          - name: created
            description: Timestamp when the gradebook record was created
          - name: lastupdated
            description: Timestamp when the gradebook record was last updated

      - name: preplacementtestresult
        description: Results from pre-placement tests for prospects.
        columns:
          - name: id
            description: Primary key for the pre-placement test result record
          - name: prospectid
            description: Foreign key reference to the prospect
          - name: score
            description: Score achieved on the pre-placement test
          - name: startlevel
            description: Starting level determined by the test
          - name: created
            description: Timestamp when the result was created
          - name: lastupdated
            description: Timestamp when the result was last updated

      - name: placementtestresult
        description: Results from placement tests for prospects.
        columns:
          - name: id
            description: Primary key for the placement test result record
          - name: prospectid
            description: Foreign key reference to the prospect
          - name: score
            description: Score achieved on the placement test
          - name: level
            description: Level determined by the test
          - name: created
            description: Timestamp when the result was created
          - name: lastupdated
            description: Timestamp when the result was last updated

      - name: placementtestinteraction
        description: Interactions during placement tests.
        columns:
          - name: id
            description: Primary key for the placement test interaction record
          - name: prospectid
            description: Foreign key reference to the prospect
          - name: placementtestactivityid
            description: Foreign key reference to the placement test activity
          - name: answer
            description: Answer provided by the prospect
          - name: created
            description: Timestamp when the interaction was created
          - name: lastupdated
            description: Timestamp when the interaction was last updated

      - name: placementtestactivity
        description: Activities that make up placement tests.
        columns:
          - name: id
            description: Primary key for the placement test activity record
          - name: placementtestlevelid
            description: Foreign key reference to the placement test level
          - name: title
            description: Title of the activity
          - name: content
            description: Content of the activity
          - name: created
            description: Timestamp when the activity was created
          - name: lastupdated
            description: Timestamp when the activity was last updated

      - name: placementtestlevels
        description: Levels used in placement tests.
        columns:
          - name: id
            description: Primary key for the placement test level record
          - name: name
            description: Name of the level
          - name: description
            description: Description of the level
          - name: sequence
            description: Sequence order of the level
          - name: created
            description: Timestamp when the level was created
          - name: lastupdated
            description: Timestamp when the level was last updated
