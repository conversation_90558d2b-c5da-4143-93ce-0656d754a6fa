{{ 
    config  (
    materialized = 'incremental',
    incremental_strategy = 'append',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) 
}}


WITH maximum_date_search_console AS (
    SELECT 
        territory_code, 
        MAX(data_date) AS max_data_date
    FROM 
        {{ this }}
    GROUP BY 
        territory_code
),

filtered_data AS (
    SELECT 
        ods.*
    FROM 
       {{ref('ods_searchconsole_url_impression')}} ods
    JOIN 
        maximum_date_search_console msc
    ON 
        msc.territory_code = ods.territory_code
    WHERE 
        ods.data_date > msc.max_data_date
),

ods_transform as (
select  
        data_date,
        clicks,
        country,
        device,
        impressions,
        regexp_replace(regexp_replace(url, 'https://', ''), 'http://', '') as page_url,
        REGEXP_EXTRACT(url, '(https?://[^/]+)(/.*)', 2) AS page_path,
        REGEXP_EXTRACT(url, '(https?://([^/]+))', 2) AS hostname,
CASE
WHEN lower(SPLIT_PART(url,'?',1)) LIKE '%/english-courses' OR lower(url) LIKE '%/english-courses/%'
OR (lower(url) LIKE '%wallstreetenglish.dz%' AND lower(SPLIT_PART(url,'?',1)) LIKE '%/formation-anglais') --specific for .dz
OR (lower(url) LIKE '%wallstreetenglish.dz%' AND lower(url) LIKE '%/formation-anglais/%') --specific for .dz
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/english-schulungskurse' OR lower(url) LIKE '%/schulungskurse/%'
OR (url NOT LIKE '%wse.edu.co/%' AND lower(SPLIT_PART(url,'?',1)) LIKE '%/curso-ingles') --specific for .co
OR (url NOT LIKE '%wse.edu.co/%' AND url LIKE '%/curso-ingles/%') --specific for .co
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/corsi-di-inglese' OR lower(url) LIKE '%/corsi-di-inglese/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/ingilizce-ogrenme' OR lower(url) LIKE '%/ingilizce-ogrenme/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/cours-anglais' OR lower(url) LIKE '%/cours-anglais/%'
THEN 'English courses'
WHEN lower(url) LIKE '%/blog%'
OR lower(url) LIKE '%blog.w%'
THEN 'Blog'
WHEN lower(SPLIT_PART(url,'?',1)) LIKE '%/exercises' OR lower(url) LIKE '%/exercises/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/exercices-anglais' OR lower(url) LIKE '%/exercices-anglais/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/ubungen' OR lower(url) LIKE '%/ubungen/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/ejercicios-ingles' OR lower(url) LIKE '%/ejercicios-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/esercizi' OR lower(url) LIKE '%/esercizi/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/ingilizce-alistirmalar' OR lower(url) LIKE '%/ingilizce-alistirmalar/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/exercice-anglais' OR lower(url) LIKE '%/exercice-anglais/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/ngu-phap-tieng-anh' OR lower(url) LIKE '%/ngu-phap-tieng-anh/%'
THEN 'Exercises'

WHEN lower(SPLIT_PART(url,'?',1)) LIKE '%/english-tests' OR lower(url) LIKE '%/english-tests/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/tests-anglais' OR lower(url) LIKE '%/tests-anglais/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/englisch-tests' OR lower(url) LIKE '%/englisch-tests/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/test-ingles' OR lower(url) LIKE '%/test-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/test-inglese' OR lower(url) LIKE '%/test-inglese/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/seviyenizi-test-edin' OR lower(url) LIKE '%/seviyenizi-test-edin/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/tests-ingles' OR lower(url) LIKE '%/tests-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/prueba-ingles' OR lower(url) LIKE '%/prueba-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/test-tieng-anh' OR lower(url) LIKE '%/test-tieng-anh/%'
THEN 'English tests'

WHEN lower(SPLIT_PART(url,'?',1)) LIKE '%/examen-anglais' OR lower(url) LIKE '%/examen-anglais/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/examenes-oficiales-ingles' OR lower(url) LIKE '%/examenes-oficiales-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/examenes-ingles' OR lower(url) LIKE '%/examenes-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/englischprufungen' OR lower(url) LIKE '%/englischprufungen/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/english-exams' OR lower(url) LIKE '%/english-exams/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/certificazioni' OR lower(url) LIKE '%/certificazioni/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/examen-oficial-ingles' OR lower(url) LIKE '%/examen-oficial-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/examens-officiels-anglais' OR lower(url) LIKE '%/examens-officiels-anglais/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/ingilizce-sertifikalar' OR lower(url) LIKE '%/ingilizce-sertifikalar/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/chung-chi-tieng-anh' OR lower(url) LIKE '%/chung-chi-tieng-anh/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/certifications' OR lower(url) LIKE '%/certifications/%'
THEN 'Certifications'

WHEN lower(SPLIT_PART(url,'?',1)) LIKE '%/tarifs' OR lower(url) LIKE '%/tarifs/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/precios' OR lower(url) LIKE '%/precios/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/preise' OR lower(url) LIKE '%/preise/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/prices' OR lower(url) LIKE '%/prices/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/prezzi-corsi-di-inglese' OR lower(url) LIKE '%/prezzi-corsi-di-inglese/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/ingilizce-kurs-fiyatlari' OR lower(url) LIKE '%/ingilizce-kurs-fiyatlari/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/hoc-phi' OR lower(url) LIKE '%/hoc-phi/%'
THEN 'Prices'


WHEN lower(SPLIT_PART(url,'?',1)) LIKE '%/centres' OR lower(url) LIKE '%/centres/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/academias-ingles' OR lower(url) LIKE '%/academias-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/escuelas' OR lower(url) LIKE '%/escuelas/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/zentren' OR lower(url) LIKE '%/zentren/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/schools' OR lower(url) LIKE '%/schools/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/scuola-inglese' OR lower(url) LIKE '%/scuola-inglese/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/centers' OR lower(url) LIKE '%/centers/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/english-center' OR lower(url) LIKE '%/english-center/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/escuela-ingles' OR lower(url) LIKE '%/escuela-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/centre-anglais' OR lower(url) LIKE '%/centre-anglais/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/academia-ingles' OR lower(url) LIKE '%/academia-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/centro-ingles' OR lower(url) LIKE '%/centro-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/institutes' OR lower(url) LIKE '%/institutes/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/ingilizce-kursu' OR lower(url) LIKE '%/ingilizce-kursu/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/trung-tam-tieng-anh' OR lower(url) LIKE '%/trung-tam-tieng-anh/%'
THEN 'Schools'

WHEN lower(SPLIT_PART(url,'?',1)) LIKE '%/apprendre-anglais' OR lower(url) LIKE '%/apprendre-anglais/%'
OR (url LIKE '%wse.edu.co/%' AND lower(SPLIT_PART(url,'?',1)) LIKE '%/curso-ingles') --specific for .co
OR (url LIKE '%wse.edu.co/%' AND url LIKE '%/curso-ingles/%') --specific for .co
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/aprende-ingles' OR lower(url) LIKE '%/aprende-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/englisch-zentren' OR lower(url) LIKE '%/englisch-zentren/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/english-schools' OR lower(url) LIKE '%/english-schools/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/impara-inglese' OR lower(url) LIKE '%/impara-inglese/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/learn-english' OR lower(url) LIKE '%/learn-english/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/aprender-ingles' OR lower(url) LIKE '%/aprender-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/formation-anglais' OR lower(url) LIKE '%/formation-anglais/%'
OR (lower(url) NOT LIKE '%wallstreetenglish.dz%' AND lower(SPLIT_PART(url,'?',1)) LIKE '%/formation-anglais') --specific for .dz
OR (lower(url) NOT LIKE '%wallstreetenglish.dz%' AND lower(url) LIKE '%/formation-anglais/%') --specific for .dz
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/instituto-ingles' OR lower(url) LIKE '%/instituto-ingles/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/english-institutes' OR lower(url) LIKE '%/english-institutes/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/ingilizce-kurslari' OR lower(url) LIKE '%/ingilizce-kurslari/%'
OR lower(SPLIT_PART(url,'?',1)) LIKE '%/trung-tam' OR lower(url) LIKE '%/trung-tam/%'
THEN 'City schools'

WHEN REGEXP_LIKE(SPLIT_PART(url,'?',1),'^(https?://[^/]+/?$)')
OR REGEXP_LIKE(SPLIT_PART(url,'?',1),'^(https?://[^/]+/[a-zA-Z]{2}/?$)') --for alt languages
THEN 'Home'

ELSE 'Other' END as page_type,
        cast(null as double precision) as  position,
        sum_position,
        query,
        territory_code,
        {{get_country_name('territory_code')}},
        table_source,  
        etl_load_date
    from filtered_data 
)
select 
    data_date,
    clicks,
    country as country_geo_loc,
    device,
    impressions,
    split_part(regexp_replace(page_url , '/$', '') , '?', 1) as page_url,
    split_part(regexp_replace(page_path , '/$', ''), '?', 1) as page_path,
    hostname,
    page_type,
    position,
    sum_position,
    query,
    territory_code,
    country_name as territory_name,
    table_source,
    etl_load_date
from 
ods_transform;
