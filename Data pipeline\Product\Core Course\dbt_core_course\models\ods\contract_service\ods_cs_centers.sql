{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        centerreferenceid,
        name,
        territoryid,
        haspilot,
        timezoneid,
        hasdigitalvalidation
    FROM 
        {{source('stage_contract_service', 'centers')}}
)

SELECT
    {{etl_load_date()}},
    id,
    centerreferenceid as center_reference_id,
    name as name,
    territoryid as territory_id,
    haspilot as has_pilot,
    timezoneid as time_zone_id,
    hasdigitalvalidation as has_digital_validation
FROM 
    RankedRecords