create table if not exists hubspot.customobjectenriched
(
    amount_for_sales___contract double precision,
    contract_category           varchar(18),
    hs_createdate               timestamp encode az64,
    hs_lastmodifieddate         timestamp encode az64,
    hs_object_id                bigint encode az64,
    name                        varchar(34),
    sign_date                   date encode az64,
    status_reason               varchar(48),
    id                          bigint encode az64,
    createdat                   timestamp encode az64,
    updatedat                   timestamp encode az64,
    archived                    varchar(8),
    territory_code              varchar(4),
    cycleid                     bigint encode az64,
    contactid                   bigint encode az64,
    contacttype                 varchar(23),
    territory_name              varchar(34)
);



