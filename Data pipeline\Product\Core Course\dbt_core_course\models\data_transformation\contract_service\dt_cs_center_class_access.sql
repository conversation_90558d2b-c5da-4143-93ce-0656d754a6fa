{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_center_class_access'
        ) }}
    {% if is_incremental() %}
        where last_updated > ((select max(last_updated) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    id,
    center_id,
    territory_class_access_id,
    is_active,
    created,
    last_updated
from ods_data as temptable