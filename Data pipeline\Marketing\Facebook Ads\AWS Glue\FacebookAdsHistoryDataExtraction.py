import HistoryPackages

Bucket = "facebook-ads-production"
S3 = HistoryPackages.CloudOperations.S3
Status = 404
Stage = 1
Operation = "DataExtraction"
Territory = ""
CycleId = ""
FromDate = ""
ToDate =""
LoadType = "Initial"
AccountId =""

HistoryPackages.logging.warning("LoadType:'%s'", format(LoadType))
DataRequest = HistoryPackages.HistoryFacebookAdsFramework.Ads.DataExtractionProcess(Territory=Territory,
                                                                      AccountId=AccountId,
                                                                      FromDate=FromDate,
                                                                      ToDate =ToDate,
                                                                      LoadType=LoadType,
                                                                      Bucket=Bucket,
                                                                      CycleId=CycleId)
HistoryPackages.logging.warning("Summary:'%s'", format(DataRequest))
Logs = {
    "Status": 200,
    "Stage": int(Stage),
    "AccountId": AccountId,
    "CutoffDate": DataRequest['CutoffDate'],
    "LoadType": LoadType,
    "Operation": Operation,
    "Territory": Territory,
    "Summary": str(DataRequest),
    "CycleId": CycleId
}
HistoryPackages.logging.warning("Logs:'%s'", format(Logs))
S3.WriteJsonFile(Bucket, f"Logs/{CycleId}/Stage{Stage}/{Territory}.json", Logs)
