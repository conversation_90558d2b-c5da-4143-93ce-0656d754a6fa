import sys
from pyspark import SparkConf, SparkContext
from pyspark.sql import SparkSession
import logging

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def initialize_spark(app_name="MyApp"):
    """
    Initialize and return a Spark session.
    """
    conf = SparkConf().setMaster("local").setAppName(app_name)
    sc = SparkContext.getOrCreate(conf=conf)
    spark = SparkSession(sc)
    return spark

def read_json_from_s3(spark, file_path):
    """
    Read JSON files from the specified S3 path into a Spark DataFrame.
    """
    return spark.read.json(file_path)

def write_df_to_s3(df, table_name, write_path):
    """
    Write the DataFrame to the specified S3 path as a table.
    """
    df.write.mode('overwrite').saveAsTable(table_name, path=write_path)

def main():
    """
    Main function to initialize Spark, read JSON data from S3, and write it back to S3 as a table.
    """
    try:
        # Initialize Spark session
        spark = initialize_spark(app_name="MyApp")
        
        # Define file paths and table name
        file_path = 's3://etl-dev-gsc-extract/url_json_response_raw/*/*'
        write_path = 's3://etl-dev-gsc-extract/raw_layer_table/gsc_searchconsole_url_impression'
        table_name = 'google_search_console_dev.gsc_searchconsole_url_impression' 

        # Read JSON data from S3
        response_df = read_json_from_s3(spark, file_path)

        # Check if the DataFrame is empty
        if response_df.rdd.isEmpty():
            logger.info("No data present today.")
        else:
            # Show DataFrame content
            response_df.show()

            # Write DataFrame to S3 as a table
            write_df_to_s3(response_df, table_name, write_path)
    except Exception as e:
        logger.error("An error occurred", exc_info=True)

if __name__ == "__main__":
    main()
