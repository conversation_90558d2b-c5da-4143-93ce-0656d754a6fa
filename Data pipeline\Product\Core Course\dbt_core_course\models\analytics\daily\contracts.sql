{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH contract_acccess_type as (select contract_id ,
	CASE
        WHEN COUNT(DISTINCT class_type_description) = 1 THEN MAX(class_type_description)
        ELSE 'full access'
    END AS class_type 
from {{ref('dt_cs_contract_class_access_type')}}
where is_active = true 
group by contract_id) 
, contract_products as (select contract_id,
    LISTAGG(product, ', ') WITHIN GROUP (ORDER BY product) as product
    from {{ref('dt_cs_contract_products')}}
    group by contract_id 
),
-- removeing 38 duplicate records in study planner
study_planner as (
    select 
        user_id,
        last_updated,
        study_plan_type
    from (
    select 
        user_id,
        last_updated,
        study_plan_type,
        ROW_NUMBER() OVER(partition by user_id order by last_updated DESC) as rn  
    from {{ref('dt_ls_study_planner')}} )
    where rn = 1
)

select 
contracts.id as contract_id
,contracts.student_id
,users.user_reference_id as student_reference_id
,users.student_code
,contracts.center_id
,contracts.consultant_id
,contracts.lab_teacher_id
,contracts.contract_reference_id
,contracts.master_contract_course_id
,contracts.group_id
,cs_centers.center_reference_id
,cs_centers.name as center_name
,cs_territory.name as territory_name
,contracts.start_date
,contracts.end_date
,contracts.created_date
,contracts.local_created_date
,contracts.last_updated_date
,contracts.local_last_updated_date
,contracts.sale_date
,contracts.cancel_date
,contracts.code
,contracts.number
,contracts.crm_contract_number
,contracts.location
,CASE 
    WHEN study_planner.last_updated >= TIMESTAMP '2023-05-26 00:00:00.000' AND study_plan_type is null THEN 'eightweeks_default'
    WHEN study_planner.study_plan_type is null THEN 'not_applicable'
    ELSE study_planner.study_plan_type
END AS study_plan_type
,contracts.producttype as product_type
,contracts.service_type
,contracts.contract_type
,contract_acccess_type.class_type as class_access_type
,contracts.source_type
,contract_products.product as contract_product
,contracts.start_level
,contracts.end_level
,contracts.price as price_local_currency
,contracts.state
,contracts.status
,contracts.current_validation_state
,contracts.total_number_of_hours
,contracts.max_no_of_cc_and_sc_classes
,contracts.refunded as refunded_price_local_currency
,contracts.is_renewed
,contracts.is_promotional
,contracts.is_transfer_in
,contracts.is_cross_center_booking
,contracts.is_membership
,contracts.is_teen
from {{ref('dt_cs_contracts')}} as contracts
left join contract_acccess_type 
on contract_acccess_type.contract_id = contracts.id
left join contract_products
on contract_products.contract_id = contracts.id
left join {{ref('dt_cs_centers')}} as cs_centers
on cs_centers.id = contracts.center_id
left join {{ref('dt_cs_territory')}} as cs_territory
on cs_territory.id = cs_centers.territory_id
left join {{ref('dt_cs_users')}} as users
on contracts.student_id = users.id
left join {{ref('dt_ls_user')}} as ls_user
on ls_user.ssds_id = users.user_reference_id
left join study_planner
on ls_user.user_id = study_planner.user_id