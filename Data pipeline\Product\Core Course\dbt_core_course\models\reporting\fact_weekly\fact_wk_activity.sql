{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH cal AS 
(
    SELECT 
        date
        , first_week_date
        , last_week_date
    FROM
        {{ ref('dim_calendar') }}
),

activity_cap AS 
(
    SELECT 
        content_item_id
        , cal.first_week_date AS date_completed
        , minimum_duration_mins AS Minimum 
        , percentile_10_duration_mins AS D1
        , percentile_25_duration_mins AS Q1
        , median_duration_mins AS Median
        , percentile_75_duration_mins AS Q3
        , percentile_90_duration_mins AS D9
        , maximum_duration_mins AS Maximum 
    FROM 
        {{ ref('activity_cap') }} cap
    INNER JOIN cal ON cap.date_completed = cal.date AND cap.date_completed = cal.last_week_date 
    
),

activity as 
(
    SELECT 
        cal.first_week_date as weekcommencing
        , coalesce('L'||cast( act.level as varchar),'') || coalesce('.U'||cast( act.unit as varchar),'') || coalesce('.L'||cast( act.lesson as varchar),'') || coalesce('.MC'||cast( act.mini_cycles as varchar),'') || coalesce('.MCS'||cast( act.mini_cycle_stage as varchar),'') as "path"
        , CONCAT('L', LPAD(CAST(act.level AS VARCHAR), 2, '0')) AS level
        , CONCAT('L', LPAD(CAST(act.unit AS VARCHAR), 2, '0')) AS unit
        , CONCAT('L', LPAD(CAST(act.lesson AS VARCHAR), 2, '0')) AS lesson
        , CONCAT('L', LPAD(CAST(act.mini_cycles AS VARCHAR), 2, '0')) AS mini_cycle
        , CONCAT('L', LPAD(CAST(act.mini_cycle_stage AS VARCHAR), 2, '0')) AS mini_cycle_stage
        , act.description
        , act.content_item_id
        , act.activity_type
        , count(act.activity_id) as count_act
        , avg(act.score) as score
    FROM
        {{ ref('activities') }} as act 
    LEFT JOIN 
        cal ON date(act.completed_date) = cal.date
    GROUP BY
        cal.first_week_date
        , act.level
        , act.unit
        , act.lesson
        , act.mini_cycles
        , act.mini_cycle_stage
        , act.description
        , act.content_item_id
        , act.activity_type
)


SELECT
    act.weekcommencing
    , act.path
    , act.level
    , act.unit
    , act.lesson
    , act.mini_cycle
    , act.mini_cycle_stage
    , act.description
    , act.content_item_id
    , cap.Minimum 
    , cap.D1
    , cap.Q1
    , cap.Median
    , cap.Q3
    , cap.D9
    , cap.Maximum 
    , CASE 
        WHEN act.activity_type = 'multimedia' THEN 'MM'
        WHEN act.activity_type = 'digital_workbook' THEN 'DW'
    END AS activity_type
    , act.count_act
    , act.score
FROM 
    activity as act
LEFT JOIN
    activity_cap as cap
    ON date(act.weekcommencing) = cap.date_completed
    AND act.content_item_id = cap.content_item_id