{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_digital_books_log') }}

{% if is_incremental() %}
where
    date_granted > (
        (
            select
                max(date_granted)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT 
    {{etl_load_date()}},
    student_id,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(category.path, '.', 1), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(category.path, '.', 1), '[^0-9]', '')
    END AS category,
    category_type.name as category_type,
    date_granted,
    {{ convert_to_local_timestamp(
        'date_granted',
        'tz.time_zone_id'
    ) }} as local_date_granted,
    center_id,
    registration_id,
    CASE
        When unlock_type = 0 then 'standard'
        When unlock_type = 1 then 'promo'
        else CAST(
            unlock_type AS varchar
        )
    end as unlock_type,
    digbooklogs.id as id,
    CASE
        When operation_type = 0 then 'bill'
        When operation_type = 1 then 'refund'
        else CAST (
            operation_type AS varchar
        )
    end as operation_type,
    sequence,
    CASE
        When workbook_type = 0 then 'digital'
        When workbook_type = 1 then 'printed'
        When workbook_type = 2 then 'notapplicable'
        Else CAST (
            workbook_type AS varchar
        )
    end as workbook_type,
    is_restart
from
    ods_data as digbooklogs
    Left Join (
        select
            id,
            path,
            category_type_id
        from
            {{ ref('ods_ls_category') }}
    ) as category
    ON digbooklogs.category_id = category.id
    Left Join (
        select
            id,
            name
        from
            {{ ref('ods_ls_category_type') }}
    ) as category_type
    on category.category_type_id = category_type.id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = digbooklogs.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
