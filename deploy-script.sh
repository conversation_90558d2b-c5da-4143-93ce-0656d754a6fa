#!/bin/bash

REPO_DIR="/home/<USER>/repo/wse-dataworks-production"
# Navigate to the repository directory
echo "Navigating to the repository directory: ${REPO_DIR}"
cd ${REPO_DIR}

sudo git reset --hard HEAD
echo "git reset completed"
sudo git fetch
echo "git fetch completed"
sudo git checkout master
echo "git checkout master"
sudo git pull
echo "git pull update completed"

# Define mappings in associative arrays
# Format: SOURCE_FOLDER[<source_path>]=<destination_path>
declare -A SOURCE_FOLDER
SOURCE_FOLDER["/home/<USER>/repo/wse-dataworks-production/DATA PIPELINE/Product/Core Course/dbt_core_course/"]="/home/<USER>/dbt/dbt_core_course"
SOURCE_FOLDER["/home/<USER>/repo/wse-dataworks-production/DATA PIPELINE/Common/Airflow Depedencies/"]="/home/<USER>/airflow/dags/dependencies"
SOURCE_FOLDER["/home/<USER>/repo/wse-dataworks-production/DATA PIPELINE/Product/Core Course/Airflow/"]="/home/<USER>/airflow/dags"

# Loop through the mappings
for SOURCE in "${!SOURCE_FOLDER[@]}"; do
    DESTINATION=${SOURCE_FOLDER[$SOURCE]}
    echo "Syncing changes from $SOURCE to $DESTINATION"

    # Perform rsync operation
    # -a, --archive: archive mode; equals -rlptgoD (no -H,-A,-X)
    # --delete: delete extraneous files from dest dirs
    # -v, --verbose: increase verbosity
    # --dry-run: perform a trial run with no changes made
    # Remove --dry-run after testing to actually perform the copying
    rsync -av --dry-run "$SOURCE" "$DESTINATION"

    if [ $? -eq 0 ]; then
        echo "Sync completed successfully."
    else
        echo "Error during sync."
    fi
done

sudo chmod -R 777 /home/<USER>/dbt
sudo chmod -R 777 /home/<USER>/airflow