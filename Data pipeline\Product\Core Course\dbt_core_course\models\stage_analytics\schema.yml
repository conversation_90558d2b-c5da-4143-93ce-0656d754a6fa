version: 2

models:
  - name: placement_start_level
    description: >
      Transforms prospect placement test entry points into standardized start levels.
      Used for analyzing student placement and progression.
    columns:
      - name: prospect_id
        description: Unique identifier for the prospect
        tests:
          - not_null:
              severity: error
      - name: placement_test_entry_point
        description: Numeric code for the placement test entry point
        tests:
          - not_null:
              severity: error
      - name: start_level
        description: Numeric start level derived from the entry point
        tests:
          - not_null:
              severity: error
      - name: start_level_category
        description: Category name for the start level (auto, beginner, intermediate, advanced)
        tests:
          - not_null:
              severity: error
      - name: center_reference_id
        description: Reference ID for the center where the prospect took the test
        tests:
          - not_null:
              severity: error

  - name: ls_booked_student
    description: >
      Transforms booked student data from the Learning Service.
      Provides a comprehensive view of class bookings with additional context.
    columns:
      - name: id
        description: Unique identifier for the booking record
        tests:
          - not_null:
              severity: error
      - name: class_id
        description: ID of the class that was booked
        tests:
          - not_null:
              severity: error
      - name: ref_class_id
        description: Reference ID for the class
        tests:
          - not_null:
              severity: warn
      - name: student_id
        description: ID of the student who booked the class
        tests:
          - not_null:
              severity: error
      - name: book_mode
        description: Mode of booking (book or standby)
        tests:
          - accepted_values:
              values: ['book', 'standby']
              severity: error
      - name: book_date
        description: Date when the booking was made
      - name: local_book_date
        description: Local timestamp for the booking date
      - name: result
        description: Result of the booking
      - name: attended
        description: Flag indicating if the student attended the class
      - name: created
        description: Timestamp when the booking record was created
        tests:
          - not_null:
              severity: error
      - name: local_created
        description: Local timestamp when the booking record was created
        tests:
          - not_null:
              severity: error
      - name: last_updated
        description: Timestamp when the booking record was last updated
        tests:
          - not_null:
              severity: error
      - name: local_last_updated
        description: Local timestamp when the booking record was last updated
        tests:
          - not_null:
              severity: error
      - name: booked_by
        description: ID of the user who made the booking
      - name: booked_role_title
        description: Role title of the user who made the booking
      - name: booked_person_type
        description: Type of person who made the booking
      - name: cancelled_by
        description: ID of the user who cancelled the booking, if applicable
      - name: cancelled_role_title
        description: Role title of the user who cancelled the booking
      - name: cancelled_person_type
        description: Type of person who cancelled the booking
      - name: is_cancelled
        description: Flag indicating if the booking was cancelled
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: book_mode_modified_date
        description: Date when the booking mode was modified
      - name: local_book_mode_modified_date
        description: Local timestamp when the booking mode was modified
      - name: standby_notification_type
        description: Type of standby notification sent
      - name: is_accessed
        description: Flag indicating if the booking was accessed
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: registration_id
        description: ID of the registration associated with the booking
      - name: booking_order_desc
        description: Order of the booking in descending order
        tests:
          - not_null:
              severity: error
      - name: standby_to_booked_flag
        description: Flag indicating if the booking was changed from standby to booked
        tests:
          - accepted_values:
              values: [0, 1]
              quote: false
              severity: error

  - name: activity_cap
    description: >
      Provides activity duration caps based on statistical analysis.
      Used to identify and handle outliers in activity duration data.
    columns:
      - name: content_item_id
        description: ID of the content item
        tests:
          - not_null:
              severity: error
      - name: date_completed
        description: Date when the activity was completed
        tests:
          - not_null:
              severity: error
      - name: minimum_duration_mins
        description: Minimum duration in minutes
        tests:
          - not_null:
              severity: error
      - name: percentile_10_duration_mins
        description: 10th percentile duration in minutes
        tests:
          - not_null:
              severity: error
      - name: percentile_25_duration_mins
        description: 25th percentile duration in minutes
        tests:
          - not_null:
              severity: error
      - name: median_duration_mins
        description: Median (50th percentile) duration in minutes
        tests:
          - not_null:
              severity: error
      - name: percentile_75_duration_mins
        description: 75th percentile duration in minutes
        tests:
          - not_null:
              severity: error
      - name: percentile_90_duration_mins
        description: 90th percentile duration in minutes
        tests:
          - not_null:
              severity: error
      - name: maximum_duration_mins
        description: Maximum duration in minutes
        tests:
          - not_null:
              severity: error

  - name: activity_cap_incremental
    description: >
      Incrementally updates activity duration caps based on new data.
      Calculates percentiles for content item durations to identify outliers.
    columns:
      - name: unique_id
        description: Unique identifier for the record
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: content_item_id
        description: ID of the content item
        tests:
          - not_null:
              severity: error
      - name: date_completed
        description: Date when the activity was completed
        tests:
          - not_null:
              severity: error
      - name: minimum_duration
        description: Minimum duration in minutes
        tests:
          - not_null:
              severity: error
      - name: percentile_10_duration
        description: 10th percentile duration in minutes
        tests:
          - not_null:
              severity: error
      - name: percentile_25_duration
        description: 25th percentile duration in minutes
        tests:
          - not_null:
              severity: error
      - name: median_duration
        description: Median (50th percentile) duration in minutes
        tests:
          - not_null:
              severity: error
      - name: percentile_75_duration
        description: 75th percentile duration in minutes
        tests:
          - not_null:
              severity: error
      - name: percentile_90_duration
        description: 90th percentile duration in minutes
        tests:
          - not_null:
              severity: error
      - name: maximum_duration
        description: Maximum duration in minutes
        tests:
          - not_null:
              severity: error

  - name: union_classes
    description: >
      Combines class data from multiple sources into a unified view.
      Used for comprehensive class analysis and reporting.
    columns:
      - name: class_id
        description: Unique identifier for the class
        tests:
          - not_null:
              severity: error
      - name: class_start_datetime
        description: Start date and time of the class
        tests:
          - not_null:
              severity: error
      - name: class_local_start_datetime
        description: Local start date and time of the class
        tests:
          - not_null:
              severity: error
      - name: class_number_of_seats
        description: Number of available seats in the class
        tests:
          - not_null:
              severity: error
      - name: class_created_datetime
        description: Date and time when the class was created
        tests:
          - not_null:
              severity: error
      - name: class_local_created_datetime
        description: Local date and time when the class was created
        tests:
          - not_null:
              severity: error
      - name: class_last_updated_datetime
        description: Date and time when the class was last updated
        tests:
          - not_null:
              severity: error
      - name: class_local_last_updated_datetime
        description: Local date and time when the class was last updated
        tests:
          - not_null:
              severity: error

  - name: snb_scheduled_class_property
    description: >
      Provides additional properties for scheduled classes.
      Used for class categorization and filtering.
    columns:
      - name: etl_load_date
        description: Date when the data was loaded
        tests:
          - not_null:
              severity: error
      - name: class_id
        description: Unique identifier for the class
        tests:
          - not_null:
              severity: error
      - name: is_b2_b
        description: Flag indicating if the class is for B2B students
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: visible_in_group
        description: Flag indicating if the class is visible in group view
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: is_teen
        description: Flag indicating if the class is for teen students
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
      - name: service_type
        description: Type of service for the class
        tests:
          - not_null:
              severity: error

sources:
  - name: activity_cap_initial
    description: >
      Initial data for activity duration caps, used as a baseline for incremental updates.
    database: awsdatacatalog
    schema: stage_analytics
    tables:
      - name: activity_cap_mm_initial
        description: Initial activity cap data for multimedia content
      - name: activity_cap_dw_initial
        description: Initial activity cap data for digital workbook content
