version: 2

models:
  - name: placement_start_level
    description: >
      Transforms prospect placement test entry points into standardized start levels.
      Used for analyzing student placement and progression.
    columns:
      - name: prospect_id
        description: Unique identifier for the prospect
      - name: placement_test_entry_point
        description: Numeric code for the placement test entry point
      - name: start_level
        description: Numeric start level derived from the entry point
      - name: start_level_category
        description: Category name for the start level (auto, beginner, intermediate, advanced)
      - name: center_reference_id
        description: Reference ID for the center where the prospect took the test

  - name: ls_booked_student
    description: >
      Transforms booked student data from the Learning Service.
      Provides a comprehensive view of class bookings with additional context.
    columns:
      - name: id
        description: Unique identifier for the booking record
      - name: class_id
        description: ID of the class that was booked
      - name: ref_class_id
        description: Reference ID for the class
      - name: student_id
        description: ID of the student who booked the class
      - name: book_mode
        description: Mode of booking (book or standby)
      - name: book_date
        description: Date when the booking was made
      - name: local_book_date
        description: Local timestamp for the booking date
      - name: result
        description: Result of the booking
      - name: attended
        description: Flag indicating if the student attended the class
      - name: created
        description: Timestamp when the booking record was created
      - name: local_created
        description: Local timestamp when the booking record was created
      - name: last_updated
        description: Timestamp when the booking record was last updated
      - name: local_last_updated
        description: Local timestamp when the booking record was last updated
      - name: booked_by
        description: ID of the user who made the booking
      - name: booked_role_title
        description: Role title of the user who made the booking
      - name: booked_person_type
        description: Type of person who made the booking
      - name: cancelled_by
        description: ID of the user who cancelled the booking, if applicable
      - name: cancelled_role_title
        description: Role title of the user who cancelled the booking
      - name: cancelled_person_type
        description: Type of person who cancelled the booking
      - name: is_cancelled
        description: Flag indicating if the booking was cancelled
      - name: book_mode_modified_date
        description: Date when the booking mode was modified
      - name: local_book_mode_modified_date
        description: Local timestamp when the booking mode was modified
      - name: standby_notification_type
        description: Type of standby notification sent
      - name: is_accessed
        description: Flag indicating if the booking was accessed
      - name: registration_id
        description: ID of the registration associated with the booking
      - name: booking_order_desc
        description: Order of the booking in descending order
      - name: standby_to_booked_flag
        description: Flag indicating if the booking was changed from standby to booked

  - name: activity_cap
    description: >
      Provides activity duration caps based on statistical analysis.
      Used to identify and handle outliers in activity duration data.
    columns:
      - name: content_item_id
        description: ID of the content item
      - name: date_completed
        description: Date when the activity was completed
      - name: minimum_duration
        description: Minimum duration in minutes
      - name: percentile_10_duration
        description: 10th percentile duration in minutes
      - name: percentile_25_duration
        description: 25th percentile duration in minutes
      - name: median_duration
        description: Median (50th percentile) duration in minutes
      - name: percentile_75_duration
        description: 75th percentile duration in minutes
      - name: percentile_90_duration
        description: 90th percentile duration in minutes
      - name: maximum_duration
        description: Maximum duration in minutes

  - name: activity_cap_incremental
    description: >
      Incrementally updates activity duration caps based on new data.
      Calculates percentiles for content item durations to identify outliers.
    columns:
      - name: unique_id
        description: Unique identifier for the record
      - name: content_item_id
        description: ID of the content item
      - name: date_completed
        description: Date when the activity was completed
      - name: minimum_duration
        description: Minimum duration in minutes
      - name: percentile_10_duration
        description: 10th percentile duration in minutes
      - name: percentile_25_duration
        description: 25th percentile duration in minutes
      - name: median_duration
        description: Median (50th percentile) duration in minutes
      - name: percentile_75_duration
        description: 75th percentile duration in minutes
      - name: percentile_90_duration
        description: 90th percentile duration in minutes
      - name: maximum_duration
        description: Maximum duration in minutes

sources:
  - name: activity_cap_initial
    description: >
      Initial data for activity duration caps, used as a baseline for incremental updates.
    database: awsdatacatalog
    schema: stage_analytics
    tables:
      - name: activity_cap_mm_initial
        description: Initial activity cap data for multimedia content
      - name: activity_cap_dw_initial
        description: Initial activity cap data for digital workbook content
