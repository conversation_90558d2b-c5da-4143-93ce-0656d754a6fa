import time
import json
import datetime
import logging
from dependencies.cloud_operations import StepFunction

# Create a Step Functions client
step_function = StepFunction.connect()


def execute_step_function(state_machine_arn, input_data):
    logging.warning(state_machine_arn)
    logging.warning(input_data)
    # Start the Step Function execution
    response = step_function.start_execution(
        stateMachineArn=state_machine_arn,
        name='airflow' + datetime.datetime.now().strftime("%d%m%y%H%M%S%f"),
        # Replace with a unique execution name
        input=json.dumps(input_data)  # Convert the input data to JSON
    )
    execution_arn = response['executionArn']

    # Poll for the execution status
    while True:
        response = step_function.describe_execution(
            executionArn=execution_arn
        )
        execution_status = response['status']
        logging.warning(execution_status)

        if execution_status == 'SUCCEEDED':
            logging.warning("Step Function execution completed successfully!")
            break
        elif execution_status in ['FAILED', 'TIMED_OUT', 'ABORTED']:
            logging.warning(f"step function execution failed or was aborted with status: {execution_status}")
            # failure or error here
            raise Exception
        else:
            logging.warning(f"step function execution is still running. Current status: {execution_status}")
            time.sleep(60)  # Wait for a minute before checking again
