{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}


Select 
    jobs.id as message_id,
    jobs.subscriber_id as student_reference_id,
    jobs.channel,
    jobs.title,
    jobs.template_id as notification_id,
    nt.notification_name as notification_type,
    jobs.message_template_id as variant_id,
    nt.variant_name as variant,
    jobs.created_at,
    jobs.updated_at
FROM
    {{ ref('ods_novu_jobs') }} as jobs
LEFT JOIN
    {{ ref('ods_novu_notification_templates') }} as nt 
    ON jobs.template_id = nt.id and jobs.message_template_id = nt.template_id