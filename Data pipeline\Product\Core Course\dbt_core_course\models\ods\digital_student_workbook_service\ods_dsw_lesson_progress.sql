{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH RankedRecords AS (
    SELECT 
        id
        ,coursecontentid
        ,studentid
        ,vocabularyscore
        ,communicationscore
        ,grammarscore
        ,activitycompletedbitwise
        ,islessoncompleted
        ,isactive
        ,{{cast_to_timestamp('created')}} as created
        ,{{cast_to_timestamp('lastupdated')}} as lastupdated
        ,isendoflessonviewed
        ,ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdated DESC) AS rn
    FROM 
        {{source('stage_digital_student_workbook_service', 'lessonprogress')}}
)

SELECT
    {{etl_load_date()}}
    ,id
    ,coursecontentid as course_content_id
    ,studentid as student_id
    ,vocabularyscore as vocabulary_score
    ,communicationscore as communication_score
    ,grammarscore as grammar_score
    ,activitycompletedbitwise as activity_completed_bitwise
    ,islessoncompleted as  is_lesson_completed
    ,isactive as is_active
    ,created
    ,lastupdated as last_updated
    ,isendoflessonviewed as is_end_of_lesson_viewed
FROM 
    RankedRecords
WHERE 
    rn = 1;