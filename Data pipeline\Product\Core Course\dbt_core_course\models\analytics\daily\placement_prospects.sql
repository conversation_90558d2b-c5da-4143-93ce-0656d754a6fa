{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with center as (
    select 
      id,
      reference_center_id
    from {{ref("dt_ls_center")}}
),
prospect as (
  select 
    id,
    user_reference_id,
    center_id,
    company_id,
    registration_date,
    created,
    last_updated,
    "source",
    show_placement_test_result,
    email,
    user_type
  from (
  select 
    id,
    -- ssds_reference_id, -- data issue in application
    user_reference_id,
    center_id,
    company_id,
    registration_date,
    -- expiration_date, -- removed from application table
    created,
    last_updated,
    -- latest_placement_test_score, -- removed from application table
    "source",
    -- trial_enabled, -- removed from application table
    show_placement_test_result,
    email,
    user_type,
    row_number() over (partition by id order by last_updated asc) as rn
  from (
      select 
        id,
        {# ssds_reference_id, #}
        user_reference_id,
        center_id,
        company_id,
        registration_date,
        -- expiration_date, -- removed from application table
        created,
        last_updated,
        -- latest_placement_test_score, -- removed from application table
        "source",
        -- trial_enabled, -- removed from application table
        show_placement_test_result,
        email,
        user_type,
        row_number() over (partition by id order by created desc) as rn
      from {{ref("dt_ls_prospect")}}
      union
      select 
        prospect.id,
        {# COALESCE(prospect.student_reference_id,user.ssds_id) as student_reference_id, #}
        user.ssds_id as user_reference_id,
        c.id as center_id,
        prospect.company_id,
        prospect.registered_on, --renamed field in application table
        -- prospect.expiration_date, -- removed from application table
        prospect.created,
        prospect.last_updated,
        -- prospect.latest_placement_test_score, -- removed from application table
        CASE 
          WHEN prospect."source" = 'nse' THEN  0
          WHEN prospect."source" = 'crm' THEN  1
          WHEN prospect."source" = 'apim' THEN 2
          ELSE NULL
        END as "source",
        -- trial_enabled, -- removed from application table
        prospect.show_test_result,
        prospect.email,
        user.role as user_type,
        row_number() over (partition by prospect.id order by prospect.created desc) as rn
      from {{ref("dt_ps_prospect")}} as prospect
      left join center as c on prospect.center_reference_id = c.reference_center_id
      left join (
        select
            "role",
            ssds_id,
            email
        from
            {{ref('dt_ls_user')}}
            where "role" ='student'
        ) as user    
        ON prospect.email = user.email

  )
)
  where rn = 1 -- this now to avoid duplication because the join is based on email
),

prospect_gradebook AS (
  select 
    id,
    prospect_id,
    start_date,
    date_completed,
    -- assignment_date, -- removed from application table
    time_remaining,
    status,
    is_time_out,
    settled_level_id
  from (
      SELECT 
          id,
          prospect_id,
          start_date,
          date_completed,
          -- assignment_date, -- removed from application table
          time_remaining,
          status,
          is_time_out,
          settled_level_id,
          ROW_NUMBER() OVER (PARTITION BY prospect_id ORDER BY last_updated DESC) AS rn
      FROM 
          {{ ref("dt_ls_prospect_gradebook") }}
      WHERE 
          status = 'final'
          and date(start_date) < date('2024-10-16') -- there is duplication due to differnce in settled level id in ls and ps so restricting history
      union
      SELECT 
          pg.id,
          pg.prospect_id,
          pg.test_started_on,  --renamed field in application table
          pg.test_completed_on, --renamed field in application table
          -- assignment_date, -- removed from application table
          pg.time_remaining,
          pg.status,
          pg.is_time_out,
          pl.id as settled_level, --renamed field in application table
          ROW_NUMBER() OVER (PARTITION BY pg.prospect_id ORDER BY pg.last_updated DESC) AS rn
      FROM 
          {{ ref('dt_ps_prospect_gradebook') }} as pg
          left join {{ ref('dt_ps_placement_test_levels') }} as pl on pg.settled_level =pl."order"
      WHERE 
          pg.status = 'final'
          and date(pg.test_started_on) > date('2024-10-15') -- there is duplication due to differnce in settled level id in ls and ps so restricting history
  )
  where rn = 1
),

placement_test_levels as (
    select 
      id,
      "order"
    from {{ref("dt_ls_placement_test_levels")}}
    union
    select 
      id,
      "order"
    from {{ref("dt_ps_placement_test_levels")}}
),

pre_placement_test_result as (
   select 
      prospect_id,
      created,
      last_updated
    from (
      select 
        prospect_id,
        created,
        last_updated,
        row_number() over (partition by prospect_id order by last_updated desc) as rn
      from {{ref("dt_ls_pre_placement_test_result")}}
      where date(created) < date('2024-10-16') -- this duplication due to differnce in created and last_updated so restricting history
      union
      select 
        prospect_id,
        created,
        last_updated,
        row_number() over (partition by prospect_id order by last_updated desc) as rn
      from {{ref("dt_ps_pre_placement_test_result")}}
      where date(created) > date('2024-10-15') -- this duplication due to differnce in created and last_updated so restricting history
    )
    where rn = 1
),
placement_start_level as(
    select 
      prospect_id,
      start_level,
      start_level_category 
    from {{ref("placement_start_level")}}
),
offline_test_results as (
    select 
      id,
      prospect_id,
      created,
      last_updated,
      reason,
      "level"
    from {{ref("dt_ps_offline_test_results")}}
),
aggregated_prospect as (
    select 
      p.id as prospect_id,
      pg.id as gradebook_id,
      -- p.ssds_reference_id, -- data issue in application
      p.user_reference_id,
      p.center_id,
      c.reference_center_id as center_reference_id,
      p.company_id,
      otr.id as oral_test_id,
      p.registration_date as prospect_registration_date,
      -- p.expiration_date as propspect_expiration_date, -- removed from application table
      p.created as prospect_created,
      p.last_updated as prospect_last_updated,
      pg.start_date as gradebook_start_date,
      pg.date_completed as gradebook_date_completed,
      -- pg.assignment_date as gradebook_assignment_date, -- removed from application table
      pptr.created as pre_placement_test_created,
      pptr.last_updated as pre_placement_test_last_updated,
      otr.created as oral_test_created,
      otr.last_updated as oral_test_last_updated,
      otr.reason as oral_test_reason,
      otr.level as oral_test_level,
      -- p.latest_placement_test_score, -- removed from application table
      p."source",
      CASE 
          WHEN p."source" = 0 THEN 'nse'
          WHEN p."source" = 1 THEN 'crm'
          WHEN p."source" = 2 THEN 'apim'
          ELSE NULL
        END as source_category,
      -- p.trial_enabled, ---- removed from application table
      p.show_placement_test_result,
      p.email,
      pg.time_remaining,
      CASE
        WHEN pg.status = 'final' THEN true
          ELSE false
        END AS placement_test_completed,
      psl.start_level as start_level_score,
      pl."order" as final_level_score,
      psl.start_level_category,
      CASE 
          WHEN pl."order" BETWEEN 3 AND 9 THEN 'beginner'
          WHEN pl."order" BETWEEN 10 AND 16 THEN 'intermediate'
          WHEN pl."order" BETWEEN 17 AND 20 THEN 'advanced'
          ELSE NULL
        END as final_level_category,
      p.user_type,
      pg.is_time_out as is_timeout,
      count(pg.prospect_id) as "no_of_tests", 
      floor(AVG(
        CASE 
          WHEN pg.is_time_out = true Then
              (ROUND(CAST(date_diff('second', pg.start_date,pg.date_completed)as double)/ 60)   -- due to the date diff conversion problem in athena(minutes) we are doing conversion based on seconds
              + ROUND(CAST(date_diff('second', pptr.created , pptr.last_updated)as double)/ 60))
        END
      )  )as "duration_with_timeout", -- added floor to get exact value from decimal
      floor(AVG(
        CASE 
          WHEN pg.is_time_out = false Then
            (ROUND(CAST(date_diff('second', pg.start_date,pg.date_completed)as double)/ 60)
              + ROUND(CAST(date_diff('second', pptr.created , pptr.last_updated)as double)/ 60))
            
        END
      ) )as "duration_without_timeout"
    from prospect as p
    left join prospect_gradebook as pg on p.id =pg.prospect_id 
    left join center as c on p.center_id = c.id
    left join placement_start_level as psl on p.id =psl.prospect_id
    left join placement_test_levels as pl on pg.settled_level_id =pl.id 
    left join pre_placement_test_result as pptr on pg.prospect_id =pptr.prospect_id 
    left join offline_test_results as otr on p.id = otr.prospect_id
    group by 
    p.id, 
    pg.id, 
    -- p.ssds_reference_id, -- data issue in application
    p.user_reference_id, 
    p.center_id, 
    p.company_id,
    otr.id,
    p.registration_date,
    -- p.expiration_date, -- removed from application table
    p.created,
    p.last_updated,
    pg.start_date,
    pg.date_completed,
    -- pg.assignment_date, -- removed from application table
    pptr.created,
    pptr.last_updated,
    otr.created,
    otr.last_updated,
    otr.reason,
    otr.level,
    -- p.latest_placement_test_score, -- removed from application table
    p."source",
    -- p.trial_enabled, -- removed from application table
    p.show_placement_test_result,
    p.email,
    pg.time_remaining,
    pg.status,
    pl."order",
    p.user_type,
    pg.is_time_out,
    c.reference_center_id,
    psl.start_level,
    psl.start_level_category
)

select
prospect_id,
gradebook_id,
-- ssds_reference_id, -- data issue in application
user_reference_id,
center_id,
center_reference_id,
company_id,
oral_test_id,
prospect_registration_date,
-- propspect_expiration_date, -- removed from application table
prospect_created,
prospect_last_updated,
gradebook_start_date,
gradebook_date_completed,
-- gradebook_assignment_date, -- removed from application table
pre_placement_test_created,
pre_placement_test_last_updated,
oral_test_created,
oral_test_last_updated,
-- latest_placement_test_score, -- removed from application table
"source",
source_category,
-- trial_enabled, -- removed from application table
show_placement_test_result,
email,
time_remaining,
placement_test_completed,
start_level_score,
final_level_score,
oral_test_level,
start_level_category,
final_level_category,
oral_test_reason,
user_type,
is_timeout,
no_of_tests,
duration_with_timeout,
duration_without_timeout
from aggregated_prospect