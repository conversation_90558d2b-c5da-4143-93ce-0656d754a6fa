{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH RankedRecords AS (
    SELECT 
        id
        ,level
        ,unit
        ,lesson
        ,url
        ,version
        ,activitycount
        ,isactive
        ,{{cast_to_timestamp('created')}} as created
        ,{{cast_to_timestamp('lastupdated')}} as lastupdated
        ,referenceid
        ,ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdated DESC) AS rn
    FROM 
        {{source('stage_digital_student_workbook_service', 'coursecontents')}}
)

SELECT
    {{etl_load_date()}}
    ,id
    ,level
    ,unit
    ,lesson
    ,url
    ,version
    ,activitycount as activity_count
    ,isactive as is_active
    ,created
    ,lastupdated as last_updated
    ,referenceid as reference_id
FROM 
    RankedRecords
WHERE 
    rn = 1;