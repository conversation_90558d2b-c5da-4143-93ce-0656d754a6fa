{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ps_pre_placement_test_result') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT 
    {{etl_load_date()}},
    start_level,
    created,
    created_by,
    last_updated,
    last_updated_by,
    id,
    prospect_id,
    result,
    pre_placement_test_activity_id,
    pre_placement_test_interaction_id
from
    ods_data as preplacementtestresult
