{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        servicetype,
        duration,
        durationfixed,
        maxnumberofstudents,
        maxnumberofstudentsfixed,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        classtypeid,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_schedule_and_booking_service',
            'classtypeconfigurationforservicetype'
        ) }}
)
SELECT
    {{etl_load_date()}},
    servicetype as service_type,
    duration as duration,
    durationfixed as duration_fixed,
    maxnumberofstudents as max_number_of_students,
    maxnumberofstudents as max_number_of_studentsfixed,
    {{ cast_to_timestamp('created') }} as created,
    {{ cast_to_timestamp('lastupdated') }} as last_updated,
    id,
    classtypeid as class_type_id
FROM
    rankedrecords
WHERE
    rn = 1;
