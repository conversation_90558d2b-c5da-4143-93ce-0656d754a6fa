{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        id,
        mastercontractid,
        listprice,
        discount,
        netprice,
        vat,
        totalpayable,
        cancelrefundprice,
        cancelvat,
        canceltotalpayable,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
        ROW_NUMBER() OVER (PARTITION BY id ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'mastercontractpriceinformation')}}
)

SELECT
    {{etl_load_date()}},
    id,
    mastercontractid as master_contract_id,
    listprice as list_price,
    discount as discount,
    netprice as net_price,
    vat as vat,
    totalpayable as total_payable,
    cancelrefundprice as cancel_refund_price,
    cancelvat as cancel_vat,
    canceltotalpayable as cancel_total_payable,
    {{cast_to_timestamp('createddate')}} as created_date,
    {{cast_to_timestamp('lastupdateddate')}} as last_updated_date
FROM 
    RankedRecords
WHERE 
    rn = 1;