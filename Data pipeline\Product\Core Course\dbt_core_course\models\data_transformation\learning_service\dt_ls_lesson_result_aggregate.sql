{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_lesson_result_aggregate') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT
    {{etl_load_date()}},
    lessonresagg.id as id,
    category.path as category,
    duration,
    progress,
    contitemtype.name as content_item_type,
    average_score,
    average_count,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(categorylesson.path, '.', 1), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(categorylesson.path, '.', 1), '[^0-9]', '')
    END AS level,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(categorylesson.path, '.', 2), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(categorylesson.path, '.', 2), '[^0-9]', '')
    END AS unit,
    CASE
        WHEN LENGTH(
            REGEXP_REPLACE(SPLIT_PART(categorylesson.path, '.', 3), '[^0-9]', '')
        ) = 0 THEN NULL
        ELSE REGEXP_REPLACE(SPLIT_PART(categorylesson.path, '.', 3), '[^0-9]', '')
    END AS lesson,
    category_type.name as category_type,
    lesson_number,
    student_id,
    created,
    {{ convert_to_local_timestamp(
        'created',
        'tz.time_zone_id'
    ) }} as local_created,
    last_updated,
    {{ convert_to_local_timestamp(
        'last_updated',
        'tz.time_zone_id'
    ) }} as local_last_updated
from
    ods_data as lessonresagg
    Left Join (
        select
            id,
            path
        from
            {{ ref('ods_ls_category') }}
    ) as category
    ON lessonresagg.unit_id = category.id
    Left Join (
        select
            id,
            path,
            category_type_id
        from
            {{ ref('ods_ls_category') }}
    ) as categorylesson
    ON lessonresagg.lesson_id = categorylesson.id
    Left Join (
        select
            id,
            name
        from
            {{ ref('ods_ls_category_type') }}
    ) as category_type
    ON categorylesson.category_type_id = category_type.id
    Left Join (
        select
            id,
            name
        from
            {{ ref('ods_ls_content_item_type') }}
    ) as contitemtype
    ON lessonresagg.content_item_type_id = contitemtype.id
    Left Join (
        select
            user_id,
            center_id
        from
            {{ ref('ods_ls_user') }}
    ) as User
    ON lessonresagg.student_id = User.user_id
    Left Join (
        select
            id,
            reference_center_id
        from
            {{ ref('ods_ls_center') }}
    ) as center
    ON center.id = User.center_id
    Left Join (
        select
            center_reference_id,
            time_zone_id
        from
            {{ ref ('ods_cc_center') }}
    ) as tz
    ON center.reference_center_id = tz.center_reference_id
