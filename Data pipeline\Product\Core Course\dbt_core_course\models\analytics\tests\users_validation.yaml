version: 2

models:
  - name: users
    columns:
      - name: user_id
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: student_reference_id
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: user_name
        tests:
          - not_null:
              severity: warn
      - name: created_at
        tests:
          - not_null:
              severity: warn
      - name: role
        tests:
          - accepted_values:
              values: ['student', 'teacher', 'other', 'prospect', 'service manager', 'consultant', 'director', 'limited consultant', 'lab teacher', 'regional administrator', 'contract consultant', 'receptionist', 'national service manager', 'limited service manager', 'limited center director', 'regional service manager', 'national operations manager', 'general manager', 'territory admin', 'global administrator', 'franchisee', 'system administrator', 'finance manager']
              severity: warn
      - name: role_type
        tests:
          - accepted_values:
              values: ['student', 'staff']
              severity: warn
