{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with union_tables as (
    SELECT 
        lower(REPLACE(REPLACE(dw_cap.id, '{', ''), '}', ''))as content_item_id,
        cal.date                                            as date_completed,
        dw_cap.minimum                                      as minimum_duration,
        dw_cap.d1                                           as percentile_10_duration,
        dw_cap.q1                                           as percentile_25_duration,
        dw_cap.median                                       as median_duration,
        dw_cap.q3                                           as percentile_75_duration,
        dw_cap.d9                                           as percentile_90_duration,
        dw_cap.maximum                                      as maximum_duration
    FROM 
        {{source('activity_cap_initial','activity_cap_dw_initial')}} as dw_cap --change table name for prod
    LEFT JOIN 
        (select date,first_week_date from reporting.dim_calendar where year <= 2024) as cal
    ON
        cal.first_week_date = dw_cap.weekcommencing
    WHERE 
        cal.date <= date('2024-06-06') -- Based on Initial Load
    UNION
    SELECT 
        lower(REPLACE(REPLACE(mm_cap.id, '{', ''), '}', ''))as content_item_id,
        cal.date                                            as date_completed,
        mm_cap.minimum                                      as minimum_duration,
        mm_cap.d1                                           as percentile_10_duration,
        mm_cap.q1                                           as percentile_25_duration,
        mm_cap.median                                       as median_duration,
        mm_cap.q3                                           as percentile_75_duration,
        mm_cap.d9                                           as percentile_90_duration,
        mm_cap.maximum                                      as maximum_duration
    FROM 
        {{source('activity_cap_initial','activity_cap_mm_initial')}} as mm_cap
    LEFT JOIN 
        (select date,first_week_date from reporting.dim_calendar where year <= 2024) as cal
    ON
        cal.first_week_date = mm_cap.weekcommencing
    WHERE 
        cal.date <= date('2024-06-06') -- Based on Initial Load
    UNION
    SELECT
        content_item_id,
        date_completed,
        minimum_duration,
        percentile_10_duration,
        percentile_25_duration,
        median_duration,
        percentile_75_duration,
        percentile_90_duration,
        maximum_duration
    FROM
        {{ref('activity_cap_incremental')}}
)

select
    content_item_id,
    date_completed,
    minimum_duration as minimum_duration_mins,
    percentile_10_duration as percentile_10_duration_mins,
    percentile_25_duration as percentile_25_duration_mins,
    median_duration as median_duration_mins,
    percentile_75_duration as percentile_75_duration_mins,
    percentile_90_duration as percentile_90_duration_mins,
    maximum_duration as maximum_duration_mins
FROM
    union_tables