import os
import json
import sys
import time
import boto3
from awsglue.transforms import *
from awsglue import DynamicFrame
from google.cloud import bigquery
from pyspark.sql import SparkSession
from pyspark.sql.functions import col
from awsglue.utils import getResolvedOptions
from pyspark.context import SparkContext
from awsglue.context import GlueContext
from awsglue.job import Job
from datetime import datetime, timedelta, timezone

# Step 1: Parse job parameters
args = getResolvedOptions(sys.argv, ['JOB_NAME'])

# Step 2: Initialize Spark and Glue contexts
sc = SparkContext()
glueContext = GlueContext(sc)
spark = glueContext.spark_session

# Step 3: Initialize Glue job
job = Job(glueContext)
job.init(args['JOB_NAME'], args)

# Step 4: Define date range
# Get current UTC date
end_date = datetime.now(timezone.utc).date()

# Get start date (30 days before the current UTC date)
start_date = end_date - timedelta(days=30)

print("Start Date:", start_date)
print("End Date:", end_date)


# Step 5: Fetch BigQuery credentials from AWS Secrets Manager
secret_name = "core_course_big_query_service_account"
region_name = "eu-west-1"
session = boto3.session.Session()
client = session.client(service_name="secretsmanager", region_name=region_name)
response = client.get_secret_value(SecretId=secret_name)
credentials_dict = json.loads(response["SecretString"])
key_file_path = "/tmp/bigquery_key.json"

with open(key_file_path, "w") as key_file:
    key_file.write(json.dumps(credentials_dict))

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = key_file_path

# Step 6: Configure BigQuery
project_id = "core-course-bigquery"
dataset_id = "dbt_prod"
table_id = "fact_study_events"
bq_client = bigquery.Client()
table_ref = f"{project_id}.{dataset_id}.{table_id}"

# Step 7: Check if the table exists in BigQuery
table_exists = True
try:
    bq_client.get_table(table_ref)
except Exception:
    table_exists = False

# Step 8: Delete data from BigQuery if table exists
if table_exists:
    delete_query = f"""
    DELETE FROM `{project_id}.{dataset_id}.{table_id}`
    WHERE DATE(event_date) BETWEEN DATE('{start_date}') AND DATE('{end_date}')
    """
    query_job = bq_client.query(delete_query)
    query_job.result()

# Step 9: Query Athena (CSV Output)
database = "reporting"
OUTPUT_LOCATION = "s3://prod-corecourse/growthbook/athena-results/"
athena_client = boto3.client('athena', region_name='eu-west-1')

QUERY = f"""
SELECT 
    user_id,
    event_date,
    event_type,
    event_description,
    event_detail,
    event_user,
    event_count,
    event_duration,
    event_detail_sum
FROM reporting.fact_study_events 
WHERE event_date BETWEEN DATE('{start_date}') AND DATE('{end_date}')
"""

response = athena_client.start_query_execution(
    QueryString=QUERY,
    QueryExecutionContext={'Database': database},
    ResultConfiguration={'OutputLocation': OUTPUT_LOCATION}
)

query_execution_id = response['QueryExecutionId']

# Step 10: Wait for Athena query to complete
while True:
    status = athena_client.get_query_execution(QueryExecutionId=query_execution_id)
    state = status['QueryExecution']['Status']['State']
    if state in ['SUCCEEDED', 'FAILED', 'CANCELLED']:
        break
    time.sleep(2)

if state != 'SUCCEEDED':
    raise Exception(f"Athena query failed: {state}")

print(f"Athena query {query_execution_id} succeeded.")

# Step 11: Read Athena results directly from S3 as CSV
athena_s3_output_path = f"{OUTPUT_LOCATION}{query_execution_id}.csv"
print(f"Reading Athena output from: {athena_s3_output_path}")

spark_df = spark.read.option("header", "true").csv(athena_s3_output_path)
# Cast only non-string columns
spark_df = spark_df.withColumn("event_date", col("event_date").cast("date")) \
                   .withColumn("event_count", col("event_count").cast("int")) \
                   .withColumn("event_duration", col("event_duration").cast("double")) \
                   .withColumn("event_detail_sum", col("event_detail_sum").cast("int"))
spark_df.show(5)

# Step 12: Write data to BigQuery
GoogleBigQuery_node = glueContext.write_dynamic_frame.from_options(
    frame=DynamicFrame.fromDF(spark_df, glueContext, "GoogleBigQuery_node"),
    connection_type="bigquery",
    connection_options={
        "connectionName": "Core Course BigQuery",
        "parentProject": project_id,
        "writeMethod": "direct",
        "writeDisposition": "WRITE_APPEND",
        "table": f"{dataset_id}.{table_id}"
    },
    transformation_ctx="GoogleBigQuery_node"
)

print("Data written to BigQuery successfully.")

# Step 13: Delete Athena output from S3
s3 = boto3.resource('s3')
bucket_name = "prod-corecourse"
prefix = f"growthbook/athena-results/{query_execution_id}.csv"
bucket = s3.Bucket(bucket_name)

print(f"Deleting Athena results: s3://{bucket_name}/{prefix}")
bucket.objects.filter(Prefix=prefix).delete()
print("Deleted Athena temporary results.")

# Step 14: Clean up temporary credentials
os.remove(key_file_path)

# Step 15: Commit Glue job
job.commit()