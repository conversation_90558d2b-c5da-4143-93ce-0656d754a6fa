{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        groupingid,
        iscurrent,
        {{ cast_to_timestamp('startdate') }} as startdate,
        {{ cast_to_timestamp('enddate') }} as enddate,
        {{ cast_to_timestamp('created') }} as created,
        registrationtypeid,
        {{ cast_to_timestamp('schedulepreferencesubmitted') }} as schedulepreferencesubmitted,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        contractstatus,
        workbooktype,
        englishanytime,
        offertype,
        isb2b,
        registrationstatus,
        coursestatus,
        allowselfbooking,
        iscrosscenterbooking,
        servicetypeid,
        isonlineclassaccess,
        isincenterclassaccess,
        maxnoofccandscclasses,
        ismembership,
        onboardingaccess,
        isteen,
        id,
        userid,
        contractid,
        centerid,
        currentcontentitemid,
        labteacherid,
        consultantid,
        b2bcoursetype,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'registration'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    groupingid as grouping_id,
    iscurrent as is_current,
    startdate as start_date,
    enddate as end_date,
    created as created,
    registrationtypeid as registration_type_id,
    schedulepreferencesubmitted as schedule_preference_submitted,
    lastupdated as last_updated,
    contractstatus as contract_status,
    workbooktype as workbook_type,
    englishanytime as english_anytime,
    offertype as offer_type,
    isb2b as is_b2_b,
    registrationstatus as registration_status,
    coursestatus as course_status,
    allowselfbooking as allow_self_booking,
    iscrosscenterbooking as is_cross_center_booking,
    servicetypeid as service_type_id,
    isonlineclassaccess as is_online_class_access,
    isincenterclassaccess as is_in_center_class_access,
    maxnoofccandscclasses as max_no_of_cc_and_sc_classes,
    ismembership as is_membership,
    onboardingaccess as onboarding_access,
    isteen as is_teen,
    id,
    userid as user_id,
    contractid as contract_id,
    centerid as center_id,
    currentcontentitemid as current_content_item_id,
    labteacherid as lab_teacher_id,
    consultantid as consultant_id,
    b2bcoursetype as b2_b_course_type
FROM
    rankedrecords
WHERE
    rn = 1;
