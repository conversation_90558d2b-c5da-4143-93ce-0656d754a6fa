version: 2

models:
  - name: classes
    columns:
      - name: class_id
        tests:
          - not_null:
              severity: error
          - unique:
              severity: error
      - name: class_center_reference_id
        tests:
          - not_null:
              severity: error
      - name: class_start_datetime
        tests:
          - not_null:
              severity: error
      - name: class_local_start_datetime
        tests:
          - not_null:
              severity: error
      - name: class_date_student
        tests:
          - not_null:
              severity: error
      - name: class_number_of_seats
        tests:
          - not_null:
              severity: error
      - name: class_created_datetime
        tests:
          - not_null:
              severity: error
      - name: class_local_created_datetime
        tests:
          - not_null:
              severity: error
      - name: class_last_updated_datetime
        tests:
          - not_null:
              severity: error
      - name: class_local_last_updated_datetime
        tests:
          - not_null:
              severity: error
      - name: class_service_type
        tests:
          - accepted_values:
              values: ['standard', 'combined', 'vip']
              severity: error
      - name: class_type_billable
        tests:
          - accepted_values:
              values: [false, true]
              quote: false
              severity: error
