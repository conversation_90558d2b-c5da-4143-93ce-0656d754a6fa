
pipeline_list = []
class PipelineFormatting:
    @staticmethod
    def pipeline_flatten(OwnersDataExtract, DataProperties, AdditionalProperty):
        for label_dict in OwnersDataExtract:
            label_data = {key: value for key, value in label_dict.items() if key in DataProperties and key != 'stages'}
            for deal_stages in label_dict['stages']:
                dealstage_data = {key: value for key, value in deal_stages.items() if key in DataProperties and key != 'metadata'}
                dealstage_dict = {
                                    "dealstage_label" : dealstage_data['label'],
                                    "dealstage_id": dealstage_data['id'],
                                    "dealstage_createdat": dealstage_data['createdAt'],
                                    "dealstage_updatedat": dealstage_data['updatedAt'],
                                    "dealstage_archived": dealstage_data['archived']
                }
                deal_metadata = {key: value for key, value in deal_stages['metadata'].items() if key in DataProperties and key == 'probability'}
                deal_prob_dict = {"dealstage_probabilty": deal_metadata['probability']}
                deal_labels_merge = {** dealstage_dict, ** deal_prob_dict}
                pipeline_dict = {**label_data, ** deal_labels_merge, **AdditionalProperty}

                pipeline_list.append(pipeline_dict)
        return pipeline_list

