{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_master_contract_products'
        ) }}

    {% if is_incremental() %}
        where last_updated_date > ((select max(last_updated_date) from {{ this }}))
    {% endif %}
)

SELECT {{etl_load_date()}},
    mcproducts.id as id,
    master_contract_course_id,
    products.name as product,
    is_active,
    created_date,
    {{convert_to_local_timestamp('created_date','time_zone_id')}} as local_created_date,
    last_updated_date,
    {{convert_to_local_timestamp('last_updated_date','time_zone_id')}} as local_last_updated_date
from ods_data as mcproducts
    left join (
        select id,
            name
        from {{ ref( 'ods_cs_products' ) }}
    ) as products on mcproducts.product_id = products.id
    left join (
        select id,
            master_contract_id
        from {{ ref( 'ods_cs_master_contract_courses' ) }}
    ) as mccourses on mcproducts.master_contract_course_id = mccourses.id
    left join (
        select id,
            center_id
        from {{ ref( 'ods_cs_master_contracts' ) }}
    ) as mcontracts on mccourses.master_contract_id = mcontracts.id
    left join (
        select id,
            center_reference_id 
        from {{ ref( 'ods_cs_centers' ) }}
    ) as center on center.id = mcontracts.center_id
    left join (
        select center_reference_id,
            time_zone_id
        from {{ ref ('ods_cc_center') }}
    ) as tz on center.center_reference_id = tz.center_reference_id