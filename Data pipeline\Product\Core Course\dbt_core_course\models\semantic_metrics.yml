version: 2

metrics:
  - name: student_retention_rate
    description: "Percentage of students who remain active from one week to the next"
    calculation_method: derived
    expression: "{{ metric('valid_current_students', {'where': 'first_contract = false'}) }} / {{ metric('valid_current_students', {'offset': '-1 week'}) }}"
    label: "Student Retention Rate"
    timestamp: first_week_date
    time_grains: [week, month, quarter, year]
    meta:
      owner: "Data Team"
      format: "percentage"

  - name: new_student_activation_rate
    description: "Percentage of new students who become active in their first week"
    calculation_method: derived
    expression: "{{ metric('active_students_weekly', {'where': 'first_contract = true'}) }} / {{ metric('new_students') }}"
    label: "New Student Activation Rate"
    timestamp: first_week_date
    time_grains: [week, month, quarter, year]
    meta:
      owner: "Data Team"
      format: "percentage"

  - name: new_students
    description: "Count of students with first contracts starting in this period"
    calculation_method: count_distinct
    expression: student_reference_id
    label: "New Students"
    timestamp: first_week_date
    time_grains: [day, week, month, quarter, year]
    where: first_contract = true
    model: ref('weekly_student_details')
    meta:
      owner: "Data Team"

  - name: weekly_class_attendance_rate
    description: "Percentage of booked classes that were attended"
    calculation_method: derived
    expression: "{{ metric('cc_attended_1wk') }} / {{ metric('cc_booked_1wk') }}"
    label: "Weekly Class Attendance Rate"
    timestamp: first_week_date
    time_grains: [week, month, quarter, year]
    meta:
      owner: "Data Team"
      format: "percentage"

  - name: cc_attended_1wk
    description: "Count of conversation classes attended in the last week"
    calculation_method: sum
    expression: cc_attended_1wk
    label: "CC Attended (1wk)"
    timestamp: first_week_date
    time_grains: [day, week, month, quarter, year]
    model: ref('weekly_student_progress')
    meta:
      owner: "Data Team"

  - name: cc_booked_1wk
    description: "Count of conversation classes booked in the last week"
    calculation_method: sum
    expression: cc_booked_1wk
    label: "CC Booked (1wk)"
    timestamp: first_week_date
    time_grains: [day, week, month, quarter, year]
    model: ref('weekly_student_progress')
    meta:
      owner: "Data Team"

  - name: avg_weekly_learning_time
    description: "Average total learning time per student per week (minutes)"
    calculation_method: derived
    expression: "{{ metric('avg_weekly_duration') }} + ({{ metric('weekly_class_attendance_rate') }} * 60)"
    label: "Avg Weekly Learning Time (mins)"
    timestamp: first_week_date
    time_grains: [week, month, quarter, year]
    meta:
      owner: "Data Team"
      format: "decimal"

  - name: student_progression_rate
    description: "Average number of levels started per student per month"
    calculation_method: derived
    expression: "{{ metric('levels_started_mtd') }} / {{ metric('total_students_mtd') }}"
    label: "Student Progression Rate"
    timestamp: date
    time_grains: [month, quarter, year]
    meta:
      owner: "Data Team"
      format: "decimal"

  - name: levels_started_mtd
    description: "Count of levels started month to date"
    calculation_method: sum
    expression: levels_started_mtd
    label: "Levels Started (MTD)"
    timestamp: date
    time_grains: [day, week, month, quarter, year]
    model: ref('daily_student_progress')
    meta:
      owner: "Data Team"

  - name: total_students_mtd
    description: "Count of unique students month to date"
    calculation_method: count_distinct
    expression: student_id
    label: "Total Students (MTD)"
    timestamp: date
    time_grains: [day, week, month, quarter, year]
    model: ref('daily_student_progress')
    meta:
      owner: "Data Team"
