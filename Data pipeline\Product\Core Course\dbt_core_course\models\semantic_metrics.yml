version: 2

metrics:
  - name: student_retention_rate
    description: "Percentage of students who remain active from one week to the next"
    type: ratio
    label: "Student Retention Rate"
    type_params:
      numerator: 
        model_name: weekly_student_details
        measure_name: valid_current_students
        filter: "first_contract = false"
      denominator: 
        model_name: weekly_student_details
        measure_name: valid_current_students
        time_grain: week
        offset: -1
    meta:
      owner: "Data Team"
      format: "percentage"

  - name: new_student_activation_rate
    description: "Percentage of new students who become active in their first week"
    type: ratio
    label: "New Student Activation Rate"
    type_params:
      numerator: 
        model_name: weekly_student_progress
        measure_name: total_students
        filter: "multimedia_activities_1wk > 0 OR workbook_activities_1wk > 0 OR cc_attended_1wk > 0"
      denominator: 
        model_name: weekly_student_details
        measure_name: new_students
    meta:
      owner: "Data Team"
      format: "percentage"

  - name: weekly_class_attendance_rate
    description: "Percentage of booked classes that were attended"
    type: ratio
    label: "Weekly Class Attendance Rate"
    type_params:
      numerator: 
        model_name: weekly_student_progress
        measure_name: cc_attended_1wk
      denominator: 
        model_name: weekly_student_progress
        measure_name: cc_booked_1wk
    meta:
      owner: "Data Team"
      format: "percentage"

  - name: avg_weekly_learning_time
    description: "Average total learning time per student per week (minutes)"
    type: expression
    label: "Avg Weekly Learning Time (mins)"
    type_params:
      expr: "avg_weekly_duration + (weekly_class_attendance_rate * 60)"
    meta:
      owner: "Data Team"
      format: "decimal"

  - name: student_progression_rate
    description: "Average number of levels started per student per month"
    type: ratio
    label: "Student Progression Rate"
    type_params:
      numerator: 
        model_name: daily_student_progress
        measure_name: levels_started_mtd
      denominator: 
        model_name: daily_student_progress
        measure_name: total_students
    meta:
      owner: "Data Team"
      format: "decimal"
