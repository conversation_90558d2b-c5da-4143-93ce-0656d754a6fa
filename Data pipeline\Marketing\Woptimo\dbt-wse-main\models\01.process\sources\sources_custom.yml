version: 2

sources:
  #CRAWL

  - name: crawl
    description: >
      Crawl made with Scream<PERSON> Frog in the cloud
      Update frequence: monthly but currently still needs a manual work.
      Note: Other ouptputs are possible and exported in wse-marketing's Cloud Storage.
      Input data: Config in a Cloud Storage Bucket (woptimo-dev), list of domains is manual. 
    database: wse-marketing
    schema: crawler
    tables:
      - name: wse
        identifier: internal_all_*
      - name: 404_inlinks
        identifier: client_error_4xx_inlinks_*


# DATAFORSEO

  - name: rank
    description: >
      Ranking information from dataforseo. 
      Update frequence: Crawled weekly on Monday. 
      Source of keywords is [COMPLETE] 
    database: wse-marketing  
    schema: dataforseo_via_n8n
    tables:
      - name: rank
        identifier: rank_mobile_* 

  - name: ideas
    description: >
      keyword ideas provided by dataforseo based on Google Ads keyword generator. 
      Update frequence: [COMPLETE]. 
      Source of keyword input is [COMPLETE] 
    database: clients-woptimo
    schema: dataforseo
    tables:
      - name: ideas
        identifier: ideas_wse_*            


  #WEBPAGETEST

  - name: webpagetest
    description: >
      Via CloudFunction hosted by <PERSON><PERSON><PERSON><PERSON> (clients-woptimo)
      Update frequency: monthly
      Input data: sheets but should be a workable
    database: clients-woptimo
    schema: webpagetest
    tables:
      - name: it
        identifier: wpt_wse_it_20230112

  #SEARCH VOLUME

  - name: search_volume
    description: >
      Input data: via sheets (to be changed)
      Update frequency: manual (to be changed eventually for monthly)
      Evolution of the model to get search volume history
    database: clients-woptimo
    schema: dataforseo
    tables:
      - name: search_volume
        identifier: search_vol_manuel_wse        


  #INDEXING

  - name: indexing
    description: >
      Indexing data via CloudFunction that calls DataForSEO (site: url)
      Input data: sheets, but should be a workable table.
    database: clients-woptimo
    schema: dataforseo_indexation
    tables:
      - name: wse
        identifier: indexation_dfs_wse        

  #SEMRUSH: waiting for WSE's API token 
  #SEMRUSH: list of competitor's keyword
  #SEMRUSH: backlinks

  #GTM: to be done

  #LONG TERM: GSC Coverage, server logs, Bing Webmaster Tools, 
  #LONG TERM: Lexical fields, Google My Business