{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'dbt_unique_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_cs_product_type_products'
        ) }}
)

SELECT {{etl_load_date()}},
    dbt_unique_id,
    Product_Type_Id,
    Product_Id
from ods_data