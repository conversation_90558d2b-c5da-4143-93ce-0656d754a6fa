{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

with ods_data as (

    select
        *
    from
        {{ ref('ods_ls_class_type') }}

{% if is_incremental() %}
where
    last_updated > (
        (
            select
                max(last_updated)
            from
                {{ this }}
        )
    )
{% endif %}
)
SELECT
    {{etl_load_date()}},
    class_type.id as id,
    is_deleted,
    color,
    title,
    code,
    is_online,
    has_description,
    accepts_standby,
    has_to_be_pre_booked,
    category_type,
    deluxe_duration,
    deluxe_durationfixed,
    deluxe_max_number_of_students,
    deluxe_max_number_of_studentsfixed,
    vip_configuration,
    vip_duration,
    vip_durationfixed,
    vip_max_number_of_students,
    vip_max_number_of_studentsfixed,
    created,
    last_updated
from
    ods_data as class_type
