import json

import requests
from functools import wraps
import time
import logging

WrapperCount = []


def countcall(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        wrapper.count += 1
        WrapperCount.append(wrapper.count)
        result = func(*args, **kwargs)
        logging.warning(f'{func.__name__} has been called {wrapper.count} times')
        return result

    wrapper.count = 0
    return wrapper


def retry(num_retries, exception_to_check, sleep_time):
    """
    Decorator that retries the execution of a function if it raises a specific exception.
    """

    def decorate(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for i in range(1, num_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exception_to_check as e:
                    logging.warning(f"{func.__name__} raised {e.__class__.__name__}. Retrying...")
                    if i < num_retries:
                        time.sleep(sleep_time)
                        logging.warning("retrying the code for  ", i)

        return wrapper

    return decorate


@retry(num_retries=2, exception_to_check=Exception, sleep_time=160)
def ApiDataExtract(DataRequestUrl, DefaultInputJson, Headers, Object, QueryString, Operation):
    try:
        if Object in ['contacts', 'deals', 'companies'] and Operation == 'data_extract':
            logging.warning("Entering this if")
            DataResponse = requests.request("POST", DataRequestUrl,
                                            json=DefaultInputJson, headers=Headers)
            if DataResponse.status_code == 200:
                JsonDataResponse = DataResponse.json()
                if 'results' in JsonDataResponse:
                    TotalRecord = len(JsonDataResponse['results'])
                    logging.warning("The length of record is ", TotalRecord)
                    return JsonDataResponse

                if 'status' in JsonDataResponse:
                    logging.warning("It came inside status")
                    logging.warning("The status is ", JsonDataResponse['status'])

                    if JsonDataResponse['status'] == 'error' and JsonDataResponse['category'] == 'EXPIRED_AUTHENTICATION':
                        logging.warning("Entering Acess Token loop")
                        return JsonDataResponse
                    else:
                        raise Exception
            else:
                logging.warning(DataResponse.status_code)
                JsonDataResponse = DataResponse.json()
                return JsonDataResponse
        if (Object in ['contacts', 'deals', 'owners', 'companies'] and Operation == 'data_extract_archive') or (
                Object in ['owners', 'associationdeals', 'associationcontacts', 'associationcompanies', 'customobject', 'salespipeline'] and Operation == 'data_extract'):
            logging.warning("Entered get Call Operation")
            OwnersDataResponse = requests.request("GET", DataRequestUrl, headers=Headers,
                                                  params=QueryString)
            if OwnersDataResponse.status_code == 200:
                JsonDataResponse = OwnersDataResponse.json()
                return JsonDataResponse
            else:
                logging.warning("The Status code of error is '%s'", format(OwnersDataResponse.status_code))
                raise Exception

    except Exception as error:
        logging.warning(error)
        raise Exception


@countcall
def ApiCall(DataRequestUrl, DefaultInputJson, Headers, Object, QueryString, Operation):
    Data = ApiDataExtract(DataRequestUrl, DefaultInputJson, Headers, Object, QueryString, Operation)
    return Data


def ApiCount():
    return WrapperCount[-1]
