{{
    config(
        tags=["incremental","test"],
        materialized='incremental',
        incremental_strategy = 'insert_overwrite',        
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}

-- Get only conversion events from init GA

with a as (
select 
  event_date,  {{ga_session_id()}} session_id,
  event_name,
  event_timestamp,


 FROM {{ ref('ga_init_all_test') }}

where user_pseudo_id is not null
and event_name in 
(
{% for conv in var('conversion_events') %}
"{{conv}}"{% if not loop.last %},{% endif %}
{% endfor %}
)
and {{increment()}}
), b as (
select
session_id,event_date,
{% for conv in var('conversion_events') %}
MIN(CASE WHEN event_name="{{conv}}" THEN event_timestamp ELSE NULL END) {{conv}}_ts,
{% endfor %}

from a
group by session_id,event_date

)

-- When the event is before the second page then consider it as a conversion on lp. Because of collection hazard, we round to the same minute

select *,
{% for conv in var('conversion_events') %}
case when DATETIME_TRUNC(EXTRACT(DATETIME FROM TIMESTAMP_MICROS({{conv}}_ts)),  MINUTE) <= DATETIME_TRUNC(EXTRACT(DATETIME FROM TIMESTAMP_MICROS(second_page_ts)),  MINUTE) 
or (second_page_ts IS NULL and {{conv}}_ts is NOT NULL)
then 1 else 0 end as {{conv}}_on_lp,

case when session_channel="google_organic" and DATETIME_TRUNC(EXTRACT(DATETIME FROM TIMESTAMP_MICROS({{conv}}_ts)),  MINUTE) <= DATETIME_TRUNC(EXTRACT(DATETIME FROM TIMESTAMP_MICROS(second_page_ts)),  MINUTE)
or (second_page_ts IS NULL and {{conv}}_ts is NOT NULL) then 1 else 0 end as google_organic_{{conv}}_on_lp,

case when session_channel like "%organic" then session_{{conv}} else 0 end as organic_session_{{conv}},
{% endfor %}

 from b  
 full join {{ ref('ga_sessions_test') }} g using (session_id,event_date)

{% if is_incremental() %}
 where   g.event_date  {{daily_run()}}
{% endif %}  