{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        {{ cast_to_timestamp('createddate') }} as createddate,
        firstdayoftheweek,
        isactive,
        isdaylightsaving,
        istwentyfourhourformat,
        {{ cast_to_timestamp('lastmodifieddate') }} as lastmodifieddate,
        isdualclassaccess,
        dctechnologytype,
        id,
        code,
        isocode,
        name,
        territoryreferenceid,
        timezoneid,
        navisionclient,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastmodifieddate DESC
        ) AS rn
    FROM
        {{ source(
            'stage_center_configuration_service',
            'territory'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    createddate as created_date,
    firstdayoftheweek as first_day_of_the_week,
    isactive as is_active,
    isdaylightsaving as is_day_light_saving,
    istwentyfourhourformat as is_twenty_four_hour_format,
    lastmodifieddate as last_modified_date,
    isdualclassaccess as is_dual_class_access,
    dctechnologytype as dc_technology_type,
    id,
    code,
    isocode as iso_code,
    name,
    territoryreferenceid as territory_reference_id,
    timezoneid as time_zone_id,
    navisionclient as navision_client
FROM
    rankedrecords
WHERE
    rn = 1;