{{ config(
    MATERIALIZED = 'table',
    table_type = 'iceberg',
    FORMAT = 'parquet'
) }}


WITH CTE_1 AS
		(
        SELECT			
            C.center_reference_id AS center_reference_id
            ,C."name" AS center_name
            ,CT.id AS contract_id
            ,CT.is_teen
            ,CC.change_type
            ,CT.start_date
            ,CT.end_date
            ,CT.start_level_id AS contract_start_level_id
            ,CT.end_level_id AS contract_end_level_id
            ,CC.previous_value
            ,CC.present_value
            ,CC.reason
            ,CC.created_date
            ,CC.modified_by_id
            ,CC.modified_field_id 
            ,CT.price
            ,CT.contract_type
            ,PT.id course_type	     
            ,ROW_NUMBER() OVER(PARTITION BY CT.id ORDER BY CC.created_date) Rno
            ,CT.service_type AS service_type
            ,tz.time_zone_id AS time_zone_id
        FROM				{{ref('ods_cs_centers')}} C
        INNER JOIN	{{ref('ods_cs_contracts')}} CT
        ON					C.id = CT.center_id
        INNER JOIN	{{ref('ods_ls_center')}} LS_CNTR
        ON					LS_CNTR.reference_center_id = C.center_reference_id
        INNER JOIN  {{ref('ods_cc_center')}} tz 
        ON  tz.center_reference_id = C.center_reference_id
        INNER JOIN	{{ref('ods_cs_contracts_audit_info')}} CC 
        ON					CT.id = CC.contract_id		
        AND					change_type IN (8,13) 
        LEFT JOIN		{{ref('ods_cs_product_types')}} PT
        ON					PT.ID = CT.product_type_id
        WHERE				CT.is_promotional = false
        --AND					CT.contract_type = 1
        AND					CC.modified_field_id IN
        ( 
                    'f70d9ba0-290b-47a7-8e11-20f73a2c8824'	--start_level_id
            ,		'fa063854-be56-4e10-9444-abe4999cbfd2'	--end_level_id
            ,		'df7eb5dc-84db-4250-9157-c4843ec3df82'	--start_date
            ,		'a3efcb4b-e149-42c8-938f-e27935028669'	--end_date
            ,		'4c38bdb1-e878-4661-86ae-53d9b347b565'	--Price
            ,		'205bf03b-a0bf-406f-8cce-aa270f09f0b4'  --service_type
            )
		),

		--SELECT * FROM CTE_1 WHERE center_name Like 'Santiago: Costanera' AND contract_id = 'a8ff74e4-98a3-43cb-8db8-32a106589521';
	
		CTE_2 AS

		(
				SELECT 
                    T.center_reference_id 
                    ,T.contract_id
                    ,T.is_teen
                    ,T.center_name
                    ,U.last_name
                    ,U.first_name
                    ,U.student_code
                    ,U1.last_name || ' ' || U1.first_name AS consultant
                    ,U2.last_name || ' ' || U2.first_name AS editor
                    ,T.course_type
                    ,contract_start_level_id
                    ,contract_end_level_id
                    ,CASE WHEN T.modified_field_id = '4c38bdb1-e878-4661-86ae-53d9b347b565' THEN CAST(T.present_value AS DECIMAL) END AS modified_price
                    ,CASE WHEN T.modified_field_id = '4c38bdb1-e878-4661-86ae-53d9b347b565' THEN CAST(T.previous_value AS DECIMAL) END AS original_price
                    ,CASE WHEN T.modified_field_id = 'df7eb5dc-84db-4250-9157-c4843ec3df82' THEN DATE(T.previous_value) END AS orig_start_date  
                    ,CASE WHEN T.modified_field_id = 'df7eb5dc-84db-4250-9157-c4843ec3df82' THEN DATE(T.present_value) END AS mod_start_date
                    ,CASE WHEN T.modified_field_id = 'a3efcb4b-e149-42c8-938f-e27935028669' THEN DATE(T.previous_value) END AS orig_end_date  
                    ,CASE WHEN T.modified_field_id = 'a3efcb4b-e149-42c8-938f-e27935028669' THEN DATE(T.present_value) END AS mod_end_date
                    ,CASE WHEN T.modified_field_id = '205bf03b-a0bf-406f-8cce-aa270f09f0b4' THEN T.present_value END AS modified_service_type
                    ,CASE WHEN T.modified_field_id = '205bf03b-a0bf-406f-8cce-aa270f09f0b4' THEN T.previous_value END AS original_service_type
                    ,modified_field_id
                    ,T.created_date
                    ,T.reason AS mod_reason
                    ,T.service_type		
                    ,T.time_zone_id
                    ,T.contract_type
				FROM				CTE_1 T
				INNER JOIN	{{ref('ods_cs_contracts')}} CT
				ON	T.contract_id = CT.id
				INNER JOIN	{{ref('ods_cs_users')}} U
				ON	CT.student_id = U.id
				AND	U.user_type = 4					--Student
				INNER JOIN	{{ref('ods_cs_users')}} U1	
				ON	CT.consultant_id = U1.id
				AND	U1.user_type = 3					--consultant
				INNER JOIN	{{ref('ods_cs_users')}} U2
				ON	T.modified_by_id = U2.id	--editor
		),

		mod_start_level AS

		(
				SELECT 
                    PL."order" AS mod_start_level 
                    ,contract_id
                    ,modified_field_id 
                    ,created_date
				FROM	CTE_1 T
				LEFT JOIN		{{ref('ods_cs_product_levels')}} PL
				ON	T.present_value = PL.id   
				WHERE	T.modified_field_id = 'f70d9ba0-290b-47a7-8e11-20f73a2c8824'
		),

		org_start_level AS

        (
				SELECT			PL."order" AS org_start_level
								,		contract_id
								,		modified_field_id
								,		created_date 
				FROM				CTE_1 T
				LEFT JOIN		{{ref('ods_cs_product_levels')}} PL
				ON					T.previous_value = PL.id   
				WHERE				T.modified_field_id = 'f70d9ba0-290b-47a7-8e11-20f73a2c8824'  
		),

		mod_end_level AS

        (
				SELECT			PL."order" AS mod_end_level
								,		contract_id
								,		modified_field_id 
								,		created_date
				FROM				CTE_1 T
				LEFT JOIN		{{ref('ods_cs_product_levels')}} PL
				ON					T.present_value = PL.id   
				WHERE				T.modified_field_id = 'fa063854-be56-4e10-9444-abe4999cbfd2' 
		),

		org_end_level AS

		(
				
				SELECT			PL."order" AS org_end_level
								,		contract_id
								,		modified_field_id 
								,		created_date
				FROM				CTE_1 T
				LEFT JOIN		{{ref('ods_cs_product_levels')}} PL
				ON					T.previous_value = PL.id   
				WHERE				T.modified_field_id = 'fa063854-be56-4e10-9444-abe4999cbfd2' 
		),

		FINAL AS

		(
				Select			T1.center_reference_id 
								,		T1.contract_id
								,		T1.center_name
								,		T1.last_name
								,		T1.first_name
								,		T1.student_code
								,		T1.consultant
								,		T1.editor
								,		T1.course_type
								,		C.is_renewed
								,		C.is_membership
								,		C.contract_type
								,		C.location
								,		C.is_promotional
								,		Sl.org_start_level
								,		MSL.mod_start_level
								,		EL.org_end_level
								,		MEL.mod_end_level
								,		PL."order" AS c_start_level
								,		PL1."order" As c_end_level
								,		contract_end_level_id
								,		CASE WHEN original_price IS NULL THEN C.Price END AS original_price1
								,		CASE WHEN modified_price IS NULL THEN C.price END AS modified_price1
								,		COALESCE(original_price, C.Price) AS original_price 
								,		COALESCE(modified_price, C.Price) AS modified_price
								,		CASE 
														WHEN T1.original_service_type IS NULL AND C.service_type=1 AND T1.is_teen = false THEN 'Standard'
														WHEN T1.original_service_type IS NULL AND C.service_type=2 AND T1.is_teen = false THEN 'VIP' 
														WHEN T1.original_service_type IS NULL AND C.service_type=1 AND T1.is_teen = true THEN 'Teens Standard' 
														WHEN T1.original_service_type IS NULL AND C.service_type=2 AND T1.is_teen = true THEN 'Teens VIP' 
														WHEN T1.original_service_type = 'Standard' AND T1.is_teen = false THEN 'Standard' 
														WHEN T1.original_service_type = 'VIP' AND T1.is_teen = false THEN 'VIP' 
														WHEN T1.original_service_type = 'Standard' AND T1.is_teen = true THEN 'Teens Standard' 
														WHEN T1.original_service_type = 'VIP' AND T1.is_teen = true THEN 'Teens VIP' 
										END AS original_service_type 
								,		CASE 
														WHEN T1.modified_service_type IS NULL AND C.service_type=1 AND T1.is_teen = false THEN 'Standard'
														WHEN T1.modified_service_type IS NULL AND C.service_type=2 AND T1.is_teen = false THEN 'VIP' 
														WHEN T1.modified_service_type IS NULL AND C.service_type=1 AND T1.is_teen = true THEN 'Teens Standard' 
														WHEN T1.modified_service_type IS NULL AND C.service_type=2 AND T1.is_teen = true THEN 'Teens VIP' 
														WHEN T1.modified_service_type = 'Standard' AND T1.is_teen = false THEN 'Standard' 
														WHEN T1.modified_service_type = 'VIP' AND T1.is_teen = false THEN 'VIP' 
														WHEN T1.modified_service_type = 'Standard' AND T1.is_teen = true THEN 'Teens Standard' 
														WHEN T1.modified_service_type = 'VIP' AND T1.is_teen = true THEN 'Teens VIP' 
										END AS modified_service_type 
								,CAST(date_format(COALESCE(mod_start_date,C.start_date),'%Y-%m-%d %H:%i:%s.%f') as timestamp(6)) AS mod_start_date
								,CAST(date_format(COALESCE(orig_start_date,C.start_date),'%Y-%m-%d %H:%i:%s.%f') as timestamp(6)) AS orig_start_date
								,CAST(date_format(COALESCE(mod_end_date,C.end_date),'%Y-%m-%d %H:%i:%s.%f') as timestamp(6)) AS mod_end_date
								,CAST(date_format(COALESCE(orig_end_date,C.end_date),'%Y-%m-%d %H:%i:%s.%f') as timestamp(6)) AS orig_end_date 
								,{{convert_to_local_timestamp('t1.created_date','t1.time_zone_id')}} AS formatted_mod_date
								,date_format({{convert_to_local_timestamp('t1.created_date','t1.time_zone_id')}}, '%b %d %Y') AS mod_date
								,T1.modified_field_id
								,T1.mod_reason

								,		CASE 
											WHEN T1.service_type = 1 AND T1.is_teen = false THEN 'Standard'
											WHEN T1.service_type = 2 AND T1.is_teen = false THEN 'VIP' 
											WHEN T1.service_type = 1 AND T1.is_teen = true THEN 'Teens Standard' 
											WHEN T1.service_type = 2 AND T1.is_teen = true THEN 'Teens VIP' END AS service_type

								,		T1.time_zone_id
				FROM				CTE_2 T1
				LEFT JOIN		{{ref('ods_cs_contracts')}} C
				ON					C.id=t1.contract_id 
				LEFT JOIN		org_start_level AS SL
				ON					SL.contract_id=T1.contract_id and SL.modified_field_id= T1.modified_field_id and T1.created_date= SL.created_date
				LEFT JOIN		mod_start_level AS MSL
				ON					MSL.contract_id=T1.contract_id and MSL.modified_field_id= T1.modified_field_id and T1.created_date= MSL.created_date
				LEFT JOIN		org_end_level AS EL
				ON					EL.contract_id=T1.contract_id and EL.modified_field_id= T1.modified_field_id  and T1.created_date= EL.created_date
				LEFT JOIN		mod_end_level AS MEL
				ON					MEL.contract_id=T1.contract_id and MEL.modified_field_id= T1.modified_field_id  and T1.created_date= MEL.created_date
				LEFT JOIN		{{ref('ods_cs_product_levels')}} PL
				ON					T1.contract_start_level_id= PL.id 
				LEFT JOIN		{{ref('ods_cs_product_levels')}} PL1
				ON					T1.contract_end_level_id = PL1.id
		),

		MODIFICATIONS AS

		(SELECT					center_reference_id
								,	center_name
								,	contract_id
								,	last_name
								,	first_name
								,	student_code
								,	course_type
								,	is_renewed
								,	is_membership
								,	contract_type
								,	location
								,	is_promotional
								,	date_format({{convert_to_local_timestamp('orig_start_date','time_zone_id')}}, '%b %d %Y') AS orig_start_date
								,	date_format({{convert_to_local_timestamp('orig_end_date','time_zone_id')}}, '%b %d %Y') AS orig_end_date
								,	date_format({{convert_to_local_timestamp('mod_start_date','time_zone_id')}}, '%b %d %Y') AS mod_start_date
								,	date_format({{convert_to_local_timestamp('mod_end_date','time_zone_id')}}, '%b %d %Y') AS mod_end_date
								,	DATE_DIFF('DAY', mod_start_date, mod_end_date) - DATE_DIFF('DAY', orig_start_date, orig_end_date) AS diff_no_of_days
								,	DATE_DIFF('DAY', mod_start_date, mod_end_date) AS days
								,	COALESCE(org_start_level,c_start_level) AS org_start_level
								,	COALESCE(mod_start_level,c_start_level) AS mod_start_level
								,	COALESCE(org_end_level,c_end_level) AS org_end_level 
								,	COALESCE(mod_end_level ,c_end_level) AS mod_end_level
								,	(COALESCE(org_end_level,c_end_level) - COALESCE(org_start_level,c_start_level)) + 1 AS purchased_no_of_levels
								,	(COALESCE(mod_end_level ,c_end_level ) - COALESCE(mod_start_level,c_start_level)) + 1 AS modified_no_of_levels
								,	CAST(original_price AS DECIMAL(38,2)) AS original_price
								,	CAST(modified_price AS DECIMAL(38,2)) AS modified_price
								,	CAST(modified_price AS DECIMAL(38,2)) - CAST(original_price AS DECIMAL(38,2)) AS price_difference
								,	original_service_type 
								,	modified_service_type
								,	mod_date
								,	consultant
								,	editor
								,	mod_reason
								,	CASE 
														WHEN modified_field_id	= 'f70d9ba0-290b-47a7-8e11-20f73a2c8824' THEN 'start_level_idChange'
														WHEN modified_field_id	= 'fa063854-be56-4e10-9444-abe4999cbfd2' THEN	'end_level_idChange'
														WHEN modified_field_id	= 'df7eb5dc-84db-4250-9157-c4843ec3df82' THEN	'start_dateChange'
														WHEN modified_field_id	= 'a3efcb4b-e149-42c8-938f-e27935028669' THEN	'end_dateChange'
														WHEN modified_field_id	= '4c38bdb1-e878-4661-86ae-53d9b347b565' THEN	'PriceChange'
														WHEN modified_field_id	= '205bf03b-a0bf-406f-8cce-aa270f09f0b4' THEN 'service_typeChange' 
										END AS modified_column
								,		service_type
								,		formatted_mod_date
			From					FINAL
			)	 
			SELECT 				
                center_reference_id
                ,center_name
                ,contract_id
                ,last_name
                ,first_name
                ,student_code
                ,course_type
                ,is_renewed
                ,is_membership
                ,contract_type
                ,location
                ,is_promotional
                ,orig_start_date 
                ,orig_end_date
                ,mod_start_date
                ,mod_end_date
                ,diff_no_of_days
                ,days
                ,org_start_level
                ,mod_start_level
                ,org_end_level 
                ,mod_end_level  
                ,(modified_no_of_levels - purchased_no_of_levels) AS diff_levels
                ,original_price
                ,modified_price
                ,price_difference
                ,original_service_type 
                ,modified_service_type 
                ,mod_date
                ,consultant
                ,editor 
                ,mod_reason
                ,modified_column
                ,service_type
                ,date_format(formatted_mod_date, '%m/%d/%Y') AS formatted_mod_date
		From MODIFICATIONS;