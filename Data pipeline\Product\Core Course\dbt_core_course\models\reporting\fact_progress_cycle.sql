{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}
 
 
with students as
(
select fds.student_reference_id
,fds.center_reference_id
,fds.contract_type
,fds.location
,case when fds.group_id = 'Individual' then 'individual' else 'group' end as is_group
,fds.class_access_type
,fds.service_type
,fds.is_teen
,fds."date"
FROM {{ ref('fact_daily_students') }} fds
where fds."date" = date_trunc('month',"date") + interval '1' month - interval '1' day
and fds.contract_inclusions = true
and fds.is_promotional = false
and fds."date" >= date('2022-01-01') --restricting history range
),
study_data_agg as (
SELECT
    s.student_reference_id
    ,s.contract_type
    ,s.location
    ,s.is_group
    ,s.class_access_type
    ,s.service_type
    ,s.is_teen
    ,date_trunc('month',events.event_date) + interval '1' month - interval '1' day                       AS event_month
    ,events.center_reference_id
    ,events.event_date
    ,events.event_type
    ,events.event_description
    ,events.event_detail1
    ,events.event_detail2
    ,events.event_detail3
    ,events.event_start_time
    ,events.event_end_time
    ,events.event_duration
    ,events.event_elapsed_time
from students s
left join {{ ref('study_events') }} events on s.student_reference_id = events.student_reference_id
  AND s."date" = events.event_month
where (event_type = 'self study' or event_type = 'attended class'
AND event_description like '%encounter%')
),
student_unit_summary as
(
select
student_reference_id
,center_reference_id
,contract_type
,location
,is_group
,class_access_type
,service_type
,is_teen
,try(CAST(event_detail1 AS int)) as unit
,count(distinct event_date) filter (where event_type = 'self study') as self_study_days
,sum(event_elapsed_time) filter (where event_type = 'self study') as self_study_elapsed_time
,sum(event_duration) filter (where event_type = 'self study') as self_study_duration
,min(event_start_time) filter (where event_type = 'self study' and event_description = 'multimedia' and event_detail2 = '1' and event_detail3 = 'lesson start') as mm_unit_start_date
,max(event_end_time) filter (where event_type = 'self study' and event_description = 'multimedia' and event_detail2 = '3' and event_detail3 = 'lesson exercise') as mm_unit_end_date
,min(event_start_time) filter (where event_type = 'attended class' and event_description like '%encounter%') as enc_att_first_date
,min(event_start_time) filter (where event_type = 'attended class' and event_description like '%encounter%' and event_detail2 = 'continue') as enc_passed_date
from study_data_agg
where (event_type = 'self study'
 or event_type = 'attended class' and event_description like '%encounter%')
group by  
student_reference_id
,center_reference_id
,contract_type
,location
,is_group
,class_access_type
,service_type
,is_teen
,event_detail1
),
student_unit_summary_w_lag AS
(
select *
,lag(enc_passed_date) over (partition by student_reference_id order by enc_passed_date) as last_enc_passed_date
from student_unit_summary
where mm_unit_start_date is not null
and enc_passed_date is not null
and unit is not null
order by student_reference_id, enc_passed_date
),
page_navigation_events AS (
       SELECT  
               pne.user_id                                                                               AS student_reference_id
              ,NULL                                                                                      AS unit
              ,NULL                                                                                      AS center_reference_id
              ,event_date
              ,date_trunc('month',event_date) + interval '1' month - interval '1' day                    AS event_month
              ,'navigation'                                                                              AS event_type
              ,pne.event_name                                                                            AS event_description
              ,CASE WHEN regexp_like(pne.screen, 'grade') THEN 'gradebook'
                    WHEN regexp_like(pne.screen, 'y_progress') THEN 'gradebook'
                    WHEN regexp_like(pne.screen, '_progress_enc') THEN 'gradebook'
                    ELSE 'other' END                                                                     AS event_detail1
              ,pne.screen                                                                                AS event_detail2
              ,null                                                                                      AS event_detail3
              ,from_unixtime(pne.event_timestamp / 1000000)                                              AS event_start_time
              ,from_unixtime(pne.event_timestamp / 1000000)                                              AS event_end_time
              ,0                                                                                         AS event_duration
              ,0                                                                                         AS event_elapsed_time
       FROM {{ source('bigquery', 'page_navigation_events') }} pne
),
navigation_data_filtered AS (
    SELECT
        unit_summary.student_reference_id,
        unit_summary.center_reference_id,
        MIN(nav.event_start_time) FILTER (WHERE nav.event_type = 'navigation') AS first_event_after_encounter,
        MIN(nav.event_start_time) FILTER (WHERE nav.event_type = 'navigation' AND nav.event_detail1 = 'gradebook') AS first_gradebook_event_after_encounter,
        unit_summary.unit
    FROM student_unit_summary_w_lag unit_summary     
    FULL OUTER JOIN page_navigation_events nav ON unit_summary.student_reference_id = nav.student_reference_id      
    AND nav.event_start_time BETWEEN unit_summary.last_enc_passed_date + INTERVAL '1' hour
    AND unit_summary.enc_passed_date     
    GROUP BY 
    unit_summary.student_reference_id, 
    unit_summary.center_reference_id, 
    unit_summary.unit
)
SELECT
       unit_summary.student_reference_id
       ,unit_summary.center_reference_id
       ,unit_summary.class_access_type
       ,unit_summary.contract_type
       ,CAST(unit_summary.enc_passed_date AS TIMESTAMP(6)) as enc_passed_date
       ,CAST(nav.first_event_after_encounter AS TIMESTAMP(6)) as first_event_after_encounter
       ,CAST(nav.first_gradebook_event_after_encounter AS TIMESTAMP(6)) as first_gradebook_event_after_encounter
       ,CAST(unit_summary.enc_att_first_date AS TIMESTAMP(6)) as enc_att_first_date
       ,unit_summary.is_group
       ,unit_summary.is_teen
       ,unit_summary.location
       ,CAST(unit_summary.mm_unit_end_date AS TIMESTAMP(6)) as mm_unit_end_date
       ,CAST(unit_summary.mm_unit_start_date AS TIMESTAMP(6)) as mm_unit_start_date
       ,unit_summary.self_study_days
       ,unit_summary.self_study_duration
       ,unit_summary.self_study_elapsed_time
       ,unit_summary.service_type
       ,unit_summary.unit
       ,date(last_enc_passed_date) AS date_last_enc_passed_date
       ,date(enc_passed_date) AS date_enc_passed_date
       ,least(date_diff('day',last_enc_passed_date,enc_passed_date),90) AS days_from_last_enc_to_passed_end
       ,least(date_diff('day',last_enc_passed_date,mm_unit_start_date),90) AS days_from_last_enc_to_nxt_unit
       ,CASE WHEN date_diff('day',last_enc_passed_date,mm_unit_start_date) < 0 THEN 0 ELSE least(date_diff('day',last_enc_passed_date,mm_unit_start_date),90) END AS days_from_enc_to_nxt_unit_no_neg
       ,least(date_diff('day',mm_unit_start_date,mm_unit_end_date),90) AS days_from_unit_start_to_unit_end
       ,least(date_diff('day',mm_unit_end_date,enc_att_first_date),90) AS days_from_unit_end_to_att_enc
       ,CASE WHEN date_diff('day',mm_unit_end_date,enc_att_first_date) < 0 THEN 0 ELSE least(date_diff('day',mm_unit_end_date,enc_att_first_date),90) END        AS days_from_unit_end_to_att_enc_no_neg
       ,least(date_diff('day',enc_att_first_date,enc_passed_date),90) AS days_from_first_enc_to_passed_enc
       ,CAST(current_timestamp AS TIMESTAMP(6)) AS load_date
       ,least(date_diff('hour', last_enc_passed_date,first_event_after_encounter),90*24)-1                                                                       AS hours_from_encounter_to_connect
       ,least(date_diff('minute', last_enc_passed_date, first_event_after_encounter),90*60*24)-60                                                                AS minutes_from_encounter_to_connect
       ,least(date_diff('hour', last_enc_passed_date, first_gradebook_event_after_encounter),90*24)-1                                                            AS hours_from_encounter_to_gradebook
       ,least(date_diff('minute', last_enc_passed_date, first_gradebook_event_after_encounter),90*60*24)-60                                                      AS minutes_from_encounter_to_gradebook
FROM student_unit_summary_w_lag unit_summary
LEFT JOIN navigation_data_filtered nav
  ON unit_summary.student_reference_id = nav.student_reference_id
  AND unit_summary.unit = nav.unit
  AND unit_summary.center_reference_id = nav.center_reference_id
WHERE last_enc_passed_date IS NOT NULL