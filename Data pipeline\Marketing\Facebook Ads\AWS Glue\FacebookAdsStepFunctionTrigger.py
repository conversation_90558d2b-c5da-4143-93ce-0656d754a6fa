import CloudOperations
import logging

StepFunction = CloudOperations.StepFunction

# Assigning the string "FacebookAds" to the ExecutionInput variable
ExecutionInput = "FacebookAds"

# Assigning the ARN of the state machine to the StateMachineArn variable
StateMachineArn = 'arn:aws:states:eu-north-1:262158335980:stateMachine:FacebookAdsWorkFlow'

# Checking if there are any running step functions for the specified state machine ARN
CheckStateMachine = StepFunction.CheckStepFunctionsRunning(StateMachineArn)

# Checking the length of the 'executions' list returned by the CheckStepFunctionsRunning function
if len(CheckStateMachine['executions']) == 0:
    logging.warning("There is no running step function proceed to trigger the step function")
    StepFunction.StartStepFunction(ExecutionInput, StateMachineArn)
else:
    logging\
        .warning("There is currently step function in running status please verify for long run of step function")
