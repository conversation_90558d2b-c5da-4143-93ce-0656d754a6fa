import logging
import os
import ast
from dependencies import cloud_operations
from dependencies import db_operations
from dependencies.pipeline_prerequisite import toggle_dag_state
from dependencies import sharepoint_excel_to_csv_file
from dependencies.slack_alerts import task_failure_callback, task_success_callback
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.operators.bash import BashOperator
from datetime import datetime

# Define the DAG
default_args = {
    'owner': 'wse_data_team',
    'start_date': datetime(2024, 9, 19),
    # Add other default_args as needed
}

redshift_execute = db_operations.Database

dag = DAG('current_exchange_rate', 
          default_args=default_args, 
          schedule_interval='25 6,8,10,12,18,22 * * *',  # You can set your desired schedule_interval
          catchup=False,
          tags=['core-course']
          )

s3 = cloud_operations.S3

file_info = s3.read_json_file(bucket="prod-corecourse", file_path="config/current_exchange_rate_config.json")
logging.warning(file_info)

remove_list = []
tables = []

for item in file_info:
    logging.warning(file_info[item]['execution_type'])
    if file_info[item]['execution_type'] == "yes":
        tables.append(item)
    if file_info[item]['execution_type'] != "yes":
        remove_list.append(item)
for key in remove_list:
    file_info.pop(key)

logging.warning(file_info)
logging.warning(tables)


def redshift_copy_command(**kwargs):
    table_name = kwargs['table_name']
    file_path = 's3://prod-corecourse/' + s3.dynamic_file_path('prod-corecourse',
                                                              'data/external_sources/' + table_name + '/')
    redshift_table_name = f"external_sources.{table_name}"

    pre_reqisite_query = """TRUNCATE TABLE {} """.format(redshift_table_name)
    truncate_statement = "truncating the table " + redshift_table_name + " before copying the latest data"
    truncate_execute = redshift_execute.execution('WriteTable', pre_reqisite_query, truncate_statement)
    logging.warning(truncate_execute)

    statement_name = "This query is external_sources classes copy of table " + redshift_table_name + " to redshift"
    copy_command_query = """COPY {}
    FROM '{}'
    IAM_ROLE 'arn:aws:iam::262158335980:role/RedshitS3access'
    PARQUET;""".format(redshift_table_name, file_path)
    logging.warning(copy_command_query)
    copy_execute = redshift_execute.execution('WriteTable', copy_command_query, statement_name)
    logging.warning(copy_execute)
    logging.warning("Data  external_sources copy from S3 to redshift completed for table", redshift_table_name)


# function to call prerequisite
def execute_prerequisite():
    toggle_dag_state(dag)


# basic prerequisite check
prerequisite_check = PythonOperator(
    task_id='prerequisite_check',
    python_callable=execute_prerequisite,
    provide_context=True,
    dag=dag
)

# get the core course dbt path
HOME = os.environ["HOME"]  # retrieve the location of your home folder
dbt_path = os.path.join(HOME, "dbt/dbt_core_course")  # path to your dbt project
seeds_path = f"{dbt_path}/seeds"
logging.warning("dbt path: ", dbt_path)

for i in tables:
    op_kwargs = {
        "secret_name": file_info[i]["secret_name"],
        "region_name": file_info[i]["region_name"],
        "sharepoint_folder": file_info[i]["sharepoint_folder"],
        "sharepoint_filename": file_info[i]["sharepoint_filename"],
        "sharepoint_file_sheet_name": file_info[i]["sharepoint_file_sheet_name"],
        "dbt_folder": seeds_path,
        "dbt_filename": file_info[i]["dbt_filename"],
        "seed_file_index": ast.literal_eval(file_info[i]["seed_file_index"]),
        "seed_file_header": ast.literal_eval(file_info[i]["seed_file_header"])
    }
    redshift_op_kwargs = {'table_name': i}
    excel_to_csv = PythonOperator(
        task_id=f"{i}",
        python_callable=sharepoint_excel_to_csv_file.read_excel,
        on_failure_callback=task_failure_callback,
        op_kwargs=op_kwargs,
        dag=dag
    )
    seed_execution = BashOperator(
        task_id=f"{i}_athena_table_creation",
        bash_command="cd /home/<USER>/dbt"
                    + " && source dbt-venv/bin/activate"  # Activate the dbt virtual
                    + f" && cd {dbt_path}"  # Go to the path containing your dbt project environment
                    + f" && dbt seed --select {i}",  # run the model!
        on_failure_callback=task_failure_callback,
        on_success_callback =task_success_callback,
        dag=dag
    )
    
    if file_info[i]["is_redshift"] =="yes":
        external_sources_to_redshift = PythonOperator(
            task_id=f"athena_to_redshift_{i}",
            python_callable=redshift_copy_command,
            on_failure_callback=task_failure_callback,
            op_kwargs=redshift_op_kwargs,
            dag=dag
        )
        
    # Set up the task dependency
    prerequisite_check >> excel_to_csv
    excel_to_csv >> seed_execution
    if file_info[i]["is_redshift"] =="yes":
        seed_execution >> external_sources_to_redshift
if __name__ == "__main__":
    dag.cli()
