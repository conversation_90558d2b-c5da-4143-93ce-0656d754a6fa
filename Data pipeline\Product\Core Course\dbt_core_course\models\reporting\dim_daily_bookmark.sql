{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet',
    unique_key = 'dbt_unique_id'
) }}

with bookmark_mm as
    (
        select
            contract_reference_id
            ,"path" as bookmark_mm_path
            ,"level" as bookmark_mm_level
            ,unit as bookmark_mm_unit
            ,lesson as bookmark_mm_lesson
        from {{ref("bookmark")}}
        where content_item_type ='activity'
 
)
,bookmark_wb as
    (
        select
            contract_reference_id
            ,"path" as bookmark_wb_path
            ,"level" as bookmark_wb_level
            ,unit as bookmark_wb_unit
        from {{ref("bookmark")}}
        where content_item_type ='workbook'
 
)
,bookmark_enc as
    (
        select
            contract_reference_id
            ,"path" as bookmark_enc_path
            ,"level" as bookmark_enc_level
            ,unit as bookmark_enc_unit
        from {{ref("bookmark")}}
        where content_item_type ='encounter'
 
)
,course_level as
    (
        select
            contract_reference_id
            ,course_level
        from {{ref("bookmark")}}
 
)
,calendar as
    (
    select
        "date"
    from reporting.dim_calendar
    where "date" = current_date
    )
,dim_bookmark as (
    select 
        cal.date
        , cc.contract_id as contract_reference_id
        , bmk_mm.bookmark_mm_level
        , bmk_mm.bookmark_mm_unit
        , bmk_wb.bookmark_wb_level
        , bmk_wb.bookmark_wb_unit
        , bmk_enc.bookmark_enc_level
        , bmk_enc.bookmark_enc_unit
        , cl.course_level
        , row_number() over (partition by c.contract_reference_id, c.product_type, cal."date" order by valid_to desc) as rn
    from calendar cal 
        left join {{ ref('contracts_changes') }} cc on cc.valid_from <= cal."date" and cc.valid_to >= cal."date"
        left join bookmark_mm  bmk_mm on cc.contract_id = bmk_mm.contract_reference_id
        left join bookmark_wb  bmk_wb on cc.contract_id = bmk_wb.contract_reference_id
        left join bookmark_enc  bmk_enc on cc.contract_id = bmk_enc.contract_reference_id
        left join {{ref('contracts')}} c on cc.contract_id = c.contract_reference_id
        left join course_level cl on cc.contract_id = cl.contract_reference_id
    where cc.status = 'valid'
        and c.product_type in ('core course')
)


select 
    "date"
    , contract_reference_id
    , (cast("date" as varchar) || cast(contract_reference_id as varchar)) as dbt_unique_id
    , bookmark_mm_level
    , bookmark_mm_unit
    , bookmark_wb_level
    , bookmark_wb_unit
    , bookmark_enc_level
    , bookmark_enc_unit
    , course_level
    , rn
    , CAST(current_timestamp AS TIMESTAMP(6)) AS load_date
from dim_bookmark
where rn = 1