import time
from operator import itemgetter
import TimeConversion
import ast
import logging


class DataFormating:
    @staticmethod
    def DataFormation(DataResponse, TerritoryCode, Defaultproperties, PropertiesStructure, CycleId):
        """
        Format the data response received from the API.

        Args:
            DataResponse (dict): The data response received from the API.
            TerritoryCode (str): The territory code.
            Defaultproperties (list): List of default properties.
            PropertiesStructure (dict): Dictionary representing the structure of properties.
            CycleId (str): The cycle ID.

        Returns:
            list: Formatted data as a list of dictionaries.
        """
        TargetTemp1 = {}
        TargetTemp2 = {}
        TotalPages = []
        AdditionalProperty = {"territory_code": TerritoryCode, "cycleid": CycleId }
        TotalRecord = len(DataResponse["results"])

        # Add additional properties to each data record
        for Count in range(0, TotalRecord, 1):
            DataResponse["results"][Count]["properties"].update(AdditionalProperty)

        DataResponseResult = DataResponse["results"]


        for Iterate in range(0, TotalRecord, 1):
            Step1 = DataResponseResult[Iterate]
            DataResponseProperties = DataResponse["results"][Iterate]['properties']

            Result1 = {}
            Result2 = {}
            for Column in Defaultproperties:
                if Column in Step1:
                    TargetTemp1[Column] = None
                    Result1 = {i: Step1.get(i) for i, j in TargetTemp1.items()}
                if Column in DataResponseProperties:
                    TargetTemp2[Column] = None
                    Result2 = {i: DataResponseProperties.get(i) for i, j in
                               TargetTemp2.items()}

            Updated = {**Result1, **Result2}

            Final = {**PropertiesStructure, **Updated}
            TotalPages.append(Final)
            time.sleep(0.015)

        return TotalPages

    @staticmethod
    def IncrementalDateExtract(DataExtract, DefaultInputJson, FilterProperty):
        """
        Extract the last modified date from the data extract and update the DefaultInputJson with the new filter value.

        Args:
            DataExtract (list): The data extract.
            DefaultInputJson (dict): The default input JSON.
            FilterProperty (str): The filter property.

        Returns:
            dict: The updated DefaultInputJson.
        """
        SortedTotalPages = sorted(DataExtract, key=itemgetter(FilterProperty))
        LastDictionary = str(SortedTotalPages[-1])
        logging.warning(LastDictionary)
        ConvertedLastDictionary = ast.literal_eval(LastDictionary)
        LatestLastModified = ConvertedLastDictionary[FilterProperty]
        LastMaxValue = f"{LatestLastModified}"
        LastModifiedDateFormatting = LastMaxValue.replace('T', ' ').replace('Z', '')
        logging.warning("last date: '%s'", format(LastModifiedDateFormatting))
        IncrementalLoadDate = TimeConversion.TimeFormat(LastModifiedDateFormatting)
        logging.warning("The incremental load date is :'%s' ", format(IncrementalLoadDate))
        DefaultInputJson["filterGroups"][0]["filters"][0]['value'] = IncrementalLoadDate
        logging.warning(DefaultInputJson)
        return DefaultInputJson

    @staticmethod
    def AccessTokenRegenerate(Headers,DefaultInputJson, APICall, DataRequestUrl, DataExtract, DataFormat, FilterProperty, Object, Operation):
        """
        Regenerate the access token and make the API call with the updated access token.

        Args:
            Headers (dict): The headers for the API call.
            DefaultInputJson (dict): The default input JSON.
            APICall (class): The class for making API calls.
            DataRequestUrl (str): The data request URL.
            DataExtract (list): The data extract.
            DataFormat (class): The class for data formatting.
            FilterProperty (str): The filter property.
            Object (str): The API object.
            Operation (str): The API operation.

        Returns:
            dict: The data response from the API.
        """
        DefaultInputJson["after"] = 0
        if len(DataExtract) != 0:
            IncrementalDefaultJson = DataFormat.IncrementalDateExtract(DataExtract, DefaultInputJson,
                                                                    FilterProperty)
            logging.warning("printing the incremental default json:'%s'", format(IncrementalDefaultJson))
            DataResponse = APICall.ApiCall(DataRequestUrl=DataRequestUrl, DefaultInputJson=IncrementalDefaultJson, Headers=Headers, Object=Object, QueryString=None, Operation=Operation)
            return DataResponse
        else:
            DataResponse = APICall.ApiCall(DataRequestUrl=DataRequestUrl, DefaultInputJson=DefaultInputJson, Headers=Headers, Object=Object, QueryString=None, Operation=Operation)
            return DataResponse

    @staticmethod
    def CutoffDateGenerator(DataFetchObject, FilterProperty):
        """
        Generate the cutoff date from the DataFetchObject based on the FilterProperty.

        Args:
            DataFetchObject (list): The data fetch object.
            FilterProperty (str): The filter property.

        Returns:
            str: The cutoff date.
        """
        SortedTotalPages = sorted(DataFetchObject, key=itemgetter(FilterProperty))
        LastDictionary = str(SortedTotalPages[-1])
        logging.warning(LastDictionary)
        ConvertedLastDictionary = ast.literal_eval(LastDictionary)
        LatestLastModified = ConvertedLastDictionary[FilterProperty]
        LastMaxValue = f"{LatestLastModified}"
        LastModifiedDateFormatting = LastMaxValue.replace('T', ' ').replace('Z', '')
        return LastModifiedDateFormatting

