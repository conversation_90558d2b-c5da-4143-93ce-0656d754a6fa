CREATE OR REPLACE PROCEDURE hubspot_crm.sp_factcrm()
	LANGUAGE plpgsql
AS $$


BEGIN

DROP TABLE IF EXISTS datefact;
CREATE TEMP TABLE datefact AS
SELECT date, center_key_fk, source_key_fk, individual_corporate, hubspot_owner_id, course_age_group, territory_name, page
FROM (SELECT DISTINCT mql_date AS date,
                      center_key_fk,
                      source_key_fk,
                      individual_corporate,
                      hubspot_owner_id,
                      course_age_group,
                      territory_name,
                      page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      UNION
      SELECT DISTINCT useful_contact_date AS date,
                      center_key_fk,
                      source_key_fk,
                      individual_corporate,
                      hubspot_owner_id,
                      course_age_group,
                      territory_name,
                      page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      UNION
      SELECT DISTINCT booked_date AS date,
                      center_key_fk,
                      source_key_fk,
                      individual_corporate,
                      hubspot_owner_id,
                      course_age_group,
                      territory_name,
                      page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      UNION
      SELECT DISTINCT showed_date AS date,
                      center_key_fk,
                      source_key_fk,
                      individual_corporate,
                      hubspot_owner_id,
                      course_age_group,
                      territory_name,
                      page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      UNION
      SELECT DISTINCT contract_date AS date,
                      center_key_fk,
                      source_key_fk,
                      individual_corporate,
                      hubspot_owner_id,
                      course_age_group,
                      territory_name,
                      page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      UNION
            SELECT DISTINCT lead_date AS date,
                      center_key_fk,
                      source_key_fk,
                      individual_corporate,
                      hubspot_owner_id,
                      course_age_group,
                      territory_name,
                      page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      union
      SELECT DISTINCT refund_date AS date,
                      center_key_fk,
                      source_key_fk,
                      individual_corporate,
                      hubspot_owner_id,
                      course_age_group,
                      territory_name,
                      page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)) CTESAMPLE
WHERE date BETWEEN '2019-01-01' AND cast(sysdate-1 as date);


TRUNCATE TABLE hubspot_crm.fact_crm;

INSERT INTO hubspot_crm.fact_crm(daily_date, center_key, source_key, individual_corporate, hubspot_owner_id,
                                 age_group, territory_name,page)
SELECT date, center_key_fk, source_key_fk, individual_corporate, hubspot_owner_id, course_age_group, territory_name,page
FROM datefact;



UPDATE hubspot_crm.fact_crm
SET mql = mql_count
FROM (SELECT COUNT(DISTINCT contactid) AS mql_count,

             source_key_fk,
             individual_corporate,
             hubspot_owner_id,
             course_age_group,
             territory_name,
             center_key_fk,
             mql_date,
             page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      GROUP BY source_key_fk, individual_corporate, hubspot_owner_id, course_age_group, territory_name,
               center_key_fk, mql_date, page) ABC
         JOIN hubspot_crm.fact_crm fc
              ON
                          ABC.territory_name = fc.territory_name
                      AND ABC.course_age_group = fc.age_group
                      AND ABC.hubspot_owner_id = fc.hubspot_owner_id
                      AND ABC.center_key_fk = fc.center_key
                      AND ABC.source_key_fk = fc.source_key
                      AND ABC.individual_corporate = fc.individual_corporate
                      AND ABC.mql_date = fc.daily_date
                     and ABC.page = fc.page;

UPDATE hubspot_crm.fact_crm
SET lead = lead_count
FROM (SELECT COUNT(DISTINCT contactid) AS lead_count,

             source_key_fk,
             individual_corporate,
             hubspot_owner_id,
             course_age_group,
             territory_name,
             center_key_fk,
             lead_date,
             page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      GROUP BY source_key_fk, individual_corporate, hubspot_owner_id, course_age_group, territory_name,
               center_key_fk, lead_date, page) ABC
         JOIN hubspot_crm.fact_crm fc
              ON
                          ABC.territory_name = fc.territory_name
                      AND ABC.course_age_group = fc.age_group
                      AND ABC.hubspot_owner_id = fc.hubspot_owner_id
                      AND ABC.center_key_fk = fc.center_key
                      AND ABC.source_key_fk = fc.source_key
                      AND ABC.individual_corporate = fc.individual_corporate
                      AND ABC.lead_date = fc.daily_date
                      and ABC.page = fc.page;

UPDATE hubspot_crm.fact_crm
SET contacted_pipeline = contactedcount
FROM (SELECT COUNT(contactid) AS contactedcount,
             source_key_fk,
             individual_corporate,
             hubspot_owner_id,
             course_age_group,
             territory_name,
             center_key_fk,
             useful_contact_date,
             page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      GROUP BY source_key_fk, individual_corporate, hubspot_owner_id, course_age_group, territory_name,
               center_key_fk, useful_contact_date,page) ABC
         JOIN hubspot_crm.fact_crm fc
              ON
                          ABC.territory_name = fc.territory_name
                      AND ABC.course_age_group = fc.age_group
                      AND ABC.hubspot_owner_id = fc.hubspot_owner_id
                      AND ABC.center_key_fk = fc.center_key
                      AND ABC.source_key_fk = fc.source_key
                      AND ABC.individual_corporate = fc.individual_corporate
                      AND ABC.useful_contact_date = fc.daily_date
                      and ABC.page = fc.page;

UPDATE hubspot_crm.fact_crm
SET booked_pipeline = bookedcount
FROM (SELECT COUNT(contactid) AS bookedcount,
             source_key_fk,
             individual_corporate,
             hubspot_owner_id,
             course_age_group,
             territory_name,
             center_key_fk,
             booked_date,
             page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      GROUP BY source_key_fk, individual_corporate, hubspot_owner_id, course_age_group, territory_name,
               center_key_fk, booked_date,page) ABC
         JOIN hubspot_crm.fact_crm fc
              ON
                          ABC.territory_name = fc.territory_name
                      AND ABC.course_age_group = fc.age_group
                      AND ABC.hubspot_owner_id = fc.hubspot_owner_id
                      AND ABC.center_key_fk = fc.center_key
                      AND ABC.source_key_fk = fc.source_key
                      AND ABC.individual_corporate = fc.individual_corporate
                      AND ABC.booked_date = fc.daily_date
                      and ABC.page = fc.page;

UPDATE hubspot_crm.fact_crm
SET shows_pipeline = showcount
FROM (SELECT COUNT(DISTINCT dealid) AS showcount,
             source_key_fk,
             individual_corporate,
             hubspot_owner_id,
             course_age_group,
             territory_name,
             center_key_fk,
             showed_date,
             page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      GROUP BY source_key_fk, individual_corporate, hubspot_owner_id, course_age_group, territory_name,
               center_key_fk, showed_date,page) ABC
         JOIN hubspot_crm.fact_crm fc
              ON
                          ABC.territory_name = fc.territory_name
                      AND ABC.course_age_group = fc.age_group
                      AND ABC.hubspot_owner_id = fc.hubspot_owner_id
                      AND ABC.center_key_fk = fc.center_key
                      AND ABC.source_key_fk = fc.source_key
                      AND ABC.individual_corporate = fc.individual_corporate
                      AND ABC.showed_date = fc.daily_date
                      and ABC.page = fc.page;

UPDATE hubspot_crm.fact_crm
SET contracts_pipeline = contractscount
FROM (SELECT COUNT(contract_date) AS contractscount,
             source_key_fk,
             individual_corporate,
             hubspot_owner_id,
             course_age_group,
             territory_name,
             center_key_fk,
             contract_date,
             page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      GROUP BY source_key_fk, individual_corporate, hubspot_owner_id, course_age_group, territory_name,
               center_key_fk, contract_date, page) ABC
         JOIN hubspot_crm.fact_crm fc
              ON ABC.territory_name = fc.territory_name
                  AND ABC.course_age_group = fc.age_group
                  AND ABC.hubspot_owner_id = fc.hubspot_owner_id
                  AND ABC.center_key_fk = fc.center_key
                  AND ABC.source_key_fk = fc.source_key
                  AND ABC.individual_corporate = fc.individual_corporate
                  AND ABC.contract_date = fc.daily_date
                  and ABC.page = fc.page;

UPDATE hubspot_crm.fact_crm
SET sales_pipeline = salesamount
FROM (SELECT contract_date,
             source_key_fk,
             center_key_fk,
             individual_corporate,
             hubspot_owner_id,
             course_age_group,
             territory_name,
             SUM(sales) AS salesamount,
             page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      GROUP BY contract_date, source_key_fk, center_key_fk, individual_corporate, hubspot_owner_id,
               course_age_group, territory_name, page) ABC
         JOIN hubspot_crm.fact_crm fc
              ON
                          ABC.territory_name = fc.territory_name
                      AND ABC.course_age_group = fc.age_group
                      AND ABC.hubspot_owner_id = fc.hubspot_owner_id
                      AND ABC.center_key_fk = fc.center_key
                      AND ABC.source_key_fk = fc.source_key
                      AND ABC.individual_corporate = fc.individual_corporate
                      AND ABC.contract_date = fc.daily_date
                      and ABC.page = fc.page;


UPDATE hubspot_crm.fact_crm
SET contacted_funnel = contactedfunnel
FROM (SELECT COUNT(useful_contact_date) AS contactedfunnel,
             source_key_fk,
             individual_corporate,
             hubspot_owner_id,
             course_age_group,
             territory_name,
             center_key_fk,
             mql_date,
             page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      GROUP BY source_key_fk, individual_corporate, hubspot_owner_id, course_age_group, territory_name,
               center_key_fk, mql_date, page) ABC
         JOIN hubspot_crm.fact_crm fc
              ON
                          ABC.territory_name = fc.territory_name
                      AND ABC.course_age_group = fc.age_group
                      AND ABC.hubspot_owner_id = fc.hubspot_owner_id
                      AND ABC.center_key_fk = fc.center_key
                      AND ABC.source_key_fk = fc.source_key
                      AND ABC.individual_corporate = fc.individual_corporate
                      AND ABC.mql_date = fc.daily_date
                      and ABC.page = fc.page;

UPDATE hubspot_crm.fact_crm
SET booked_funnel = bookedfunnel
FROM (SELECT COUNT(booked_date) AS bookedfunnel,
             source_key_fk,
             individual_corporate,
             hubspot_owner_id,
             course_age_group,
             territory_name,
             center_key_fk,
             mql_date,
             page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      GROUP BY source_key_fk, individual_corporate, hubspot_owner_id, course_age_group, territory_name,
               center_key_fk, mql_date, page) ABC
         JOIN hubspot_crm.fact_crm fc
              ON
                          ABC.territory_name = fc.territory_name
                      AND ABC.course_age_group = fc.age_group
                      AND ABC.hubspot_owner_id = fc.hubspot_owner_id
                      AND ABC.center_key_fk = fc.center_key
                      AND ABC.source_key_fk = fc.source_key
                      AND ABC.individual_corporate = fc.individual_corporate
                      AND ABC.mql_date = fc.daily_date
                      and ABC.page = fc.page;

UPDATE hubspot_crm.fact_crm
SET shows_funnel = showsfunnel
FROM (SELECT COUNT(showed_date) AS showsfunnel,
             source_key_fk,
             individual_corporate,
             hubspot_owner_id,
             course_age_group,
             territory_name,
             center_key_fk,
             mql_date,
             page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      GROUP BY source_key_fk, individual_corporate, hubspot_owner_id, course_age_group, territory_name,
               center_key_fk, mql_date, page) ABC
         JOIN hubspot_crm.fact_crm fc
              ON
                          ABC.territory_name = fc.territory_name
                      AND ABC.course_age_group = fc.age_group
                      AND ABC.hubspot_owner_id = fc.hubspot_owner_id
                      AND ABC.center_key_fk = fc.center_key
                      AND ABC.source_key_fk = fc.source_key
                      AND ABC.individual_corporate = fc.individual_corporate
                      AND ABC.mql_date = fc.daily_date
                      and ABC.page = fc.page;


UPDATE hubspot_crm.fact_crm
SET contracts_funnel = contractsfunnel
FROM (SELECT COUNT(contract_date) AS contractsfunnel,
             source_key_fk,
             individual_corporate,
             hubspot_owner_id,
             course_age_group,
             territory_name,
             center_key_fk,
             mql_date,
             page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      GROUP BY source_key_fk, individual_corporate, hubspot_owner_id, course_age_group, territory_name,
               center_key_fk, mql_date, page) ABC
         JOIN hubspot_crm.fact_crm fc
              ON
                          ABC.territory_name = fc.territory_name
                      AND ABC.course_age_group = fc.age_group
                      AND ABC.hubspot_owner_id = fc.hubspot_owner_id
                      AND ABC.center_key_fk = fc.center_key
                      AND ABC.source_key_fk = fc.source_key
                      AND ABC.individual_corporate = fc.individual_corporate
                      AND ABC.mql_date = fc.daily_date
                      and ABC.page = fc.page;

UPDATE hubspot_crm.fact_crm
SET sales_funnel = salesfunnel
FROM (SELECT mql_date,
             source_key_fk,
             center_key_fk,
             individual_corporate,
             hubspot_owner_id,
             course_age_group,
             territory_name,
             SUM(sales) AS salesfunnel,
             page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      GROUP BY mql_date, source_key_fk, center_key_fk, individual_corporate, hubspot_owner_id, course_age_group,
               territory_name, page) ABC
         JOIN hubspot_crm.fact_crm fc
              ON
                          ABC.territory_name = fc.territory_name
                      AND ABC.course_age_group = fc.age_group
                      AND ABC.hubspot_owner_id = fc.hubspot_owner_id
                      AND ABC.center_key_fk = fc.center_key
                      AND ABC.source_key_fk = fc.source_key
                      AND ABC.individual_corporate = fc.individual_corporate
                      AND ABC.mql_date = fc.daily_date
                      and ABC.page = fc.page;


--------------------

    update hubspot_crm.fact_crm
    set amount_gifts = amountgifts
    from (
             select mql_date,
                    source_key_fk,
                    center_key_fk,
                    individual_corporate,
                    hubspot_owner_id,
                    course_age_group,
                    territory_name,
                    sum(amount_gifts) as amountgifts,
                    page
             from hubspot_crm.contactsdeals
             where (dealrows=1 or dealrows is NULL)
             GROUP BY mql_date, source_key_fk, center_key_fk, individual_corporate, hubspot_owner_id, course_age_group,
                      territory_name, page) ABC
             join hubspot_crm.fact_crm fc
                  on
                              ABC.territory_name = fc.territory_name
                          and ABC.course_age_group = fc.age_group
                          and ABC.hubspot_owner_id = fc.hubspot_owner_id
                          and ABC.center_key_fk = fc.center_key
                          and ABC.source_key_fk = fc.source_key
                          and ABC.individual_corporate = fc.individual_corporate
                          and ABC.mql_date = fc.daily_date
                          and ABC.page = fc.page;

    update hubspot_crm.fact_crm
    set amount_interest             = amountinterest,
        business_partner_1_amount=businesspartner_1_Amount,
        core_course_amount=corecourseamount,
        market_leader_1_amount=marketleader1amount,
        test_prep_group_1_amount=testprepgroup1amount,
        certifications_1_amount=certifications1_amount,
        core_course_fit_amount=core_course_fitamount,
        core_course_online_1_amount=corecourseonline1_amount,
        ilc_1_amount=ilc1amount,
        test_prep_executive_1_amount=testprepexecutive1amount

    from (
             select contract_date,
                    source_key_fk,
                    center_key_fk,
                    individual_corporate,
                    hubspot_owner_id,
                    course_age_group,
                    territory_name,
                    sum(amount_interest)              as amountinterest,
                    sum(business_partner_1_amount)    as businesspartner_1_Amount,
                    sum(core_course_amount)           as corecourseamount,
                    sum(market_leader_1_amount)       as marketleader1amount,
                    sum(test_prep_group_1_amount)     as testprepgroup1amount,
                    sum(certifications_1_amount)      as certifications1_amount,
                    sum(core_course_fit_amount)       as core_course_fitamount,
                    sum(core_course_online_1_amount)  as corecourseonline1_amount,
                    sum(ilc_1_amount)                 as ilc1amount,
                    sum(test_prep_executive_1_amount) as testprepexecutive1amount,
                    page
             from hubspot_crm.contactsdeals
             where (dealrows=1 or dealrows is NULL)
             GROUP BY contract_date, source_key_fk, center_key_fk, individual_corporate, hubspot_owner_id,
                      course_age_group, territory_name, page) ABC
             join hubspot_crm.fact_crm fc
                  on
                              ABC.territory_name = fc.territory_name
                          and ABC.course_age_group = fc.age_group
                          and ABC.hubspot_owner_id = fc.hubspot_owner_id
                          and ABC.center_key_fk = fc.center_key
                          and ABC.source_key_fk = fc.source_key
                          and ABC.individual_corporate = fc.individual_corporate
                          and ABC.contract_date = fc.daily_date
                          and ABC.page = fc.page;

    update hubspot_crm.fact_crm
    set business_partner_2_hours=bp2hours,
        market_leader_2_hours=ml2hours,
        core_course_fit_2_hours=ccf2hours,
        ilc_2_hours=ilc2hours

    from (
             select contract_date,
                    source_key_fk,
                    center_key_fk,
                    individual_corporate,
                    hubspot_owner_id,
                    course_age_group,
                    territory_name,
                    sum(business_partner_2_hours) as bp2hours,
                    sum(market_leader_2_hours)    as ml2hours,
                    sum(core_course_fit_2_hours)  as ccf2hours,
                    sum(ilc_2_hours)              as ilc2hours,
                    page

             from hubspot_crm.contactsdeals
             where (dealrows=1 or dealrows is NULL)
             GROUP BY contract_date, source_key_fk, center_key_fk, individual_corporate, hubspot_owner_id,
                      course_age_group, territory_name, page) ABC
             join hubspot_crm.fact_crm fc
                  on
                              ABC.territory_name = fc.territory_name
                          and ABC.course_age_group = fc.age_group
                          and ABC.hubspot_owner_id = fc.hubspot_owner_id
                          and ABC.center_key_fk = fc.center_key
                          and ABC.source_key_fk = fc.source_key
                          and ABC.individual_corporate = fc.individual_corporate
                          and ABC.contract_date = fc.daily_date
                          and ABC.page = fc.page;

   update hubspot_crm.fact_crm
    set test_prep_units=ABC.test_prep_units,
        fa_amount=ABC.fa_amount,
        fa_levels=ABC.fa_levels,
        fa2_levels=ABC.fa2_levels,
        fa2_amount=ABC.fa2_amount,
        fa3_amount=ABC.fa3_amount,
        fa3_levels=ABC.fa3_levels,
        fa4_amount=ABC.fa4_amount,
        fam_levels=ABC.fam_levels,
        fam_amount = ABC.fam_amount,
        ic_amount=ABC.ic_amount,
        ic_levels=ABC.ic_levels,
        ic2_levels=ABC.ic2_levels,
        ic2_amount=ABC.ic2_amount,
        ic3_amount=ABC.ic3_amount,
        ic3_levels=ABC.ic3_levels,
        ic4_amount=ABC.ic4_amount,
        ic4_levels=ABC.ic4_levels,
        icm_amount=ABC.icm_amount,
        icm_levels=ABC.icm_levels

    from (
             select contract_date,
                    source_key_fk,
                    center_key_fk,
                    individual_corporate,
                    hubspot_owner_id,
                    course_age_group,
                    territory_name,
                    sum(test_prep_units) as test_prep_units,
                    sum(fa_amount)  as fa_amount,
                    sum(fa_levels)  as fa_levels,
                    sum(fa2_levels) as fa2_levels,
                    sum(fa2_amount) as fa2_amount,
                    sum(fa3_amount) as fa3_amount,
                    sum(fa3_levels) as fa3_levels,
                    sum(fa4_amount) as fa4_amount,
                    sum(fa4_levels) as fa4_levels,
                    sum(fam_amount) as fam_amount,
                    sum(fam_levels) as fam_levels,
                    sum(ic_amount) as ic_amount,
                    sum(ic_levels) as ic_levels,
                    sum(ic2_amount) as ic2_amount,
                    sum(ic2_levels) as ic2_levels,
                    sum(ic3_amount) as ic3_amount,
                    sum(ic3_levels) as ic3_levels,
                    sum(ic4_amount) as ic4_amount,
                    sum(ic4_levels) as ic4_levels,
                    sum(icm_Amount) as icm_amount,
                    sum(icm_levels) as icm_levels,
                    page


             from hubspot_crm.contactsdeals
             where (dealrows=1 or dealrows is NULL)
             GROUP BY contract_date, source_key_fk, center_key_fk, individual_corporate, hubspot_owner_id,
                      course_age_group, territory_name, page) ABC
             join hubspot_crm.fact_crm fc
                  on
                              ABC.territory_name = fc.territory_name
                          and ABC.course_age_group = fc.age_group
                          and ABC.hubspot_owner_id = fc.hubspot_owner_id
                          and ABC.center_key_fk = fc.center_key
                          and ABC.source_key_fk = fc.source_key
                          and ABC.individual_corporate = fc.individual_corporate
                          and ABC.contract_date = fc.daily_date
                          and ABC.page = fc.page;

    update hubspot_crm.fact_crm
    set core_course_levels=ABC.core_course_levels,
        certifications_2_quantity=ABC.certifications_2_quantity,
        test_prep_executive_2_quantity=ABC.test_prep_executive_2_quantity,
        test_prep_group_2_quantity=ABC.test_prep_group_2_quantity,
        core_course_online_2_levels=ABC.core_course_online_2_levels
    from (
             select contract_date,
                    source_key_fk,
                    center_key_fk,
                    individual_corporate,
                    hubspot_owner_id,
                    course_age_group,
                    territory_name,
                    sum(core_course_levels) as core_course_levels,
                    sum(certifications_2_quantity)    as certifications_2_quantity,
                    sum(test_prep_executive_2_quantity)  as test_prep_executive_2_quantity,
                    sum(test_prep_group_2_quantity) as test_prep_group_2_quantity,
                    sum(core_course_online_2_levels) as core_course_online_2_levels,
                    page

             from hubspot_crm.contactsdeals
             where (dealrows=1 or dealrows is NULL)
             GROUP BY contract_date, source_key_fk, center_key_fk, individual_corporate, hubspot_owner_id,
                      course_age_group, territory_name, page) ABC
             join hubspot_crm.fact_crm fc
                  on
                              ABC.territory_name = fc.territory_name
                          and ABC.course_age_group = fc.age_group
                          and ABC.hubspot_owner_id = fc.hubspot_owner_id
                          and ABC.center_key_fk = fc.center_key
                          and ABC.source_key_fk = fc.source_key
                          and ABC.individual_corporate = fc.individual_corporate
                          and ABC.contract_date = fc.daily_date
                          and ABC.page = fc.page;

     update hubspot_crm.fact_crm
     SET refunded_pipeline = refunded_amount
FROM (SELECT refund_date,
             source_key_fk,
             center_key_fk,
             individual_corporate,
             hubspot_owner_id,
             course_age_group,
             territory_name,
             SUM(refunded_amount) AS refunded_amount,
             page
      FROM hubspot_crm.contactsdeals
      where (dealrows=1 or dealrows is NULL)
      GROUP BY refund_date, source_key_fk, center_key_fk, individual_corporate, hubspot_owner_id,
               course_age_group, territory_name, page) ABC
         JOIN hubspot_crm.fact_crm fc
              ON
                          ABC.territory_name = fc.territory_name
                      AND ABC.course_age_group = fc.age_group
                      AND ABC.hubspot_owner_id = fc.hubspot_owner_id
                      AND ABC.center_key_fk = fc.center_key
                      AND ABC.source_key_fk = fc.source_key
                      AND ABC.individual_corporate = fc.individual_corporate
                      AND ABC.refund_date= fc.daily_date
                      and ABC.page = fc.page;

    COMMIT;


END;


$$
;


