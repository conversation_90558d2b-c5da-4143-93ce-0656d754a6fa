{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'user_id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        isactive,
        {{ cast_to_timestamp('created') }} as created,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        userid,
        initial,
        aboutme,
        workmotivation,
        city,
        ROW_NUMBER() over (
            PARTITION BY userid
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'staffmember'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    isactive as is_active,
    created,
    lastupdated as last_updated,
    userid as user_id,
    initial,
    aboutme as about_me,
    workmotivation as work_motivation,
    city
FROM
    rankedrecords
WHERE
    rn = 1;
