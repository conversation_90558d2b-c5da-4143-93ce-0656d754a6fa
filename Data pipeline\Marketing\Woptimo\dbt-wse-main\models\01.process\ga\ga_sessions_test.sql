{{
    config(
        materialized='incremental',
        tags=["test"],
        partition_by={
        "field": "event_date",
        "data_type": "date",
        "granularity": "day"
    }        
    )
}}
with a as (
SELECT 
  event_date,
  event_name,
  event_timestamp,
  CONCAT(user_pseudo_id,(SELECT ep.value.int_value FROM UNNEST(event_params) ep WHERE ep.key="ga_session_id")) session_id,
SPLIT((SELECT ep.value.string_value FROM UNNEST(event_params) ep WHERE ep.key="page_location" ),"?")[SAFE_OFFSET(0)] AS page,
REGEXp_EXTRACT({{ga_param("page_location")}}, "utm_source=([^&]+)") AS utm_source,
REGEXp_EXTRACT({{ga_param("page_location")}}, "utm_medium=([^&]+)") AS utm_medium,

{{ga_param("page_referrer")}} ref,
{{ga_param("gclid")}} gclid,
lang

 FROM {{ ref('ga_init_all_test') }}
 where user_pseudo_id is not null
and {{increment()}}
), b as (

SELECT *,
RANK() OVER (PARTITION BY session_id,event_date,page ORDER BY event_timestamp asc) ranks -- rank all page view 
FROM a
),
 c as (
select 
session_id,event_date,MIN(lang) AS lang,
min(CASE WHEN ranks=1 THEN event_timestamp END) as first_page_ts,
min(CASE WHEN ranks>1 THEN event_timestamp END) as second_page_ts, -- get the 2nd pageview timestamp, it will be needed at next stage
max(ranks) as pageviews,
ANY_VALUE(case when ranks=1 then page END) as landing_page,
STRING_AGG( ref order by event_timestamp LIMIT 1) as ref,
STRING_AGG( utm_source order by event_timestamp LIMIT 1) as utm_source,
STRING_AGG( utm_medium order by event_timestamp LIMIT 1) as utm_medium,
STRING_AGG( gclid order by event_timestamp LIMIT 1) as gclid,

{% for conv in var('conversion_events') %}
COUNTIF(event_name="{{conv}}") session_{{conv}},
{% endfor%}
FROM b
group by session_id,event_date
)


select 
*,
CASE 
WHEN gclid IS NOT NULL  THEN "google_cpc" 
WHEN utm_source IS NOT NULL THEN "utm_campaign"
WHEN ref LIKE "%google%" and gclid IS NULL  THEN "google_organic" 
WHEN 
ref LIKE "%bing%" or ref like "%yandex%" or ref like "%baidu%" or ref like "%search%" or ref like "%naver%"  or ref like "%seznam%" 
or ref like "%qwant%"  or ref like "%duckduckgo%"  or ref like "%lemoteur.orange%" or ref like "%startpage%"  or ref like "%ecosia%" 
or ref like "%lilo.org%" 
  THEN "other_organic" 
ELSE "other"
END 
AS session_channel
 from c