{{ config(
    tags=["speaking_ai_beta"],
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = ['chat_id', 'message_index'],
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}


WITH prospect_speak_beta_data AS (
  SELECT
    chat_id,
    duration,
    gpt4o_mini_cost,
    has_ended,
    messages,
    level,
    CAST(from_iso8601_timestamp(start_date) AS timestamp(6)) AS start_date,
    CAST(from_iso8601_timestamp(end_date) AS timestamp(6)) AS end_date,
    total_input_tokens,
    total_output_tokens,
    user_id,
    user_type
  FROM
  {{ source(
            'stage_speaking_ai_beta_service',
            'raw_data_prospect_speak_beta'
        ) }}
),
flattened_messages AS (
  SELECT
    ps.chat_id,
    ps.duration,
    ps.end_date,
    ps.gpt4o_mini_cost,
    ps.has_ended,
    ps.level,
    ps.start_date,
    ps.total_input_tokens,
    ps.total_output_tokens,
    ps.user_id,
    ps.user_type,
    message,
    message_ordinality AS message_index
  FROM
    prospect_speak_beta_data ps
    CROSS JOIN UNNEST(CAST(json_parse(ps.messages) AS ARRAY<JSON>)) 
    WITH ORDINALITY AS t(message, message_ordinality) -- Assigns array index
)
SELECT
  {{etl_load_date()}},
  fm.chat_id,
  cast (fm.duration as DOUBLE) as duration,
  fm.gpt4o_mini_cost,
  fm.has_ended,
  fm.level,
  fm.start_date,
  fm.end_date,
  fm.total_input_tokens,
  fm.total_output_tokens,
  fm.user_id,
  fm.user_type,
  fm.message_index,
  -- Message fields
  json_extract_scalar(fm.message, '$.interaction_id') AS message_interaction_id,
  json_extract_scalar(fm.message, '$.role') AS message_role,
  COALESCE(TRY_CAST(json_extract_scalar(fm.message, '$.output_tokens') AS INT),0) AS message_outputtokens,
  COALESCE(TRY_CAST(json_extract_scalar(fm.message, '$.input_tokens') AS INT),0) AS message_inputtokens,
  json_extract_scalar(fm.message, '$.content') AS message_content,
  COALESCE(TRY_CAST(json_extract_scalar(fm.message, '$.audio_duration') AS DOUBLE),0) AS message_audio_duration
FROM
  flattened_messages fm
ORDER BY
  fm.chat_id,
  fm.message_index