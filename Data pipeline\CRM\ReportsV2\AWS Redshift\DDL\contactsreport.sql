create table if not exists hubspot_crm.contactsreport
(
    hs_object_id                 bigint encode az64,
    lead_date                    timestamp encode az64,
    mql_date                     timestamp encode az64,
    useful_contact_date          date encode az64,
    booked_date                  timestamp encode az64,
    show_date                    timestamp encode az64,
    contract_date                timestamp encode az64,
    lost_date                    timestamp encode az64,
    first_contract_amount        numeric(19, 2) encode az64,
    call_campaign                varchar(400),
    center_name                  varchar(124),
    first_contract_center        varchar(42),
    hubspot_owner_id             varchar(32),
    first_contract_owner         bigint encode az64,
    createdate                   timestamp encode az64,
    email                        varchar(200),
    contactname                  varchar(728),
    how_did_you_hear_about_us    varchar(68),
    individual_corporate         varchar(32),
    channel                      varchar(400),
    channel_drill_down_1         varchar(200),
    channel_drill_down_2         varchar(200),
    source                       varchar(64),
    sub_source                   varchar(124),
    tmk_owner                    varchar(32),
    recent_conversion_date       timestamp encode az64,
    recent_conversion_event_name varchar(624),
    course_age_group             varchar(16),
    latestrecord                 smallint encode az64,
    etl_account                  varchar(88),
    etl_latest                   integer encode az64,
    re_engaged_lead              varchar(20)
);
