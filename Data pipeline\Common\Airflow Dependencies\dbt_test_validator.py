import re
import logging
from dependencies.slack_alerts import task_warning_callback, task_success_callback

def parse_dbt_test_results(output):
        pattern = r"Done\.\s+PASS=(\d+)\s+WARN=(\d+)\s+ERROR=(\d+)\s+SKIP=(\d+)\s+TOTAL=(\d+)"
        match = re.search(pattern, output)
        
        if match:
            return {
                'PASS': int(match.group(1)),
                'WARN': int(match.group(2)),
                'ERROR': int(match.group(3)),
                'SKIP': int(match.group(4)),
                'TOTAL': int(match.group(5))
            }
        return None
    
def check_dbt_output(task_ids, **context):
        ti = context['ti']
        all_passed = True
        warnings = []
        errors = []
        
        for dbt_test_task_id in task_ids:
            try:
                # Get the full task ID if it's from a task group
                full_task_id = dbt_test_task_id
                if 'task_group' in context:
                    full_task_id = f"{context['task_group'].group_id}.{dbt_test_task_id}"
                
                # Pull XCom data - try both stdout and return_value
                dbt_output = ti.xcom_pull(task_ids=full_task_id, key='return_value') or \
                            ti.xcom_pull(task_ids=full_task_id, key='stdout') or \
                            ti.xcom_pull(task_ids=full_task_id)
                
                if not dbt_output:
                    error_message = f"No output received from {full_task_id} task."
                    logging.error(error_message)
                    errors.append(error_message)
                    all_passed = False
                    continue
                
                # Handle case where output might be bytes
                if isinstance(dbt_output, bytes):
                    dbt_output = dbt_output.decode('utf-8')
                
                results = parse_dbt_test_results(dbt_output)
                if results:
                    summary = f"DBT Test Results: PASS={results['PASS']} WARN={results['WARN']} ERROR={results['ERROR']} SKIP={results['SKIP']} TOTAL={results['TOTAL']}"
                    logging.info(f"{full_task_id} - {summary}")
                    
                    if results['ERROR'] > 0:
                        error_message = f"{full_task_id} DBT tests completed with errors: {summary}"
                        logging.error(error_message)
                        errors.append(error_message)
                        all_passed = False
                    elif results['WARN'] > 0:
                        warning_message = f"{full_task_id} DBT tests completed with warnings: {summary}"
                        logging.warning(warning_message)
                        warnings.append(warning_message)
                    else:
                        success_message = f"{full_task_id} DBT tests completed successfully: {summary}"
                        logging.info(success_message)
                else:
                    error_message = f"{full_task_id} Could not parse DBT test results from output: {dbt_output[:200]}..."
                    logging.error(error_message)
                    errors.append(error_message)
                    all_passed = False
            except Exception as e:
                error_message = f"Error processing {full_task_id}: {str(e)}"
                logging.error(error_message)
                errors.append(error_message)
                all_passed = False
        
        # Push all messages to XCom
        if errors:
            context['task_instance'].xcom_push(key='error_message', value='\n'.join(errors))
        if warnings:
            context['task_instance'].xcom_push(key='warning_message', value='\n'.join(warnings))
        
        # Raise exception if any errors occurred
        if not all_passed:
            raise Exception("DBT test validation failed:\n" + '\n'.join(errors))
        
        # Trigger appropriate callbacks
        if warnings and not errors:
            task_warning_callback(context)
        elif all_passed and not warnings:
            task_success_callback(context)