{{ config(
    materialized = 'table',
    table_type = 'iceberg',
    format = 'parquet'
) }}

select student_id,
    date(completed_date) as completed_date,
    CAST(level AS INT) AS level,
    CAST(unit AS INT) AS unit,
    CAST(lesson AS INT) AS lesson,
    count(case when activity_type = 'multimedia' then activity_id end) as multimedia_activities, 
    count(case when activity_type = 'multimedia' and description like '%Lesson Exercise%' then activity_id end) as lesson_exercises_complete,
    count(distinct mini_cycles) as mini_cycles,
    count(distinct cast(mini_cycles as varchar) ||  cast(activities.mini_cycle_stage as varchar)) as mini_cycles_stages,
    count(workbook_activity) as workbook_activities,
    sum(case when activity_type = 'digital_workbook' then duration_cap_mins else 0 end) as duration_wb_mins,
    sum(case when activity_type = 'multimedia' then duration_cap_mins else 0 end) as duration_mm_mins, 
    SUM(duration_cap_mins) AS duration_mins,
    COUNT(DISTINCT "Session") AS sessions_count,
    CAST(current_timestamp AS TIMESTAMP(6)) as load_date
from
    {{ ref('activities') }}
group by student_id,
    date(completed_date),
    CAST(level AS INT),
    CAST(unit AS INT),
    CAST(lesson AS INT)