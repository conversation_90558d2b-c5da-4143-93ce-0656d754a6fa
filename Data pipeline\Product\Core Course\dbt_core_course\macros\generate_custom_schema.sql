{% macro generate_schema_name(custom_schema_name, node) -%}

    {%- set default_schema = target.schema -%}
    {%- if custom_schema_name is none -%}
        {{ default_schema }}

    {%- elif custom_schema_name in ('ods_learning_service', 'ods_contract_service', 'ods_center_configuration_service', 'ods_idam_service', 'ods_schedule_and_booking_service', 'ods_digital_student_workbook_service', 'ods_prospect_service','ods_conversation_ai_service','ods_novu_service','ods_onboarding_service','ods_speaking_ai_beta_service','ods_google_analytics_service') -%}
        {{ custom_schema_name | trim}}

    {%- else -%}
        {{ generate_schema_name_for_env(custom_schema_name, node) }}

    {%- endif -%}
{%- endmacro %}