{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = ['chat_id', 'message_index'],
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}


WITH conversation_data AS (
  SELECT
    chat_id,
    content_id,
    CAST(from_iso8601_timestamp(end_date) AS timestamp(6)) AS end_date,
    gpt4o_mini_cost,
    hasEnded,
    messages,
    CAST(from_iso8601_timestamp(start_date) AS timestamp(6)) AS start_date,
    total_input_tokens,
    total_output_tokens,
    user_id,
    contract_id
  FROM
  {{ source(
            'stage_conversation_ai_service',
            'raw_data_conversation'
        ) }}
),
flattened_messages AS (
  SELECT
    cd.chat_id,
    cd.content_id,
    cd.end_date,
    cd.gpt4o_mini_cost,
    cd.hasEnded,
    cd.start_date,
    cd.total_input_tokens,
    cd.total_output_tokens,
    cd.user_id,
    cd.contract_id,
    message,
    message_ordinality AS message_index
  FROM
    conversation_data cd
    CROSS JOIN UNNEST(CAST(json_parse(cd.messages) AS ARRAY<JSON>)) 
    WITH ORDINALITY AS t(message, message_ordinality) -- Assigns array index
)
SELECT
  {{etl_load_date()}},
  fm.chat_id,
  fm.content_id,
  fm.end_date,
  fm.gpt4o_mini_cost,
  fm.hasEnded,
  fm.start_date,
  fm.total_input_tokens,
  fm.total_output_tokens,
  fm.user_id,
  fm.contract_id,
  fm.message_index,
  -- Message fields
  json_extract_scalar(fm.message, '$.interaction_id') AS message_interaction_id,
  json_extract_scalar(fm.message, '$.role') AS message_role,
  COALESCE(TRY_CAST(json_extract_scalar(fm.message, '$.output_tokens') AS INT),0) AS message_outputtokens,
  COALESCE(TRY_CAST(json_extract_scalar(fm.message, '$.input_tokens') AS INT),0) AS message_inputtokens,
  json_extract_scalar(fm.message, '$.content') AS message_content,
  COALESCE(TRY_CAST(json_extract_scalar(fm.message, '$.audio_duration') AS DOUBLE),0) AS message_audio_duration
FROM
  flattened_messages fm
ORDER BY
  fm.chat_id,
  fm.message_index