{{ config(
    materialized='table',
    table_type='iceberg',
    format='parquet'
) }}
 
WITH classes AS (
    SELECT class_id
        , class_center_reference_id  AS center_id
        , class_teacher_user_reference_id
        , class_start_datetime       AS class_start_date
        , class_local_start_datetime AS class_local_start_date
        , class_code
        , class_type
        , class_communication_account_type AS class_technology_type
        , hour(class_local_start_datetime) as time_of_day
        , CASE
            WHEN hour(class_local_start_datetime) IN (0, 1, 2) THEN '1. 0-2'
            WHEN hour(class_local_start_datetime) IN (3, 4, 5) THEN '2. 3-5'
            WHEN hour(class_local_start_datetime) IN (6, 7, 8) THEN '3. 6-8'
            WHEN hour(class_local_start_datetime) IN (9, 10, 11) THEN '4. 9-11'
            WHEN hour(class_local_start_datetime) IN (12, 13, 14) THEN '5. 12-14'
            WHEN hour(class_local_start_datetime) IN (15, 16, 17) THEN '6. 15-17'
            WHEN hour(class_local_start_datetime) IN (18, 19, 20) THEN '7. 18-20'
            WHEN hour(class_local_start_datetime) IN (21, 22, 23) THEN '8. 21-23'
            END AS ClassHourBand
        , CASE
                WHEN class_type LIKE '%social%'
                    THEN 'Social Club'
                WHEN class_type LIKE '%encounter%'
                    THEN 'Encounter'
                WHEN class_type LIKE '%complementary%'
                    THEN 'Complementary Class'
                WHEN class_type LIKE '%freesty%'
                    THEN 'Speak +'
                WHEN class_type LIKE '%speak+%'
                    THEN 'Speak +'
                ELSE NULL END       AS class_type_category
        , CASE
            WHEN class_service_type LIKE '%combi%'
                THEN 'Standard'
            WHEN class_service_type LIKE '%sta%'
                THEN 'Standard'
            WHEN class_service_type LIKE '%vip%'
                THEN 'VIP'

            ELSE NULL END       AS class_service_type
        , class_number_of_seats      AS number_of_seats
        , class_number_of_seats_in_stand_by
        , number_of_students
        , categories_abbreviations
        , class_online_flag
        , CASE
              WHEN class_b2b_flag = TRUE 
                  THEN 'B2B'
               WHEN class_b2b_flag = FALSE
                  THEN 'Non-B2B' 
              END AS class_b2b_flag
        , CASE
                WHEN class_teen_flag = TRUE
                    THEN 'Teens'
                ELSE 'Adults' END         AS teen_flag
        , class_teen_flag
        , class_cancelled_flag
        , class_type_billable        AS class_type_billable_flag
     FROM {{ref('classes')}} AS classes
    WHERE class_local_start_datetime >= {{filter_date()}}
    and class_type in (  'class_type'
                        ,'online social club'
                        ,'online encounter'
                        ,'encounter'
                        ,'online complementary class'
                        ,'complementary class'
                        ,'social club'
                        ,'ghost encounter'
                        ,'freestyle advanced'
                        ,'freestyle elementary'
                        ,'freestyle intermediate'
                        ,'online english corner'
                        ,'speak+ advanced'
                        ,'speak+ elementary'
                        ,'speak+ intermediate'
                        )
),

calculations AS (
    SELECT
        class_id
      , center_id
      , class_teacher_user_reference_id
      , class_local_start_date
      , date(class_local_start_date) as class_date
      , ClassHourBand
      , time_of_day
      , concat(CAST(date(class_local_start_date) as varchar),' ',ClassHourBand) as class_date_hour_band
      , class_code
      , class_type
      , class_technology_type
      , class_type_category
      , class_service_type
      , number_of_seats
      , class_number_of_seats_in_stand_by
      , number_of_students
      , categories_abbreviations
      , class_online_flag
      , class_b2b_flag
      , teen_flag
      , class_cancelled_flag
      , class_type_billable_flag
     , COUNT(class_id)
       OVER (PARTITION BY class_start_date, class_code, class_type,categories_abbreviations, teen_flag)                AS DupeClassCount
     , SUM(C.number_of_students)
       OVER (PARTITION BY class_start_date, categories_abbreviations , class_type, categories_abbreviations,teen_flag) AS DupeClassStudents
     , CONCAT(CAST(class_start_date AS VARCHAR), CAST(class_code AS VARCHAR), CAST(class_type AS VARCHAR),
              CAST(categories_abbreviations AS VARCHAR),
              CAST(teen_flag AS VARCHAR))                                                                              AS DupeKey
     , CASE WHEN C.class_id IS NOT NULL THEN 1 ELSE 0 END                                                              AS Classes
     , CASE WHEN C.number_of_students >= C.number_of_seats THEN 1 ELSE 0 END                                           AS FullyBooked
     , CASE WHEN C.number_of_students = 0 THEN 1 ELSE 0 END                                                            AS NoBooked
     , CASE WHEN C.number_of_students = 1 THEN 1 ELSE 0 END                                                            AS Booked1
     , CASE WHEN C.number_of_students = 2 THEN 1 ELSE 0 END                                                            AS Booked2
     , CASE WHEN C.number_of_students = 3 THEN 1 ELSE 0 END                                                            AS Booked3
     , CASE WHEN C.number_of_students = 4 THEN 1 ELSE 0 END                                                            AS Booked4
     , CASE WHEN C.number_of_students = 5 THEN 1 ELSE 0 END                                                            AS Booked5
     , CASE WHEN C.number_of_students = 6 THEN 1 ELSE 0 END                                                            AS Booked6
     , CASE WHEN C.number_of_students = 7 THEN 1 ELSE 0 END                                                            AS Booked7
     , CASE
           WHEN C.class_teen_flag = FALSE THEN C.class_service_type
           WHEN C.class_teen_flag = TRUE AND C.class_service_type = 'standard' THEN 'Standard Teens'
           WHEN C.class_teen_flag = TRUE AND C.class_service_type = 'combined' THEN 'Standard Teens'
           WHEN C.class_teen_flag = TRUE AND C.class_service_type = 'vip' THEN 'VIP Teens'
    END                                                                                                                AS ClassCode
    FROM classes C
), opportunity as(
SELECT
    class_id
    , center_id
    , class_teacher_user_reference_id
    , class_local_start_date
    , class_date
    , ClassHourBand
    , time_of_day
    , Class_date_hour_band
    , class_code
    , class_type
    , class_technology_type
    , class_type_category
    , class_service_type
    , number_of_seats
    , class_number_of_seats_in_stand_by
    , number_of_students
    , categories_abbreviations
    , class_online_flag
    , class_b2b_flag
    , teen_flag
    , class_cancelled_flag
    , class_type_billable_flag
    , ClassCode
    , DupeClassCount
    , DupeClassStudents
    , DupeKey
    , Classes
    , FullyBooked
    , NoBooked
    , Booked1
    , Booked2
    , Booked3
    , Booked4
    , Booked5
    , Booked6
    , Booked7
    , CASE
        WHEN DupeClassStudents <= number_of_seats * (DupeClassCount - 1)
            THEN 'Students'
        WHEN class_number_of_seats_in_stand_by > 0 AND DupeClassStudents < number_of_seats * DupeClassCount
            THEN 'Standby'
        ELSE 'No' END AS opportunity


  FROM calculations C )

SELECT 
    class_id
    , center_id
    , class_teacher_user_reference_id
    , class_local_start_date
    , class_date
    , ClassHourBand
    , time_of_day
    , Class_date_hour_band
    , class_code
    , class_type
    , class_technology_type
    , class_type_category
    , class_service_type
    , number_of_seats
    , class_number_of_seats_in_stand_by
    , number_of_students
    , categories_abbreviations
    , class_online_flag
    , class_b2b_flag
    , teen_flag
    , class_cancelled_flag
    , class_type_billable_flag
    , ClassCode
    , DupeClassCount
    , DupeClassStudents
    , DupeKey
    , Classes
    , FullyBooked
    , NoBooked
    , Booked1
    , Booked2
    , Booked3
    , Booked4
    , Booked5
    , Booked6
    , Booked7
    , opportunity
    , CASE
        WHEN (opportunity <> 'No' and DupeClassCount >0)
                THEN (DupeClassCount-1)/DupeClassCount
        ELSE 0 END AS DupeClassCountm1
    ,CAST(current_timestamp AS TIMESTAMP(6)) AS load_date 
    FROM opportunity