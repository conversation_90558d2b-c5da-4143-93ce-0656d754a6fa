{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='dbt_unique_id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}


WITH RankedRecords AS (
    SELECT 
        (groupid || cast(productid as varchar)) as dbt_unique_id,
        groupid,
        productid,
        {{cast_to_timestamp('createddate')}} as createddate,
        {{cast_to_timestamp('lastupdateddate')}} as lastupdateddate,
        ROW_NUMBER() OVER (PARTITION BY (groupid || cast(productid as varchar)) ORDER BY lastupdateddate DESC) AS rn
    FROM 
        {{source('stage_contract_service', 'groupproducts')}}
)

SELECT
    {{etl_load_date()}},
    dbt_unique_id,
    groupid as group_id,
    productid as product_id,
    {{cast_to_timestamp('createddate')}} as created_date,
    {{cast_to_timestamp('lastupdateddate')}} as last_updated_date
FROM 
    RankedRecords
WHERE 
    rn = 1;