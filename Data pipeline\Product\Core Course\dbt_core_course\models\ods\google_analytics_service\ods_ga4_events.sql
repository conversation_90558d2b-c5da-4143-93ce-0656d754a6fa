{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='dbt_unique_id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

WITH RankedRecords AS (
    SELECT 
    TO_HEX(SHA256(
    CAST((
        COALESCE(CAST(event_date AS VARCHAR), 'no_event_date') ||
        COALESCE(CAST(event_timestamp AS VARCHAR), 'no_event_timestamp') ||
        COALESCE(CAST(event_name AS VARCHAR), 'no_event_name') ||
        COALESCE(CAST(user_id AS VARCHAR), 'no_user_id')
        ) AS VARBINARY)
    )) AS dbt_unique_id,
    event_date,
    event_timestamp,
    event_name,
    user_id,
    event_params,
    user_properties,
    row_number() over(partition by TO_HEX(SHA256(
    CAST((
        COALESCE(CAST(event_date AS VARCHAR), 'no_event_date') ||
        COALESCE(CAST(event_timestamp AS VARCHAR), 'no_event_timestamp') ||
        COALESCE(CAST(event_name AS VARCHAR), 'no_event_name') ||
        COALESCE(CAST(user_id AS VARCHAR), 'no_user_id')
        ) AS VARBINARY)
    ))
    order by event_date, event_timestamp) as rn
    FROM 
        {{source('stage_google_analytics_service', 'ga4_events')}}
)

SELECT
    {{etl_load_date()}},
    dbt_unique_id,
    event_date,
    event_timestamp,
    event_name,
    user_id,
    event_params,
    user_properties
FROM 
    RankedRecords
    Where rn = 1;