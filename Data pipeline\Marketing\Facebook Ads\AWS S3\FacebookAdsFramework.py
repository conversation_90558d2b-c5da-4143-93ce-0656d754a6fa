import Packages

S3 = Packages.CloudOperations.S3
Redshift = Packages.DbOperations.Database
IamRole = Packages.DbOperations.IamRole
Region = Packages.DbOperations.Region

# Reading the Facebook Ads credentials from secret key management services and assigning them to variables
SecretInstance = Packages.CloudOperations.SecretManager
FacebookAccountCredential = SecretInstance.GetSecret("FacebookAds", "eu-north-1")
AppId = FacebookAccountCredential["app_id"]
AppSecret = FacebookAccountCredential["app_secret"]
AccessToken = FacebookAccountCredential["admin_system_user_token"]

# Initializing the FacebookAdsApi with the obtained credentials
Packages.FacebookAdsApi.init(app_id=AppId, app_secret=AppSecret, access_token=AccessToken, api_version="v15.0",
                             timeout=1200)


class Ads:
    @staticmethod
    def GetCampaignInformation(AccountId, FromDate, ToDate):
        """Function is used to extract data from Facebook Ads"""
        Packages.logging.warning("FromDate:'%s'", format(FromDate))
        Packages.logging.warning("ToDate:'%s'", format(ToDate))

        # Specify the fields to retrieve from the Facebook Ads API
        Fields = ["account_name", "account_id", "campaign_name", "campaign_id", "impressions", "objective","reach",
                  "clicks", "spend", "actions", "date_start", "date_stop"]

        # Set the parameters for the API request
        Params = {
            "time_range": {"since": FromDate, "until": ToDate}, "time_increment": 1,
            "level": "campaign", "limit": 1000}

        # Initialize an empty list to store the retrieved data
        Insights = []

        # Initialize variables for tracking the number of attempts and whether data has been fetched successfully
        Attempts, Fetched = 0, False

        # Retry the API request until data is successfully fetched or the maximum number of attempts is reached
        while not Fetched:
            try:
                # Add a delay before each API request to avoid rate limits
                Packages.time.sleep(10)
                Packages.logging.warning("Attempts:'%s'", format(Attempts))

                # Make the API request to retrieve insights data for the specified account and time range
                data_response = list(Packages.AdAccount(AccountId).get_insights(params=Params, fields=Fields))

                # Extend the Insights list with the retrieved data
                Insights.extend(data_response)
            except Exception as ErrorMessage:
                Packages.logging.warning("ErrorMessage:'%s'", format(ErrorMessage))
                Packages.logging.warning(f'Trying again in {60 + 3 * Attempts} seconds.')

                # Add a delay before retrying the API request
                Packages.time.sleep(60 + 3 * Attempts)

                # Increment the number of attempts
                Attempts += 1

                # Abort fetching if the maximum number of attempts is reached
                if Attempts > 3:
                    Packages.logging.warning('Failed after 3 Attempts, abort fetching.')
                    raise Exception
            else:
                # Set Fetched to True if data was fetched successfully
                Fetched = True

        # Return the retrieved insights data
        return Insights

    @staticmethod
    def TransformResponse(Insights, Territory):
        """Function is used to Transform JSON Response"""
        TransformedResponseJson = []

        # Check if Insights list is empty
        if len(Insights) == 0:
            return TransformedResponseJson

        # Iterate over each element in Insights
        for Columns in Insights:
            # If we do not receive an API response for these keys, we will set their values to zero
            if "clicks" not in Columns:
                Columns["clicks"] = "0"
            if "spend" not in Columns:
                Columns["spend"] = "0"
            if "impressions" not in Columns:
                Columns["impressions"] = "0"
            if "reach" not in Columns:
                Columns["reach"] = "0"
            # Check if "actions" key is present in Columns dictionary
            if "actions" in Columns:
                # Iterate over each action in "actions" list
                for iterate in range(len(Columns["actions"])):
                    # Create a dictionary to store transformed data
                    TransformStructure = {
                        "AccountId": Columns["account_id"],
                        "AccountName": Columns["account_name"],
                        "ActionType": Columns["actions"][iterate]["action_type"],
                        "ActionValue": Columns["actions"][iterate]["value"],
                        "CampaignId": Columns["campaign_id"],
                        "CampaignName": Columns["campaign_name"],
                        "Clicks": Columns["clicks"],
                        "DateStart": Columns["date_start"],
                        "DateStop": Columns["date_stop"],
                        "Impressions": Columns["impressions"],
                        "Spend": Columns["spend"],
                        "Territory": Territory,
                        "Objective": Columns["objective"],
                        "Reach":Columns["reach"]
                    }
                    # Append the transformed data to TransformedResponseJson list
                    TransformedResponseJson.append(TransformStructure)
            else:
                # Create a dictionary to store transformed data when "actions" key is not present
                TransformStructure = {
                    "AccountId": Columns["account_id"],
                    "AccountName": Columns["account_name"],
                    "ActionType": None,
                    "ActionValue": None,
                    "CampaignId": Columns["campaign_id"],
                    "CampaignName": Columns["campaign_name"],
                    "Clicks": Columns["clicks"],
                    "DateStart": Columns["date_start"],
                    "DateStop": Columns["date_stop"],
                    "Impressions": Columns["impressions"],
                    "Spend": Columns["spend"],
                    "Territory": Territory,
                    "Objective": Columns["objective"],
                    "Reach": Columns["reach"]
                }
                # Append the transformed data to TransformedResponseJson list
                TransformedResponseJson.append(TransformStructure)

        # Return the list of transformed data
        return TransformedResponseJson

    @staticmethod
    def CreateDataFrame(TransformedResponseJson):
        """Function is used to create DataFrame"""

        # Check if TransformedResponseJson is not empty
        if len(TransformedResponseJson) != 0:
            # Create a DataFrame using TransformedResponseJson
            AdsDf = Packages.pd.DataFrame(TransformedResponseJson)

            # Define the desired column names for the DataFrame
            TransformedColumnName = ["AccountId", "AccountName", "ActionType", "ActionValue", "CampaignId",
                                     "CampaignName", "Clicks", "DateStart", "DateStop", "Impressions", "Spend",
                                     "Territory", "Objective","Reach"]

            # Reindex the columns of AdsDf using TransformedColumnName
            ReindexAdsResponseDF = AdsDf.reindex(columns=TransformedColumnName)

            # Return the reindexed DataFrame
            return ReindexAdsResponseDF
        else:
            # If TransformedResponseJson is empty, create an empty DataFrame
            EmptyDf = Packages.pd.DataFrame(TransformedResponseJson)
            return EmptyDf

    @staticmethod
    def CaptureResponse(DataResponse, Territory, Bucket, CycleId):
        """Function Is Used To Store Response In S3 As CSV File"""

        # Get the minimum date from the 'DateStart' column of DataResponse
        FromDate = min(DataResponse["DateStart"])

        # Get the maximum date from the 'DateStop' column of DataResponse
        ToDate = max(DataResponse["DateStop"])

        # Generate the file key for storing the response in S3
        FileKey = f"FacebookAdsResponse/{Territory}/{CycleId}/" + \
                  f"{FromDate}-{ToDate}/{Territory}Response.csv"

        # Write the DataResponse as a CSV file to the specified file path in S3
        S3WriteDataResponse = S3.WriteCsvFile(FilePath=FileKey,
                                              Bucket=Bucket,
                                              DataResponse=DataResponse)
        Packages.logging.warning("S3WriteDataResponse:'%s'", format(S3WriteDataResponse))

        # Return the file key indicating the location of the stored response in S3
        return FileKey

    @staticmethod
    def DataExtractionProcess(AccountId, Territory, LoadType, CutoffDate, Bucket, CycleId):
        """Function Is Used To Extract Data From Ads"""
        Packages.logging.warning("AccountId:'%s'", format(AccountId))
        Packages.logging.warning("Territory:'%s'", format(Territory))
        Packages.logging.warning("LoadType:'%s'", format(LoadType))
        Packages.logging.warning("CutoffDate:'%s'", format(CutoffDate))

        # Declaring an empty list to capture DataResponse
        ResponseJson = []
        """date conversion from date: previous date +1 and ToDate: current date -1"""
        # Date conversion from CutoffDate to FromDate and ToDate
        DateFormat = "%Y-%m-%d"
        FromDateConversion = \
            Packages.datetime.datetime.strptime(CutoffDate, DateFormat) + Packages.datetime.timedelta(days=1)
        FromDate = FromDateConversion.strftime(DateFormat)
        Packages.logging.warning("FromDate:'%s'", format(FromDate))
        GetCurrentDayDate = Packages.datetime.datetime.today().strftime(DateFormat)
        ToDateConversion = \
            Packages.datetime.datetime.strptime(GetCurrentDayDate, DateFormat) - Packages.datetime.timedelta(days=1)
        ToDate = ToDateConversion.strftime(DateFormat)
        Packages.logging.warning("ToDate:'%s'", format(ToDate))
        if LoadType == "Initial":
            # If LoadType is 'Initial', iterate over each month in the date range
            DateRange = Packages.Utils.Tools.GetMonthRanges(FromDate, ToDate)
            for Date in DateRange:
                # Get campaign information for each month
                DataResponseJson = Ads.GetCampaignInformation(AccountId=AccountId,
                                                              FromDate=Date["FromDate"],
                                                              ToDate=Date["ToDate"])
                if len(DataResponseJson) != 0:
                    ResponseJson.extend(DataResponseJson)
        if LoadType == "Incremental":
            # If LoadType is 'Incremental', get data for the specified date range
            DataResponseJson = Ads.GetCampaignInformation(AccountId=AccountId,
                                                          FromDate=FromDate,
                                                          ToDate=ToDate)
            ResponseJson.extend(DataResponseJson)

        # Transform the response JSON
        TransformedResponseJson = Ads.TransformResponse(Insights=ResponseJson, Territory=Territory)

        # Create a DataFrame from the transformed response
        ResponseDf = Ads.CreateDataFrame(TransformedResponseJson=TransformedResponseJson)
        if len(ResponseDf) == 0:
            # If the DataFrame is empty, no response to store
            Packages.logging.warning("no response to store")
            Summary = {
                "CutoffDate": ToDate,
                "RecordsProcessed": 0
            }
            return Summary

        # Store the response in S3 as a CSV file
        S3WriteResponse = Ads.CaptureResponse(DataResponse=ResponseDf,
                                              Territory=Territory,
                                              Bucket=Bucket,
                                              CycleId=CycleId)

        # Execute COPY command to move data from S3 file to Redshift table
        CopyCommandQuery = """COPY facebook.ads FROM 's3://{}/{}' iam_role '{}' region '{}' IGNOREHEADER 1 CSV
                                                            timeformat 'auto';""" \
            .format(Bucket, S3WriteResponse, IamRole, Region)
        Packages.logging.warning("CopyCommandQuery:'%s'", format(CopyCommandQuery))

        ExecuteCopyCommand = Redshift.Execution(ExecutionType="WriteTable",
                                                Query=CopyCommandQuery,
                                                StatementName=f"{Territory}")
        Packages.logging.warning("ExecuteCopyCommand:'%s'", format(ExecuteCopyCommand))

        # Prepare the summary of the process
        Summary = {
            "S3": {"DataResponse": S3WriteResponse},
            "Redshift": ExecuteCopyCommand,
            "CutoffDate": max(ResponseDf['DateStop']),
            "RecordsProcessed": len(ResponseDf)
        }
        return Summary
