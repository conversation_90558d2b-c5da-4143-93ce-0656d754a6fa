import CloudOperations
import logging

StepFunction = CloudOperations.StepFunction

# Define the ExecutionInput variable with the value "NaverAds"
ExecutionInput = "NaverAds"

# Define the StateMachineArn variable with the specified ARN
StateMachineArn = 'arn:aws:states:eu-north-1:262158335980:stateMachine:NaverAdsWorkFlow'

# Call the CheckStepFunctionsRunning method from StepFunction class with StateMachineArn as an argument and assign the result to CheckStateMachine variable
CheckStateMachine = StepFunction.CheckStepFunctionsRunning(StateMachineArn)
if len(CheckStateMachine['executions']) == 0:

    # If there are no executions running for the specified state machine
    logging.warning("There is no running step function proceed to trigger the step function")

    # Call the StartStepFunction method from StepFunction class with ExecutionInput and StateMachineArn as arguments to trigger the step function
    StepFunction.StartStepFunction(ExecutionInput, StateMachineArn)
else:

    # If there are executions running for the specified state machine
    logging\
        .warning("There is currently step function in running status please verify for long run of step function")
