{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        id,
        description,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                id
        ) AS rn
    FROM
        {{ source(
            'stage_center_configuration_service',
            'timezone'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    id,
    description
FROM
    rankedrecords
WHERE
    rn = 1;
