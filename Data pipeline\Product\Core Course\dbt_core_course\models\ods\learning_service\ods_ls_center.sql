{{ config(
    materialized = 'incremental',
    incremental_strategy = 'merge',
    unique_key = 'id',
    on_schema_change = 'append_new_columns',
    table_type = 'iceberg',
    format = 'parquet'
) }}

WITH rankedrecords AS (

    SELECT
        zone,
        centernumber,
        {{ cast_to_timestamp('created') }} as created,
        maximumstudentsinstandby,
        isstandbyenabled,
        {{ cast_to_timestamp('lastupdated') }} as lastupdated,
        id,
        referencecenterid,
        name,
        territoryid,
        timezoneid,
        address1,
        address2,
        city,
        postalcode,
        primaryphone,
        email,
        state,
        country,
        imagename,
        socialnetworkid1,
        socialnetworkaddress1,
        socialnetworkid2,
        socialnetworkaddress2,
        socialnetworkid3,
        socialnetworkaddress3,
        socialnetworkid4,
        socialnetworkaddress4,
        ROW_NUMBER() over (
            PARTITION BY id
            ORDER BY
                lastupdated DESC
        ) AS rn
    FROM
        {{ source(
            'stage_learning_service',
            'center'
        ) }}
)
SELECT 
    {{etl_load_date()}},
    zone,
    centernumber as center_number,
    created,
    maximumstudentsinstandby as maximum_students_in_standby,
    isstandbyenabled as is_standby_enabled,
    lastupdated as last_updated,
    id,
    referencecenterid as reference_center_id,
    name,
    territoryid as territory_id,
    timezoneid as time_zone_id,
    address1,
    address2,
    city,
    postalcode as postal_code,
    primaryphone as primary_phone,
    email,
    state,
    country,
    imagename as image_name,
    socialnetworkid1 as social_network_id1,
    socialnetworkaddress1 as social_network_address1,
    socialnetworkid2 as social_network_id2,
    socialnetworkaddress2 as social_network_address2,
    socialnetworkid3 as social_network_id3,
    socialnetworkaddress3 as social_network_address3,
    socialnetworkid4 as social_network_id4,
    socialnetworkaddress4 as social_network_address4
FROM
    rankedrecords
WHERE
    rn = 1;
