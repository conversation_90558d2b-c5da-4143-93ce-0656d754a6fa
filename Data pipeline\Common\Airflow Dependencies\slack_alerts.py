from airflow.providers.slack.hooks.slack_webhook import SlackWebhookHook


# Send Slack notification from our success & failure callback

def task_success_callback(context):
    slack_msg = f"""
    :large_green_circle: Airflow Task succeeded.
    *Task*: {context.get('task_instance').task_id}
    *Dag*: {context.get('task_instance').dag_id}
    *Execution Time*: {context.get('execution_date')}
    *Log Url*: {context.get('task_instance').log_url}
    """

    slack_hook = SlackWebhookHook(slack_webhook_conn_id='slack_webhook')
    slack_hook.send(text=slack_msg)


def task_failure_callback(context):
    error_message = context.get('exception')
    slack_msg = f"""
    :red_circle: Airflow Task Failed.
    *Task*: {context.get('task_instance').task_id}
    *Dag*: {context.get('task_instance').dag_id}
    *Execution Time*: {context.get('execution_date')}
    *Log Url*: {context.get('task_instance').log_url}
    *Status*: {context.get('task_instance').state}
    """
    if error_message:
        slack_msg += f"\n*Error Message*: {error_message}"

    slack_hook = SlackWebhookHook(slack_webhook_conn_id='slack_webhook')
    slack_hook.send(text=slack_msg)

def task_warning_callback(context):
    warning_message = context['task_instance'].xcom_pull(key='warning_message', task_ids='check_dbt_output')
    slack_msg = f"""
    :warning: Airflow Task Warning.
    *Task*: {context.get('task_instance').task_id}
    *Dag*: {context.get('task_instance').dag_id}
    *Execution Time*: {context.get('execution_date')}
    *Log Url*: {context.get('task_instance').log_url}
    *Status*: {context.get('task_instance').state}
    *Warning*: {warning_message}
    """

    slack_hook = SlackWebhookHook(slack_webhook_conn_id='slack_webhook')
    slack_hook.send(text=slack_msg)
