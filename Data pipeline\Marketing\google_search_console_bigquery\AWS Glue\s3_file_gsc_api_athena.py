import sys
import json
import datetime
from pyspark import SparkConf, SparkContext
from pyspark.sql import SparkSession
from pyspark.sql.functions import from_unixtime, to_timestamp, when, lit
from awsglue.utils import getResolvedOptions
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)

# Initialize Spark configuration and context
conf = SparkConf().setMaster("local").setAppName("MyApp")
sc = SparkContext.getOrCreate(conf=conf)
spark = SparkSession(sc)

# Get the current date
current_date = datetime.date.today()

# Define S3 file paths
file_path = f's3://etl-dev-gsc-extract/raw_files/{current_date}/*/*.json'
write_path = 's3://etl-dev-gsc-extract/raw_layer_table/gsc_test_table_page'
table_name = 'google_search_console_dev.gsc_raw_extract'

# Read JSON files from S3
response = spark.read.json(file_path)
response.show()

# Write data to a Hive table
response.write.mode('overwrite').saveAsTable(table_name, path=write_path)
