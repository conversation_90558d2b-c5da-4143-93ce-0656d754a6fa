{{ config(
    materialized='incremental',
    incremental_strategy='merge',
    unique_key='id',
    on_schema_change='append_new_columns',
    table_type='iceberg',
    format='parquet'
) }}

with ods_data as (
    select * from {{ ref(
            'ods_snb_role'
        ) }}
)

SELECT {{etl_load_date()}},
    Id,
    Description,
    Lowered_Role_Name,
    Code,
    Created,
    Last_Updated
from
    ods_data